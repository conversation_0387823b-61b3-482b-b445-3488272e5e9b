{"version": 3, "file": "azureCliCredentials.js", "sourceRoot": "", "sources": ["../../../lib/credentials/azureCliCredentials.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,kDAA8E;AAG9E,oCAAkC;AAqElC;;GAEG;AACH,MAAa,mBAAmB;IA6B9B,YACE,gBAAoC,EACpC,SAAyB;IACzB,gDAAgD;IAChD,WAAmB,8BAA8B;QAvBnD;;;;;;;;;WASG;QACH,gDAAgD;QAChD,aAAQ,GAAW,8BAA8B,CAAC;QAElD;;;WAGG;QACc,iCAA4B,GAAW,GAAG,CAAC;QAQ1D,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED;;;;OAIG;IACU,QAAQ;;YACnB,IAAI,IAAI,CAAC,gBAAgB,EAAE,IAAI,IAAI,CAAC,uBAAuB,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;gBAC3F,IAAI;oBACF,2BAA2B;oBAC3B,IAAI,CAAC,SAAS,GAAG,MAAM,mBAAmB,CAAC,cAAc,CAAC;wBACxD,oBAAoB,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE;wBAC9C,QAAQ,EAAE,IAAI,CAAC,QAAQ;qBACxB,CAAC,CAAC;iBACJ;gBAAC,OAAO,GAAG,EAAE;oBACZ,MAAM,IAAI,KAAK,CACb,oDAAoD;wBAClD,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,CACnD,CAAC;iBACH;aACF;YACD,MAAM,MAAM,GAAkB;gBAC5B,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW;gBACvC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS;gBACnC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;aAChC,CAAC;YACF,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;IAED;;;OAGG;IACU,WAAW,CAAC,WAAwB;;YAC/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,sBAAe,CAAC,eAAe,CAAC,aAAa,EAC7C,GAAG,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,WAAW,EAAE,CAC1D,CAAC;YACF,OAAO,WAAW,CAAC;QACrB,CAAC;KAAA;IAEO,gBAAgB;QACtB,IAAI,MAAM,GAAG,IAAI,CAAC;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAC1C,IACE,IAAI,CAAC,SAAS,CAAC,SAAS;YACxB,IAAI,CAAC,SAAS,CAAC,SAAS,YAAY,IAAI;YACxC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG;gBACzD,IAAI,CAAC,4BAA4B,EACnC;YACA,MAAM,GAAG,KAAK,CAAC;SAChB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,uBAAuB;QAC7B,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IAClE,CAAC;IAEO,WAAW;QACjB,IAAI;YACF,MAAM,SAAS,GAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,MAAM,MAAM,GAAW,kBAAkB,CACvC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC;iBAC7B,QAAQ,CAAC,QAAQ,CAAC;iBAClB,KAAK,CAAC,EAAE,CAAC;iBACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;gBACT,OAAO,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC,CAAC;iBACD,IAAI,CAAC,EAAE,CAAC,CACZ,CAAC;YAEF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAC3B;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,GAAG,GAAG,qDAAqD,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;IACH,CAAC;IAEO,+BAA+B,CAAC,WAAmB,EAAE,eAAuB;QAClF,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACtE,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,eAAe,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAClF,OAAO,CACL,CAAC,WAAW,KAAK,qCAAqC;YACpD,eAAe,KAAK,8BAA8B,CAAC;YACrD,CAAC,WAAW,KAAK,8BAA8B;gBAC7C,eAAe,KAAK,qCAAqC,CAAC,CAC7D,CAAC;IACJ,CAAC;IAEO,mBAAmB;QACzB,MAAM,WAAW,GAAgB,IAAI,CAAC,WAAW,EAAE,CAAC;QACpD,yDAAyD;QACzD,8CAA8C;QAC9C,MAAM,eAAe,GACnB,WAAW,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;YAC9C,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC7F,MAAM,MAAM,GAAG,IAAI,CAAC,+BAA+B,CAAC,WAAW,EAAE,eAAe,CAAC;YAC/E,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,eAAe,KAAK,WAAW,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAO,cAAc,CAAC,UAA8B,EAAE;;YAC1D,IAAI;gBACF,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;gBACrD,IAAI,OAAO,CAAC,oBAAoB,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;iBACjD;gBACD,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAChC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACrC;gBACD,MAAM,MAAM,GAAQ,MAAM,cAAM,CAAC,YAAY,CAAC,CAAC;gBAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAC9C,OAAO,MAAwB,CAAC;aACjC;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,OAAO,GACX,mDAAmD,GAAG,cAAc,GAAG,CAAC,KAAK,EAAE,CAAC;gBAClF,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;QACH,CAAC;KAAA;IAED;;;;OAIG;IACH,MAAM,CAAO,eAAe,CAAC,oBAA6B;;YACxD,IACE,oBAAoB;gBACpB,CAAC,OAAO,oBAAoB,KAAK,QAAQ,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAC1E;gBACA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;aACvE;YACD,IAAI;gBACF,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACzC,IAAI,oBAAoB,EAAE;oBACxB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxB,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;iBACzC;gBACD,MAAM,MAAM,GAAuB,MAAM,cAAM,CAAC,YAAY,CAAC,CAAC;gBAC9D,OAAO,MAAM,CAAC;aACf;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,OAAO,GACX,kFAAkF;oBAClF,cAAc,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;QACH,CAAC;KAAA;IAED;;;;OAIG;IACH,MAAM,CAAO,sBAAsB,CAAC,oBAA4B;;YAC9D,IAAI;gBACF,MAAM,cAAM,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC,CAAC;aAC9D;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,OAAO,GACX,gEAAgE;oBAChE,cAAc,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;QACH,CAAC;KAAA;IAED;;;OAGG;IACH,MAAM,CAAO,oBAAoB,CAC/B,UAAsC,EAAE;;YAExC,IAAI,gBAAgB,GAAU,EAAE,CAAC;YACjC,IAAI;gBACF,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBACzC,IAAI,OAAO,CAAC,GAAG,EAAE;oBACf,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBAC7B;gBACD,IAAI,OAAO,CAAC,OAAO,EAAE;oBACnB,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAChC;gBACD,gBAAgB,GAAG,MAAM,cAAM,CAAC,YAAY,CAAC,CAAC;gBAC9C,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,EAAE;oBAC/C,KAAK,MAAM,GAAG,IAAI,gBAAgB,EAAE;wBAClC,IAAI,GAAG,CAAC,SAAS,EAAE;4BACjB,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,SAAS,CAAC;4BACpC,OAAO,GAAG,CAAC,SAAS,CAAC;yBACtB;qBACF;iBACF;gBACD,OAAO,gBAAgB,CAAC;aACzB;YAAC,OAAO,GAAG,EAAE;gBACZ,MAAM,OAAO,GACX,sEAAsE;oBACtE,cAAc,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;aAC1B;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACH,MAAM,CAAO,MAAM,CAAC,UAA8B,EAAE;;YAClD,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACvD,mBAAmB,CAAC,eAAe,CAAC,OAAO,CAAC,oBAAoB,CAAC;gBACjE,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC;aAC5C,CAAC,CAAC;YACH,OAAO,IAAI,mBAAmB,CAAC,eAAe,EAAE,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjF,CAAC;KAAA;CACF;AAxQD,kDAwQC"}