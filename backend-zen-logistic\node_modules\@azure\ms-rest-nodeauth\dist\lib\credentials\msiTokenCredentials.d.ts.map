{"version": 3, "file": "msiTokenCredentials.d.ts", "sourceRoot": "", "sources": ["../../../lib/credentials/msiTokenCredentials.ts"], "names": [], "mappings": "AAGA,OAAO,EAAa,WAAW,EAAE,UAAU,EAAqB,MAAM,mBAAmB,CAAC;AAC1F,OAAO,EAAE,sBAAsB,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AAGjF;;GAEG;AACH,MAAM,WAAW,UAAU;IACzB;;;;;;;;OAQG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,UAAU,CAAC,EAAE,UAAU,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,gBAAiB,SAAQ,aAAa;IACrD;;OAEG;IACH,QAAQ,EAAE,CAAC,EAAE,MAAM,GAAG,GAAG,CAAC;CAC3B;AAED;;;GAGG;AACH,8BAAsB,mBAAoB,YAAW,sBAAsB;IACzE;;;;;;;OAOG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC;IAElC;;;;;;;OAOG;gBACS,OAAO,EAAE,UAAU;IAa/B;;;;;OAKG;IACH,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,aAAa;IA+C/C;;;;OAIG;aACY,QAAQ,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAEpD,SAAS,CAAC,QAAQ,CAAC,qBAAqB,IAAI,WAAW;IAEvD;;;;;OAKG;IACU,WAAW,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;CAQzE"}