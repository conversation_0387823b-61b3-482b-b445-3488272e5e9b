import{a as S,b as z,f as vt,g as Fe,n as Y,o as me,p as ne}from"./chunk-4PPJG5BB.js";import{W as E,Y as Tt}from"./chunk-J5YWIVYY.js";import{a as fe,c as Et}from"./chunk-L3UST63Y.js";function bt(n){return new E(3e3,!1)}function bs(){return new E(3100,!1)}function ws(){return new E(3101,!1)}function As(n){return new E(3001,!1)}function Ps(n){return new E(3003,!1)}function Ns(n){return new E(3004,!1)}function At(n,e){return new E(3005,!1)}function Pt(){return new E(3006,!1)}function Nt(){return new E(3007,!1)}function Mt(n,e){return new E(3008,!1)}function Ct(n){return new E(3002,!1)}function kt(n,e,t,s,i){return new E(3010,!1)}function Dt(){return new E(3011,!1)}function Rt(){return new E(3012,!1)}function Ot(){return new E(3200,!1)}function Lt(){return new E(3202,!1)}function Ft(){return new E(3013,!1)}function It(n){return new E(3014,!1)}function zt(n){return new E(3015,!1)}function Kt(n){return new E(3016,!1)}function qt(n){return new E(3500,!1)}function Bt(n){return new E(3501,!1)}function Qt(n,e){return new E(3404,!1)}function Ms(n){return new E(3502,!1)}function Vt(n){return new E(3503,!1)}function $t(){return new E(3300,!1)}function Ut(n){return new E(3504,!1)}function Gt(n){return new E(3301,!1)}function jt(n,e){return new E(3302,!1)}function Wt(n){return new E(3303,!1)}function Ht(n,e){return new E(3400,!1)}function Yt(n){return new E(3401,!1)}function Xt(n){return new E(3402,!1)}function Jt(n,e){return new E(3505,!1)}var Cs=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function $(n){switch(n.length){case 0:return new Y;case 1:return n[0];default:return new me(n)}}function qe(n,e,t=new Map,s=new Map){let i=[],r=[],a=-1,o=null;if(e.forEach(l=>{let u=l.get("offset"),h=u==a,c=h&&o||new Map;l.forEach((_,y)=>{let f=y,g=_;if(y!=="offset")switch(f=n.normalizePropertyName(f,i),g){case ne:g=t.get(y);break;case z:g=s.get(y);break;default:g=n.normalizeStyleValue(y,f,g,i);break}c.set(f,g)}),h||r.push(c),o=c,a=u}),i.length)throw Ms(i);return r}function pe(n,e,t,s){switch(e){case"start":n.onStart(()=>s(t&&Ie(t,"start",n)));break;case"done":n.onDone(()=>s(t&&Ie(t,"done",n)));break;case"destroy":n.onDestroy(()=>s(t&&Ie(t,"destroy",n)));break}}function Ie(n,e,t){let s=t.totalTime,i=!!t.disabled,r=ge(n.element,n.triggerName,n.fromState,n.toState,e||n.phaseName,s??n.totalTime,i),a=n._data;return a!=null&&(r._data=a),r}function ge(n,e,t,s,i="",r=0,a){return{element:n,triggerName:e,fromState:t,toState:s,phaseName:i,totalTime:r,disabled:!!a}}function R(n,e,t){let s=n.get(e);return s||n.set(e,s=t),s}function Be(n){let e=n.indexOf(":"),t=n.substring(1,e),s=n.slice(e+1);return[t,s]}var ks=typeof document>"u"?null:document.documentElement;function ye(n){let e=n.parentNode||n.host||null;return e===ks?null:e}function Ds(n){return n.substring(1,6)=="ebkit"}var X=null,wt=!1;function Zt(n){X||(X=Os()||{},wt=X.style?"WebkitAppearance"in X.style:!1);let e=!0;return X.style&&!Ds(n)&&(e=n in X.style,!e&&wt&&(e="Webkit"+n.charAt(0).toUpperCase()+n.slice(1)in X.style)),e}function Rs(n){return Cs.has(n)}function Os(){return typeof document<"u"?document.body:null}function Qe(n,e){for(;e;){if(e===n)return!0;e=ye(e)}return!1}function Ve(n,e,t){if(t)return Array.from(n.querySelectorAll(e));let s=n.querySelector(e);return s?[s]:[]}var Ls=1e3,$e="{{",Fs="}}",_e="ng-enter",re="ng-leave",ae="ng-trigger",oe=".ng-trigger",Ue="ng-animating",Se=".ng-animating";function V(n){if(typeof n=="number")return n;let e=n.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:ze(parseFloat(e[1]),e[2])}function ze(n,e){switch(e){case"s":return n*Ls;default:return n}}function le(n,e,t){return n.hasOwnProperty("duration")?n:Is(n,e,t)}function Is(n,e,t){let s=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,i,r=0,a="";if(typeof n=="string"){let o=n.match(s);if(o===null)return e.push(bt(n)),{duration:0,delay:0,easing:""};i=ze(parseFloat(o[1]),o[2]);let l=o[3];l!=null&&(r=ze(parseFloat(l),o[4]));let u=o[5];u&&(a=u)}else i=n;if(!t){let o=!1,l=e.length;i<0&&(e.push(bs()),o=!0),r<0&&(e.push(ws()),o=!0),o&&e.splice(l,0,bt(n))}return{duration:i,delay:r,easing:a}}function xt(n){return n.length?n[0]instanceof Map?n:n.map(e=>new Map(Object.entries(e))):[]}function Ge(n){return Array.isArray(n)?new Map(...n):new Map(n)}function K(n,e,t){e.forEach((s,i)=>{let r=Ee(i);t&&!t.has(i)&&t.set(i,n.style[r]),n.style[r]=s})}function G(n,e){e.forEach((t,s)=>{let i=Ee(s);n.style[i]=""})}function ee(n){return Array.isArray(n)?n.length==1?n[0]:vt(n):n}function es(n,e,t){let s=e.params||{},i=je(n);i.length&&i.forEach(r=>{s.hasOwnProperty(r)||t.push(As(r))})}var Ke=new RegExp(`${$e}\\s*(.+?)\\s*${Fs}`,"g");function je(n){let e=[];if(typeof n=="string"){let t;for(;t=Ke.exec(n);)e.push(t[1]);Ke.lastIndex=0}return e}function te(n,e,t){let s=`${n}`,i=s.replace(Ke,(r,a)=>{let o=e[a];return o==null&&(t.push(Ps(a)),o=""),o.toString()});return i==s?n:i}var zs=/-+([a-z0-9])/g;function Ee(n){return n.replace(zs,(...e)=>e[1].toUpperCase())}function Ks(n){return n.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function ts(n,e){return n===0||e===0}function ss(n,e,t){if(t.size&&e.length){let s=e[0],i=[];if(t.forEach((r,a)=>{s.has(a)||i.push(a),s.set(a,r)}),i.length)for(let r=1;r<e.length;r++){let a=e[r];i.forEach(o=>a.set(o,Te(n,o)))}}return e}function O(n,e,t){switch(e.type){case S.Trigger:return n.visitTrigger(e,t);case S.State:return n.visitState(e,t);case S.Transition:return n.visitTransition(e,t);case S.Sequence:return n.visitSequence(e,t);case S.Group:return n.visitGroup(e,t);case S.Animate:return n.visitAnimate(e,t);case S.Keyframes:return n.visitKeyframes(e,t);case S.Style:return n.visitStyle(e,t);case S.Reference:return n.visitReference(e,t);case S.AnimateChild:return n.visitAnimateChild(e,t);case S.AnimateRef:return n.visitAnimateRef(e,t);case S.Query:return n.visitQuery(e,t);case S.Stagger:return n.visitStagger(e,t);default:throw Ns(e.type)}}function Te(n,e){return window.getComputedStyle(n)[e]}var gs=(()=>{class n{validateStyleProperty(t){return Zt(t)}containsElement(t,s){return Qe(t,s)}getParentElement(t){return ye(t)}query(t,s,i){return Ve(t,s,i)}computeStyle(t,s,i){return i||""}animate(t,s,i,r,a,o=[],l){return new Y(i,r)}static \u0275fac=function(s){return new(s||n)};static \u0275prov=Tt({token:n,factory:n.\u0275fac})}return n})(),is=class{static NOOP=new gs},Ze=class{},xe=class{normalizePropertyName(e,t){return e}normalizeStyleValue(e,t,s,i){return s}},qs=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),et=class extends Ze{normalizePropertyName(e,t){return Ee(e)}normalizeStyleValue(e,t,s,i){let r="",a=s.toString().trim();if(qs.has(t)&&s!==0&&s!=="0")if(typeof s=="number")r="px";else{let o=s.match(/^[+-]?[\d\.]+([a-z]*)$/);o&&o[1].length==0&&i.push(At(e,s))}return a+r}};var Pe="*";function Bs(n,e){let t=[];return typeof n=="string"?n.split(/\s*,\s*/).forEach(s=>Qs(s,t,e)):t.push(n),t}function Qs(n,e,t){if(n[0]==":"){let l=Vs(n,t);if(typeof l=="function"){e.push(l);return}n=l}let s=n.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(s==null||s.length<4)return t.push(zt(n)),e;let i=s[1],r=s[2],a=s[3];e.push(ns(i,a));let o=i==Pe&&a==Pe;r[0]=="<"&&!o&&e.push(ns(a,i))}function Vs(n,e){switch(n){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(t,s)=>parseFloat(s)>parseFloat(t);case":decrement":return(t,s)=>parseFloat(s)<parseFloat(t);default:return e.push(Kt(n)),"* => *"}}var ve=new Set(["true","1"]),be=new Set(["false","0"]);function ns(n,e){let t=ve.has(n)||be.has(n),s=ve.has(e)||be.has(e);return(i,r)=>{let a=n==Pe||n==i,o=e==Pe||e==r;return!a&&t&&typeof i=="boolean"&&(a=i?ve.has(n):be.has(n)),!o&&s&&typeof r=="boolean"&&(o=r?ve.has(e):be.has(e)),a&&o}}var ys=":self",$s=new RegExp(`s*${ys}s*,?`,"g");function ft(n,e,t,s){return new tt(n).build(e,t,s)}var rs="",tt=class{_driver;constructor(e){this._driver=e}build(e,t,s){let i=new st(t);return this._resetContextStyleTimingState(i),O(this,ee(e),i)}_resetContextStyleTimingState(e){e.currentQuerySelector=rs,e.collectedStyles=new Map,e.collectedStyles.set(rs,new Map),e.currentTime=0}visitTrigger(e,t){let s=t.queryCount=0,i=t.depCount=0,r=[],a=[];return e.name.charAt(0)=="@"&&t.errors.push(Pt()),e.definitions.forEach(o=>{if(this._resetContextStyleTimingState(t),o.type==S.State){let l=o,u=l.name;u.toString().split(/\s*,\s*/).forEach(h=>{l.name=h,r.push(this.visitState(l,t))}),l.name=u}else if(o.type==S.Transition){let l=this.visitTransition(o,t);s+=l.queryCount,i+=l.depCount,a.push(l)}else t.errors.push(Nt())}),{type:S.Trigger,name:e.name,states:r,transitions:a,queryCount:s,depCount:i,options:null}}visitState(e,t){let s=this.visitStyle(e.styles,t),i=e.options&&e.options.params||null;if(s.containsDynamicStyles){let r=new Set,a=i||{};s.styles.forEach(o=>{o instanceof Map&&o.forEach(l=>{je(l).forEach(u=>{a.hasOwnProperty(u)||r.add(u)})})}),r.size&&t.errors.push(Mt(e.name,[...r.values()]))}return{type:S.State,name:e.name,style:s,options:i?{params:i}:null}}visitTransition(e,t){t.queryCount=0,t.depCount=0;let s=O(this,ee(e.animation),t),i=Bs(e.expr,t.errors);return{type:S.Transition,matchers:i,animation:s,queryCount:t.queryCount,depCount:t.depCount,options:J(e.options)}}visitSequence(e,t){return{type:S.Sequence,steps:e.steps.map(s=>O(this,s,t)),options:J(e.options)}}visitGroup(e,t){let s=t.currentTime,i=0,r=e.steps.map(a=>{t.currentTime=s;let o=O(this,a,t);return i=Math.max(i,t.currentTime),o});return t.currentTime=i,{type:S.Group,steps:r,options:J(e.options)}}visitAnimate(e,t){let s=Ws(e.timings,t.errors);t.currentAnimateTimings=s;let i,r=e.styles?e.styles:Fe({});if(r.type==S.Keyframes)i=this.visitKeyframes(r,t);else{let a=e.styles,o=!1;if(!a){o=!0;let u={};s.easing&&(u.easing=s.easing),a=Fe(u)}t.currentTime+=s.duration+s.delay;let l=this.visitStyle(a,t);l.isEmptyStep=o,i=l}return t.currentAnimateTimings=null,{type:S.Animate,timings:s,style:i,options:null}}visitStyle(e,t){let s=this._makeStyleAst(e,t);return this._validateStyleAst(s,t),s}_makeStyleAst(e,t){let s=[],i=Array.isArray(e.styles)?e.styles:[e.styles];for(let o of i)typeof o=="string"?o===z?s.push(o):t.errors.push(Ct(o)):s.push(new Map(Object.entries(o)));let r=!1,a=null;return s.forEach(o=>{if(o instanceof Map&&(o.has("easing")&&(a=o.get("easing"),o.delete("easing")),!r)){for(let l of o.values())if(l.toString().indexOf($e)>=0){r=!0;break}}}),{type:S.Style,styles:s,easing:a,offset:e.offset,containsDynamicStyles:r,options:null}}_validateStyleAst(e,t){let s=t.currentAnimateTimings,i=t.currentTime,r=t.currentTime;s&&r>0&&(r-=s.duration+s.delay),e.styles.forEach(a=>{typeof a!="string"&&a.forEach((o,l)=>{let u=t.collectedStyles.get(t.currentQuerySelector),h=u.get(l),c=!0;h&&(r!=i&&r>=h.startTime&&i<=h.endTime&&(t.errors.push(kt(l,h.startTime,h.endTime,r,i)),c=!1),r=h.startTime),c&&u.set(l,{startTime:r,endTime:i}),t.options&&es(o,t.options,t.errors)})})}visitKeyframes(e,t){let s={type:S.Keyframes,styles:[],options:null};if(!t.currentAnimateTimings)return t.errors.push(Dt()),s;let i=1,r=0,a=[],o=!1,l=!1,u=0,h=e.steps.map(w=>{let A=this._makeStyleAst(w,t),C=A.offset!=null?A.offset:js(A.styles),N=0;return C!=null&&(r++,N=A.offset=C),l=l||N<0||N>1,o=o||N<u,u=N,a.push(N),A});l&&t.errors.push(Rt()),o&&t.errors.push(Ot());let c=e.steps.length,_=0;r>0&&r<c?t.errors.push(Lt()):r==0&&(_=i/(c-1));let y=c-1,f=t.currentTime,g=t.currentAnimateTimings,v=g.duration;return h.forEach((w,A)=>{let C=_>0?A==y?1:_*A:a[A],N=C*v;t.currentTime=f+g.delay+N,g.duration=N,this._validateStyleAst(w,t),w.offset=C,s.styles.push(w)}),s}visitReference(e,t){return{type:S.Reference,animation:O(this,ee(e.animation),t),options:J(e.options)}}visitAnimateChild(e,t){return t.depCount++,{type:S.AnimateChild,options:J(e.options)}}visitAnimateRef(e,t){return{type:S.AnimateRef,animation:this.visitReference(e.animation,t),options:J(e.options)}}visitQuery(e,t){let s=t.currentQuerySelector,i=e.options||{};t.queryCount++,t.currentQuery=e;let[r,a]=Us(e.selector);t.currentQuerySelector=s.length?s+" "+r:r,R(t.collectedStyles,t.currentQuerySelector,new Map);let o=O(this,ee(e.animation),t);return t.currentQuery=null,t.currentQuerySelector=s,{type:S.Query,selector:r,limit:i.limit||0,optional:!!i.optional,includeSelf:a,animation:o,originalSelector:e.selector,options:J(e.options)}}visitStagger(e,t){t.currentQuery||t.errors.push(Ft());let s=e.timings==="full"?{duration:0,delay:0,easing:"full"}:le(e.timings,t.errors,!0);return{type:S.Stagger,animation:O(this,ee(e.animation),t),timings:s,options:null}}};function Us(n){let e=!!n.split(/\s*,\s*/).find(t=>t==ys);return e&&(n=n.replace($s,"")),n=n.replace(/@\*/g,oe).replace(/@\w+/g,t=>oe+"-"+t.slice(1)).replace(/:animating/g,Se),[n,e]}function Gs(n){return n?fe({},n):null}var st=class{errors;queryCount=0;depCount=0;currentTransition=null;currentQuery=null;currentQuerySelector=null;currentAnimateTimings=null;currentTime=0;collectedStyles=new Map;options=null;unsupportedCSSPropertiesFound=new Set;constructor(e){this.errors=e}};function js(n){if(typeof n=="string")return null;let e=null;if(Array.isArray(n))n.forEach(t=>{if(t instanceof Map&&t.has("offset")){let s=t;e=parseFloat(s.get("offset")),s.delete("offset")}});else if(n instanceof Map&&n.has("offset")){let t=n;e=parseFloat(t.get("offset")),t.delete("offset")}return e}function Ws(n,e){if(n.hasOwnProperty("duration"))return n;if(typeof n=="number"){let r=le(n,e).duration;return We(r,0,"")}let t=n;if(t.split(/\s+/).some(r=>r.charAt(0)=="{"&&r.charAt(1)=="{")){let r=We(0,0,"");return r.dynamic=!0,r.strValue=t,r}let i=le(t,e);return We(i.duration,i.delay,i.easing)}function J(n){return n?(n=fe({},n),n.params&&(n.params=Gs(n.params))):n={},n}function We(n,e,t){return{duration:n,delay:e,easing:t}}function mt(n,e,t,s,i,r,a=null,o=!1){return{type:1,element:n,keyframes:e,preStyleProps:t,postStyleProps:s,duration:i,delay:r,totalTime:i+r,easing:a,subTimeline:o}}var ie=class{_map=new Map;get(e){return this._map.get(e)||[]}append(e,t){let s=this._map.get(e);s||this._map.set(e,s=[]),s.push(...t)}has(e){return this._map.has(e)}clear(){this._map.clear()}},Hs=1,Ys=":enter",Xs=new RegExp(Ys,"g"),Js=":leave",Zs=new RegExp(Js,"g");function pt(n,e,t,s,i,r=new Map,a=new Map,o,l,u=[]){return new it().buildKeyframes(n,e,t,s,i,r,a,o,l,u)}var it=class{buildKeyframes(e,t,s,i,r,a,o,l,u,h=[]){u=u||new ie;let c=new nt(e,t,u,i,r,h,[]);c.options=l;let _=l.delay?V(l.delay):0;c.currentTimeline.delayNextStep(_),c.currentTimeline.setStyles([a],null,c.errors,l),O(this,s,c);let y=c.timelines.filter(f=>f.containsAnimation());if(y.length&&o.size){let f;for(let g=y.length-1;g>=0;g--){let v=y[g];if(v.element===t){f=v;break}}f&&!f.allowOnlyTimelineStyles()&&f.setStyles([o],null,c.errors,l)}return y.length?y.map(f=>f.buildKeyframes()):[mt(t,[],[],[],0,_,"",!1)]}visitTrigger(e,t){}visitState(e,t){}visitTransition(e,t){}visitAnimateChild(e,t){let s=t.subInstructions.get(t.element);if(s){let i=t.createSubContext(e.options),r=t.currentTimeline.currentTime,a=this._visitSubInstructions(s,i,i.options);r!=a&&t.transformIntoNewTimeline(a)}t.previousNode=e}visitAnimateRef(e,t){let s=t.createSubContext(e.options);s.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],t,s),this.visitReference(e.animation,s),t.transformIntoNewTimeline(s.currentTimeline.currentTime),t.previousNode=e}_applyAnimationRefDelays(e,t,s){for(let i of e){let r=i?.delay;if(r){let a=typeof r=="number"?r:V(te(r,i?.params??{},t.errors));s.delayNextStep(a)}}}_visitSubInstructions(e,t,s){let r=t.currentTimeline.currentTime,a=s.duration!=null?V(s.duration):null,o=s.delay!=null?V(s.delay):null;return a!==0&&e.forEach(l=>{let u=t.appendInstructionToTimeline(l,a,o);r=Math.max(r,u.duration+u.delay)}),r}visitReference(e,t){t.updateOptions(e.options,!0),O(this,e.animation,t),t.previousNode=e}visitSequence(e,t){let s=t.subContextCount,i=t,r=e.options;if(r&&(r.params||r.delay)&&(i=t.createSubContext(r),i.transformIntoNewTimeline(),r.delay!=null)){i.previousNode.type==S.Style&&(i.currentTimeline.snapshotCurrentStyles(),i.previousNode=Ne);let a=V(r.delay);i.delayNextStep(a)}e.steps.length&&(e.steps.forEach(a=>O(this,a,i)),i.currentTimeline.applyStylesToKeyframe(),i.subContextCount>s&&i.transformIntoNewTimeline()),t.previousNode=e}visitGroup(e,t){let s=[],i=t.currentTimeline.currentTime,r=e.options&&e.options.delay?V(e.options.delay):0;e.steps.forEach(a=>{let o=t.createSubContext(e.options);r&&o.delayNextStep(r),O(this,a,o),i=Math.max(i,o.currentTimeline.currentTime),s.push(o.currentTimeline)}),s.forEach(a=>t.currentTimeline.mergeTimelineCollectedStyles(a)),t.transformIntoNewTimeline(i),t.previousNode=e}_visitTiming(e,t){if(e.dynamic){let s=e.strValue,i=t.params?te(s,t.params,t.errors):s;return le(i,t.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,t){let s=t.currentAnimateTimings=this._visitTiming(e.timings,t),i=t.currentTimeline;s.delay&&(t.incrementTime(s.delay),i.snapshotCurrentStyles());let r=e.style;r.type==S.Keyframes?this.visitKeyframes(r,t):(t.incrementTime(s.duration),this.visitStyle(r,t),i.applyStylesToKeyframe()),t.currentAnimateTimings=null,t.previousNode=e}visitStyle(e,t){let s=t.currentTimeline,i=t.currentAnimateTimings;!i&&s.hasCurrentStyleProperties()&&s.forwardFrame();let r=i&&i.easing||e.easing;e.isEmptyStep?s.applyEmptyStep(r):s.setStyles(e.styles,r,t.errors,t.options),t.previousNode=e}visitKeyframes(e,t){let s=t.currentAnimateTimings,i=t.currentTimeline.duration,r=s.duration,o=t.createSubContext().currentTimeline;o.easing=s.easing,e.styles.forEach(l=>{let u=l.offset||0;o.forwardTime(u*r),o.setStyles(l.styles,l.easing,t.errors,t.options),o.applyStylesToKeyframe()}),t.currentTimeline.mergeTimelineCollectedStyles(o),t.transformIntoNewTimeline(i+r),t.previousNode=e}visitQuery(e,t){let s=t.currentTimeline.currentTime,i=e.options||{},r=i.delay?V(i.delay):0;r&&(t.previousNode.type===S.Style||s==0&&t.currentTimeline.hasCurrentStyleProperties())&&(t.currentTimeline.snapshotCurrentStyles(),t.previousNode=Ne);let a=s,o=t.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!i.optional,t.errors);t.currentQueryTotal=o.length;let l=null;o.forEach((u,h)=>{t.currentQueryIndex=h;let c=t.createSubContext(e.options,u);r&&c.delayNextStep(r),u===t.element&&(l=c.currentTimeline),O(this,e.animation,c),c.currentTimeline.applyStylesToKeyframe();let _=c.currentTimeline.currentTime;a=Math.max(a,_)}),t.currentQueryIndex=0,t.currentQueryTotal=0,t.transformIntoNewTimeline(a),l&&(t.currentTimeline.mergeTimelineCollectedStyles(l),t.currentTimeline.snapshotCurrentStyles()),t.previousNode=e}visitStagger(e,t){let s=t.parentContext,i=t.currentTimeline,r=e.timings,a=Math.abs(r.duration),o=a*(t.currentQueryTotal-1),l=a*t.currentQueryIndex;switch(r.duration<0?"reverse":r.easing){case"reverse":l=o-l;break;case"full":l=s.currentStaggerTime;break}let h=t.currentTimeline;l&&h.delayNextStep(l);let c=h.currentTime;O(this,e.animation,t),t.previousNode=e,s.currentStaggerTime=i.currentTime-c+(i.startTime-s.currentTimeline.startTime)}},Ne={},nt=class n{_driver;element;subInstructions;_enterClassName;_leaveClassName;errors;timelines;parentContext=null;currentTimeline;currentAnimateTimings=null;previousNode=Ne;subContextCount=0;options={};currentQueryIndex=0;currentQueryTotal=0;currentStaggerTime=0;constructor(e,t,s,i,r,a,o,l){this._driver=e,this.element=t,this.subInstructions=s,this._enterClassName=i,this._leaveClassName=r,this.errors=a,this.timelines=o,this.currentTimeline=l||new Me(this._driver,t,0),o.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,t){if(!e)return;let s=e,i=this.options;s.duration!=null&&(i.duration=V(s.duration)),s.delay!=null&&(i.delay=V(s.delay));let r=s.params;if(r){let a=i.params;a||(a=this.options.params={}),Object.keys(r).forEach(o=>{(!t||!a.hasOwnProperty(o))&&(a[o]=te(r[o],a,this.errors))})}}_copyOptions(){let e={};if(this.options){let t=this.options.params;if(t){let s=e.params={};Object.keys(t).forEach(i=>{s[i]=t[i]})}}return e}createSubContext(e=null,t,s){let i=t||this.element,r=new n(this._driver,i,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(i,s||0));return r.previousNode=this.previousNode,r.currentAnimateTimings=this.currentAnimateTimings,r.options=this._copyOptions(),r.updateOptions(e),r.currentQueryIndex=this.currentQueryIndex,r.currentQueryTotal=this.currentQueryTotal,r.parentContext=this,this.subContextCount++,r}transformIntoNewTimeline(e){return this.previousNode=Ne,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,t,s){let i={duration:t??e.duration,delay:this.currentTimeline.currentTime+(s??0)+e.delay,easing:""},r=new rt(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,i,e.stretchStartingKeyframe);return this.timelines.push(r),i}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,t,s,i,r,a){let o=[];if(i&&o.push(this.element),e.length>0){e=e.replace(Xs,"."+this._enterClassName),e=e.replace(Zs,"."+this._leaveClassName);let l=s!=1,u=this._driver.query(this.element,e,l);s!==0&&(u=s<0?u.slice(u.length+s,u.length):u.slice(0,s)),o.push(...u)}return!r&&o.length==0&&a.push(It(t)),o}},Me=class n{_driver;element;startTime;_elementTimelineStylesLookup;duration=0;easing=null;_previousKeyframe=new Map;_currentKeyframe=new Map;_keyframes=new Map;_styleSummary=new Map;_localTimelineStyles=new Map;_globalTimelineStyles;_pendingStyles=new Map;_backFill=new Map;_currentEmptyStepKeyframe=null;constructor(e,t,s,i){this._driver=e,this.element=t,this.startTime=s,this._elementTimelineStylesLookup=i,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(t),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(t,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let t=this._keyframes.size===1&&this._pendingStyles.size;this.duration||t?(this.forwardTime(this.currentTime+e),t&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,t){return this.applyStylesToKeyframe(),new n(this._driver,e,t||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=Hs,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,t){this._localTimelineStyles.set(e,t),this._globalTimelineStyles.set(e,t),this._styleSummary.set(e,{time:this.currentTime,value:t})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[t,s]of this._globalTimelineStyles)this._backFill.set(t,s||z),this._currentKeyframe.set(t,z);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,t,s,i){t&&this._previousKeyframe.set("easing",t);let r=i&&i.params||{},a=xs(e,this._globalTimelineStyles);for(let[o,l]of a){let u=te(l,r,s);this._pendingStyles.set(o,u),this._localTimelineStyles.has(o)||this._backFill.set(o,this._globalTimelineStyles.get(o)??z),this._updateStyle(o,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,t)=>{this._currentKeyframe.set(t,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,t)=>{this._currentKeyframe.has(t)||this._currentKeyframe.set(t,e)}))}snapshotCurrentStyles(){for(let[e,t]of this._localTimelineStyles)this._pendingStyles.set(e,t),this._updateStyle(e,t)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let t in this._currentKeyframe)e.push(t);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((t,s)=>{let i=this._styleSummary.get(s);(!i||t.time>i.time)&&this._updateStyle(s,t.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,t=new Set,s=this._keyframes.size===1&&this.duration===0,i=[];this._keyframes.forEach((o,l)=>{let u=new Map([...this._backFill,...o]);u.forEach((h,c)=>{h===ne?e.add(c):h===z&&t.add(c)}),s||u.set("offset",l/this.duration),i.push(u)});let r=[...e.values()],a=[...t.values()];if(s){let o=i[0],l=new Map(o);o.set("offset",0),l.set("offset",1),i=[o,l]}return mt(this.element,i,r,a,this.duration,this.startTime,this.easing,!1)}},rt=class extends Me{keyframes;preStyleProps;postStyleProps;_stretchStartingKeyframe;timings;constructor(e,t,s,i,r,a,o=!1){super(e,t,a.delay),this.keyframes=s,this.preStyleProps=i,this.postStyleProps=r,this._stretchStartingKeyframe=o,this.timings={duration:a.duration,delay:a.delay,easing:a.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:t,duration:s,easing:i}=this.timings;if(this._stretchStartingKeyframe&&t){let r=[],a=s+t,o=t/a,l=new Map(e[0]);l.set("offset",0),r.push(l);let u=new Map(e[0]);u.set("offset",as(o)),r.push(u);let h=e.length-1;for(let c=1;c<=h;c++){let _=new Map(e[c]),y=_.get("offset"),f=t+y*s;_.set("offset",as(f/a)),r.push(_)}s=a,t=0,i="",e=r}return mt(this.element,e,this.preStyleProps,this.postStyleProps,s,t,i,!0)}};function as(n,e=3){let t=Math.pow(10,e-1);return Math.round(n*t)/t}function xs(n,e){let t=new Map,s;return n.forEach(i=>{if(i==="*"){s??=e.keys();for(let r of s)t.set(r,z)}else for(let[r,a]of i)t.set(r,a)}),t}function os(n,e,t,s,i,r,a,o,l,u,h,c,_){return{type:0,element:n,triggerName:e,isRemovalTransition:i,fromState:t,fromStyles:r,toState:s,toStyles:a,timelines:o,queriedElements:l,preStyleProps:u,postStyleProps:h,totalTime:c,errors:_}}var He={},Ce=class{_triggerName;ast;_stateStyles;constructor(e,t,s){this._triggerName=e,this.ast=t,this._stateStyles=s}match(e,t,s,i){return ei(this.ast.matchers,e,t,s,i)}buildStyles(e,t,s){let i=this._stateStyles.get("*");return e!==void 0&&(i=this._stateStyles.get(e?.toString())||i),i?i.buildStyles(t,s):new Map}build(e,t,s,i,r,a,o,l,u,h){let c=[],_=this.ast.options&&this.ast.options.params||He,y=o&&o.params||He,f=this.buildStyles(s,y,c),g=l&&l.params||He,v=this.buildStyles(i,g,c),w=new Set,A=new Map,C=new Map,N=i==="void",Z={params:_s(g,_),delay:this.ast.options?.delay},B=h?[]:pt(e,t,this.ast.animation,r,a,f,v,Z,u,c),k=0;return B.forEach(D=>{k=Math.max(D.duration+D.delay,k)}),c.length?os(t,this._triggerName,s,i,N,f,v,[],[],A,C,k,c):(B.forEach(D=>{let j=D.element,x=R(A,j,new Set);D.preStyleProps.forEach(W=>x.add(W));let gt=R(C,j,new Set);D.postStyleProps.forEach(W=>gt.add(W)),j!==t&&w.add(j)}),os(t,this._triggerName,s,i,N,f,v,B,[...w.values()],A,C,k))}};function ei(n,e,t,s,i){return n.some(r=>r(e,t,s,i))}function _s(n,e){let t=fe({},e);return Object.entries(n).forEach(([s,i])=>{i!=null&&(t[s]=i)}),t}var at=class{styles;defaultParams;normalizer;constructor(e,t,s){this.styles=e,this.defaultParams=t,this.normalizer=s}buildStyles(e,t){let s=new Map,i=_s(e,this.defaultParams);return this.styles.styles.forEach(r=>{typeof r!="string"&&r.forEach((a,o)=>{a&&(a=te(a,i,t));let l=this.normalizer.normalizePropertyName(o,t);a=this.normalizer.normalizeStyleValue(o,l,a,t),s.set(o,a)})}),s}};function ti(n,e,t){return new ot(n,e,t)}var ot=class{name;ast;_normalizer;transitionFactories=[];fallbackTransition;states=new Map;constructor(e,t,s){this.name=e,this.ast=t,this._normalizer=s,t.states.forEach(i=>{let r=i.options&&i.options.params||{};this.states.set(i.name,new at(i.style,r,s))}),ls(this.states,"true","1"),ls(this.states,"false","0"),t.transitions.forEach(i=>{this.transitionFactories.push(new Ce(e,i,this.states))}),this.fallbackTransition=si(e,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,t,s,i){return this.transitionFactories.find(a=>a.match(e,t,s,i))||null}matchStyles(e,t,s){return this.fallbackTransition.buildStyles(e,t,s)}};function si(n,e,t){let s=[(a,o)=>!0],i={type:S.Sequence,steps:[],options:null},r={type:S.Transition,animation:i,matchers:s,options:null,queryCount:0,depCount:0};return new Ce(n,r,e)}function ls(n,e,t){n.has(e)?n.has(t)||n.set(t,n.get(e)):n.has(t)&&n.set(e,n.get(t))}var ii=new ie,lt=class{bodyNode;_driver;_normalizer;_animations=new Map;_playersById=new Map;players=[];constructor(e,t,s){this.bodyNode=e,this._driver=t,this._normalizer=s}register(e,t){let s=[],i=[],r=ft(this._driver,t,s,i);if(s.length)throw Vt(s);this._animations.set(e,r)}_buildPlayer(e,t,s){let i=e.element,r=qe(this._normalizer,e.keyframes,t,s);return this._driver.animate(i,r,e.duration,e.delay,e.easing,[],!0)}create(e,t,s={}){let i=[],r=this._animations.get(e),a,o=new Map;if(r?(a=pt(this._driver,t,r,_e,re,new Map,new Map,s,ii,i),a.forEach(h=>{let c=R(o,h.element,new Map);h.postStyleProps.forEach(_=>c.set(_,null))})):(i.push($t()),a=[]),i.length)throw Ut(i);o.forEach((h,c)=>{h.forEach((_,y)=>{h.set(y,this._driver.computeStyle(c,y,z))})});let l=a.map(h=>{let c=o.get(h.element);return this._buildPlayer(h,new Map,c)}),u=$(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let t=this._getPlayer(e);t.destroy(),this._playersById.delete(e);let s=this.players.indexOf(t);s>=0&&this.players.splice(s,1)}_getPlayer(e){let t=this._playersById.get(e);if(!t)throw Gt(e);return t}listen(e,t,s,i){let r=ge(t,"","","");return pe(this._getPlayer(e),s,r,i),()=>{}}command(e,t,s,i){if(s=="register"){this.register(e,i[0]);return}if(s=="create"){let a=i[0]||{};this.create(e,t,a);return}let r=this._getPlayer(e);switch(s){case"play":r.play();break;case"pause":r.pause();break;case"reset":r.reset();break;case"restart":r.restart();break;case"finish":r.finish();break;case"init":r.init();break;case"setPosition":r.setPosition(parseFloat(i[0]));break;case"destroy":this.destroy(e);break}}},us="ng-animate-queued",ni=".ng-animate-queued",Ye="ng-animate-disabled",ri=".ng-animate-disabled",ai="ng-star-inserted",oi=".ng-star-inserted",li=[],Ss={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},ui={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},q="__ng_removed",ce=class{namespaceId;value;options;get params(){return this.options.params}constructor(e,t=""){this.namespaceId=t;let s=e&&e.hasOwnProperty("value"),i=s?e.value:e;if(this.value=hi(i),s){let r=e,{value:a}=r,o=Et(r,["value"]);this.options=o}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let t=e.params;if(t){let s=this.options.params;Object.keys(t).forEach(i=>{s[i]==null&&(s[i]=t[i])})}}},ue="void",Xe=new ce(ue),ut=class{id;hostElement;_engine;players=[];_triggers=new Map;_queue=[];_elementListeners=new Map;_hostClassName;constructor(e,t,s){this.id=e,this.hostElement=t,this._engine=s,this._hostClassName="ng-tns-"+e,I(t,this._hostClassName)}listen(e,t,s,i){if(!this._triggers.has(t))throw jt(s,t);if(s==null||s.length==0)throw Wt(t);if(!di(s))throw Ht(s,t);let r=R(this._elementListeners,e,[]),a={name:t,phase:s,callback:i};r.push(a);let o=R(this._engine.statesByElement,e,new Map);return o.has(t)||(I(e,ae),I(e,ae+"-"+t),o.set(t,Xe)),()=>{this._engine.afterFlush(()=>{let l=r.indexOf(a);l>=0&&r.splice(l,1),this._triggers.has(t)||o.delete(t)})}}register(e,t){return this._triggers.has(e)?!1:(this._triggers.set(e,t),!0)}_getTrigger(e){let t=this._triggers.get(e);if(!t)throw Yt(e);return t}trigger(e,t,s,i=!0){let r=this._getTrigger(t),a=new he(this.id,t,e),o=this._engine.statesByElement.get(e);o||(I(e,ae),I(e,ae+"-"+t),this._engine.statesByElement.set(e,o=new Map));let l=o.get(t),u=new ce(s,this.id);if(!(s&&s.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),o.set(t,u),l||(l=Xe),!(u.value===ue)&&l.value===u.value){if(!pi(l.params,u.params)){let g=[],v=r.matchStyles(l.value,l.params,g),w=r.matchStyles(u.value,u.params,g);g.length?this._engine.reportError(g):this._engine.afterFlush(()=>{G(e,v),K(e,w)})}return}let _=R(this._engine.playersByElement,e,[]);_.forEach(g=>{g.namespaceId==this.id&&g.triggerName==t&&g.queued&&g.destroy()});let y=r.matchTransition(l.value,u.value,e,u.params),f=!1;if(!y){if(!i)return;y=r.fallbackTransition,f=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:t,transition:y,fromState:l,toState:u,player:a,isFallbackTransition:f}),f||(I(e,us),a.onStart(()=>{se(e,us)})),a.onDone(()=>{let g=this.players.indexOf(a);g>=0&&this.players.splice(g,1);let v=this._engine.playersByElement.get(e);if(v){let w=v.indexOf(a);w>=0&&v.splice(w,1)}}),this.players.push(a),_.push(a),a}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(t=>t.delete(e)),this._elementListeners.forEach((t,s)=>{this._elementListeners.set(s,t.filter(i=>i.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let t=this._engine.playersByElement.get(e);t&&(t.forEach(s=>s.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,t){let s=this._engine.driver.query(e,oe,!0);s.forEach(i=>{if(i[q])return;let r=this._engine.fetchNamespacesByElement(i);r.size?r.forEach(a=>a.triggerLeaveAnimation(i,t,!1,!0)):this.clearElementCache(i)}),this._engine.afterFlushAnimationsDone(()=>s.forEach(i=>this.clearElementCache(i)))}triggerLeaveAnimation(e,t,s,i){let r=this._engine.statesByElement.get(e),a=new Map;if(r){let o=[];if(r.forEach((l,u)=>{if(a.set(u,l.value),this._triggers.has(u)){let h=this.trigger(e,u,ue,i);h&&o.push(h)}}),o.length)return this._engine.markElementAsRemoved(this.id,e,!0,t,a),s&&$(o).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let t=this._elementListeners.get(e),s=this._engine.statesByElement.get(e);if(t&&s){let i=new Set;t.forEach(r=>{let a=r.name;if(i.has(a))return;i.add(a);let l=this._triggers.get(a).fallbackTransition,u=s.get(a)||Xe,h=new ce(ue),c=new he(this.id,a,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:a,transition:l,fromState:u,toState:h,player:c,isFallbackTransition:!0})})}}removeNode(e,t){let s=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,t),this.triggerLeaveAnimation(e,t,!0))return;let i=!1;if(s.totalAnimations){let r=s.players.length?s.playersByQueriedElement.get(e):[];if(r&&r.length)i=!0;else{let a=e;for(;a=a.parentNode;)if(s.statesByElement.get(a)){i=!0;break}}}if(this.prepareLeaveAnimationListeners(e),i)s.markElementAsRemoved(this.id,e,!1,t);else{let r=e[q];(!r||r===Ss)&&(s.afterFlush(()=>this.clearElementCache(e)),s.destroyInnerAnimations(e),s._onRemovalComplete(e,t))}}insertNode(e,t){I(e,this._hostClassName)}drainQueuedTransitions(e){let t=[];return this._queue.forEach(s=>{let i=s.player;if(i.destroyed)return;let r=s.element,a=this._elementListeners.get(r);a&&a.forEach(o=>{if(o.name==s.triggerName){let l=ge(r,s.triggerName,s.fromState.value,s.toState.value);l._data=e,pe(s.player,o.phase,l,o.callback)}}),i.markedForDestroy?this._engine.afterFlush(()=>{i.destroy()}):t.push(s)}),this._queue=[],t.sort((s,i)=>{let r=s.transition.ast.depCount,a=i.transition.ast.depCount;return r==0||a==0?r-a:this._engine.driver.containsElement(s.element,i.element)?1:-1})}destroy(e){this.players.forEach(t=>t.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},ct=class{bodyNode;driver;_normalizer;players=[];newHostElements=new Map;playersByElement=new Map;playersByQueriedElement=new Map;statesByElement=new Map;disabledNodes=new Set;totalAnimations=0;totalQueuedPlayers=0;_namespaceLookup={};_namespaceList=[];_flushFns=[];_whenQuietFns=[];namespacesByHostElement=new Map;collectedEnterElements=[];collectedLeaveElements=[];onRemovalComplete=(e,t)=>{};_onRemovalComplete(e,t){this.onRemovalComplete(e,t)}constructor(e,t,s){this.bodyNode=e,this.driver=t,this._normalizer=s}get queuedPlayers(){let e=[];return this._namespaceList.forEach(t=>{t.players.forEach(s=>{s.queued&&e.push(s)})}),e}createNamespace(e,t){let s=new ut(e,t,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,t)?this._balanceNamespaceList(s,t):(this.newHostElements.set(t,s),this.collectEnterElement(t)),this._namespaceLookup[e]=s}_balanceNamespaceList(e,t){let s=this._namespaceList,i=this.namespacesByHostElement;if(s.length-1>=0){let a=!1,o=this.driver.getParentElement(t);for(;o;){let l=i.get(o);if(l){let u=s.indexOf(l);s.splice(u+1,0,e),a=!0;break}o=this.driver.getParentElement(o)}a||s.unshift(e)}else s.push(e);return i.set(t,e),e}register(e,t){let s=this._namespaceLookup[e];return s||(s=this.createNamespace(e,t)),s}registerTrigger(e,t,s){let i=this._namespaceLookup[e];i&&i.register(t,s)&&this.totalAnimations++}destroy(e,t){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let s=this._fetchNamespace(e);this.namespacesByHostElement.delete(s.hostElement);let i=this._namespaceList.indexOf(s);i>=0&&this._namespaceList.splice(i,1),s.destroy(t),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let t=new Set,s=this.statesByElement.get(e);if(s){for(let i of s.values())if(i.namespaceId){let r=this._fetchNamespace(i.namespaceId);r&&t.add(r)}}return t}trigger(e,t,s,i){if(we(t)){let r=this._fetchNamespace(e);if(r)return r.trigger(t,s,i),!0}return!1}insertNode(e,t,s,i){if(!we(t))return;let r=t[q];if(r&&r.setForRemoval){r.setForRemoval=!1,r.setForMove=!0;let a=this.collectedLeaveElements.indexOf(t);a>=0&&this.collectedLeaveElements.splice(a,1)}if(e){let a=this._fetchNamespace(e);a&&a.insertNode(t,s)}i&&this.collectEnterElement(t)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,t){t?this.disabledNodes.has(e)||(this.disabledNodes.add(e),I(e,Ye)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),se(e,Ye))}removeNode(e,t,s){if(we(t)){let i=e?this._fetchNamespace(e):null;i?i.removeNode(t,s):this.markElementAsRemoved(e,t,!1,s);let r=this.namespacesByHostElement.get(t);r&&r.id!==e&&r.removeNode(t,s)}else this._onRemovalComplete(t,s)}markElementAsRemoved(e,t,s,i,r){this.collectedLeaveElements.push(t),t[q]={namespaceId:e,setForRemoval:i,hasAnimation:s,removedBeforeQueried:!1,previousTriggersValues:r}}listen(e,t,s,i,r){return we(t)?this._fetchNamespace(e).listen(t,s,i,r):()=>{}}_buildInstruction(e,t,s,i,r){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,s,i,e.fromState.options,e.toState.options,t,r)}destroyInnerAnimations(e){let t=this.driver.query(e,oe,!0);t.forEach(s=>this.destroyActiveAnimationsForElement(s)),this.playersByQueriedElement.size!=0&&(t=this.driver.query(e,Se,!0),t.forEach(s=>this.finishActiveQueriedAnimationOnElement(s)))}destroyActiveAnimationsForElement(e){let t=this.playersByElement.get(e);t&&t.forEach(s=>{s.queued?s.markedForDestroy=!0:s.destroy()})}finishActiveQueriedAnimationOnElement(e){let t=this.playersByQueriedElement.get(e);t&&t.forEach(s=>s.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return $(this.players).onDone(()=>e());e()})}processLeaveNode(e){let t=e[q];if(t&&t.setForRemoval){if(e[q]=Ss,t.namespaceId){this.destroyInnerAnimations(e);let s=this._fetchNamespace(t.namespaceId);s&&s.clearElementCache(e)}this._onRemovalComplete(e,t.setForRemoval)}e.classList?.contains(Ye)&&this.markElementAsDisabled(e,!1),this.driver.query(e,ri,!0).forEach(s=>{this.markElementAsDisabled(s,!1)})}flush(e=-1){let t=[];if(this.newHostElements.size&&(this.newHostElements.forEach((s,i)=>this._balanceNamespaceList(s,i)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let s=0;s<this.collectedEnterElements.length;s++){let i=this.collectedEnterElements[s];I(i,ai)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let s=[];try{t=this._flushAnimations(s,e)}finally{for(let i=0;i<s.length;i++)s[i]()}}else for(let s=0;s<this.collectedLeaveElements.length;s++){let i=this.collectedLeaveElements[s];this.processLeaveNode(i)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(s=>s()),this._flushFns=[],this._whenQuietFns.length){let s=this._whenQuietFns;this._whenQuietFns=[],t.length?$(t).onDone(()=>{s.forEach(i=>i())}):s.forEach(i=>i())}}reportError(e){throw Xt(e)}_flushAnimations(e,t){let s=new ie,i=[],r=new Map,a=[],o=new Map,l=new Map,u=new Map,h=new Set;this.disabledNodes.forEach(d=>{h.add(d);let m=this.driver.query(d,ni,!0);for(let p=0;p<m.length;p++)h.add(m[p])});let c=this.bodyNode,_=Array.from(this.statesByElement.keys()),y=ds(_,this.collectedEnterElements),f=new Map,g=0;y.forEach((d,m)=>{let p=_e+g++;f.set(m,p),d.forEach(T=>I(T,p))});let v=[],w=new Set,A=new Set;for(let d=0;d<this.collectedLeaveElements.length;d++){let m=this.collectedLeaveElements[d],p=m[q];p&&p.setForRemoval&&(v.push(m),w.add(m),p.hasAnimation?this.driver.query(m,oi,!0).forEach(T=>w.add(T)):A.add(m))}let C=new Map,N=ds(_,Array.from(w));N.forEach((d,m)=>{let p=re+g++;C.set(m,p),d.forEach(T=>I(T,p))}),e.push(()=>{y.forEach((d,m)=>{let p=f.get(m);d.forEach(T=>se(T,p))}),N.forEach((d,m)=>{let p=C.get(m);d.forEach(T=>se(T,p))}),v.forEach(d=>{this.processLeaveNode(d)})});let Z=[],B=[];for(let d=this._namespaceList.length-1;d>=0;d--)this._namespaceList[d].drainQueuedTransitions(t).forEach(p=>{let T=p.player,P=p.element;if(Z.push(T),this.collectedEnterElements.length){let M=P[q];if(M&&M.setForMove){if(M.previousTriggersValues&&M.previousTriggersValues.has(p.triggerName)){let H=M.previousTriggersValues.get(p.triggerName),F=this.statesByElement.get(p.element);if(F&&F.has(p.triggerName)){let de=F.get(p.triggerName);de.value=H,F.set(p.triggerName,de)}}T.destroy();return}}let Q=!c||!this.driver.containsElement(c,P),L=C.get(P),U=f.get(P),b=this._buildInstruction(p,s,U,L,Q);if(b.errors&&b.errors.length){B.push(b);return}if(Q){T.onStart(()=>G(P,b.fromStyles)),T.onDestroy(()=>K(P,b.toStyles)),i.push(T);return}if(p.isFallbackTransition){T.onStart(()=>G(P,b.fromStyles)),T.onDestroy(()=>K(P,b.toStyles)),i.push(T);return}let St=[];b.timelines.forEach(M=>{M.stretchStartingKeyframe=!0,this.disabledNodes.has(M.element)||St.push(M)}),b.timelines=St,s.append(P,b.timelines);let vs={instruction:b,player:T,element:P};a.push(vs),b.queriedElements.forEach(M=>R(o,M,[]).push(T)),b.preStyleProps.forEach((M,H)=>{if(M.size){let F=l.get(H);F||l.set(H,F=new Set),M.forEach((de,Le)=>F.add(Le))}}),b.postStyleProps.forEach((M,H)=>{let F=u.get(H);F||u.set(H,F=new Set),M.forEach((de,Le)=>F.add(Le))})});if(B.length){let d=[];B.forEach(m=>{d.push(Jt(m.triggerName,m.errors))}),Z.forEach(m=>m.destroy()),this.reportError(d)}let k=new Map,D=new Map;a.forEach(d=>{let m=d.element;s.has(m)&&(D.set(m,m),this._beforeAnimationBuild(d.player.namespaceId,d.instruction,k))}),i.forEach(d=>{let m=d.element;this._getPreviousPlayers(m,!1,d.namespaceId,d.triggerName,null).forEach(T=>{R(k,m,[]).push(T),T.destroy()})});let j=v.filter(d=>fs(d,l,u)),x=new Map;hs(x,this.driver,A,u,z).forEach(d=>{fs(d,l,u)&&j.push(d)});let W=new Map;y.forEach((d,m)=>{hs(W,this.driver,new Set(d),l,ne)}),j.forEach(d=>{let m=x.get(d),p=W.get(d);x.set(d,new Map([...m?.entries()??[],...p?.entries()??[]]))});let Oe=[],yt=[],_t={};a.forEach(d=>{let{element:m,player:p,instruction:T}=d;if(s.has(m)){if(h.has(m)){p.onDestroy(()=>K(m,T.toStyles)),p.disabled=!0,p.overrideTotalTime(T.totalTime),i.push(p);return}let P=_t;if(D.size>1){let L=m,U=[];for(;L=L.parentNode;){let b=D.get(L);if(b){P=b;break}U.push(L)}U.forEach(b=>D.set(b,P))}let Q=this._buildAnimation(p.namespaceId,T,k,r,W,x);if(p.setRealPlayer(Q),P===_t)Oe.push(p);else{let L=this.playersByElement.get(P);L&&L.length&&(p.parentPlayer=$(L)),i.push(p)}}else G(m,T.fromStyles),p.onDestroy(()=>K(m,T.toStyles)),yt.push(p),h.has(m)&&i.push(p)}),yt.forEach(d=>{let m=r.get(d.element);if(m&&m.length){let p=$(m);d.setRealPlayer(p)}}),i.forEach(d=>{d.parentPlayer?d.syncPlayerEvents(d.parentPlayer):d.destroy()});for(let d=0;d<v.length;d++){let m=v[d],p=m[q];if(se(m,re),p&&p.hasAnimation)continue;let T=[];if(o.size){let Q=o.get(m);Q&&Q.length&&T.push(...Q);let L=this.driver.query(m,Se,!0);for(let U=0;U<L.length;U++){let b=o.get(L[U]);b&&b.length&&T.push(...b)}}let P=T.filter(Q=>!Q.destroyed);P.length?fi(this,m,P):this.processLeaveNode(m)}return v.length=0,Oe.forEach(d=>{this.players.push(d),d.onDone(()=>{d.destroy();let m=this.players.indexOf(d);this.players.splice(m,1)}),d.play()}),Oe}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,t,s,i,r){let a=[];if(t){let o=this.playersByQueriedElement.get(e);o&&(a=o)}else{let o=this.playersByElement.get(e);if(o){let l=!r||r==ue;o.forEach(u=>{u.queued||!l&&u.triggerName!=i||a.push(u)})}}return(s||i)&&(a=a.filter(o=>!(s&&s!=o.namespaceId||i&&i!=o.triggerName))),a}_beforeAnimationBuild(e,t,s){let i=t.triggerName,r=t.element,a=t.isRemovalTransition?void 0:e,o=t.isRemovalTransition?void 0:i;for(let l of t.timelines){let u=l.element,h=u!==r,c=R(s,u,[]);this._getPreviousPlayers(u,h,a,o,t.toState).forEach(y=>{let f=y.getRealPlayer();f.beforeDestroy&&f.beforeDestroy(),y.destroy(),c.push(y)})}G(r,t.fromStyles)}_buildAnimation(e,t,s,i,r,a){let o=t.triggerName,l=t.element,u=[],h=new Set,c=new Set,_=t.timelines.map(f=>{let g=f.element;h.add(g);let v=g[q];if(v&&v.removedBeforeQueried)return new Y(f.duration,f.delay);let w=g!==l,A=mi((s.get(g)||li).map(k=>k.getRealPlayer())).filter(k=>{let D=k;return D.element?D.element===g:!1}),C=r.get(g),N=a.get(g),Z=qe(this._normalizer,f.keyframes,C,N),B=this._buildPlayer(f,Z,A);if(f.subTimeline&&i&&c.add(g),w){let k=new he(e,o,g);k.setRealPlayer(B),u.push(k)}return B});u.forEach(f=>{R(this.playersByQueriedElement,f.element,[]).push(f),f.onDone(()=>ci(this.playersByQueriedElement,f.element,f))}),h.forEach(f=>I(f,Ue));let y=$(_);return y.onDestroy(()=>{h.forEach(f=>se(f,Ue)),K(l,t.toStyles)}),c.forEach(f=>{R(i,f,[]).push(y)}),y}_buildPlayer(e,t,s){return t.length>0?this.driver.animate(e.element,t,e.duration,e.delay,e.easing,s):new Y(e.duration,e.delay)}},he=class{namespaceId;triggerName;element;_player=new Y;_containsRealPlayer=!1;_queuedCallbacks=new Map;destroyed=!1;parentPlayer=null;markedForDestroy=!1;disabled=!1;queued=!0;totalTime=0;constructor(e,t,s){this.namespaceId=e,this.triggerName=t,this.element=s}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((t,s)=>{t.forEach(i=>pe(e,s,void 0,i))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let t=this._player;t.triggerCallback&&e.onStart(()=>t.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,t){R(this._queuedCallbacks,e,[]).push(t)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let t=this._player;t.triggerCallback&&t.triggerCallback(e)}};function ci(n,e,t){let s=n.get(e);if(s){if(s.length){let i=s.indexOf(t);s.splice(i,1)}s.length==0&&n.delete(e)}return s}function hi(n){return n??null}function we(n){return n&&n.nodeType===1}function di(n){return n=="start"||n=="done"}function cs(n,e){let t=n.style.display;return n.style.display=e??"none",t}function hs(n,e,t,s,i){let r=[];t.forEach(l=>r.push(cs(l)));let a=[];s.forEach((l,u)=>{let h=new Map;l.forEach(c=>{let _=e.computeStyle(u,c,i);h.set(c,_),(!_||_.length==0)&&(u[q]=ui,a.push(u))}),n.set(u,h)});let o=0;return t.forEach(l=>cs(l,r[o++])),a}function ds(n,e){let t=new Map;if(n.forEach(o=>t.set(o,[])),e.length==0)return t;let s=1,i=new Set(e),r=new Map;function a(o){if(!o)return s;let l=r.get(o);if(l)return l;let u=o.parentNode;return t.has(u)?l=u:i.has(u)?l=s:l=a(u),r.set(o,l),l}return e.forEach(o=>{let l=a(o);l!==s&&t.get(l).push(o)}),t}function I(n,e){n.classList?.add(e)}function se(n,e){n.classList?.remove(e)}function fi(n,e,t){$(t).onDone(()=>n.processLeaveNode(e))}function mi(n){let e=[];return Es(n,e),e}function Es(n,e){for(let t=0;t<n.length;t++){let s=n[t];s instanceof me?Es(s.players,e):e.push(s)}}function pi(n,e){let t=Object.keys(n),s=Object.keys(e);if(t.length!=s.length)return!1;for(let i=0;i<t.length;i++){let r=t[i];if(!e.hasOwnProperty(r)||n[r]!==e[r])return!1}return!0}function fs(n,e,t){let s=t.get(n);if(!s)return!1;let i=e.get(n);return i?s.forEach(r=>i.add(r)):e.set(n,s),t.delete(n),!0}var ke=class{_driver;_normalizer;_transitionEngine;_timelineEngine;_triggerCache={};onRemovalComplete=(e,t)=>{};constructor(e,t,s){this._driver=t,this._normalizer=s,this._transitionEngine=new ct(e.body,t,s),this._timelineEngine=new lt(e.body,t,s),this._transitionEngine.onRemovalComplete=(i,r)=>this.onRemovalComplete(i,r)}registerTrigger(e,t,s,i,r){let a=e+"-"+i,o=this._triggerCache[a];if(!o){let l=[],u=[],h=ft(this._driver,r,l,u);if(l.length)throw Qt(i,l);o=ti(i,h,this._normalizer),this._triggerCache[a]=o}this._transitionEngine.registerTrigger(t,i,o)}register(e,t){this._transitionEngine.register(e,t)}destroy(e,t){this._transitionEngine.destroy(e,t)}onInsert(e,t,s,i){this._transitionEngine.insertNode(e,t,s,i)}onRemove(e,t,s){this._transitionEngine.removeNode(e,t,s)}disableAnimations(e,t){this._transitionEngine.markElementAsDisabled(e,t)}process(e,t,s,i){if(s.charAt(0)=="@"){let[r,a]=Be(s),o=i;this._timelineEngine.command(r,t,a,o)}else this._transitionEngine.trigger(e,t,s,i)}listen(e,t,s,i,r){if(s.charAt(0)=="@"){let[a,o]=Be(s);return this._timelineEngine.listen(a,t,o,r)}return this._transitionEngine.listen(e,t,s,i,r)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function gi(n,e){let t=null,s=null;return Array.isArray(e)&&e.length?(t=Je(e[0]),e.length>1&&(s=Je(e[e.length-1]))):e instanceof Map&&(t=Je(e)),t||s?new yi(n,t,s):null}var yi=(()=>{class n{_element;_startStyles;_endStyles;static initialStylesByElement=new WeakMap;_state=0;_initialStyles;constructor(t,s,i){this._element=t,this._startStyles=s,this._endStyles=i;let r=n.initialStylesByElement.get(t);r||n.initialStylesByElement.set(t,r=new Map),this._initialStyles=r}start(){this._state<1&&(this._startStyles&&K(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(K(this._element,this._initialStyles),this._endStyles&&(K(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(n.initialStylesByElement.delete(this._element),this._startStyles&&(G(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(G(this._element,this._endStyles),this._endStyles=null),K(this._element,this._initialStyles),this._state=3)}}return n})();function Je(n){let e=null;return n.forEach((t,s)=>{_i(s)&&(e=e||new Map,e.set(s,t))}),e}function _i(n){return n==="display"||n==="position"}var De=class{element;keyframes;options;_specialStyles;_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_duration;_delay;_initialized=!1;_finished=!1;_started=!1;_destroyed=!1;_finalKeyframe;_originalOnDoneFns=[];_originalOnStartFns=[];domPlayer;time=0;parentPlayer=null;currentSnapshot=new Map;constructor(e,t,s,i){this.element=e,this.keyframes=t,this.options=s,this._specialStyles=i,this._duration=s.duration,this._delay=s.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let t=()=>this._onFinish();this.domPlayer.addEventListener("finish",t),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",t)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let t=[];return e.forEach(s=>{t.push(Object.fromEntries(s))}),t}_triggerWebAnimation(e,t,s){return e.animate(this._convertKeyframesToObject(t),s)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((s,i)=>{i!=="offset"&&e.set(i,this._finished?s:Te(this.element,i))}),this.currentSnapshot=e}triggerCallback(e){let t=e==="start"?this._onStartFns:this._onDoneFns;t.forEach(s=>s()),t.length=0}},ht=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,t){return Qe(e,t)}getParentElement(e){return ye(e)}query(e,t,s){return Ve(e,t,s)}computeStyle(e,t,s){return Te(e,t)}animate(e,t,s,i,r,a=[]){let o=i==0?"both":"forwards",l={duration:s,delay:i,fill:o};r&&(l.easing=r);let u=new Map,h=a.filter(y=>y instanceof De);ts(s,i)&&h.forEach(y=>{y.currentSnapshot.forEach((f,g)=>u.set(g,f))});let c=xt(t).map(y=>new Map(y));c=ss(e,c,u);let _=gi(e,c);return new De(e,c,l,_)}};function Ni(n,e){return n==="noop"?new ke(e,new gs,new xe):new ke(e,new ht,new et)}var ms=class{_driver;_animationAst;constructor(e,t){this._driver=e;let s=[],r=ft(e,t,s,[]);if(s.length)throw qt(s);this._animationAst=r}buildTimelines(e,t,s,i,r){let a=Array.isArray(t)?Ge(t):t,o=Array.isArray(s)?Ge(s):s,l=[];r=r||new ie;let u=pt(this._driver,e,this._animationAst,_e,re,a,o,i,r,l);if(l.length)throw Bt(l);return u}},Ae="@",Ts="@.disabled",Re=class{namespaceId;delegate;engine;_onDestroy;\u0275type=0;constructor(e,t,s,i){this.namespaceId=e,this.delegate=t,this.engine=s,this._onDestroy=i}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,t){return this.delegate.createElement(e,t)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,t){this.delegate.appendChild(e,t),this.engine.onInsert(this.namespaceId,t,e,!1)}insertBefore(e,t,s,i=!0){this.delegate.insertBefore(e,t,s),this.engine.onInsert(this.namespaceId,t,e,i)}removeChild(e,t,s){this.parentNode(t)&&this.engine.onRemove(this.namespaceId,t,this.delegate)}selectRootElement(e,t){return this.delegate.selectRootElement(e,t)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,t,s,i){this.delegate.setAttribute(e,t,s,i)}removeAttribute(e,t,s){this.delegate.removeAttribute(e,t,s)}addClass(e,t){this.delegate.addClass(e,t)}removeClass(e,t){this.delegate.removeClass(e,t)}setStyle(e,t,s,i){this.delegate.setStyle(e,t,s,i)}removeStyle(e,t,s){this.delegate.removeStyle(e,t,s)}setProperty(e,t,s){t.charAt(0)==Ae&&t==Ts?this.disableAnimations(e,!!s):this.delegate.setProperty(e,t,s)}setValue(e,t){this.delegate.setValue(e,t)}listen(e,t,s,i){return this.delegate.listen(e,t,s,i)}disableAnimations(e,t){this.engine.disableAnimations(e,t)}},dt=class extends Re{factory;constructor(e,t,s,i,r){super(t,s,i,r),this.factory=e,this.namespaceId=t}setProperty(e,t,s){t.charAt(0)==Ae?t.charAt(1)=="."&&t==Ts?(s=s===void 0?!0:!!s,this.disableAnimations(e,s)):this.engine.process(this.namespaceId,e,t.slice(1),s):this.delegate.setProperty(e,t,s)}listen(e,t,s,i){if(t.charAt(0)==Ae){let r=Si(e),a=t.slice(1),o="";return a.charAt(0)!=Ae&&([a,o]=Ei(a)),this.engine.listen(this.namespaceId,r,a,o,l=>{let u=l._data||-1;this.factory.scheduleListenerCallback(u,s,l)})}return this.delegate.listen(e,t,s,i)}};function Si(n){switch(n){case"body":return document.body;case"document":return document;case"window":return window;default:return n}}function Ei(n){let e=n.indexOf("."),t=n.substring(0,e),s=n.slice(e+1);return[t,s]}var ps=class{delegate;engine;_zone;_currentId=0;_microtaskId=1;_animationCallbacksBuffer=[];_rendererCache=new Map;_cdRecurDepth=0;constructor(e,t,s){this.delegate=e,this.engine=t,this._zone=s,t.onRemovalComplete=(i,r)=>{r?.removeChild(null,i)}}createRenderer(e,t){let s="",i=this.delegate.createRenderer(e,t);if(!e||!t?.data?.animation){let u=this._rendererCache,h=u.get(i);if(!h){let c=()=>u.delete(i);h=new Re(s,i,this.engine,c),u.set(i,h)}return h}let r=t.id,a=t.id+"-"+this._currentId;this._currentId++,this.engine.register(a,e);let o=u=>{Array.isArray(u)?u.forEach(o):this.engine.registerTrigger(r,a,e,u.name,u)};return t.data.animation.forEach(o),new dt(this,a,i,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,t,s){if(e>=0&&e<this._microtaskId){this._zone.run(()=>t(s));return}let i=this._animationCallbacksBuffer;i.length==0&&queueMicrotask(()=>{this._zone.run(()=>{i.forEach(r=>{let[a,o]=r;a(o)}),this._animationCallbacksBuffer=[]})}),i.push([t,s])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}componentReplaced(e){this.engine.flush(),this.delegate.componentReplaced?.(e)}};export{is as AnimationDriver,gs as NoopAnimationDriver,ms as \u0275Animation,ke as \u0275AnimationEngine,dt as \u0275AnimationRenderer,ps as \u0275AnimationRendererFactory,Ze as \u0275AnimationStyleNormalizer,Re as \u0275BaseAnimationRenderer,_e as \u0275ENTER_CLASSNAME,re as \u0275LEAVE_CLASSNAME,xe as \u0275NoopAnimationStyleNormalizer,he as \u0275TransitionAnimationPlayer,ht as \u0275WebAnimationsDriver,De as \u0275WebAnimationsPlayer,et as \u0275WebAnimationsStyleNormalizer,ts as \u0275allowPreviousPlayerStylesMerge,Ks as \u0275camelCaseToDashCase,Qe as \u0275containsElement,Ni as \u0275createEngine,ye as \u0275getParentElement,Ve as \u0275invokeQuery,xt as \u0275normalizeKeyframes,Zt as \u0275validateStyleProperty,Rs as \u0275validateWebAnimatableStyleProperty};
