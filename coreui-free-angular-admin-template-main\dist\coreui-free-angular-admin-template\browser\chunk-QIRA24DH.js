import{b as D}from"./chunk-3MARWV4R.js";import{e as F,h as k,l as w,r as L,s as v,v as z,w as I}from"./chunk-LKW33VZJ.js";import{E as a,F as c,I as d,ba as s,ca as p,da as u,ea as x,fa as S,ia as h,ja as E,ka as b,la as f,n as m,na as g,oa as y,qa as C}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as i,Ib as t,Jb as n,eb as r,fc as e}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var l=class l{constructor(){}};l.\u0275fac=function(o){return new(o||l)},l.\u0275cmp=r({type:l,selectors:[["app-layout"]],decls:373,vars:0,consts:[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","forms/layout#form-grid"],["xs",""],["aria-label","First name","cFormControl","","placeholder","First name"],["aria-label","Last name","cFormControl","","placeholder","Last name"],["href","https://coreui.io/docs/layout/gutters/"],["href","forms/layout#gutters"],[1,"g-3"],["cForm","",1,"row","g-3"],["md","6"],["cLabel","","for","inputEmail4"],["cFormControl","","id","inputEmail4","type","email"],["cLabel","","for","inputPassword4"],["cFormControl","","id","inputPassword4","type","password"],["cLabel","","for","inputAddress"],["cFormControl","","id","inputAddress","placeholder","1234 Main St"],["cLabel","","for","inputAddress2"],["cFormControl","","id","inputAddress2","placeholder","Apartment, studio, or floor"],["cLabel","","for","inputCity"],["cFormControl","","id","inputCity"],["md","4"],["cLabel","","for","inputState"],["cSelect","","id","inputState"],["md","2"],["cLabel","","for","inputZip"],["cFormControl","","id","inputZip"],["cFormCheckInput","","id","gridCheck","name","gridCheck","type","checkbox"],["cFormCheckLabel","","for","gridCheck"],["cButton","","type","submit"],["href","forms/layout#horizontal-form"],[1,"mb-3"],["cLabel","","for","inputEmail3",1,"col-sm-2","col-form-label"],["sm","10"],["cFormControl","","id","inputEmail3","type","email"],["cLabel","","for","inputPassword3",1,"col-sm-2","col-form-label"],["cFormControl","","id","inputPassword3","type","password"],[1,"row","mb-3"],[1,"col-form-label","col-sm-2","pt-0"],["cFormCheckInput","","checked","","id","gridRadios1","name","gridRadios","type","radio","value","option1"],["cFormCheckLabel","","for","gridRadios1"],["cFormCheckInput","","id","gridRadios2","name","gridRadios","type","radio","value","option2"],["cFormCheckLabel","","for","gridRadios2"],["cFormCheckInput","","id","gridRadios3","name","gridRadios","type","radio","value","option3"],["cFormCheckLabel","","for","gridRadios3"],["sm","10",1,"offset-sm-2"],["cFormCheckInput","","id","gridCheck1","name","gridCheck1","type","checkbox"],["cFormCheckLabel","","for","gridCheck1"],["href","forms/layout#horizontal-form-label-sizing"],["cLabel","col","cCol","","sm","2","sizing","sm","for","colFormLabelSm"],["cFormControl","","sizing","sm","id","colFormLabelSm","placeholder","col-form-label-sm","type","email"],["cLabel","col","cCol","","sm","2","for","colFormLabel"],["cFormControl","","id","colFormLabel","placeholder","col-form-label","type","email"],["cLabel","col","cCol","","sm","2","sizing","lg","for","colFormLabelLg"],["cFormControl","","sizing","lg","id","colFormLabelLg","placeholder","col-form-label-lg","type","email"],["href","forms/layout#column-sizing"],["sm","7"],["aria-label","City","cFormControl","","placeholder","City"],["sm",""],["aria-label","State","cFormControl","","placeholder","State"],["aria-label","Zip","cFormControl","","placeholder","Zip"],["href","forms/layout#auto-sizing"],["cForm","",1,"row","gy-2","gx-3","align-items-center"],["xs","auto"],["cLabel","","for","autoSizingInput",1,"visually-hidden"],["cFormControl","","id","autoSizingInput","placeholder","Jane Doe"],["cLabel","","for","autoSizingInputGroup",1,"visually-hidden"],["cInputGroupText",""],["cFormControl","","id","autoSizingInputGroup","placeholder","Username"],["cLabel","","for","autoSizingSelect",1,"visually-hidden"],["cSelect","","id","autoSizingSelect"],["value","1"],["value","2"],["value","3"],["cFormCheckInput","","id","autoSizingCheck","name","autoSizingCheck","type","checkbox"],["cFormCheckLabel","","for","autoSizingCheck"],["cForm","",1,"row","gx-3","gy-2","align-items-center"],["sm","3"],["cLabel","","for","specificSizeInputName",1,"visually-hidden"],["cFormControl","","id","specificSizeInputName","placeholder","Jane Doe"],["cLabel","","for","specificSizeInputGroupUsername",1,"visually-hidden"],["cFormControl","","id","specificSizeInputGroupUsername","placeholder","Username"],["cLabel","","for","specificSizeSelect",1,"visually-hidden"],["cSelect","","id","specificSizeSelect"],["cFormCheckInput","","id","autoSizingCheck2","name","autoSizingCheck2","type","checkbox"],["cFormCheckLabel","","for","autoSizingCheck2"],["href","forms/layout#inline-forms"],["cForm","",1,"row","row-cols-lg-auto","g-3","align-items-center"],["cLabel","","for","inlineFormInputGroupUsername",1,"visually-hidden"],["cFormControl","","id","inlineFormInputGroupUsername","placeholder","Username"],["cLabel","","for","inlineFormSelectPref",1,"visually-hidden"],["cSelect","","id","inlineFormSelectPref"],["inline",""],["cFormCheckInput","","id","inlineFormCheck","name","inlineFormCheck","type","checkbox"],["cFormCheckLabel","","for","inlineFormCheck"]],template:function(o,P){o&1&&(i(0,"c-row")(1,"c-col",0)(2,"c-card",1)(3,"c-card-header"),e(4,`
        `),i(5,"strong"),e(6,"Layout"),t(),e(7," "),i(8,"small"),e(9,"Form grid"),t(),e(10,`
      `),t(),i(11,"c-card-body")(12,"p",2),e(13," More complex forms can be built using our grid classes. Use these for form layouts that require multiple columns, varied widths, and additional alignment options. "),t(),i(14,"app-docs-example",3)(15,"c-row")(16,"c-col",4),n(17,"input",5),t(),i(18,"c-col",4),n(19,"input",6),t()()()()()(),i(20,"c-col",0)(21,"c-card",1)(22,"c-card-header"),e(23,`
        `),i(24,"strong"),e(25,"Layout"),t(),e(26," "),i(27,"small"),e(28,"Gutters"),t(),e(29,`
      `),t(),i(30,"c-card-body")(31,"p",2),e(32," By adding "),i(33,"a",7),e(34,"gutter modifier classes"),t(),e(35," , you can have control over the gutter width in as well the inline as block direction. "),t(),i(36,"app-docs-example",8)(37,"c-row",9)(38,"c-col",4),n(39,"input",5),t(),i(40,"c-col",4),n(41,"input",6),t()()(),i(42,"p",2),e(43," More complex layouts can also be created with the grid system. "),t(),i(44,"app-docs-example",8)(45,"form",10)(46,"c-col",11)(47,"label",12),e(48,"Email"),t(),n(49,"input",13),t(),i(50,"c-col",11)(51,"label",14),e(52,"Password"),t(),n(53,"input",15),t(),i(54,"c-col",0)(55,"label",16),e(56,"Address"),t(),n(57,"input",17),t(),i(58,"c-col",0)(59,"label",18),e(60,"Address 2"),t(),n(61,"input",19),t(),i(62,"c-col",11)(63,"label",20),e(64,"City"),t(),n(65,"input",21),t(),i(66,"c-col",22)(67,"label",23),e(68,"State"),t(),i(69,"select",24)(70,"option"),e(71,"Choose..."),t(),i(72,"option"),e(73,"..."),t()()(),i(74,"c-col",25)(75,"label",26),e(76,"Zip"),t(),n(77,"input",27),t(),i(78,"c-col",0)(79,"c-form-check"),n(80,"input",28),i(81,"label",29),e(82,"Check me out"),t()()(),i(83,"c-col",0)(84,"button",30),e(85,"Sign in"),t()()()()()()(),i(86,"c-col",0)(87,"c-card",1)(88,"c-card-header"),e(89,`
        `),i(90,"strong"),e(91,"Layout"),t(),e(92," "),i(93,"small"),e(94,"Horizontal form"),t(),e(95,`
      `),t(),i(96,"c-card-body")(97,"p",2),e(98," Create horizontal forms with the grid by adding the "),i(99,"code"),e(100,".row"),t(),e(101," class to form groups and using the "),i(102,"code"),e(103,".col-*-*"),t(),e(104," classes to specify the width of your labels and controls. Be sure to add "),i(105,"code"),e(106,".col-form-label"),t(),e(107," to your "),i(108,"code"),e(109,"<label>"),t(),e(110,"s as well so they're vertically centered with their associated form controls. "),t(),i(111,"p",2),e(112," At times, you maybe need to use margin or padding utilities to create that perfect alignment you need. For example, we've removed the "),i(113,"code"),e(114,"padding-top"),t(),e(115," on our stacked radio inputs label to better align the text baseline. "),t(),i(116,"app-docs-example",31)(117,"form")(118,"c-row",32)(119,"label",33),e(120," Email "),t(),i(121,"c-col",34),n(122,"input",35),t()(),i(123,"c-row",32)(124,"label",36),e(125," Password "),t(),i(126,"c-col",34),n(127,"input",37),t()(),i(128,"fieldset",38)(129,"legend",39),e(130,"Radios"),t(),i(131,"c-col",34)(132,"c-form-check"),n(133,"input",40),i(134,"label",41),e(135,"First radio"),t()(),i(136,"c-form-check"),n(137,"input",42),i(138,"label",43),e(139,"Second radio"),t()(),i(140,"c-form-check"),n(141,"input",44),i(142,"label",45),e(143,"Third disabled radio"),t()()()(),i(144,"c-row",32)(145,"c-col",46)(146,"c-form-check"),n(147,"input",47),i(148,"label",48),e(149,"Example checkbox"),t()()()(),i(150,"button",30),e(151,"Sign in"),t()()()()()(),i(152,"c-col",0)(153,"c-card",1)(154,"c-card-header"),e(155,`
        `),i(156,"strong"),e(157,"Layout"),t(),e(158," "),i(159,"small"),e(160,"Horizontal form label sizing"),t(),e(161,`
      `),t(),i(162,"c-card-body")(163,"p",2),e(164," Be sure to use "),i(165,"code"),e(166,".col-form-label-sm"),t(),e(167," or "),i(168,"code"),e(169,".col-form-label-lg"),t(),e(170," to your "),i(171,"code"),e(172,"<label>"),t(),e(173,"s or "),i(174,"code"),e(175,"<legend>"),t(),e(176,"s to correctly follow the size of "),i(177,"code"),e(178,".form-control-lg"),t(),e(179," and "),i(180,"code"),e(181,".form-control-sm"),t(),e(182,". "),t(),i(183,"app-docs-example",49)(184,"c-row",32)(185,"label",50),e(186," Email "),t(),i(187,"c-col",34),n(188,"input",51),t()(),i(189,"c-row",32)(190,"label",52),e(191," Email "),t(),i(192,"c-col",34),n(193,"input",53),t()(),i(194,"c-row")(195,"label",54),e(196," Email "),t(),i(197,"c-col",34),n(198,"input",55),t()()()()()(),i(199,"c-col",0)(200,"c-card",1)(201,"c-card-header"),e(202,`
        `),i(203,"strong"),e(204,"Layout"),t(),e(205," "),i(206,"small"),e(207,"Column sizing"),t(),e(208,`
      `),t(),i(209,"c-card-body")(210,"p",2),e(211," As shown in the previous examples, our grid system allows you to place any number of "),i(212,"code"),e(213,"<c-col>"),t(),e(214,"s within a "),i(215,"code"),e(216,"<c-row>"),t(),e(217,". They'll split the available width equally between them. You may also pick a subset of your columns to take up more or less space, while the remaining "),i(218,"code"),e(219,"<c-col>"),t(),e(220,"s equally split the rest, with specific column classes like "),i(221,"code"),e(222,'<c-col sm="7">'),t(),e(223,". "),t(),i(224,"app-docs-example",56)(225,"c-row",9)(226,"c-col",57),n(227,"input",58),t(),i(228,"c-col",59),n(229,"input",60),t(),i(230,"c-col",59),n(231,"input",61),t()()()()()(),i(232,"c-col",0)(233,"c-card",1)(234,"c-card-header"),e(235,`
        `),i(236,"strong"),e(237,"Layout"),t(),e(238," "),i(239,"small"),e(240,"Auto-sizing"),t(),e(241,`
      `),t(),i(242,"c-card-body")(243,"p",2),e(244," The example below uses a flexbox utility to vertically center the contents and changes "),i(245,"code"),e(246,"<c-col>"),t(),e(247," to "),i(248,"code"),e(249,'<c-col xs="auto">'),t(),e(250," so that your columns only take up as much space as needed. Put another way, the column sizes itself based on the contents. "),t(),i(251,"app-docs-example",62)(252,"form",63)(253,"c-col",64)(254,"label",65),e(255," Name "),t(),n(256,"input",66),t(),i(257,"c-col",64)(258,"label",67),e(259," Username "),t(),i(260,"c-input-group")(261,"span",68),e(262,"@"),t(),n(263,"input",69),t()(),i(264,"c-col",64)(265,"label",70),e(266," Preference "),t(),i(267,"select",71)(268,"option"),e(269,"Choose..."),t(),i(270,"option",72),e(271,"One"),t(),i(272,"option",73),e(273,"Two"),t(),i(274,"option",74),e(275,"Three"),t()()(),i(276,"c-col",64)(277,"c-form-check"),n(278,"input",75),i(279,"label",76),e(280,"Remember me"),t()()(),i(281,"c-col",64)(282,"button",30),e(283,"Submit"),t()()()(),i(284,"p",2),e(285," You can then remix that once again with size-specific column classes. "),t(),i(286,"app-docs-example",62)(287,"form",77)(288,"c-col",78)(289,"label",79),e(290," Name "),t(),n(291,"input",80),t(),i(292,"c-col",78)(293,"label",81),e(294," Username "),t(),i(295,"c-input-group")(296,"span",68),e(297,"@"),t(),n(298,"input",82),t()(),i(299,"c-col",78)(300,"label",83),e(301," Preference "),t(),i(302,"select",84)(303,"option"),e(304,"Choose..."),t(),i(305,"option",72),e(306,"One"),t(),i(307,"option",73),e(308,"Two"),t(),i(309,"option",74),e(310,"Three"),t()()(),i(311,"c-col",64)(312,"c-form-check"),n(313,"input",85),i(314,"label",86),e(315,"Remember me"),t()()(),i(316,"c-col",64)(317,"button",30),e(318,"Submit"),t()()()()()()(),i(319,"c-col",0)(320,"c-card",1)(321,"c-card-header"),e(322,`
        `),i(323,"strong"),e(324,"Layout"),t(),e(325," "),i(326,"small"),e(327,"Inline forms"),t(),e(328,`
      `),t(),i(329,"c-card-body")(330,"p",2),e(331," Use the "),i(332,"code"),e(333,'<c-col xs="auto">'),t(),e(334," class to create horizontal layouts. By adding "),i(335,"a",7),e(336,"gutter modifier classes"),t(),e(337,", we will have gutters in horizontal and vertical directions. The "),i(338,"code"),e(339,".align-items-center"),t(),e(340," aligns the form elements to the middle, making the "),i(341,"code"),e(342,"<CFormCheck>"),t(),e(343," align properly. "),t(),i(344,"app-docs-example",87)(345,"form",88)(346,"c-col",0)(347,"label",89),e(348," Username "),t(),i(349,"c-input-group")(350,"span",68),e(351,"@"),t(),n(352,"input",90),t()(),i(353,"c-col",0)(354,"label",91),e(355," Preference "),t(),i(356,"select",92)(357,"option"),e(358,"Choose..."),t(),i(359,"option",72),e(360,"One"),t(),i(361,"option",73),e(362,"Two"),t(),i(363,"option",74),e(364,"Three"),t()()(),i(365,"c-col",0)(366,"c-form-check",93),n(367,"input",94),i(368,"label",95),e(369,"Remember me"),t()()(),i(370,"c-col",0)(371,"button",30),e(372,"Submit"),t()()()()()()()())},dependencies:[C,y,a,d,c,D,x,I,w,L,v,F,z,k,s,h,E,u,S,p,m,g,b,f],encapsulation:2});var R=l;export{R as LayoutComponent};
