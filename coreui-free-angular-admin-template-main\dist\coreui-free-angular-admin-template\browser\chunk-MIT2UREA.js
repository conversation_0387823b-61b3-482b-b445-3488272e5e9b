import{a as I,b as T}from"./chunk-C7XZ7IYY.js";import{E as v,F as w,I as S,oa as D,qa as B}from"./chunk-Y7QKHPW3.js";import{b as E}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Cc as b,Gb as C,Hb as t,Hc as x,Ib as o,Jb as u,Tb as g,Ub as y,Va as h,Za as f,ba as c,eb as s,fc as n,ma as p}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var N=["*"],l=class l{constructor(){this.document=c(p);this.renderer=c(f)}themeColors(){Array.from(this.document.querySelectorAll(".theme-color")).forEach(r=>{let e=r,a=I("background-color",e)??"#fff",d=this.renderer.createElement("table");d.innerHTML=`
          <table class="table w-100">
            <tr>
              <td class="text-muted">HEX:</td>
              <td class="font-weight-bold">${T(a)}</td>
            </tr>
            <tr>
              <td class="text-muted">RGB:</td>
              <td class="font-weight-bold">${a}</td>
            </tr>
          </table>
        `,this.renderer.appendChild(e.parentNode,d)})}ngOnInit(){}ngAfterViewInit(){this.themeColors()}};l.\u0275fac=function(e){return new(e||l)},l.\u0275cmp=s({type:l,selectors:[["ng-component"]],decls:29,vars:0,consts:[[1,"mb-4"],["color","primary"],["color","secondary"],["color","success"],["color","danger"],["color","warning"],["color","info"],["color","light"],["color","dark"]],template:function(e,a){e&1&&(t(0,"c-card",0)(1,"c-card-header"),n(2," Theme colors "),o(),t(3,"c-card-body")(4,"c-row")(5,"app-theme-color",1)(6,"h6"),n(7,"Brand Primary Color"),o()(),t(8,"app-theme-color",2)(9,"h6"),n(10,"Brand Secondary Color"),o()(),t(11,"app-theme-color",3)(12,"h6"),n(13,"Brand Success Color"),o()(),t(14,"app-theme-color",4)(15,"h6"),n(16,"Brand Danger Color"),o()(),t(17,"app-theme-color",5)(18,"h6"),n(19,"Brand Warning Color"),o()(),t(20,"app-theme-color",6)(21,"h6"),n(22,"Brand Info Color"),o()(),t(23,"app-theme-color",7)(24,"h6"),n(25,"Brand Light Color"),o()(),t(26,"app-theme-color",8)(27,"h6"),n(28,"Brand Dark Color"),o()()()()())},dependencies:()=>[v,S,w,B,m],encapsulation:2});var M=l,i=class i{constructor(){this.color=x("");this.colorClasses=b(()=>{let r=this.color();return{"theme-color w-75 rounded mb-3":!0,[`bg-${r}`]:!!r}})}};i.\u0275fac=function(e){return new(e||i)},i.\u0275cmp=s({type:i,selectors:[["app-theme-color"]],hostAttrs:[2,"display","contents"],inputs:{color:[1,"color"]},ngContentSelectors:N,decls:3,vars:1,consts:[["xl","2","md","4","sm","6","xs","12",1,"my-4","ms-4"],[2,"padding-top","75%",3,"ngClass"]],template:function(e,a){e&1&&(g(),t(0,"c-col",0),u(1,"div",1),y(2),o()),e&2&&(h(),C("ngClass",a.colorClasses()))},dependencies:[D,E],encapsulation:2});var m=i;export{M as ColorsComponent,m as ThemeColorComponent};
