import{b as g}from"./chunk-3MARWV4R.js";import{E as S,F as E,I as o,ab as s,bb as b,cb as p,oa as c,q as d,qa as h,s as r}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as l,Hb as e,Ib as n,Va as i,eb as a,fc as t}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var m=class m{constructor(){}};m.\u0275fac=function(x){return new(x||m)},m.\u0275cmp=a({type:m,selectors:[["app-tables"]],decls:2120,vars:14,consts:[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/table"],["cTable",""],["scope","col"],["scope","row"],["colSpan","2"],["href","components/table#variants"],["cTableColor","primary"],["cTableColor","secondary"],["cTableColor","success"],["cTableColor","danger"],["cTableColor","warning"],["cTableColor","info"],["cTableColor","light"],["cTableColor","dark"],["href","components/table#striped-rows"],["cTable","",3,"striped"],["cTable","","color","dark",3,"striped"],["cTable","","color","success",3,"striped"],["href","components/table#striped-columns"],["stripedColumns","","cTable",""],["href","components/table#hoverable-rows"],["cTable","",3,"hover"],["cTable","","color","dark",3,"hover"],["cTable","",3,"hover","striped"],["href","components/table#active-tables"],["cTableActive",""],["cTableActive","","colSpan","2"],["cTable","","color","dark"],["href","components/table#bordered-tables"],["cTable","",3,"bordered"],["href","https://coreui.io/docs/4.0/utilities/borders#border-color"],["borderColor","primary","cTable","",3,"bordered","cBorder"],["href","components/table#tables-without-borders"],["cTable","",3,"borderless"],["borderless","","cTable","","color","dark"],["href","components/table#small-tables"],["cTable","",3,"small"],["href","components/table#vertical-alignment"],["cAlign","middle","cTable","",3,"responsive"],["scope","col",1,"w-25"],["cAlign","bottom"],["cAlign","top"],["href","components/table#nesting"],["colSpan","4"],["href","components/table#table-head"],["href","components/table#table-foot"],["href","components/table#captions"],["cTable","","caption","top"]],template:function(x,f){x&1&&(e(0,"c-row"),t(1,`
  `),e(2,"c-col",0),t(3,`
    `),e(4,"c-card",1),t(5,`
      `),e(6,"c-card-header"),t(7,`
        `),e(8,"strong"),t(9,"Angular Table"),n(),t(10," "),e(11,"small"),t(12,"Basic example"),n(),t(13,`
      `),n(),t(14,`
      `),e(15,"c-card-body"),t(16,`
        `),e(17,"p",2),t(18,`
          Using the most basic table CoreUI, here's how `),e(19,"code"),t(20,"cTable"),n(),t(21,`-based
          tables look in CoreUI.
        `),n(),t(22,`
        `),e(23,"app-docs-example",3),t(24,`
          `),e(25,"table",4),t(26,`
            `),e(27,"thead"),t(28,`
            `),e(29,"tr"),t(30,`
              `),e(31,"th",5),t(32,"#"),n(),t(33,`
              `),e(34,"th",5),t(35,"Class"),n(),t(36,`
              `),e(37,"th",5),t(38,"Heading"),n(),t(39,`
              `),e(40,"th",5),t(41,"Heading"),n(),t(42,`
            `),n(),t(43,`
            `),n(),t(44,`
            `),e(45,"tbody"),t(46,`
            `),e(47,"tr"),t(48,`
              `),e(49,"th",6),t(50,"1"),n(),t(51,`
              `),e(52,"td"),t(53,"Mark"),n(),t(54,`
              `),e(55,"td"),t(56,"Otto"),n(),t(57,`
              `),e(58,"td"),t(59,"@mdo"),n(),t(60,`
            `),n(),t(61,`
            `),e(62,"tr"),t(63,`
              `),e(64,"th",6),t(65,"2"),n(),t(66,`
              `),e(67,"td"),t(68,"Jacob"),n(),t(69,`
              `),e(70,"td"),t(71,"Thornton"),n(),t(72,`
              `),e(73,"td"),t(74,"@fat"),n(),t(75,`
            `),n(),t(76,`
            `),e(77,"tr"),t(78,`
              `),e(79,"th",6),t(80,"3"),n(),t(81,`
              `),e(82,"td",7),t(83,"Larry the Bird"),n(),t(84,`
              `),e(85,"td"),t(86,"@twitter"),n(),t(87,`
            `),n(),t(88,`
            `),n(),t(89,`
          `),n(),t(90,`
        `),n(),t(91,`
      `),n(),t(92,`
    `),n(),t(93,`
  `),n(),t(94,`
  `),e(95,"c-col",0),t(96,`
    `),e(97,"c-card",1),t(98,`
      `),e(99,"c-card-header"),t(100,`
        `),e(101,"strong"),t(102,"Angular Table"),n(),t(103," "),e(104,"small"),t(105,"Variants"),n(),t(106,`
      `),n(),t(107,`
      `),e(108,"c-card-body"),t(109,`
        `),e(110,"p",2),t(111,`
          Use contextual classes to color tables, table rows or individual cells.
        `),n(),t(112,`
        `),e(113,"app-docs-example",8),t(114,`
          `),e(115,"table",4),t(116,`
            `),e(117,"thead"),t(118,`
            `),e(119,"tr"),t(120,`
              `),e(121,"th",5),t(122,"Class"),n(),t(123,`
              `),e(124,"th",5),t(125,"Heading"),n(),t(126,`
              `),e(127,"th",5),t(128,"Heading"),n(),t(129,`
            `),n(),t(130,`
            `),n(),t(131,`
            `),e(132,"tbody"),t(133,`
            `),e(134,"tr"),t(135,`
              `),e(136,"th",6),t(137,"Default"),n(),t(138,`
              `),e(139,"td"),t(140,"Cell"),n(),t(141,`
              `),e(142,"td"),t(143,"Cell"),n(),t(144,`
            `),n(),t(145,`
            `),e(146,"tr",9),t(147,`
              `),e(148,"th",6),t(149,"Primary"),n(),t(150,`
              `),e(151,"td"),t(152,"Cell"),n(),t(153,`
              `),e(154,"td"),t(155,"Cell"),n(),t(156,`
            `),n(),t(157,`
            `),e(158,"tr",10),t(159,`
              `),e(160,"th",6),t(161,"Secondary"),n(),t(162,`
              `),e(163,"td"),t(164,"Cell"),n(),t(165,`
              `),e(166,"td"),t(167,"Cell"),n(),t(168,`
            `),n(),t(169,`
            `),e(170,"tr",11),t(171,`
              `),e(172,"th",6),t(173,"Success"),n(),t(174,`
              `),e(175,"td"),t(176,"Cell"),n(),t(177,`
              `),e(178,"td"),t(179,"Cell"),n(),t(180,`
            `),n(),t(181,`
            `),e(182,"tr",12),t(183,`
              `),e(184,"th",6),t(185,"Danger"),n(),t(186,`
              `),e(187,"td"),t(188,"Cell"),n(),t(189,`
              `),e(190,"td"),t(191,"Cell"),n(),t(192,`
            `),n(),t(193,`
            `),e(194,"tr",13),t(195,`
              `),e(196,"th",6),t(197,"Warning"),n(),t(198,`
              `),e(199,"td"),t(200,"Cell"),n(),t(201,`
              `),e(202,"td"),t(203,"Cell"),n(),t(204,`
            `),n(),t(205,`
            `),e(206,"tr",14),t(207,`
              `),e(208,"th",6),t(209,"Info"),n(),t(210,`
              `),e(211,"td"),t(212,"Cell"),n(),t(213,`
              `),e(214,"td"),t(215,"Cell"),n(),t(216,`
            `),n(),t(217,`
            `),e(218,"tr",15),t(219,`
              `),e(220,"th",6),t(221,"Light"),n(),t(222,`
              `),e(223,"td"),t(224,"Cell"),n(),t(225,`
              `),e(226,"td"),t(227,"Cell"),n(),t(228,`
            `),n(),t(229,`
            `),e(230,"tr",16),t(231,`
              `),e(232,"th",6),t(233,"Dark"),n(),t(234,`
              `),e(235,"td"),t(236,"Cell"),n(),t(237,`
              `),e(238,"td"),t(239,"Cell"),n(),t(240,`
            `),n(),t(241,`
            `),n(),t(242,`
          `),n(),t(243,`
        `),n(),t(244,`
      `),n(),t(245,`
    `),n(),t(246,`
  `),n(),t(247,`
  `),e(248,"c-col",0),t(249,`
    `),e(250,"c-card",1),t(251,`
      `),e(252,"c-card-header"),t(253,`
        `),e(254,"strong"),t(255,"Angular Table"),n(),t(256," "),e(257,"small"),t(258,"Striped rows"),n(),t(259,`
      `),n(),t(260,`
      `),e(261,"c-card-body"),t(262,`
        `),e(263,"p",2),t(264,`
          Use `),e(265,"code"),t(266,"striped"),n(),t(267," property to add zebra-striping to any table row within the "),e(268,"code"),t(269,"<tbody>"),n(),t(270,`.
        `),n(),t(271,`
        `),e(272,"app-docs-example",17),t(273,`
          `),e(274,"table",18),t(275,`
            `),e(276,"thead"),t(277,`
            `),e(278,"tr"),t(279,`
              `),e(280,"th",5),t(281,"#"),n(),t(282,`
              `),e(283,"th",5),t(284,"Class"),n(),t(285,`
              `),e(286,"th",5),t(287,"Heading"),n(),t(288,`
              `),e(289,"th",5),t(290,"Heading"),n(),t(291,`
            `),n(),t(292,`
            `),n(),t(293,`
            `),e(294,"tbody"),t(295,`
            `),e(296,"tr"),t(297,`
              `),e(298,"th",6),t(299,"1"),n(),t(300,`
              `),e(301,"td"),t(302,"Mark"),n(),t(303,`
              `),e(304,"td"),t(305,"Otto"),n(),t(306,`
              `),e(307,"td"),t(308,"@mdo"),n(),t(309,`
            `),n(),t(310,`
            `),e(311,"tr"),t(312,`
              `),e(313,"th",6),t(314,"2"),n(),t(315,`
              `),e(316,"td"),t(317,"Jacob"),n(),t(318,`
              `),e(319,"td"),t(320,"Thornton"),n(),t(321,`
              `),e(322,"td"),t(323,"@fat"),n(),t(324,`
            `),n(),t(325,`
            `),e(326,"tr"),t(327,`
              `),e(328,"th",6),t(329,"3"),n(),t(330,`
              `),e(331,"td",7),t(332,"Larry the Bird"),n(),t(333,`
              `),e(334,"td"),t(335,"@twitter"),n(),t(336,`
            `),n(),t(337,`
            `),n(),t(338,`
          `),n(),t(339,`
        `),n(),t(340,`
        `),e(341,"p",2),t(342,`
          These classes can also be added to table variants:
        `),n(),t(343,`
        `),e(344,"app-docs-example",17),t(345,`
          `),e(346,"table",19),t(347,`
            `),e(348,"thead"),t(349,`
            `),e(350,"tr"),t(351,`
              `),e(352,"th",5),t(353,"#"),n(),t(354,`
              `),e(355,"th",5),t(356,"Class"),n(),t(357,`
              `),e(358,"th",5),t(359,"Heading"),n(),t(360,`
              `),e(361,"th",5),t(362,"Heading"),n(),t(363,`
            `),n(),t(364,`
            `),n(),t(365,`
            `),e(366,"tbody"),t(367,`
            `),e(368,"tr"),t(369,`
              `),e(370,"th",6),t(371,"1"),n(),t(372,`
              `),e(373,"td"),t(374,"Mark"),n(),t(375,`
              `),e(376,"td"),t(377,"Otto"),n(),t(378,`
              `),e(379,"td"),t(380,"@mdo"),n(),t(381,`
            `),n(),t(382,`
            `),e(383,"tr"),t(384,`
              `),e(385,"th",6),t(386,"2"),n(),t(387,`
              `),e(388,"td"),t(389,"Jacob"),n(),t(390,`
              `),e(391,"td"),t(392,"Thornton"),n(),t(393,`
              `),e(394,"td"),t(395,"@fat"),n(),t(396,`
            `),n(),t(397,`
            `),e(398,"tr"),t(399,`
              `),e(400,"th",6),t(401,"3"),n(),t(402,`
              `),e(403,"td",7),t(404,"Larry the Bird"),n(),t(405,`
              `),e(406,"td"),t(407,"@twitter"),n(),t(408,`
            `),n(),t(409,`
            `),n(),t(410,`
          `),n(),t(411,`
        `),n(),t(412,`
        `),e(413,"app-docs-example",17),t(414,`
          `),e(415,"table",20),t(416,`
            `),e(417,"thead"),t(418,`
            `),e(419,"tr"),t(420,`
              `),e(421,"th",5),t(422,"#"),n(),t(423,`
              `),e(424,"th",5),t(425,"Class"),n(),t(426,`
              `),e(427,"th",5),t(428,"Heading"),n(),t(429,`
              `),e(430,"th",5),t(431,"Heading"),n(),t(432,`
            `),n(),t(433,`
            `),n(),t(434,`
            `),e(435,"tbody"),t(436,`
            `),e(437,"tr"),t(438,`
              `),e(439,"th",6),t(440,"1"),n(),t(441,`
              `),e(442,"td"),t(443,"Mark"),n(),t(444,`
              `),e(445,"td"),t(446,"Otto"),n(),t(447,`
              `),e(448,"td"),t(449,"@mdo"),n(),t(450,`
            `),n(),t(451,`
            `),e(452,"tr"),t(453,`
              `),e(454,"th",6),t(455,"2"),n(),t(456,`
              `),e(457,"td"),t(458,"Jacob"),n(),t(459,`
              `),e(460,"td"),t(461,"Thornton"),n(),t(462,`
              `),e(463,"td"),t(464,"@fat"),n(),t(465,`
            `),n(),t(466,`
            `),e(467,"tr"),t(468,`
              `),e(469,"th",6),t(470,"3"),n(),t(471,`
              `),e(472,"td",7),t(473,"Larry the Bird"),n(),t(474,`
              `),e(475,"td"),t(476,"@twitter"),n(),t(477,`
            `),n(),t(478,`
            `),n(),t(479,`
          `),n(),t(480,`
        `),n(),t(481,`
      `),n(),t(482,`
    `),n(),t(483,`
  `),n(),t(484,`
  `),e(485,"c-col",0),t(486,`
    `),e(487,"c-card",1),t(488,`
      `),e(489,"c-card-header"),t(490,`
        `),e(491,"strong"),t(492,"Angular Table"),n(),t(493," "),e(494,"small"),t(495,"Striped columns"),n(),t(496,`
      `),n(),t(497,`
      `),e(498,"c-card-body"),t(499,`
        `),e(500,"p",2),t(501,`
          Use `),e(502,"code"),t(503,"stripedColumn"),n(),t(504,` property to add zebra-striping to any table column.
        `),n(),t(505,`
        `),e(506,"app-docs-example",21),t(507,`
          `),e(508,"table",22),t(509,`
            `),e(510,"thead"),t(511,`
            `),e(512,"tr"),t(513,`
              `),e(514,"th",5),t(515,"#"),n(),t(516,`
              `),e(517,"th",5),t(518,"Class"),n(),t(519,`
              `),e(520,"th",5),t(521,"Heading"),n(),t(522,`
              `),e(523,"th",5),t(524,"Heading"),n(),t(525,`
            `),n(),t(526,`
            `),n(),t(527,`
            `),e(528,"tbody"),t(529,`
            `),e(530,"tr"),t(531,`
              `),e(532,"th",6),t(533,"1"),n(),t(534,`
              `),e(535,"td"),t(536,"Mark"),n(),t(537,`
              `),e(538,"td"),t(539,"Otto"),n(),t(540,`
              `),e(541,"td"),t(542,"@mdo"),n(),t(543,`
            `),n(),t(544,`
            `),e(545,"tr"),t(546,`
              `),e(547,"th",6),t(548,"2"),n(),t(549,`
              `),e(550,"td"),t(551,"Jacob"),n(),t(552,`
              `),e(553,"td"),t(554,"Thornton"),n(),t(555,`
              `),e(556,"td"),t(557,"@fat"),n(),t(558,`
            `),n(),t(559,`
            `),e(560,"tr"),t(561,`
              `),e(562,"th",6),t(563,"3"),n(),t(564,`
              `),e(565,"td",7),t(566,"Larry the Bird"),n(),t(567,`
              `),e(568,"td"),t(569,"@twitter"),n(),t(570,`
            `),n(),t(571,`
            `),n(),t(572,`
          `),n(),t(573,`
        `),n(),t(574,`
      `),n(),t(575,`
    `),n(),t(576,`
  `),n(),t(577,`
  `),e(578,"c-col",0),t(579,`
    `),e(580,"c-card",1),t(581,`
      `),e(582,"c-card-header"),t(583,`
        `),e(584,"strong"),t(585,"Angular Table"),n(),t(586," "),e(587,"small"),t(588,"Hoverable rows"),n(),t(589,`
      `),n(),t(590,`
      `),e(591,"c-card-body"),t(592,`
        `),e(593,"p",2),t(594,`
          Use `),e(595,"code"),t(596,'[hover]="true"'),n(),t(597,` property to enable a hover state on table rows within a
          `),e(598,"code"),t(599,"<tbody>"),n(),t(600,`.
        `),n(),t(601,`
        `),e(602,"app-docs-example",23),t(603,`
          `),e(604,"table",24),t(605,`
            `),e(606,"thead"),t(607,`
            `),e(608,"tr"),t(609,`
              `),e(610,"th",5),t(611,"#"),n(),t(612,`
              `),e(613,"th",5),t(614,"Class"),n(),t(615,`
              `),e(616,"th",5),t(617,"Heading"),n(),t(618,`
              `),e(619,"th",5),t(620,"Heading"),n(),t(621,`
            `),n(),t(622,`
            `),n(),t(623,`
            `),e(624,"tbody"),t(625,`
            `),e(626,"tr"),t(627,`
              `),e(628,"th",6),t(629,"1"),n(),t(630,`
              `),e(631,"td"),t(632,"Mark"),n(),t(633,`
              `),e(634,"td"),t(635,"Otto"),n(),t(636,`
              `),e(637,"td"),t(638,"@mdo"),n(),t(639,`
            `),n(),t(640,`
            `),e(641,"tr"),t(642,`
              `),e(643,"th",6),t(644,"2"),n(),t(645,`
              `),e(646,"td"),t(647,"Jacob"),n(),t(648,`
              `),e(649,"td"),t(650,"Thornton"),n(),t(651,`
              `),e(652,"td"),t(653,"@fat"),n(),t(654,`
            `),n(),t(655,`
            `),e(656,"tr"),t(657,`
              `),e(658,"th",6),t(659,"3"),n(),t(660,`
              `),e(661,"td",7),t(662,"Larry the Bird"),n(),t(663,`
              `),e(664,"td"),t(665,"@twitter"),n(),t(666,`
            `),n(),t(667,`
            `),n(),t(668,`
          `),n(),t(669,`
        `),n(),t(670,`
        `),e(671,"app-docs-example",23),t(672,`
          `),e(673,"table",25),t(674,`
            `),e(675,"thead"),t(676,`
            `),e(677,"tr"),t(678,`
              `),e(679,"th",5),t(680,"#"),n(),t(681,`
              `),e(682,"th",5),t(683,"Class"),n(),t(684,`
              `),e(685,"th",5),t(686,"Heading"),n(),t(687,`
              `),e(688,"th",5),t(689,"Heading"),n(),t(690,`
            `),n(),t(691,`
            `),n(),t(692,`
            `),e(693,"tbody"),t(694,`
            `),e(695,"tr"),t(696,`
              `),e(697,"th",6),t(698,"1"),n(),t(699,`
              `),e(700,"td"),t(701,"Mark"),n(),t(702,`
              `),e(703,"td"),t(704,"Otto"),n(),t(705,`
              `),e(706,"td"),t(707,"@mdo"),n(),t(708,`
            `),n(),t(709,`
            `),e(710,"tr"),t(711,`
              `),e(712,"th",6),t(713,"2"),n(),t(714,`
              `),e(715,"td"),t(716,"Jacob"),n(),t(717,`
              `),e(718,"td"),t(719,"Thornton"),n(),t(720,`
              `),e(721,"td"),t(722,"@fat"),n(),t(723,`
            `),n(),t(724,`
            `),e(725,"tr"),t(726,`
              `),e(727,"th",6),t(728,"3"),n(),t(729,`
              `),e(730,"td",7),t(731,"Larry the Bird"),n(),t(732,`
              `),e(733,"td"),t(734,"@twitter"),n(),t(735,`
            `),n(),t(736,`
            `),n(),t(737,`
          `),n(),t(738,`
        `),n(),t(739,`
        `),e(740,"app-docs-example",23),t(741,`
          `),e(742,"table",26),t(743,`
            `),e(744,"thead"),t(745,`
            `),e(746,"tr"),t(747,`
              `),e(748,"th",5),t(749,"#"),n(),t(750,`
              `),e(751,"th",5),t(752,"Class"),n(),t(753,`
              `),e(754,"th",5),t(755,"Heading"),n(),t(756,`
              `),e(757,"th",5),t(758,"Heading"),n(),t(759,`
            `),n(),t(760,`
            `),n(),t(761,`
            `),e(762,"tbody"),t(763,`
            `),e(764,"tr"),t(765,`
              `),e(766,"th",6),t(767,"1"),n(),t(768,`
              `),e(769,"td"),t(770,"Mark"),n(),t(771,`
              `),e(772,"td"),t(773,"Otto"),n(),t(774,`
              `),e(775,"td"),t(776,"@mdo"),n(),t(777,`
            `),n(),t(778,`
            `),e(779,"tr"),t(780,`
              `),e(781,"th",6),t(782,"2"),n(),t(783,`
              `),e(784,"td"),t(785,"Jacob"),n(),t(786,`
              `),e(787,"td"),t(788,"Thornton"),n(),t(789,`
              `),e(790,"td"),t(791,"@fat"),n(),t(792,`
            `),n(),t(793,`
            `),e(794,"tr"),t(795,`
              `),e(796,"th",6),t(797,"3"),n(),t(798,`
              `),e(799,"td",7),t(800,"Larry the Bird"),n(),t(801,`
              `),e(802,"td"),t(803,"@twitter"),n(),t(804,`
            `),n(),t(805,`
            `),n(),t(806,`
          `),n(),t(807,`
        `),n(),t(808,`
      `),n(),t(809,`
    `),n(),t(810,`
  `),n(),t(811,`
  `),e(812,"c-col",0),t(813,`
    `),e(814,"c-card",1),t(815,`
      `),e(816,"c-card-header"),t(817,`
        `),e(818,"strong"),t(819,"Angular Table"),n(),t(820," "),e(821,"small"),t(822,"Active tables"),n(),t(823,`
      `),n(),t(824,`
      `),e(825,"c-card-body"),t(826,`
        `),e(827,"app-docs-example",27),t(828,`
          `),e(829,"table",4),t(830,`
            `),e(831,"thead"),t(832,`
            `),e(833,"tr"),t(834,`
              `),e(835,"th",5),t(836,"#"),n(),t(837,`
              `),e(838,"th",5),t(839,"Class"),n(),t(840,`
              `),e(841,"th",5),t(842,"Heading"),n(),t(843,`
              `),e(844,"th",5),t(845,"Heading"),n(),t(846,`
            `),n(),t(847,`
            `),n(),t(848,`
            `),e(849,"tbody"),t(850,`
            `),e(851,"tr",28),t(852,`
              `),e(853,"th",6),t(854,"1"),n(),t(855,`
              `),e(856,"td"),t(857,"Mark"),n(),t(858,`
              `),e(859,"td"),t(860,"Otto"),n(),t(861,`
              `),e(862,"td"),t(863,"@mdo"),n(),t(864,`
            `),n(),t(865,`
            `),e(866,"tr"),t(867,`
              `),e(868,"th",6),t(869,"2"),n(),t(870,`
              `),e(871,"td"),t(872,"Jacob"),n(),t(873,`
              `),e(874,"td"),t(875,"Thornton"),n(),t(876,`
              `),e(877,"td"),t(878,"@fat"),n(),t(879,`
            `),n(),t(880,`
            `),e(881,"tr"),t(882,`
              `),e(883,"th",6),t(884,"3"),n(),t(885,`
              `),e(886,"td",29),t(887,`
                Larry the Bird
              `),n(),t(888,`
              `),e(889,"td"),t(890,"@twitter"),n(),t(891,`
            `),n(),t(892,`
            `),n(),t(893,`
          `),n(),t(894,`
        `),n(),t(895,`
        `),e(896,"app-docs-example",27),t(897,`
          `),e(898,"table",30),t(899,`
            `),e(900,"thead"),t(901,`
            `),e(902,"tr"),t(903,`
              `),e(904,"th",5),t(905,"#"),n(),t(906,`
              `),e(907,"th",5),t(908,"Class"),n(),t(909,`
              `),e(910,"th",5),t(911,"Heading"),n(),t(912,`
              `),e(913,"th",5),t(914,"Heading"),n(),t(915,`
            `),n(),t(916,`
            `),n(),t(917,`
            `),e(918,"tbody"),t(919,`
            `),e(920,"tr",28),t(921,`
              `),e(922,"th",6),t(923,"1"),n(),t(924,`
              `),e(925,"td"),t(926,"Mark"),n(),t(927,`
              `),e(928,"td"),t(929,"Otto"),n(),t(930,`
              `),e(931,"td"),t(932,"@mdo"),n(),t(933,`
            `),n(),t(934,`
            `),e(935,"tr"),t(936,`
              `),e(937,"th",6),t(938,"2"),n(),t(939,`
              `),e(940,"td"),t(941,"Jacob"),n(),t(942,`
              `),e(943,"td"),t(944,"Thornton"),n(),t(945,`
              `),e(946,"td"),t(947,"@fat"),n(),t(948,`
            `),n(),t(949,`
            `),e(950,"tr"),t(951,`
              `),e(952,"th",6),t(953,"3"),n(),t(954,`
              `),e(955,"td",29),t(956,`
                Larry the Bird
              `),n(),t(957,`
              `),e(958,"td"),t(959,"@twitter"),n(),t(960,`
            `),n(),t(961,`
            `),n(),t(962,`
          `),n(),t(963,`
        `),n(),t(964,`
      `),n(),t(965,`
    `),n(),t(966,`
  `),n(),t(967,`
  `),e(968,"c-col",0),t(969,`
    `),e(970,"c-card",1),t(971,`
      `),e(972,"c-card-header"),t(973,`
        `),e(974,"strong"),t(975,"Angular Table"),n(),t(976," "),e(977,"small"),t(978,"Bordered tables"),n(),t(979,`
      `),n(),t(980,`
      `),e(981,"c-card-body"),t(982,`
        `),e(983,"p",2),t(984,`
          Add `),e(985,"code"),t(986,"bordered"),n(),t(987,` property for borders on all sides of the table and cells.
        `),n(),t(988,`
        `),e(989,"app-docs-example",31),t(990,`
          `),e(991,"table",32),t(992,`
            `),e(993,"thead"),t(994,`
            `),e(995,"tr"),t(996,`
              `),e(997,"th",5),t(998,"#"),n(),t(999,`
              `),e(1e3,"th",5),t(1001,"Class"),n(),t(1002,`
              `),e(1003,"th",5),t(1004,"Heading"),n(),t(1005,`
              `),e(1006,"th",5),t(1007,"Heading"),n(),t(1008,`
            `),n(),t(1009,`
            `),n(),t(1010,`
            `),e(1011,"tbody"),t(1012,`
            `),e(1013,"tr"),t(1014,`
              `),e(1015,"th",6),t(1016,"1"),n(),t(1017,`
              `),e(1018,"td"),t(1019,"Mark"),n(),t(1020,`
              `),e(1021,"td"),t(1022,"Otto"),n(),t(1023,`
              `),e(1024,"td"),t(1025,"@mdo"),n(),t(1026,`
            `),n(),t(1027,`
            `),e(1028,"tr"),t(1029,`
              `),e(1030,"th",6),t(1031,"2"),n(),t(1032,`
              `),e(1033,"td"),t(1034,"Jacob"),n(),t(1035,`
              `),e(1036,"td"),t(1037,"Thornton"),n(),t(1038,`
              `),e(1039,"td"),t(1040,"@fat"),n(),t(1041,`
            `),n(),t(1042,`
            `),e(1043,"tr"),t(1044,`
              `),e(1045,"th",6),t(1046,"3"),n(),t(1047,`
              `),e(1048,"td",7),t(1049,"Larry the Bird"),n(),t(1050,`
              `),e(1051,"td"),t(1052,"@twitter"),n(),t(1053,`
            `),n(),t(1054,`
            `),n(),t(1055,`
          `),n(),t(1056,`
        `),n(),t(1057,`
        `),e(1058,"p",2),t(1059,`
          `),e(1060,"a",33),t(1061,`
            Border color utilities
          `),n(),t(1062,` can be added to change colors:
        `),n(),t(1063,`
        `),e(1064,"app-docs-example",31),t(1065,`
          `),e(1066,"table",34),t(1067,`
            `),e(1068,"thead"),t(1069,`
            `),e(1070,"tr"),t(1071,`
              `),e(1072,"th",5),t(1073,"#"),n(),t(1074,`
              `),e(1075,"th",5),t(1076,"Class"),n(),t(1077,`
              `),e(1078,"th",5),t(1079,"Heading"),n(),t(1080,`
              `),e(1081,"th",5),t(1082,"Heading"),n(),t(1083,`
            `),n(),t(1084,`
            `),n(),t(1085,`
            `),e(1086,"tbody"),t(1087,`
            `),e(1088,"tr"),t(1089,`
              `),e(1090,"th",6),t(1091,"1"),n(),t(1092,`
              `),e(1093,"td"),t(1094,"Mark"),n(),t(1095,`
              `),e(1096,"td"),t(1097,"Otto"),n(),t(1098,`
              `),e(1099,"td"),t(1100,"@mdo"),n(),t(1101,`
            `),n(),t(1102,`
            `),e(1103,"tr"),t(1104,`
              `),e(1105,"th",6),t(1106,"2"),n(),t(1107,`
              `),e(1108,"td"),t(1109,"Jacob"),n(),t(1110,`
              `),e(1111,"td"),t(1112,"Thornton"),n(),t(1113,`
              `),e(1114,"td"),t(1115,"@fat"),n(),t(1116,`
            `),n(),t(1117,`
            `),e(1118,"tr"),t(1119,`
              `),e(1120,"th",6),t(1121,"3"),n(),t(1122,`
              `),e(1123,"td",7),t(1124,"Larry the Bird"),n(),t(1125,`
              `),e(1126,"td"),t(1127,"@twitter"),n(),t(1128,`
            `),n(),t(1129,`
            `),n(),t(1130,`
          `),n(),t(1131,`
        `),n(),t(1132,`
      `),n(),t(1133,`
    `),n(),t(1134,`
  `),n(),t(1135,`
  `),e(1136,"c-col",0),t(1137,`
    `),e(1138,"c-card",1),t(1139,`
      `),e(1140,"c-card-header"),t(1141,`
        `),e(1142,"strong"),t(1143,"Angular Table"),n(),t(1144," "),e(1145,"small"),t(1146,"Tables without borders"),n(),t(1147,`
      `),n(),t(1148,`
      `),e(1149,"c-card-body"),t(1150,`
        `),e(1151,"p",2),t(1152,`
          Add `),e(1153,"code"),t(1154,"borderless"),n(),t(1155,` property for a table without borders.
        `),n(),t(1156,`
        `),e(1157,"app-docs-example",35),t(1158,`
          `),e(1159,"table",36),t(1160,`
            `),e(1161,"thead"),t(1162,`
            `),e(1163,"tr"),t(1164,`
              `),e(1165,"th",5),t(1166,"#"),n(),t(1167,`
              `),e(1168,"th",5),t(1169,"Class"),n(),t(1170,`
              `),e(1171,"th",5),t(1172,"Heading"),n(),t(1173,`
              `),e(1174,"th",5),t(1175,"Heading"),n(),t(1176,`
            `),n(),t(1177,`
            `),n(),t(1178,`
            `),e(1179,"tbody"),t(1180,`
            `),e(1181,"tr"),t(1182,`
              `),e(1183,"th",6),t(1184,"1"),n(),t(1185,`
              `),e(1186,"td"),t(1187,"Mark"),n(),t(1188,`
              `),e(1189,"td"),t(1190,"Otto"),n(),t(1191,`
              `),e(1192,"td"),t(1193,"@mdo"),n(),t(1194,`
            `),n(),t(1195,`
            `),e(1196,"tr"),t(1197,`
              `),e(1198,"th",6),t(1199,"2"),n(),t(1200,`
              `),e(1201,"td"),t(1202,"Jacob"),n(),t(1203,`
              `),e(1204,"td"),t(1205,"Thornton"),n(),t(1206,`
              `),e(1207,"td"),t(1208,"@fat"),n(),t(1209,`
            `),n(),t(1210,`
            `),e(1211,"tr"),t(1212,`
              `),e(1213,"th",6),t(1214,"3"),n(),t(1215,`
              `),e(1216,"td",7),t(1217,"Larry the Bird"),n(),t(1218,`
              `),e(1219,"td"),t(1220,"@twitter"),n(),t(1221,`
            `),n(),t(1222,`
            `),n(),t(1223,`
          `),n(),t(1224,`
        `),n(),t(1225,`
        `),e(1226,"app-docs-example",35),t(1227,`
          `),e(1228,"table",37),t(1229,`
            `),e(1230,"thead"),t(1231,`
            `),e(1232,"tr"),t(1233,`
              `),e(1234,"th",5),t(1235,"#"),n(),t(1236,`
              `),e(1237,"th",5),t(1238,"Class"),n(),t(1239,`
              `),e(1240,"th",5),t(1241,"Heading"),n(),t(1242,`
              `),e(1243,"th",5),t(1244,"Heading"),n(),t(1245,`
            `),n(),t(1246,`
            `),n(),t(1247,`
            `),e(1248,"tbody"),t(1249,`
            `),e(1250,"tr"),t(1251,`
              `),e(1252,"th",6),t(1253,"1"),n(),t(1254,`
              `),e(1255,"td"),t(1256,"Mark"),n(),t(1257,`
              `),e(1258,"td"),t(1259,"Otto"),n(),t(1260,`
              `),e(1261,"td"),t(1262,"@mdo"),n(),t(1263,`
            `),n(),t(1264,`
            `),e(1265,"tr"),t(1266,`
              `),e(1267,"th",6),t(1268,"2"),n(),t(1269,`
              `),e(1270,"td"),t(1271,"Jacob"),n(),t(1272,`
              `),e(1273,"td"),t(1274,"Thornton"),n(),t(1275,`
              `),e(1276,"td"),t(1277,"@fat"),n(),t(1278,`
            `),n(),t(1279,`
            `),e(1280,"tr"),t(1281,`
              `),e(1282,"th",6),t(1283,"3"),n(),t(1284,`
              `),e(1285,"td",7),t(1286,"Larry the Bird"),n(),t(1287,`
              `),e(1288,"td"),t(1289,"@twitter"),n(),t(1290,`
            `),n(),t(1291,`
            `),n(),t(1292,`
          `),n(),t(1293,`
        `),n(),t(1294,`
      `),n(),t(1295,`
    `),n(),t(1296,`
  `),n(),t(1297,`
  `),e(1298,"c-col",0),t(1299,`
    `),e(1300,"c-card",1),t(1301,`
      `),e(1302,"c-card-header"),t(1303,`
        `),e(1304,"strong"),t(1305,"Angular Table"),n(),t(1306," "),e(1307,"small"),t(1308,"Small tables"),n(),t(1309,`
      `),n(),t(1310,`
      `),e(1311,"c-card-body"),t(1312,`
        `),e(1313,"p",2),t(1314,`
          Add `),e(1315,"code"),t(1316,"small"),n(),t(1317," property to make any "),e(1318,"code"),t(1319,"cTable"),n(),t(1320,` more compact
          by cutting all cell `),e(1321,"code"),t(1322,"padding"),n(),t(1323,` in half.
        `),n(),t(1324,`
        `),e(1325,"app-docs-example",38),t(1326,`
          `),e(1327,"table",39),t(1328,`
            `),e(1329,"thead"),t(1330,`
            `),e(1331,"tr"),t(1332,`
              `),e(1333,"th",5),t(1334,"#"),n(),t(1335,`
              `),e(1336,"th",5),t(1337,"Class"),n(),t(1338,`
              `),e(1339,"th",5),t(1340,"Heading"),n(),t(1341,`
              `),e(1342,"th",5),t(1343,"Heading"),n(),t(1344,`
            `),n(),t(1345,`
            `),n(),t(1346,`
            `),e(1347,"tbody"),t(1348,`
            `),e(1349,"tr"),t(1350,`
              `),e(1351,"th",6),t(1352,"1"),n(),t(1353,`
              `),e(1354,"td"),t(1355,"Mark"),n(),t(1356,`
              `),e(1357,"td"),t(1358,"Otto"),n(),t(1359,`
              `),e(1360,"td"),t(1361,"@mdo"),n(),t(1362,`
            `),n(),t(1363,`
            `),e(1364,"tr"),t(1365,`
              `),e(1366,"th",6),t(1367,"2"),n(),t(1368,`
              `),e(1369,"td"),t(1370,"Jacob"),n(),t(1371,`
              `),e(1372,"td"),t(1373,"Thornton"),n(),t(1374,`
              `),e(1375,"td"),t(1376,"@fat"),n(),t(1377,`
            `),n(),t(1378,`
            `),e(1379,"tr"),t(1380,`
              `),e(1381,"th",6),t(1382,"3"),n(),t(1383,`
              `),e(1384,"td",7),t(1385,"Larry the Bird"),n(),t(1386,`
              `),e(1387,"td"),t(1388,"@twitter"),n(),t(1389,`
            `),n(),t(1390,`
            `),n(),t(1391,`
          `),n(),t(1392,`
        `),n(),t(1393,`
      `),n(),t(1394,`
    `),n(),t(1395,`
  `),n(),t(1396,`
  `),e(1397,"c-col",0),t(1398,`
    `),e(1399,"c-card",1),t(1400,`
      `),e(1401,"c-card-header"),t(1402,`
        `),e(1403,"strong"),t(1404,"Angular Table"),n(),t(1405," "),e(1406,"small"),t(1407,"Vertical alignment"),n(),t(1408,`
      `),n(),t(1409,`
      `),e(1410,"c-card-body"),t(1411,`
        `),e(1412,"p",2),t(1413,`
          Table cells of `),e(1414,"code"),t(1415,"<thead>"),n(),t(1416,` are always vertical aligned to the
          bottom. Table cells in `),e(1417,"code"),t(1418,"<tbody>"),n(),t(1419,` inherit their alignment from
          `),e(1420,"code"),t(1421,"cTable"),n(),t(1422,` and are aligned to the the top by default. Use the align
          property to re-align where needed.
        `),n(),t(1423,`
        `),e(1424,"app-docs-example",40),t(1425,`
          `),e(1426,"table",41),t(1427,`
            `),e(1428,"thead"),t(1429,`
            `),e(1430,"tr"),t(1431,`
              `),e(1432,"th",42),t(1433,`
                Heading 1
              `),n(),t(1434,`
              `),e(1435,"th",42),t(1436,`
                Heading 2
              `),n(),t(1437,`
              `),e(1438,"th",42),t(1439,`
                Heading 3
              `),n(),t(1440,`
              `),e(1441,"th",42),t(1442,`
                Heading 4
              `),n(),t(1443,`
            `),n(),t(1444,`
            `),n(),t(1445,`
            `),e(1446,"tbody"),t(1447,`
            `),e(1448,"tr"),t(1449,`
              `),e(1450,"td"),t(1451,`
                This cell inherits `),e(1452,"code"),t(1453,"vertical-align: middle;"),n(),t(1454,` from the table
              `),n(),t(1455,`
              `),e(1456,"td"),t(1457,`
                This cell inherits `),e(1458,"code"),t(1459,"vertical-align: middle;"),n(),t(1460,` from the table
              `),n(),t(1461,`
              `),e(1462,"td"),t(1463,`
                This cell inherits `),e(1464,"code"),t(1465,"vertical-align: middle;"),n(),t(1466,` from the table
              `),n(),t(1467,`
              `),e(1468,"td"),t(1469,`
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonstrate how the vertical alignment works in the
                preceding cells.
              `),n(),t(1470,`
            `),n(),t(1471,`
            `),e(1472,"tr",43),t(1473,`
              `),e(1474,"td"),t(1475,`
                This cell inherits `),e(1476,"code"),t(1477,"vertical-align: bottom;"),n(),t(1478,` from the table row
              `),n(),t(1479,`
              `),e(1480,"td"),t(1481,`
                This cell inherits `),e(1482,"code"),t(1483,"vertical-align: bottom;"),n(),t(1484,` from the table row
              `),n(),t(1485,`
              `),e(1486,"td"),t(1487,`
                This cell inherits `),e(1488,"code"),t(1489,"vertical-align: bottom;"),n(),t(1490,` from the table row
              `),n(),t(1491,`
              `),e(1492,"td"),t(1493,`
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonstrate how the vertical alignment works in the
                preceding cells.
              `),n(),t(1494,`
            `),n(),t(1495,`
            `),e(1496,"tr"),t(1497,`
              `),e(1498,"td"),t(1499,`
                This cell inherits `),e(1500,"code"),t(1501,"vertical-align: middle;"),n(),t(1502,` from the table
              `),n(),t(1503,`
              `),e(1504,"td"),t(1505,`
                This cell inherits `),e(1506,"code"),t(1507,"vertical-align: middle;"),n(),t(1508,` from the table
              `),n(),t(1509,`
              `),e(1510,"td",44),t(1511,"This cell is aligned to the top."),n(),t(1512,`
              `),e(1513,"td"),t(1514,`
                This here is some placeholder text, intended to take up quite a bit of
                vertical space, to demonsCTableRowate how the vertical alignment works in the
                preceding cells.
              `),n(),t(1515,`
            `),n(),t(1516,`
            `),n(),t(1517,`
          `),n(),t(1518,`
        `),n(),t(1519,`
      `),n(),t(1520,`
    `),n(),t(1521,`
  `),n(),t(1522,`
  `),e(1523,"c-col",0),t(1524,`
    `),e(1525,"c-card",1),t(1526,`
      `),e(1527,"c-card-header"),t(1528,`
        `),e(1529,"strong"),t(1530,"Angular Table"),n(),t(1531," "),e(1532,"small"),t(1533,"Nesting"),n(),t(1534,`
      `),n(),t(1535,`
      `),e(1536,"c-card-body"),t(1537,`
        `),e(1538,"p",2),t(1539,`
          Border styles, active styles, and table variants are not inherited by nested tables.
        `),n(),t(1540,`
        `),e(1541,"app-docs-example",45),t(1542,`
          `),e(1543,"table",18),t(1544,`
            `),e(1545,"thead"),t(1546,`
            `),e(1547,"tr"),t(1548,`
              `),e(1549,"th",5),t(1550,"#"),n(),t(1551,`
              `),e(1552,"th",5),t(1553,"Class"),n(),t(1554,`
              `),e(1555,"th",5),t(1556,"Heading"),n(),t(1557,`
              `),e(1558,"th",5),t(1559,"Heading"),n(),t(1560,`
            `),n(),t(1561,`
            `),n(),t(1562,`
            `),e(1563,"tbody"),t(1564,`
            `),e(1565,"tr"),t(1566,`
              `),e(1567,"th",6),t(1568,"1"),n(),t(1569,`
              `),e(1570,"td"),t(1571,"Mark"),n(),t(1572,`
              `),e(1573,"td"),t(1574,"Otto"),n(),t(1575,`
              `),e(1576,"td"),t(1577,"@mdo"),n(),t(1578,`
            `),n(),t(1579,`
            `),e(1580,"tr"),t(1581,`
              `),e(1582,"th",46),t(1583,`
                `),e(1584,"table",4),t(1585,`
                  `),e(1586,"thead"),t(1587,`
                  `),e(1588,"tr"),t(1589,`
                    `),e(1590,"th",5),t(1591,"Header"),n(),t(1592,`
                    `),e(1593,"th",5),t(1594,"Header"),n(),t(1595,`
                    `),e(1596,"th",5),t(1597,"Header"),n(),t(1598,`
                  `),n(),t(1599,`
                  `),n(),t(1600,`
                  `),e(1601,"tbody"),t(1602,`
                  `),e(1603,"tr"),t(1604,`
                    `),e(1605,"th",6),t(1606,"A"),n(),t(1607,`
                    `),e(1608,"td"),t(1609,"First"),n(),t(1610,`
                    `),e(1611,"td"),t(1612,"Last"),n(),t(1613,`
                  `),n(),t(1614,`
                  `),e(1615,"tr"),t(1616,`
                    `),e(1617,"th",6),t(1618,"B"),n(),t(1619,`
                    `),e(1620,"td"),t(1621,"First"),n(),t(1622,`
                    `),e(1623,"td"),t(1624,"Last"),n(),t(1625,`
                  `),n(),t(1626,`
                  `),e(1627,"tr"),t(1628,`
                    `),e(1629,"th",6),t(1630,"C"),n(),t(1631,`
                    `),e(1632,"td"),t(1633,"First"),n(),t(1634,`
                    `),e(1635,"td"),t(1636,"Last"),n(),t(1637,`
                  `),n(),t(1638,`
                  `),n(),t(1639,`
                `),n(),t(1640,`
              `),n(),t(1641,`
            `),n(),t(1642,`
            `),e(1643,"tr"),t(1644,`
              `),e(1645,"th",6),t(1646,"3"),n(),t(1647,`
              `),e(1648,"td",7),t(1649,"Larry the Bird"),n(),t(1650,`
              `),e(1651,"td"),t(1652,"@twitter"),n(),t(1653,`
            `),n(),t(1654,`
            `),n(),t(1655,`
          `),n(),t(1656,`
        `),n(),t(1657,`
      `),n(),t(1658,`
    `),n(),t(1659,`
  `),n(),t(1660,`
  `),e(1661,"c-col",0),t(1662,`
    `),e(1663,"c-card",1),t(1664,`
      `),e(1665,"c-card-header"),t(1666,`
        `),e(1667,"strong"),t(1668,"Angular Table"),n(),t(1669," "),e(1670,"small"),t(1671,"Table head"),n(),t(1672,`
      `),n(),t(1673,`
      `),e(1674,"c-card-body"),t(1675,`
        `),e(1676,"p",2),t(1677,`
          Similar to tables and dark tables, use the modifier prop
          `),e(1678,"code"),t(1679,'color="light"'),n(),t(1680," or "),e(1681,"code"),t(1682,'color="dark"'),n(),t(1683,` to make
          `),e(1684,"code"),t(1685,"<thead>"),n(),t(1686,`s appear light or dark gray.
        `),n(),t(1687,`
        `),e(1688,"app-docs-example",47),t(1689,`
          `),e(1690,"table",4),t(1691,`
            `),e(1692,"thead",15),t(1693,`
            `),e(1694,"tr"),t(1695,`
              `),e(1696,"th",5),t(1697,"#"),n(),t(1698,`
              `),e(1699,"th",5),t(1700,"Class"),n(),t(1701,`
              `),e(1702,"th",5),t(1703,"Heading"),n(),t(1704,`
              `),e(1705,"th",5),t(1706,"Heading"),n(),t(1707,`
            `),n(),t(1708,`
            `),n(),t(1709,`
            `),e(1710,"tbody"),t(1711,`
            `),e(1712,"tr"),t(1713,`
              `),e(1714,"th",6),t(1715,"1"),n(),t(1716,`
              `),e(1717,"td"),t(1718,"Mark"),n(),t(1719,`
              `),e(1720,"td"),t(1721,"Otto"),n(),t(1722,`
              `),e(1723,"td"),t(1724,"@mdo"),n(),t(1725,`
            `),n(),t(1726,`
            `),e(1727,"tr"),t(1728,`
              `),e(1729,"th",6),t(1730,"2"),n(),t(1731,`
              `),e(1732,"td"),t(1733,"Jacob"),n(),t(1734,`
              `),e(1735,"td"),t(1736,"Thornton"),n(),t(1737,`
              `),e(1738,"td"),t(1739,"@fat"),n(),t(1740,`
            `),n(),t(1741,`
            `),e(1742,"tr"),t(1743,`
              `),e(1744,"th",6),t(1745,"3"),n(),t(1746,`
              `),e(1747,"td"),t(1748,"Larry"),n(),t(1749,`
              `),e(1750,"td"),t(1751,"the Bird"),n(),t(1752,`
              `),e(1753,"td"),t(1754,"@twitter"),n(),t(1755,`
            `),n(),t(1756,`
            `),n(),t(1757,`
          `),n(),t(1758,`
        `),n(),t(1759,`
        `),e(1760,"app-docs-example",47),t(1761,`
          `),e(1762,"table",4),t(1763,`
            `),e(1764,"thead",16),t(1765,`
            `),e(1766,"tr"),t(1767,`
              `),e(1768,"th",5),t(1769,"#"),n(),t(1770,`
              `),e(1771,"th",5),t(1772,"Class"),n(),t(1773,`
              `),e(1774,"th",5),t(1775,"Heading"),n(),t(1776,`
              `),e(1777,"th",5),t(1778,"Heading"),n(),t(1779,`
            `),n(),t(1780,`
            `),n(),t(1781,`
            `),e(1782,"tbody"),t(1783,`
            `),e(1784,"tr"),t(1785,`
              `),e(1786,"th",6),t(1787,"1"),n(),t(1788,`
              `),e(1789,"td"),t(1790,"Mark"),n(),t(1791,`
              `),e(1792,"td"),t(1793,"Otto"),n(),t(1794,`
              `),e(1795,"td"),t(1796,"@mdo"),n(),t(1797,`
            `),n(),t(1798,`
            `),e(1799,"tr"),t(1800,`
              `),e(1801,"th",6),t(1802,"2"),n(),t(1803,`
              `),e(1804,"td"),t(1805,"Jacob"),n(),t(1806,`
              `),e(1807,"td"),t(1808,"Thornton"),n(),t(1809,`
              `),e(1810,"td"),t(1811,"@fat"),n(),t(1812,`
            `),n(),t(1813,`
            `),e(1814,"tr"),t(1815,`
              `),e(1816,"th",6),t(1817,"3"),n(),t(1818,`
              `),e(1819,"td",7),t(1820,"Larry the Bird"),n(),t(1821,`
              `),e(1822,"td"),t(1823,"@twitter"),n(),t(1824,`
            `),n(),t(1825,`
            `),n(),t(1826,`
          `),n(),t(1827,`
        `),n(),t(1828,`
      `),n(),t(1829,`
    `),n(),t(1830,`
  `),n(),t(1831,`
  `),e(1832,"c-col",0),t(1833,`
    `),e(1834,"c-card",1),t(1835,`
      `),e(1836,"c-card-header"),t(1837,`
        `),e(1838,"strong"),t(1839,"Angular Table"),n(),t(1840," "),e(1841,"small"),t(1842,"Table foot"),n(),t(1843,`
      `),n(),t(1844,`
      `),e(1845,"c-card-body"),t(1846,`
        `),e(1847,"app-docs-example",48),t(1848,`
          `),e(1849,"table",4),t(1850,`
            `),e(1851,"thead",15),t(1852,`
            `),e(1853,"tr"),t(1854,`
              `),e(1855,"th",5),t(1856,"#"),n(),t(1857,`
              `),e(1858,"th",5),t(1859,"Class"),n(),t(1860,`
              `),e(1861,"th",5),t(1862,"Heading"),n(),t(1863,`
              `),e(1864,"th",5),t(1865,"Heading"),n(),t(1866,`
            `),n(),t(1867,`
            `),n(),t(1868,`
            `),e(1869,"tbody"),t(1870,`
            `),e(1871,"tr"),t(1872,`
              `),e(1873,"th",6),t(1874,"1"),n(),t(1875,`
              `),e(1876,"td"),t(1877,"Mark"),n(),t(1878,`
              `),e(1879,"td"),t(1880,"Otto"),n(),t(1881,`
              `),e(1882,"td"),t(1883,"@mdo"),n(),t(1884,`
            `),n(),t(1885,`
            `),e(1886,"tr"),t(1887,`
              `),e(1888,"th",6),t(1889,"2"),n(),t(1890,`
              `),e(1891,"td"),t(1892,"Jacob"),n(),t(1893,`
              `),e(1894,"td"),t(1895,"Thornton"),n(),t(1896,`
              `),e(1897,"td"),t(1898,"@fat"),n(),t(1899,`
            `),n(),t(1900,`
            `),e(1901,"tr"),t(1902,`
              `),e(1903,"th",6),t(1904,"3"),n(),t(1905,`
              `),e(1906,"td",7),t(1907,"Larry the Bird"),n(),t(1908,`
              `),e(1909,"td"),t(1910,"@twitter"),n(),t(1911,`
            `),n(),t(1912,`
            `),n(),t(1913,`
            `),e(1914,"tfoot"),t(1915,`
            `),e(1916,"tr"),t(1917,`
              `),e(1918,"td"),t(1919,"Footer"),n(),t(1920,`
              `),e(1921,"td"),t(1922,"Footer"),n(),t(1923,`
              `),e(1924,"td"),t(1925,"Footer"),n(),t(1926,`
              `),e(1927,"td"),t(1928,"Footer"),n(),t(1929,`
            `),n(),t(1930,`
            `),n(),t(1931,`
          `),n(),t(1932,`
        `),n(),t(1933,`
      `),n(),t(1934,`
    `),n(),t(1935,`
  `),n(),t(1936,`
  `),e(1937,"c-col",0),t(1938,`
    `),e(1939,"c-card",1),t(1940,`
      `),e(1941,"c-card-header"),t(1942,`
        `),e(1943,"strong"),t(1944,"Angular Table"),n(),t(1945," "),e(1946,"small"),t(1947,"Captions"),n(),t(1948,`
      `),n(),t(1949,`
      `),e(1950,"c-card-body"),t(1951,`
        `),e(1952,"p",2),t(1953,`
          A `),e(1954,"code"),t(1955,"<caption>"),n(),t(1956,` functions like a heading for a table. It helps
          users with screen readers to find a table and understand what it's about and
          decide if they want to read it.
        `),n(),t(1957,`
        `),e(1958,"app-docs-example",49),t(1959,`
          `),e(1960,"table",4),t(1961,`
            `),e(1962,"caption"),t(1963,"List of users"),n(),t(1964,`
            `),e(1965,"thead"),t(1966,`
            `),e(1967,"tr"),t(1968,`
              `),e(1969,"th",5),t(1970,"#"),n(),t(1971,`
              `),e(1972,"th",5),t(1973,"Class"),n(),t(1974,`
              `),e(1975,"th",5),t(1976,"Heading"),n(),t(1977,`
              `),e(1978,"th",5),t(1979,"Heading"),n(),t(1980,`
            `),n(),t(1981,`
            `),n(),t(1982,`
            `),e(1983,"tbody"),t(1984,`
            `),e(1985,"tr"),t(1986,`
              `),e(1987,"th",6),t(1988,"1"),n(),t(1989,`
              `),e(1990,"td"),t(1991,"Mark"),n(),t(1992,`
              `),e(1993,"td"),t(1994,"Otto"),n(),t(1995,`
              `),e(1996,"td"),t(1997,"@mdo"),n(),t(1998,`
            `),n(),t(1999,`
            `),e(2e3,"tr"),t(2001,`
              `),e(2002,"th",6),t(2003,"2"),n(),t(2004,`
              `),e(2005,"td"),t(2006,"Jacob"),n(),t(2007,`
              `),e(2008,"td"),t(2009,"Thornton"),n(),t(2010,`
              `),e(2011,"td"),t(2012,"@fat"),n(),t(2013,`
            `),n(),t(2014,`
            `),e(2015,"tr"),t(2016,`
              `),e(2017,"th",6),t(2018,"3"),n(),t(2019,`
              `),e(2020,"td"),t(2021,"Larry"),n(),t(2022,`
              `),e(2023,"td"),t(2024,"the Bird"),n(),t(2025,`
              `),e(2026,"td"),t(2027,"@twitter"),n(),t(2028,`
            `),n(),t(2029,`
            `),n(),t(2030,`
          `),n(),t(2031,`
        `),n(),t(2032,`
        `),e(2033,"p",2),t(2034,`
          You can also put the `),e(2035,"code"),t(2036,"<caption>"),n(),t(2037,` on the top of the table with
          `),e(2038,"code"),t(2039,'caption="top"'),n(),t(2040,`.
        `),n(),t(2041,`
        `),e(2042,"app-docs-example",49),t(2043,`
          `),e(2044,"table",50),t(2045,`
            `),e(2046,"caption"),t(2047,"List of users"),n(),t(2048,`
            `),e(2049,"thead"),t(2050,`
            `),e(2051,"tr"),t(2052,`
              `),e(2053,"th",5),t(2054,"#"),n(),t(2055,`
              `),e(2056,"th",5),t(2057,"Class"),n(),t(2058,`
              `),e(2059,"th",5),t(2060,"Heading"),n(),t(2061,`
              `),e(2062,"th",5),t(2063,"Heading"),n(),t(2064,`
            `),n(),t(2065,`
            `),n(),t(2066,`
            `),e(2067,"tbody"),t(2068,`
            `),e(2069,"tr"),t(2070,`
              `),e(2071,"th",6),t(2072,"1"),n(),t(2073,`
              `),e(2074,"td"),t(2075,"Mark"),n(),t(2076,`
              `),e(2077,"td"),t(2078,"Otto"),n(),t(2079,`
              `),e(2080,"td"),t(2081,"@mdo"),n(),t(2082,`
            `),n(),t(2083,`
            `),e(2084,"tr"),t(2085,`
              `),e(2086,"th",6),t(2087,"2"),n(),t(2088,`
              `),e(2089,"td"),t(2090,"Jacob"),n(),t(2091,`
              `),e(2092,"td"),t(2093,"Thornton"),n(),t(2094,`
              `),e(2095,"td"),t(2096,"@fat"),n(),t(2097,`
            `),n(),t(2098,`
            `),e(2099,"tr"),t(2100,`
              `),e(2101,"th",6),t(2102,"3"),n(),t(2103,`
              `),e(2104,"td"),t(2105,"Larry"),n(),t(2106,`
              `),e(2107,"td"),t(2108,"the Bird"),n(),t(2109,`
              `),e(2110,"td"),t(2111,"@twitter"),n(),t(2112,`
            `),n(),t(2113,`
            `),n(),t(2114,`
          `),n(),t(2115,`
        `),n(),t(2116,`
      `),n(),t(2117,`
    `),n(),t(2118,`
  `),n(),t(2119,`
`),n()),x&2&&(i(274),l("striped",!0),i(72),l("striped",!0),i(69),l("striped",!0),i(189),l("hover",!0),i(69),l("hover",!0),i(69),l("hover",!0)("striped",!0),i(249),l("bordered",!0),i(75),l("bordered",!0)("cBorder",1),i(93),l("borderless",!0),i(168),l("small",!0),i(99),l("responsive",!0),i(117),l("striped",!0))},dependencies:[h,c,S,o,E,g,p,s,b,r,d],encapsulation:2});var y=m;export{y as TablesComponent};
