import{c as E}from"./chunk-3MARWV4R.js";import{Ra as A,l as _,s as k}from"./chunk-ZWMDU7YR.js";import{E as $,F as D,I as F,e as f,f as y,oa as O,qa as T}from"./chunk-Y7QKHPW3.js";import{v as w}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Eb as g,Fb as I,Gb as l,Hb as o,Ib as s,Jb as p,Sb as b,Va as n,ba as m,eb as h,fc as d,gc as x,hc as v,ja as u,ka as C,sc as S}from"./chunk-J5YWIVYY.js";import{a as c}from"./chunk-L3UST63Y.js";var L=(a,e)=>e[0];function M(a,e){if(a&1&&(o(0,"c-col",2),u(),p(1,"svg",3),C(),o(2,"div"),d(3),s()()),a&2){let t=e.$implicit,r=b();l("md",3)("sm",4)("xl",2)("xs",6),n(),l("name",t[0])("title",t[0]),n(2),x(r.toKebabCase(t[0]))}}var i=class i{constructor(){this.route=m(w);this.iconSet=m(f);this.title="CoreUI Icons";let e=this.iconSet;e.icons=c(c(c({},A),_),k)}ngOnInit(){let e=this.route?.routeConfig?.path,t="cil";e==="coreui-icons"?(this.title=`${this.title} - Free`,t="cil"):e==="brands"?(this.title=`${this.title} - Brands`,t="cib"):e==="flags"&&(this.title=`${this.title} - Flags`,t="cif"),this.icons=this.getIconsView(t)}toKebabCase(e){return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()}getIconsView(e){return Object.entries(this.iconSet.icons).filter(t=>t[0].startsWith(e))}};i.\u0275fac=function(t){return new(t||i)},i.\u0275cmp=h({type:i,selectors:[["ng-component"]],features:[S([f])],decls:8,vars:1,consts:[["href","https://github.com/coreui/coreui-icons","text","GitHub"],[1,"text-center"],[1,"mb-5",3,"md","sm","xl","xs"],["cIcon","","size","3xl",3,"name","title"]],template:function(t,r){t&1&&(o(0,"c-card")(1,"c-card-header"),d(2),p(3,"app-docs-link",0),s(),o(4,"c-card-body")(5,"c-row",1),g(6,M,4,7,"c-col",2,L),s()()()),t&2&&(n(2),v(" ",r.title," "),n(4),I(r.icons))},dependencies:[$,F,D,O,E,y,T],encapsulation:2});var H=i;export{H as CoreUIIconsComponent};
