{"_from": "@apidevtools/swagger-methods@^3.0.2", "_id": "@apidevtools/swagger-methods@3.0.2", "_inBundle": false, "_integrity": "sha512-QAkD5kK2b1WfjDS/UQn/qQkbwF31uqRjPTrsCs5ZG9BQGAkjwvqGFjjPqAuzac/IYzpPtRzjCP1WrTuAIjMrXg==", "_location": "/@apidevtools/swagger-methods", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@apidevtools/swagger-methods@^3.0.2", "name": "@apidevtools/swagger-methods", "escapedName": "@apidevtools%2fswagger-methods", "scope": "@apidevtools", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/@apidevtools/swagger-parser"], "_resolved": "https://registry.npmjs.org/@apidevtools/swagger-methods/-/swagger-methods-3.0.2.tgz", "_shasum": "b789a362e055b0340d04712eafe7027ddc1ac267", "_spec": "@apidevtools/swagger-methods@^3.0.2", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\@apidevtools\\swagger-parser", "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "bugs": {"url": "https://github.com/APIDevTools/swagger-methods/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "HTTP methods that are supported by Swagger 2.0", "devDependencies": {"@jsdevtools/eslint-config": "^1.0.4", "@jsdevtools/version-bump-prompt": "^6.0.6", "chai": "^4.2.0", "eslint": "^7.5.0", "methods": "^1.1.2", "mocha": "^8.0.1", "npm-check": "^5.9.0", "nyc": "^15.1.0", "shx": "^0.3.2", "swagger-schema-official": "2.0.0-bab6bed"}, "files": ["lib"], "homepage": "https://github.com/APIDevTools/swagger-methods", "keywords": ["swagger", "http", "methods"], "license": "MIT", "main": "lib/index.js", "name": "@apidevtools/swagger-methods", "repository": {"type": "git", "url": "git+https://github.com/APIDevTools/swagger-methods.git"}, "scripts": {"bump": "bump --tag --push --all", "clean": "shx rm -rf .nyc_output coverage", "coverage": "nyc node_modules/mocha/bin/mocha", "lint": "eslint lib test", "release": "npm run upgrade && npm run clean && npm test && npm run bump", "test": "mocha && npm run lint", "upgrade": "npm-check -u && npm audit fix"}, "version": "3.0.2"}