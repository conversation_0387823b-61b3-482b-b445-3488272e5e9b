{"version": 3, "file": "keyVaultFactory.js", "sourceRoot": "", "sources": ["../../../lib/credentials/keyVaultFactory.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;AAE/F,+EAA4E;AAC5E,qGAAkG;AAClG,qEAAkE;AAClE,mFAAgF;AAChF,+DAA4D;AAC5D,mEAAgE;AAChE,iEAA8D;AAC9D,iEAA8D;AAC9D,yCAAgF;AAGhF,SAAgB,mBAAmB,CAAC,WAAgC;IAClE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnD,MAAM,aAAa,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,CAAC;IAEvE,OAAO,aAAa,CAAC;AACvB,CAAC;AALD,kDAKC;AAED,SAAS,QAAQ,CAAC,WAAgC;IAChD,IAAI,WAAW,YAAY,6DAA6B,EAAE;QACxD,OAAO,IAAI,6DAA6B,CAAC;YACvC,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,SAAS,EAAE,WAAW,CAAC,SAAS;YAChC,aAAa,EAAE,WAAW,CAAC,aAAa;YACxC,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC,CAAC;KACJ;SAAM,IAAI,WAAW,YAAY,6CAAqB,EAAE;QACvD,OAAO,IAAI,6CAAqB,CAAC;YAC/B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,WAAW,EAAE,WAAW,CAAC,WAAW;SACrC,CAAC,CAAC;KACJ;SAAM,IAAI,WAAW,YAAY,yCAAmB,EAAE;QACrD,MAAM,IAAI,KAAK,CACb,kFAAkF,CACnF,CAAC;KACH;SAAM;QACL,OAAO,WAAW,CAAC;KACpB;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,WAAgC;IAClE,OAAO,CAAC,SAAc,EAAE,EAAE,CACxB,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9B,mEAAmE;QACnE,MAAM,uBAAuB,GAAG,CAC9B,GAAU,EACV,aAA4C,EAC5C,EAAE;YACF,IAAI,GAAG,EAAE;gBACP,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;aACpB;YAED,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvB,OAAO,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aACpC;YAED,aAAa,GAAG,aAA8B,CAAC;YAC/C,2FAA2F;YAC3F,MAAM,kBAAkB,GAAG,aAAa,CAAC,SAAS,GAAG,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC;YACrF,OAAO,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACrC,CAAC,CAAC;QAEF,uCAAuC;QACvC,IAAI,WAAW,YAAY,2CAAoB,EAAE;YAC/C,MAAM,OAAO,GAAG,IAAI,iCAAqB,CACvC,SAAS,CAAC,aAAa,EACvB,IAAI,EACJ,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC,KAAK,CACzD,CAAC;YACF,IAAI,WAAW,YAAY,yDAA2B,EAAE;gBACtD,OAAO,OAAO,CAAC,iCAAiC,CAC9C,SAAS,CAAC,QAAQ,EAClB,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,MAAM,EAClB,uBAAuB,CACxB,CAAC;aACH;iBAAM,IAAI,WAAW,YAAY,+EAAsC,EAAE;gBACxE,OAAO,OAAO,CAAC,iCAAiC,CAC9C,SAAS,CAAC,QAAQ,EAClB,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,WAAW,EACvB,WAAW,CAAC,UAAU,EACtB,uBAAuB,CACxB,CAAC;aACH;iBAAM,IAAI,WAAW,YAAY,2CAAoB,EAAE;gBACtD,OAAO,OAAO,CAAC,gCAAgC,CAC7C,SAAS,CAAC,QAAQ,EAClB,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,QAAQ,EACpB,uBAAuB,CACxB,CAAC;aACH;iBAAM,IAAI,WAAW,YAAY,+CAAsB,EAAE;gBACxD,OAAO,OAAO,CAAC,YAAY,CACzB,SAAS,CAAC,QAAQ,EAClB,WAAW,CAAC,QAAQ,EACpB,WAAW,CAAC,QAAQ,EACpB,uBAAuB,CACxB,CAAC;aACH;SACF;aAAM,IAAI,WAAW,YAAY,yCAAmB,EAAE;YACrD,OAAO,WAAW,CAAC,QAAQ,EAAE,CAAC;SAC/B;aAAM;YACL,OAAO,MAAM,CACX,IAAI,KAAK,CACP,iFAAiF;gBAC/E,6CAA6C,CAChD,CACF,CAAC;SACH;IACH,CAAC,CAAC,CAAC;AACP,CAAC"}