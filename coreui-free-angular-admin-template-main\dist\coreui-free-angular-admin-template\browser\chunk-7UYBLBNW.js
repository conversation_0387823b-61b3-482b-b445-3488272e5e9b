import{a as D}from"./chunk-5AY4JT2K.js";import{a as h}from"./chunk-C7XZ7IYY.js";import{V as z,X as K,Y as Q,Z as $,_ as q,f as k,g as j,n as P,oa as T,pb as U,qa as E,sb as X}from"./chunk-Y7QKHPW3.js";import{y as N}from"./chunk-VLR5A2CC.js";import{$b as R,Ab as A,Bb as F,Db as M,Eb as B,Fb as L,Gb as r,Hb as i,Hc as G,Ib as a,Ic as H,Jb as p,Nc as _,Sb as f,Va as l,_b as W,ac as V,ba as S,dc as O,eb as w,fc as t,hc as J,ja as g,ka as C,kb as x,tc as d}from"./chunk-J5YWIVYY.js";import{a as s,b as u}from"./chunk-L3UST63Y.js";function oe(n,o){if(n&1&&(i(0,"c-chart",5,0),t(2),a()),n&2){let e=V(1),m=f().$implicit,I=f();r("data",m.data)("options",I.chartOptions),l(2),J(" ",e.id," ")}}function re(n,o){if(n&1&&(i(0,"c-col",2)(1,"c-widget-stat-d",3),g(),p(2,"svg",4),A(3,oe,3,3,"c-chart",5),a()()),n&2){let e=o.$implicit,m=f();l(),O(e.capBg??null),r("color",e.color??"")("values",e.values),l(),r("name",e.icon),l(),F(m.withCharts()?3:-1)}}var b=class b{constructor(){this.changeDetectorRef=S(_);this.withCharts=G();this.chartOptions={elements:{line:{tension:.4},point:{radius:0,hitRadius:10,hoverRadius:4,hoverBorderWidth:3}},maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{x:{display:!1},y:{display:!1}}};this.labels=["January","February","March","April","May","June","July"];this.datasets={borderWidth:2,fill:!0};this.colors={backgroundColor:"rgba(255,255,255,.1)",borderColor:"rgba(255,255,255,.55)",pointHoverBackgroundColor:"#fff",pointBackgroundColor:"rgba(255,255,255,.55)"};this.brandData=[{icon:"cibFacebook",values:[{title:"friends",value:"89K"},{title:"feeds",value:"459"}],capBg:{"--cui-card-cap-bg":"#3b5998"},labels:[...this.labels],data:{labels:[...this.labels],datasets:[s(u(s({},this.datasets),{data:[65,59,84,84,51,55,40],label:"Facebook"}),this.colors)]}},{icon:"cibTwitter",values:[{title:"followers",value:"973k"},{title:"tweets",value:"1.792"}],capBg:{"--cui-card-cap-bg":"#00aced"},data:{labels:[...this.labels],datasets:[s(u(s({},this.datasets),{data:[1,13,9,17,34,41,38],label:"Twitter"}),this.colors)]}},{icon:"cib-linkedin",values:[{title:"contacts",value:"500"},{title:"feeds",value:"1.292"}],capBg:{"--cui-card-cap-bg":"#4875b4"},data:{labels:[...this.labels],datasets:[s(u(s({},this.datasets),{data:[78,81,80,45,34,12,40],label:"LinkedIn"}),this.colors)]}},{icon:"cilCalendar",values:[{title:"events",value:"12+"},{title:"meetings",value:"4"}],capBg:{"--cui-card-cap-bg":"var(--cui-warning)"},data:{labels:[...this.labels],datasets:[s(u(s({},this.datasets),{data:[35,23,56,22,97,23,64],label:"Events"}),this.colors)]}}]}capStyle(o){return o?{"--cui-card-cap-bg":o}:{}}ngAfterContentInit(){this.changeDetectorRef.detectChanges()}};b.\u0275fac=function(e){return new(e||b)},b.\u0275cmp=w({type:b,selectors:[["app-widgets-brand"]],inputs:{withCharts:[1,"withCharts"]},decls:3,vars:0,consts:[["chart","cChart"],[1,"g-4"],["sm","6","xl","3"],[3,"color","values"],["cIcon","","height","52",1,"my-4","text-white",3,"name"],["type","line",1,"position-absolute","w-100","h-100",3,"data","options"]],template:function(e,m){e&1&&(i(0,"c-row",1),B(1,re,4,6,"c-col",2,M),a()),e&2&&(l(),L(m.brandData))},dependencies:[E,T,X,k,D],encapsulation:2});var Y=b;var c=()=>[];function le(n,o){n&1&&(t(0,`
        26K
        `),i(1,"span",9),t(2,`
          (-12.4% `),g(),p(3,"svg",10),t(4,`)
        `),a(),t(5,`
      `))}function se(n,o){n&1&&(i(0,"c-dropdown",11)(1,"button",12),g(),p(2,"svg",13),a(),C(),i(3,"ul",14)(4,"li")(5,"a",15),t(6,"Action"),a()(),i(7,"li")(8,"a",15),t(9,"Another action"),a()(),i(10,"li")(11,"a",15),t(12,"Something else here"),a()(),i(13,"li"),p(14,"hr",16),a(),i(15,"li")(16,"button",17),t(17,"Separated link"),a()()()()),n&2&&(l(),r("caret",!1),l(4),r("routerLink",d(4,c)),l(3),r("routerLink",d(5,c)),l(3),r("routerLink",d(6,c)))}function pe(n,o){if(n&1&&p(0,"c-chart",18),n&2){let e=f();r("data",e.data[0])("options",e.options[0])("type","line")}}function de(n,o){n&1&&(t(0,`
        `),i(1,"span"),t(2,"$6.200"),a(),t(3,`
        `),i(4,"span",9),t(5,`
          (40.9%`),g(),p(6,"svg",19),t(7,`)
        `),a(),t(8,`
      `))}function ce(n,o){n&1&&(i(0,"c-dropdown",11)(1,"button",12),g(),p(2,"svg",13),a(),C(),i(3,"ul",14)(4,"li")(5,"a",15),t(6,"Action"),a()(),i(7,"li")(8,"a",15),t(9,"Another action"),a()(),i(10,"li")(11,"a",15),t(12,"Something else here"),a()(),i(13,"li")(14,"a",20),t(15,"Disabled"),a()()()()),n&2&&(l(),r("caret",!1),l(4),r("routerLink",d(6,c)),l(3),r("routerLink",d(7,c)),l(3),r("routerLink",d(8,c)),l(3),r("disabled",!0)("routerLink",d(9,c)))}function me(n,o){if(n&1&&p(0,"c-chart",21),n&2){let e=f();r("data",e.data[1])("options",e.options[1])}}function ue(n,o){n&1&&(t(0,`
        `),i(1,"span"),t(2,"2.49"),a(),t(3,`
        `),i(4,"span",9),t(5,`
          (84.7% `),g(),p(6,"svg",19),t(7,`)
        `),a(),t(8,`
      `))}function ge(n,o){n&1&&(i(0,"c-dropdown",11)(1,"button",12),g(),p(2,"svg",13),a(),C(),i(3,"ul",14)(4,"li")(5,"a",15),t(6,"Action"),a()(),i(7,"li")(8,"a",15),t(9,"Another action"),a()(),i(10,"li")(11,"a",15),t(12,"Something else here"),a()(),i(13,"li")(14,"a",20),t(15,"Disabled"),a()()()()),n&2&&(l(),r("caret",!1),l(4),r("routerLink",d(6,c)),l(3),r("routerLink",d(7,c)),l(3),r("routerLink",d(8,c)),l(3),r("disabled",!0)("routerLink",d(9,c)))}function fe(n,o){if(n&1&&p(0,"c-chart",22),n&2){let e=f();r("data",e.data[2])("options",e.options[2])}}function he(n,o){n&1&&(t(0,`
        `),i(1,"span"),t(2,"44K"),a(),t(3,`
        `),i(4,"span",9),t(5,`
          (-23.6% `),g(),p(6,"svg",10),t(7,`)
        `),a(),t(8,`
      `))}function be(n,o){n&1&&(i(0,"c-dropdown",11)(1,"button",12),g(),p(2,"svg",13),a(),C(),i(3,"ul",14)(4,"li")(5,"a",15),t(6,"Action"),a()(),i(7,"li")(8,"a",15),t(9,"Another action"),a()(),i(10,"li")(11,"a",15),t(12,"Something else here"),a()(),i(13,"li")(14,"a",20),t(15,"Disabled"),a()()()()),n&2&&(l(),r("caret",!1),l(4),r("routerLink",d(6,c)),l(3),r("routerLink",d(7,c)),l(3),r("routerLink",d(8,c)),l(3),r("disabled",!0)("routerLink",d(9,c)))}function ye(n,o){if(n&1&&p(0,"c-chart",23),n&2){let e=f();r("data",e.data[3])("options",e.options[3])}}var ve=["chart"],y=class y{constructor(){this.changeDetectorRef=S(_);this.data=[];this.options=[];this.labels=["January","February","March","April","May","June","July","August","September","October","November","December","January","February","March","April"];this.datasets=[[{label:"My First dataset",backgroundColor:"transparent",borderColor:"rgba(255,255,255,.55)",pointBackgroundColor:h("--cui-primary"),pointHoverBorderColor:h("--cui-primary"),data:[65,59,84,84,51,55,40]}],[{label:"My Second dataset",backgroundColor:"transparent",borderColor:"rgba(255,255,255,.55)",pointBackgroundColor:h("--cui-info"),pointHoverBorderColor:h("--cui-info"),data:[1,18,9,17,34,22,11]}],[{label:"My Third dataset",backgroundColor:"rgba(255,255,255,.2)",borderColor:"rgba(255,255,255,.55)",pointBackgroundColor:h("--cui-warning"),pointHoverBorderColor:h("--cui-warning"),data:[78,81,80,45,34,12,40],fill:!0}],[{label:"My Fourth dataset",backgroundColor:"rgba(255,255,255,.2)",borderColor:"rgba(255,255,255,.55)",data:[78,81,80,45,34,12,40,85,65,23,12,98,34,84,67,82],barPercentage:.7}]];this.optionsDefault={plugins:{legend:{display:!1}},maintainAspectRatio:!1,scales:{x:{border:{display:!1},grid:{display:!1,drawBorder:!1},ticks:{display:!1}},y:{min:30,max:89,display:!1,grid:{display:!1},ticks:{display:!1}}},elements:{line:{borderWidth:1,tension:.4},point:{radius:4,hitRadius:10,hoverRadius:4}}}}ngOnInit(){this.setData()}ngAfterContentInit(){this.changeDetectorRef.detectChanges()}setData(){for(let o=0;o<4;o++)this.data[o]={labels:o<3?this.labels.slice(0,7):this.labels,datasets:this.datasets[o]};this.setOptions()}setOptions(){for(let o=0;o<4;o++){let e=JSON.parse(JSON.stringify(this.optionsDefault));switch(o){case 0:{this.options.push(e);break}case 1:{e.scales.y.min=-9,e.scales.y.max=39,e.elements.line.tension=0,this.options.push(e);break}case 2:{e.scales.x={display:!1},e.scales.y={display:!1},e.elements.line.borderWidth=2,e.elements.point.radius=0,this.options.push(e);break}case 3:{e.scales.x.grid={display:!1,drawTicks:!1},e.scales.x.grid={display:!1,drawTicks:!1,drawBorder:!1},e.scales.y.min=void 0,e.scales.y.max=void 0,e.elements={},this.options.push(e);break}}}}};y.\u0275fac=function(e){return new(e||y)},y.\u0275cmp=w({type:y,selectors:[["app-widgets-dropdown"]],decls:21,vars:4,consts:[[1,"g-4"],["sm","6","xl","3"],["color","primary",3,"title"],["cTemplateId","widgetValueTemplate"],["cTemplateId","widgetActionTemplate"],["cTemplateId","widgetChartTemplate"],["color","info",3,"title"],["color","warning",3,"title"],["color","danger",3,"title"],[1,"fw-normal",2,"font-size","12px"],["cIcon","","name","cilArrowBottom","size","sm"],["alignment","end","variant","btn-group"],["cButton","","cDropdownToggle","","color","transparent","aria-label","Open dropdown",1,"p-0","text-white",3,"caret"],["cIcon","","name","cilOptions"],["cDropdownMenu",""],["cDropdownItem","",3,"routerLink"],["cDropdownDivider",""],["cDropdownItem",""],["height","70",1,"mt-3","mx-3",3,"data","options","type"],["cIcon","","name","cilArrowTop","size","sm"],["cDropdownItem","",3,"disabled","routerLink"],["height","70","type","line",1,"mt-3","mx-3",3,"data","options"],["height","70","type","line",1,"mt-3",3,"data","options"],["height","70","type","bar",1,"mt-3","mx-3",3,"data","options"]],template:function(e,m){e&1&&(i(0,"c-row",0)(1,"c-col",1)(2,"c-widget-stat-a",2),x(3,le,6,0,"ng-template",3)(4,se,18,7,"ng-template",4)(5,pe,1,3,"ng-template",5),a()(),i(6,"c-col",1)(7,"c-widget-stat-a",6),x(8,de,9,0,"ng-template",3)(9,ce,16,10,"ng-template",4)(10,me,1,2,"ng-template",5),a()(),i(11,"c-col",1)(12,"c-widget-stat-a",7),x(13,ue,9,0,"ng-template",3)(14,ge,16,10,"ng-template",4)(15,fe,1,2,"ng-template",5),a()(),i(16,"c-col",1)(17,"c-widget-stat-a",8),x(18,he,9,0,"ng-template",3)(19,be,16,10,"ng-template",4)(20,ye,1,2,"ng-template",5),a()()()),e&2&&(l(2),r("title","Users"),l(5),r("title","Income"),l(5),r("title","Conversion Rate"),l(5),r("title","Sessions"))},dependencies:[E,T,U,j,k,$,P,Q,K,q,N,z,D],encapsulation:2});var Z=y,v=class v{constructor(){this.chartComponent=H.required("chart");this.colors={label:"My dataset",backgroundColor:"rgba(77,189,116,.2)",borderColor:"#4dbd74",pointHoverBackgroundColor:"#fff"};this.labels=["Mo","Tu","We","Th","Fr","Sa","Su"];this.data={labels:this.labels,datasets:[u(s({data:[65,59,84,84,51,55,40]},this.colors),{fill:{value:65}})]};this.options={maintainAspectRatio:!1,plugins:{legend:{display:!1}},elements:{line:{tension:.4}}}}ngAfterViewInit(){setTimeout(()=>{let o=()=>u(s({},this.data),{labels:["Jan","Feb","Mar","Apr","May"],datasets:[u(s({},this.data.datasets[0]),{data:[42,88,42,66,77],fill:{value:55}}),u(s({},this.data.datasets[0]),{borderColor:"#ffbd47",data:[88,42,66,77,42]})]}),e=["Jan","Feb","Mar","Apr","May"],m=[42,88,42,66,77],{datasets:I,labels:Ce}=s({},this.data),te=this.chartComponent()?.chart?.data.datasets.length;console.log("before",te),this.data=u(s({},this.data),{datasets:[u(s({},this.data.datasets[0]),{data:m}),u(s({},this.data.datasets[0]),{borderColor:"#ffbd47",data:[88,42,66,77,42]})],labels:e}),setTimeout(()=>{let ne=this.chartComponent()?.chart?.data.datasets.length;console.log("after",ne)})},5e3)}};v.\u0275fac=function(e){return new(e||v)},v.\u0275cmp=w({type:v,selectors:[["app-chart-sample"]],viewQuery:function(e,m){e&1&&W(m.chartComponent,ve,5),e&2&&R()},decls:2,vars:2,consts:[["chart",""],["type","line","width","300",3,"data","options"]],template:function(e,m){e&1&&p(0,"c-chart",1,0),e&2&&r("data",m.data)("options",m.options)},dependencies:[D],encapsulation:2});var ee=v;export{Y as a,Z as b};
