{"version": 3, "file": "subscriptionUtils.js", "sourceRoot": "", "sources": ["../../../lib/subscriptionManagement/subscriptionUtils.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,4CAA4C;AAE5C,oGAAiG;AACjG,yDAAsD;AAkEtD;;;;;GAKG;AACH,SAAsB,eAAe,CACnC,WAAiC,EACjC,UAAU,GAAG,YAAY;;QAEzB,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,6BAAa,CAAC,iBAAiB,EAAE;YAChF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC7B;QAED,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACrD,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,0BAA0B,CAAC;QACnE,MAAM,MAAM,GAAG,GAAG,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,uBAAuB,UAAU,EAAE,CAAC;QAChG,MAAM,GAAG,GAAiC;YACxC,GAAG,EAAE,MAAM;YACX,MAAM,EAAE,KAAK;SACd,CAAC;QACF,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;YACpB,MAAM,OAAO,GAAQ,GAAG,CAAC,UAAU,CAAC;YACpC,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;gBAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aAC9B;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CAAA;AA1BD,0CA0BC;AAED,SAAsB,2BAA2B,CAC/C,WAAiC,EACjC,UAAoB,EACpB,UAAU,GAAG,YAAY;;QAEzB,IAAI,aAAa,GAAyB,EAAE,CAAC;QAC7C,IAAI,QAAQ,GAAG,MAAM,CAAC;QACtB,IAAI,QAAgB,CAAC;QACrB,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC;QAC1C,IAAI,WAAW,YAAY,iEAA+B,EAAE;YAC1D,QAAQ,GAAG,kBAAkB,CAAC;YAC9B,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC;SACjC;aAAM;YACL,QAAQ,GAAS,WAAY,CAAC,QAAQ,CAAC;SACxC;QACD,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE;YAC/B,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;YAC5B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,0BAA0B,CAAC;YACnE,MAAM,MAAM,GAAG,GAAG,OAAO,GACvB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAC/B,6BAA6B,UAAU,EAAE,CAAC;YAC1C,MAAM,GAAG,GAAiC;gBACxC,GAAG,EAAE,MAAM;gBACX,MAAM,EAAE,KAAK;aACd,CAAC;YAEF,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1C,MAAM,gBAAgB,GAAgB,GAAG,CAAC,UAAW,CAAC,KAAK,CAAC;YAC5D,aAAa,GAAG,aAAa,CAAC,MAAM,CAClC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;gBAC9B,CAAC,CAAC,QAAQ,GAAG,MAAM,CAAC;gBACpB,CAAC,CAAC,IAAI,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;gBAC5C,CAAC,CAAC,eAAe,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;gBACjD,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC;gBACvB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,cAAc,CAAC;gBACxB,OAAO,CAAC,CAAC,WAAW,CAAC;gBACrB,OAAO,CAAC,CAAC,cAAc,CAAC;gBACxB,OAAO,CAAC,CAAC,oBAAoB,CAAC;gBAC9B,OAAO,CAAC,CAAC;YACX,CAAC,CAAC,CACH,CAAC;SACH;QACD,6BAA6B;QAC7B,WAAW,CAAC,MAAM,GAAG,cAAc,CAAC;QACpC,OAAO,aAAa,CAAC;IACvB,CAAC;CAAA;AA9CD,kEA8CC"}