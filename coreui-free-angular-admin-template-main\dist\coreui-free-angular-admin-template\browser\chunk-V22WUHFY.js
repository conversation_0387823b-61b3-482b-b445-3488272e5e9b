import{b as I}from"./chunk-3MARWV4R.js";import{r as A,s as G,w as T}from"./chunk-LKW33VZJ.js";import{E as s,F as x,I as S,V as E,X as g,Y as b,Z as h,_ as y,ea as F,fa as v,ia as w,ja as f,ka as C,la as k,n as c,oa as L,qa as D}from"./chunk-Y7QKHPW3.js";import{y as u}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as o,Hb as n,Ib as e,Jb as i,Va as r,eb as d,fc as t,tc as l}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var a=()=>[],p=class p{constructor(){}};p.\u0275fac=function(m){return new(m||p)},p.\u0275cmp=d({type:p,selectors:[["app-input-groups"]],decls:492,vars:68,consts:[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","forms/input-group"],[1,"mb-3"],["cInputGroupText","","id","basic-addon1"],["aria-describedby","basic-addon1","aria-label","Username","cFormControl","","placeholder","Username"],["aria-describedby","basic-addon2","aria-label","Recipient's username","cFormControl","","placeholder","Recipient's username"],["cInputGroupText","","id","basic-addon2"],["cLabel","","for","basic-url"],["cInputGroupText","","id","basic-addon3"],["aria-describedby","basic-addon3","cFormControl","","id","basic-url"],["cInputGroupText",""],["aria-label","Amount (to the nearest dollar)","cFormControl",""],["aria-label","Username","cFormControl","","placeholder","Username"],["aria-label","Server","cFormControl","","placeholder","Server"],["aria-label","With textarea","cFormControl",""],["href","forms/input-group#wrapping"],[1,"flex-nowrap"],["cInputGroupText","","id","addon-wrapping"],["aria-describedby","addon-wrapping","aria-label","Username","cFormControl","","placeholder","Username"],["href","forms/input-group#sizing"],["sizing","sm",1,"mb-3"],["cInputGroupText","","id","inputGroup-sizing-sm"],["aria-describedby","inputGroup-sizing-sm","aria-label","Sizing example input","cFormControl",""],["cInputGroupText","","id","inputGroup-sizing-default"],["aria-describedby","inputGroup-sizing-default","aria-label","Sizing example input","cFormControl",""],["sizing","lg"],["cInputGroupText","","id","inputGroup-sizing-lg"],["aria-describedby","inputGroup-sizing-lg","aria-label","Sizing example input","cFormControl",""],["href","forms/input-group#checkboxes-and-radios"],["cFormCheckInput","","id","checkOne","type","checkbox"],["aria-label","Text input with checkbox","cFormControl",""],["cFormCheckInput","","id","radioOne","type","radio"],["aria-label","Text input with radio button","cFormControl",""],["href","forms/input-group#multiple-inputs"],["aria-label","First name","cFormControl",""],["aria-label","Last name","cFormControl",""],["href","forms/input-group#multiple-addons"],["aria-label","Dollar amount (with dot and two decimal places)","cFormControl",""],["href","forms/input-group#button-addons"],["cButton","","color","secondary","id","button-addon1","type","button","variant","outline"],["aria-describedby","button-addon1","aria-label","Example text with button addon","cFormControl","","placeholder",""],["aria-describedby","button-addon2","aria-label","Recipient's username","cFormControl","","placeholder","Recipient's username"],["cButton","","color","secondary","id","button-addon2","type","button","variant","outline"],["cButton","","color","secondary","type","button","variant","outline"],["aria-label","Example text with two button addons","cFormControl","","placeholder",""],["aria-label","Recipient's username with two button addons","cFormControl","","placeholder","Recipient's username"],["href","forms/input-group#buttons-with-dropdowns"],["cButton","","cDropdownToggle","","color","secondary","variant","outline"],["cDropdownMenu",""],["cDropdownItem","",3,"routerLink"],["cDropdownDivider",""],["aria-label","Text input with dropdown button","cFormControl",""],["alignment","end"],["aria-label","Text input with 2 dropdown buttons","cFormControl",""],["href","forms/input-group#segmented-buttons"],["cButton","","cDropdownToggle","","color","secondary","variant","outline",3,"split"],[1,"visually-hidden"],["aria-label","Text input with segmented dropdown button","cFormControl",""],["cButton","","color","secondary","variant","outline"],["href","forms/input-group#custom-select"],["cInputGroupText","","for","inputGroupSelect01"],["cSelect","","id","inputGroupSelect01"],["value","1"],["value","2"],["value","3"],["cSelect","","id","inputGroupSelect02"],["cInputGroupText","","for","inputGroupSelect02"],["aria-label","Example select with button addon","cSelect","","id","inputGroupSelect03"],["aria-label","Example select with button addon","cSelect","","id","inputGroupSelect04"],["href","forms/input-group#custom-file-input"],["cInputGroupText","","for","inputGroupFile01"],["cFormControl","","id","inputGroupFile01","type","file"],["cFormControl","","id","inputGroupFile02","type","file"],["cInputGroupText","","for","inputGroupFile02"],["cButton","","color","secondary","id","inputGroupFileAddon03","type","button","variant","outline"],["aria-describedby","inputGroupFileAddon03","aria-label","Upload","cFormControl","","id","inputGroupFile03","type","file"],["aria-describedby","inputGroupFileAddon04","aria-label","Upload","cFormControl","","id","inputGroupFile04","type","file"],["cButton","","color","secondary","id","inputGroupFileAddon04","type","button","variant","outline"]],template:function(m,O){m&1&&(n(0,"c-row")(1,"c-col",0)(2,"c-card",1)(3,"c-card-header"),t(4,`
        `),n(5,"strong"),t(6,"Angular Input group"),e(),t(7," "),n(8,"small"),t(9,"Basic example"),e(),t(10,`
      `),e(),n(11,"c-card-body")(12,"p",2),t(13," Place one add-on or button on either side of an input. You may also place one on both sides of an input. Remember to place "),n(14,"code"),t(15,"<label>"),e(),t(16,"s outside the input group. "),e(),n(17,"app-docs-example",3)(18,"c-input-group",4)(19,"span",5),t(20,"@"),e(),i(21,"input",6),e(),n(22,"c-input-group",4),i(23,"input",7),n(24,"span",8),t(25,"@example.com"),e()(),n(26,"label",9),t(27,"Your vanity URL"),e(),n(28,"c-input-group",4)(29,"span",10),t(30,"https://example.com/users/"),e(),i(31,"input",11),e(),n(32,"c-input-group",4)(33,"span",12),t(34,"$"),e(),i(35,"input",13),n(36,"span",12),t(37,".00"),e()(),n(38,"c-input-group",4),i(39,"input",14),n(40,"span",12),t(41,"@"),e(),i(42,"input",15),e(),n(43,"c-input-group")(44,"span",12),t(45,"With textarea"),e(),i(46,"textarea",16),e()()()()(),n(47,"c-col",0)(48,"c-card",1)(49,"c-card-header"),t(50,`
        `),n(51,"strong"),t(52,"Angular Input group"),e(),t(53," "),n(54,"small"),t(55,"Wrapping"),e(),t(56,`
      `),e(),n(57,"c-card-body")(58,"p",2),t(59," Input groups wrap by default via "),n(60,"code"),t(61,"flex-wrap: wrap"),e(),t(62," in order to accommodate custom form field validation within an input group. You may disable this with "),n(63,"code"),t(64,".flex-nowrap"),e(),t(65,". "),e(),n(66,"app-docs-example",17)(67,"c-input-group",18)(68,"span",19),t(69,"@"),e(),i(70,"input",20),e()()()()(),n(71,"c-col",0)(72,"c-card",1)(73,"c-card-header"),t(74,`
        `),n(75,"strong"),t(76,"Angular Input group"),e(),t(77," "),n(78,"small"),t(79,"Sizing"),e(),t(80,`
      `),e(),n(81,"c-card-body")(82,"p",2),t(83," Add the relative form sizing classes to the "),n(84,"code"),t(85,"<c-input-group>"),e(),t(86," itself and contents within will automatically resize\u2014no need for repeating the form control size classes on each element. "),e(),n(87,"p",2)(88,"strong"),t(89,"Sizing on the individual input group elements isn't supported."),e()(),n(90,"app-docs-example",21)(91,"c-input-group",22)(92,"span",23),t(93,"Small"),e(),i(94,"input",24),e(),n(95,"c-input-group",4)(96,"span",25),t(97,"Default"),e(),i(98,"input",26),e(),n(99,"c-input-group",27)(100,"span",28),t(101,"Large"),e(),i(102,"input",29),e()()()()(),n(103,"c-col",0)(104,"c-card",1)(105,"c-card-header"),t(106,`
        `),n(107,"strong"),t(108,"Angular Input group"),e(),t(109," "),n(110,"small"),t(111,"Checkboxes and radios"),e(),t(112,`
      `),e(),n(113,"c-card-body")(114,"p",2),t(115," Place any checkbox or radio option within an input group's addon instead of text. "),e(),n(116,"app-docs-example",30)(117,"c-input-group",4)(118,"span",12)(119,"div"),i(120,"input",31),e()(),i(121,"input",32),e(),n(122,"c-input-group")(123,"span",12)(124,"div"),i(125,"input",33),e()(),i(126,"input",34),e()()()()(),n(127,"c-col",0)(128,"c-card",1)(129,"c-card-header"),t(130,`
        `),n(131,"strong"),t(132,"Angular Input group"),e(),t(133," "),n(134,"small"),t(135,"Multiple inputs"),e(),t(136,`
      `),e(),n(137,"c-card-body")(138,"p",2),t(139," While multiple "),n(140,"code"),t(141,"<CFormInput>"),e(),t(142,"s are supported visually, validation styles are only available for input groups with a single "),n(143,"code"),t(144,"cFormControl"),e(),t(145,". "),e(),n(146,"app-docs-example",35)(147,"c-input-group")(148,"span",12),t(149,"First and last name"),e(),i(150,"input",36)(151,"input",37),e()()()()(),n(152,"c-col",0)(153,"c-card",1)(154,"c-card-header"),t(155,`
        `),n(156,"strong"),t(157,"Angular Input group"),e(),t(158," "),n(159,"small"),t(160,"Multiple addons"),e(),t(161,`
      `),e(),n(162,"c-card-body")(163,"p",2),t(164," Multiple add-ons are supported and can be mixed with checkbox and radio input versions.. "),e(),n(165,"app-docs-example",38)(166,"c-input-group",4)(167,"span",12),t(168,"$"),e(),n(169,"span",12),t(170,"0.00"),e(),i(171,"input",39),e(),n(172,"c-input-group"),i(173,"input",39),n(174,"span",12),t(175,"$"),e(),n(176,"span",12),t(177,"0.00"),e()()()()()(),n(178,"c-col",0)(179,"c-card",1)(180,"c-card-header"),t(181,`
        `),n(182,"strong"),t(183,"Angular Input group"),e(),t(184," "),n(185,"small"),t(186,"Button addons"),e(),t(187,`
      `),e(),n(188,"c-card-body")(189,"p",2),t(190," Button add-ons are supported. "),e(),n(191,"app-docs-example",40)(192,"c-input-group",4)(193,"button",41),t(194," Button "),e(),i(195,"input",42),e(),n(196,"c-input-group",4),i(197,"input",43),n(198,"button",44),t(199," Button "),e()(),n(200,"c-input-group",4)(201,"button",45),t(202," Button "),e(),n(203,"button",45),t(204," Button "),e(),i(205,"input",46),e(),n(206,"c-input-group"),i(207,"input",47),n(208,"button",45),t(209," Button "),e(),n(210,"button",45),t(211," Button "),e()()()()()(),n(212,"c-col",0)(213,"c-card",1)(214,"c-card-header"),t(215,`
        `),n(216,"strong"),t(217,"Angular Input group"),e(),t(218," "),n(219,"small"),t(220,"Buttons with dropdowns"),e(),t(221,`
      `),e(),n(222,"c-card-body")(223,"app-docs-example",48)(224,"c-input-group",4)(225,"c-dropdown")(226,"button",49),t(227," Dropdown "),e(),n(228,"ul",50)(229,"li")(230,"a",51),t(231,"Action"),e()(),n(232,"li")(233,"a",51),t(234,"Another action"),e()(),n(235,"li")(236,"a",51),t(237,"Something else here"),e()(),n(238,"li"),i(239,"hr",52),e(),n(240,"li")(241,"a",51),t(242,"Separated link"),e()()()(),i(243,"input",53),e(),n(244,"c-input-group",4),i(245,"input",53),n(246,"c-dropdown",54)(247,"button",49),t(248," Dropdown "),e(),n(249,"ul",50)(250,"li")(251,"a",51),t(252,"Action"),e()(),n(253,"li")(254,"a",51),t(255,"Another action"),e()(),n(256,"li")(257,"a",51),t(258,"Something else here"),e()(),n(259,"li"),i(260,"hr",52),e(),n(261,"li")(262,"a",51),t(263,"Separated link"),e()()()()(),n(264,"c-input-group")(265,"c-dropdown")(266,"button",49),t(267," Dropdown "),e(),n(268,"ul",50)(269,"li")(270,"a",51),t(271,"Action"),e()(),n(272,"li")(273,"a",51),t(274,"Another action"),e()(),n(275,"li")(276,"a",51),t(277,"Something else here"),e()(),n(278,"li"),i(279,"hr",52),e(),n(280,"li")(281,"a",51),t(282,"Separated link"),e()()()(),i(283,"input",55),n(284,"c-dropdown",54)(285,"button",49),t(286," Dropdown "),e(),n(287,"ul",50)(288,"li")(289,"a",51),t(290,"Other Action"),e()(),n(291,"li")(292,"a",51),t(293,"Another action"),e()(),n(294,"li")(295,"a",51),t(296,"Something else here"),e()(),n(297,"li"),i(298,"hr",52),e(),n(299,"li")(300,"a",51),t(301,"Separated link"),e()()()()()()()()(),n(302,"c-col",0)(303,"c-card",1)(304,"c-card-header"),t(305,`
        `),n(306,"strong"),t(307,"Angular Input group"),e(),t(308," "),n(309,"small"),t(310,"Segmented buttons"),e(),t(311,`
      `),e(),n(312,"c-card-body")(313,"app-docs-example",56)(314,"c-input-group",4)(315,"c-dropdown")(316,"button",45),t(317," Action "),e(),n(318,"button",57)(319,"span",58),t(320,"Toggle Dropdown"),e()(),n(321,"ul",50)(322,"li")(323,"a",51),t(324,"Action"),e()(),n(325,"li")(326,"a",51),t(327,"Another action"),e()(),n(328,"li")(329,"a",51),t(330,"Something else here"),e()(),n(331,"li"),i(332,"hr",52),e(),n(333,"li")(334,"a",51),t(335,"Separated link"),e()()()(),i(336,"input",59),e(),n(337,"c-input-group",4),i(338,"input",59),n(339,"c-dropdown",54)(340,"button",60),t(341," Action "),e(),n(342,"button",57)(343,"span",58),t(344,"Toggle Dropdown"),e()(),n(345,"ul",50)(346,"li")(347,"a",51),t(348,"Action"),e()(),n(349,"li")(350,"a",51),t(351,"Another action"),e()(),n(352,"li")(353,"a",51),t(354,"Something else here"),e()(),n(355,"li"),i(356,"hr",52),e(),n(357,"li")(358,"a",51),t(359,"Separated link"),e()()()()(),n(360,"c-input-group")(361,"c-dropdown")(362,"button",45),t(363," Action "),e(),n(364,"button",57)(365,"span",58),t(366,"Toggle Dropdown"),e()(),n(367,"ul",50)(368,"li")(369,"a",51),t(370,"Action"),e()(),n(371,"li")(372,"a",51),t(373,"Another action"),e()(),n(374,"li")(375,"a",51),t(376,"Something else here"),e()(),n(377,"li"),i(378,"hr",52),e(),n(379,"li")(380,"a",51),t(381,"Separated link"),e()()()(),i(382,"input",59),n(383,"c-dropdown",54)(384,"button",60),t(385," Action "),e(),n(386,"button",57)(387,"span",58),t(388,"Toggle Dropdown"),e()(),n(389,"ul",50)(390,"li")(391,"a",51),t(392,"Action"),e()(),n(393,"li")(394,"a",51),t(395,"Another action"),e()(),n(396,"li")(397,"a",51),t(398,"Something else here"),e()(),n(399,"li"),i(400,"hr",52),e(),n(401,"li")(402,"a",51),t(403,"Separated link"),e()()()()()()()()(),n(404,"c-col",0)(405,"c-card",1)(406,"c-card-header"),t(407,`
        `),n(408,"strong"),t(409,"Angular Input group"),e(),t(410," "),n(411,"small"),t(412,"Custom select"),e(),t(413,`
      `),e(),n(414,"c-card-body")(415,"app-docs-example",61)(416,"c-input-group",4)(417,"label",62),t(418," Options "),e(),n(419,"select",63)(420,"option"),t(421,"Choose..."),e(),n(422,"option",64),t(423,"One"),e(),n(424,"option",65),t(425,"Two"),e(),n(426,"option",66),t(427,"Three"),e()()(),n(428,"c-input-group",4)(429,"select",67)(430,"option"),t(431,"Choose..."),e(),n(432,"option",64),t(433,"One"),e(),n(434,"option",65),t(435,"Two"),e(),n(436,"option",66),t(437,"Three"),e()(),n(438,"label",68),t(439," Options "),e()(),n(440,"c-input-group",4)(441,"button",45),t(442," Button "),e(),n(443,"select",69)(444,"option"),t(445,"Choose..."),e(),n(446,"option",64),t(447,"One"),e(),n(448,"option",65),t(449,"Two"),e(),n(450,"option",66),t(451,"Three"),e()()(),n(452,"c-input-group")(453,"select",70)(454,"option"),t(455,"Choose..."),e(),n(456,"option",64),t(457,"One"),e(),n(458,"option",65),t(459,"Two"),e(),n(460,"option",66),t(461,"Three"),e()(),n(462,"button",45),t(463," Button "),e()()()()()(),n(464,"c-col",0)(465,"c-card",1)(466,"c-card-header"),t(467,`
        `),n(468,"strong"),t(469,"Angular Input group"),e(),t(470," "),n(471,"small"),t(472,"Custom file input"),e(),t(473,`
      `),e(),n(474,"c-card-body")(475,"app-docs-example",71)(476,"c-input-group",4)(477,"label",72),t(478," Upload "),e(),i(479,"input",73),e(),n(480,"c-input-group",4),i(481,"input",74),n(482,"label",75),t(483," Upload "),e()(),n(484,"c-input-group",4)(485,"button",76),t(486," Button "),e(),i(487,"input",77),e(),n(488,"c-input-group"),i(489,"input",78),n(490,"button",79),t(491," Button "),e()()()()()()()),m&2&&(r(230),o("routerLink",l(36,a)),r(3),o("routerLink",l(37,a)),r(3),o("routerLink",l(38,a)),r(5),o("routerLink",l(39,a)),r(10),o("routerLink",l(40,a)),r(3),o("routerLink",l(41,a)),r(3),o("routerLink",l(42,a)),r(5),o("routerLink",l(43,a)),r(8),o("routerLink",l(44,a)),r(3),o("routerLink",l(45,a)),r(3),o("routerLink",l(46,a)),r(5),o("routerLink",l(47,a)),r(8),o("routerLink",l(48,a)),r(3),o("routerLink",l(49,a)),r(3),o("routerLink",l(50,a)),r(5),o("routerLink",l(51,a)),r(18),o("split",!0),r(5),o("routerLink",l(52,a)),r(3),o("routerLink",l(53,a)),r(3),o("routerLink",l(54,a)),r(5),o("routerLink",l(55,a)),r(8),o("split",!0),r(5),o("routerLink",l(56,a)),r(3),o("routerLink",l(57,a)),r(3),o("routerLink",l(58,a)),r(5),o("routerLink",l(59,a)),r(6),o("split",!0),r(5),o("routerLink",l(60,a)),r(3),o("routerLink",l(61,a)),r(3),o("routerLink",l(62,a)),r(5),o("routerLink",l(63,a)),r(6),o("split",!0),r(5),o("routerLink",l(64,a)),r(3),o("routerLink",l(65,a)),r(3),o("routerLink",l(66,a)),r(5),o("routerLink",l(67,a)))},dependencies:[D,L,s,S,x,I,C,k,F,w,v,c,h,b,g,y,u,E,f,T,A,G],encapsulation:2});var B=p;export{B as InputGroupsComponent};
