{"version": 3, "file": "azureCliCredentials.d.ts", "sourceRoot": "", "sources": ["../../../lib/credentials/azureCliCredentials.ts"], "names": [], "mappings": "AAGA,OAAO,EAAgC,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAC9E,OAAO,EAAE,sBAAsB,EAAE,aAAa,EAAE,MAAM,0BAA0B,CAAC;AACjF,OAAO,EAAE,kBAAkB,EAAE,MAAM,6CAA6C,CAAC;AAWjF;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,SAAS,EAAE,IAAI,CAAC;IAChB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;CACnB;AAED;;;GAGG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED,MAAM,WAAW,kBAAkB;IACjC;;OAEG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B;;;;;;;;;OASG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,qBAAa,mBAAoB,YAAW,sBAAsB;IAChE;;OAEG;IACH,gBAAgB,EAAE,kBAAkB,CAAC;IACrC;;OAEG;IACH,SAAS,EAAE,cAAc,CAAC;IAE1B;;;;;;;;;OASG;IAEH,QAAQ,EAAE,MAAM,CAAkC;IAElD;;;OAGG;IACH,OAAO,CAAC,QAAQ,CAAC,4BAA4B,CAAe;gBAG1D,gBAAgB,EAAE,kBAAkB,EACpC,SAAS,EAAE,cAAc,EAEzB,QAAQ,GAAE,MAAuC;IAOnD;;;;OAIG;IACU,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC;IAwB/C;;;OAGG;IACU,WAAW,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;IASxE,OAAO,CAAC,gBAAgB;IAcxB,OAAO,CAAC,uBAAuB;IAI/B,OAAO,CAAC,WAAW;IAoBnB,OAAO,CAAC,+BAA+B;IAWvC,OAAO,CAAC,mBAAmB;IAe3B;;;OAGG;WACU,cAAc,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,cAAc,CAAC;IAqBtF;;;;OAIG;WACU,eAAe,CAAC,oBAAoB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,kBAAkB,CAAC;IAuBxF;;;;OAIG;WACU,sBAAsB,CAAC,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAWhF;;;OAGG;WACU,oBAAoB,CAC/B,OAAO,GAAE,0BAA+B,GACvC,OAAO,CAAC,kBAAkB,EAAE,CAAC;IA4BhC;;;;;;;OAOG;WACU,MAAM,CAAC,OAAO,GAAE,kBAAuB,GAAG,OAAO,CAAC,mBAAmB,CAAC;CAOpF"}