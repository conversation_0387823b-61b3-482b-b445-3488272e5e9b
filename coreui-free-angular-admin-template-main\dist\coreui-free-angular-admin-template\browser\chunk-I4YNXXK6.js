import{r as Zr,s as Jr,v as en}from"./chunk-LKW33VZJ.js";import{a as C,b as qr,c as Kr}from"./chunk-EN2VGXKU.js";import{A as Lr,O as Dr,Ta as Nr,Ua as Wr,V as zr,Va as yt,W as xr,Wa as Yr,X as Ur,Xa as Gr,Y as kr,Ya as Qr,Z as Tr,Za as Xr,_ as Ar,aa as Br,b as he,c as bt,d as At,f as Ne,ma as ft,sa as Hr,ta as jr,u as Er,ua as $r,w as Ir,x as Rr,xa as Fr,ya as Vr}from"./chunk-Y7QKHPW3.js";import{b as wr,c as mt,e as _r,i as Ve,j as Mr,n as f,o as Z,p as y,w as Sr,x as ht,y as vt,z as Pr}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{$ as He,A as p,Ab as dr,Bb as ur,C as xt,Cc as yr,Da as lr,E as tr,Eb as gr,Fb as mr,G as pt,Gb as h,H as rr,Hb as s,Hc as Cr,I as nr,Ib as l,Jb as u,Kb as dt,Lb as ut,M as ir,Mb as kt,Nb as se,Nc as Or,Q as et,Qb as M,R as H,S as or,Sb as K,T as z,Tb as hr,Ua as cr,Ub as vr,Va as m,Vb as br,Wa as pr,Wb as G,Xb as V,Y as d,Yb as N,Za as je,_a as c,a as st,aa as g,ac as ge,ba as tt,c as Be,cc as Fe,eb as Y,ec as me,f as B,fc as b,g as lt,gb as $,ha as R,hc as gt,i as ct,ia as L,ib as q,j as Kt,ja as P,ka as D,kb as ie,l as Je,m as I,ma as W,mb as ue,nb as $e,p as T,r as Zt,ra as ar,rc as rt,sc as fr,tc as nt,uc as Q,va as sr,w as k,x as ae,y as Jt,ya as j,z as er,zb as Ut,zc as Tt}from"./chunk-J5YWIVYY.js";import{a as E,b as re,e as Xt,f as qt}from"./chunk-L3UST63Y.js";var We=class We extends Br{constructor(){super()}};We.\u0275fac=function(r){return new(r||We)},We.\u0275cmp=Y({type:We,selectors:[["app-default-footer"]],features:[q],decls:10,vars:0,consts:[["href","https://coreui.io/","target","_blank"],[1,"ms-auto"],["href","https://coreui.io/angular","target","_blank"]],template:function(r,t){r&1&&(s(0,"div")(1,"a",0),b(2,"CoreUI"),l(),s(3,"span"),b(4," \xA9 2025 creativeLabs"),l()(),s(5,"div",1),b(6," Powered by "),s(7,"a",2)(8,"span"),b(9," CoreUI for Angular"),l()()())},encapsulation:2});var Ct=We;var ve=class ve{constructor(){this.emitChangeSource=new B;this.changeEmitted$=this.emitChangeSource.asObservable();this.emitCustomizerSource=new B;this.customizerChangeEmitted$=this.emitCustomizerSource.asObservable();this.emitCustomizerCMSource=new B;this.customizerCMChangeEmitted$=this.emitCustomizerCMSource.asObservable();this.emitNotiSidebarSource=new B;this.notiSidebarChangeEmitted$=this.emitNotiSidebarSource.asObservable()}emitChange(e){this.emitChangeSource.next(e)}emitCustomizerChange(e){this.emitCustomizerSource.next(e)}emitCustomizerCMChange(e){this.emitCustomizerCMSource.next(e)}emitNotiSidebarChange(e){this.emitNotiSidebarSource.next(e)}destroy(){this.emitChangeSource.complete(),this.emitCustomizerSource.complete(),this.emitCustomizerCMSource.complete(),this.emitNotiSidebarSource.complete()}};ve.\u0275fac=function(r){return new(r||ve)},ve.\u0275prov=d({token:ve,factory:ve.\u0275fac,providedIn:"root"});var le=ve;var $n=()=>({placement:"bottom-start"}),Fn=(i,e)=>e.name;function Vn(i,e){i&1&&kt(0)}function Nn(i,e){i&1&&kt(0)}function Wn(i,e){if(i&1&&(s(0,"li")(1,"div",49),b(2),l()()),i&2){let r=K(2);m(2),gt(" ",r.getUserEmail()," ")}}function Yn(i,e){i&1&&(s(0,"span"),b(1,"Logout"),l())}function Gn(i,e){i&1&&(s(0,"span"),u(1,"span",50),b(2," Logging out... "),l())}function Qn(i,e){if(i&1){let r=se();s(0,"c-dropdown",22)(1,"button",23),u(2,"c-avatar",24),l(),s(3,"ul",25)(4,"li")(5,"h6",26),b(6),l()(),ie(7,Wn,3,1,"li",27),s(8,"li")(9,"a",28),P(),u(10,"svg",29),b(11," Updates "),D(),s(12,"c-badge",30),b(13," 42"),l()()(),s(14,"li")(15,"a",31),P(),u(16,"svg",32),b(17," Messages "),D(),s(18,"c-badge",33),b(19," 42 "),l()()(),s(20,"li")(21,"a",28),P(),u(22,"svg",34),b(23," Tasks "),D(),s(24,"c-badge",35),b(25," 42"),l()()(),s(26,"li")(27,"a",28),P(),u(28,"svg",36),b(29," Comments "),D(),s(30,"c-badge",37),b(31," 42"),l()()(),s(32,"li")(33,"h6",38),b(34," Settings "),l()(),u(35,"li"),s(36,"li")(37,"a",28),P(),u(38,"svg",39),b(39," Profile "),l()(),D(),s(40,"li")(41,"a",28),P(),u(42,"svg",40),b(43," Settings "),l()(),D(),s(44,"li")(45,"a",28),P(),u(46,"svg",41),b(47," Payments "),D(),s(48,"c-badge",42),b(49," 42 "),l()()(),s(50,"li")(51,"a",28),P(),u(52,"svg",43),b(53," Projects "),D(),s(54,"c-badge",44),b(55," 42 "),l()()(),s(56,"li"),u(57,"hr",45),l(),s(58,"li")(59,"a",28),P(),u(60,"svg",46),b(61," Lock Account "),l()(),D(),s(62,"li")(63,"a",47),M("click",function(){R(r);let n=K();return L(n.onLogout())}),P(),u(64,"svg",48),ie(65,Yn,2,0,"span",27)(66,Gn,3,0,"span",27),l()()()()}if(i&2){let r=K();h("popperOptions",nt(9,$n)),m(),h("caret",!1),m(),h("size","md"),m(4),gt(" ",r.getUserDisplayName()," "),m(),h("ngIf",r.getUserEmail()),m(56),Fe("disabled",r.isLoggingOut),m(2),h("ngIf",!r.isLoggingOut),m(),h("ngIf",r.isLoggingOut)}}function Xn(i,e){if(i&1){let r=se();s(0,"button",56),M("click",function(){let n=R(r).$implicit,o=K(2);return L(o.colorMode.set(n.name))}),P(),u(1,"svg",57),b(2),l()}if(i&2){let r=e.$implicit,t=K(2);h("active",t.colorMode()===r.name),m(),h("name",r.icon),m(),gt(" ",r.text," ")}}function qn(i,e){if(i&1&&(s(0,"c-dropdown",51)(1,"button",52),P(),u(2,"svg",53),l(),D(),s(3,"div",54),gr(4,Xn,3,3,"button",55,Fn),l()()),i&2){let r=K();m(),h("caret",!1),m(),h("name",r.icons()),m(2),mr(r.colorModes)}}var wt,Ye=class Ye extends Hr{constructor(r,t,n){super();this.authService=r;this.router=t;this.layoutService=n;qt(this,wt,tt(Dr));this.colorMode=Xt(this,wt).colorMode;this.colorModes=[{name:"light",text:"Light",icon:"cilSun"},{name:"dark",text:"Dark",icon:"cilMoon"},{name:"auto",text:"Auto",icon:"cilContrast"}];this.icons=yr(()=>{let r=this.colorMode();return this.colorModes.find(t=>t.name===r)?.icon??"cilSun"});this.currentUser=null;this.isAuthenticated=!1;this.isLoggingOut=!1;this.destroy$=new B;this.sidebarId=Cr("sidebar1");this.newMessages=[{id:0,from:"Jessica Williams",avatar:"7.jpg",status:"success",title:"Urgent: System Maintenance Tonight",time:"Just now",link:"apps/email/inbox/message",message:"Attention team, we'll be conducting critical system maintenance tonight from 10 PM to 2 AM. Plan accordingly..."},{id:1,from:"Richard Johnson",avatar:"6.jpg",status:"warning",title:"Project Update: Milestone Achieved",time:"5 minutes ago",link:"apps/email/inbox/message",message:"Kudos on hitting sales targets last quarter! Let's keep the momentum. New goals, new victories ahead..."},{id:2,from:"Angela Rodriguez",avatar:"5.jpg",status:"danger",title:"Social Media Campaign Launch",time:"1:52 PM",link:"apps/email/inbox/message",message:"Exciting news! Our new social media campaign goes live tomorrow. Brace yourselves for engagement..."},{id:3,from:"Jane Lewis",avatar:"4.jpg",status:"info",title:"Inventory Checkpoint",time:"4:03 AM",link:"apps/email/inbox/message",message:"Team, it's time for our monthly inventory check. Accurate counts ensure smooth operations. Let's nail it..."},{id:3,from:"Ryan Miller",avatar:"4.jpg",status:"info",title:"Customer Feedback Results",time:"3 days ago",link:"apps/email/inbox/message",message:"Our latest customer feedback is in. Let's analyze and discuss improvements for an even better service..."}];this.newNotifications=[{id:0,title:"New user registered",icon:"cilUserFollow",color:"success"},{id:1,title:"User deleted",icon:"cilUserUnfollow",color:"danger"},{id:2,title:"Sales report is ready",icon:"cilChartPie",color:"info"},{id:3,title:"New client",icon:"cilBasket",color:"primary"},{id:4,title:"Server overloaded",icon:"cilSpeedometer",color:"warning"}];this.newStatus=[{id:0,title:"CPU Usage",value:25,color:"info",details:"348 Processes. 1/4 Cores."},{id:1,title:"Memory Usage",value:70,color:"warning",details:"11444GB/16384MB"},{id:2,title:"SSD 1 Usage",value:90,color:"danger",details:"243GB/256GB"}];this.newTasks=[{id:0,title:"Upgrade NPM",value:0,color:"info"},{id:1,title:"ReactJS Version",value:25,color:"danger"},{id:2,title:"VueJS Version",value:50,color:"warning"},{id:3,title:"Add new layouts",value:75,color:"info"},{id:4,title:"Angular Version",value:100,color:"success"}]}ngOnInit(){this.authService.isAuthenticated$.pipe(H(this.destroy$)).subscribe(r=>{this.isAuthenticated=r}),this.authService.currentUser$.pipe(H(this.destroy$)).subscribe(r=>{this.currentUser=r})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onLogout(){this.isLoggingOut||(this.isLoggingOut=!0,this.authService.logout().pipe(H(this.destroy$)).subscribe({next:()=>{console.log("Logout successful"),this.router.navigate(["/login"])},error:r=>{console.error("Logout error:",r),this.router.navigate(["/login"])},complete:()=>{this.isLoggingOut=!1}}))}getUserDisplayName(){return this.currentUser?this.currentUser.prenom&&this.currentUser.nom?`${this.currentUser.prenom} ${this.currentUser.nom}`:this.currentUser.nom?this.currentUser.nom:this.currentUser.email||"User":"User"}getUserEmail(){return this.currentUser?.email||""}toggleCustomizer(){this.layoutService.emitCustomizerChange("toggle")}};wt=new WeakMap,Ye.\u0275fac=function(t){return new(t||Ye)(c(Kr),c(ht),c(le))},Ye.\u0275cmp=Y({type:Ye,selectors:[["app-default-header"]],inputs:{sidebarId:[1,"sidebarId"]},features:[q],decls:37,vars:5,consts:[["userDropdown",""],["themeDropdown",""],[1,"border-bottom","px-4",3,"fluid"],["cHeaderToggler","","toggle","visible","aria-label","Toggle sidebar navigation",1,"btn",2,"margin-inline-start","-14px",3,"cSidebarToggle"],["cIcon","","name","cilMenu","size","lg"],[1,"d-none","d-md-flex"],["cNavLink","","routerLink","/dashboard","routerLinkActive","active"],["cNavLink","","routerLink","/users","routerLinkActive","active"],["cNavLink","","routerLink","/settings","routerLinkActive","active"],[1,"d-none","d-md-flex","ms-auto"],["cNavLink",""],["cIcon","","name","cilBell","size","lg",1,"my-1"],["cIcon","","name","cilList","size","lg",1,"my-1"],["cIcon","","name","cilEnvelopeOpen","size","lg",1,"my-1"],["cNavLink","","title","Theme Customizer",2,"cursor","pointer",3,"click"],["cIcon","","name","cilSettings","size","lg",1,"my-1"],[1,"ms-auto","ms-md-0"],[1,"nav-item","py-1"],[1,"vr","h-100","mx-2","text-body","text-opacity-75"],[4,"ngTemplateOutlet"],[1,"mx-0"],[1,"px-4",3,"fluid"],["variant","nav-item",3,"popperOptions"],["cDropdownToggle","","aria-label","Open user menu",1,"py-0","pe-0",3,"caret"],["shape","rounded-1","src","./assets/images/avatars/8.jpg","status","success","textColor","primary","alt","User avatar",3,"size"],["cDropdownMenu","",1,"pt-0","w-auto"],["cDropdownHeader","",1,"bg-body-secondary","text-body-secondary","fw-semibold","py-2","rounded-top"],[4,"ngIf"],["cDropdownItem","","routerLink",""],["cIcon","","name","cilBell",1,"me-2"],["color","info",1,"ms-2","float-end"],["cDropdownItem","","routerLink","/apps/email/inbox"],["cIcon","","name","cilEnvelopeOpen",1,"me-2"],["color","success",1,"ms-2","float-end"],["cIcon","","name","cilTask",1,"me-2"],["color","danger",1,"ms-2","float-end"],["cIcon","","name","cilCommentSquare",1,"me-2"],["color","warning",1,"ms-auto"],["cDropdownHeader","",1,"bg-body-secondary","text-body-secondary","fw-semibold","py-2"],["cIcon","","name","cilUser",1,"me-2"],["cIcon","","name","cilSettings",1,"me-2"],["cIcon","","name","cilCreditCard",1,"me-2"],["color","secondary",1,"ms-2","float-end"],["cIcon","","name","cilFile",1,"me-2"],["color","primary",1,"ms-2","float-end"],["cDropdownDivider",""],["cIcon","","name","cilLockLocked",1,"me-2"],["cDropdownItem","",2,"cursor","pointer",3,"click"],["cIcon","","name","cilAccountLogout",1,"me-2"],[1,"px-3","py-1","text-muted","small"],["role","status","aria-hidden","true",1,"spinner-border","spinner-border-sm","me-2"],["alignment","end","variant","nav-item"],["cDropdownToggle","","aria-label","Open theme picker",3,"caret"],["cIcon","","size","lg",3,"name"],["cDropdownMenu",""],["cDropdownItem","",1,"d-flex","align-items-center",3,"active"],["cDropdownItem","",1,"d-flex","align-items-center",3,"click","active"],["cIcon","","size","lg",1,"me-2",3,"name"]],template:function(t,n){if(t&1){let o=se();dt(0),s(1,"c-container",2)(2,"button",3),P(),u(3,"svg",4),l(),D(),s(4,"c-header-nav",5)(5,"c-nav-item")(6,"a",6),b(7,"Dashboard"),l()(),s(8,"c-nav-item")(9,"a",7),b(10,"Users"),l()(),s(11,"c-nav-item")(12,"a",8),b(13,"Settings"),l()()(),s(14,"c-header-nav",9)(15,"a",10),P(),u(16,"svg",11),l(),D(),s(17,"a",10),P(),u(18,"svg",12),l(),D(),s(19,"a",10),P(),u(20,"svg",13),l(),D(),s(21,"a",14),M("click",function(){return R(o),L(n.toggleCustomizer())}),P(),u(22,"svg",15),l()(),D(),s(23,"c-header-nav",16)(24,"div",17),u(25,"div",18),l(),ie(26,Vn,1,0,"ng-container",19),s(27,"div",17),u(28,"div",18),l()(),s(29,"c-header-nav",20),ie(30,Nn,1,0,"ng-container",19),l()(),s(31,"c-container",21),u(32,"c-breadcrumb-router"),l(),ut(),ie(33,Qn,67,10,"ng-template",null,0,Tt)(35,qn,6,2,"ng-template",null,1,Tt)}if(t&2){let o=ge(34),a=ge(36);m(),h("fluid",!0),m(),h("cSidebarToggle",n.sidebarId()),m(24),h("ngTemplateOutlet",a),m(4),h("ngTemplateOutlet",o),m(),h("fluid",!0)}},dependencies:[Ve,mt,_r,ft,$r,yt,Ne,jr,Vr,Fr,vt,Pr,Lr,Tr,kr,Ir,Ur,xr,Ar,Rr,zr],encapsulation:2});var Ot=Ye;function ce(i){return i!=null&&`${i}`!="false"}var Kn=new He("cdk-dir-doc",{providedIn:"root",factory:Zn});function Zn(){return tt(W)}var Jn=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function tn(i){let e=i?.toLowerCase()||"";return e==="auto"&&typeof navigator<"u"&&navigator?.language?Jn.test(navigator.language)?"rtl":"ltr":e==="rtl"?"rtl":"ltr"}var _t=(()=>{class i{get value(){return this.valueSignal()}valueSignal=ar("ltr");change=new ue;constructor(){let r=tt(Kn,{optional:!0});if(r){let t=r.body?r.body.dir:null,n=r.documentElement?r.documentElement.dir:null;this.valueSignal.set(tn(t||n||"ltr"))}}ngOnDestroy(){this.change.complete()}static \u0275fac=function(t){return new(t||i)};static \u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var Ge=function(i){return i[i.NORMAL=0]="NORMAL",i[i.NEGATED=1]="NEGATED",i[i.INVERTED=2]="INVERTED",i}(Ge||{}),Mt;function it(){if(typeof document!="object"||!document)return Ge.NORMAL;if(Mt==null){let i=document.createElement("div"),e=i.style;i.dir="rtl",e.width="1px",e.overflow="auto",e.visibility="hidden",e.pointerEvents="none",e.position="absolute";let r=document.createElement("div"),t=r.style;t.width="2px",t.height="1px",i.appendChild(r),document.body.appendChild(i),Mt=Ge.NORMAL,i.scrollLeft===0&&(i.scrollLeft=1,Mt=i.scrollLeft===0?Ge.NEGATED:Ge.INVERTED),i.remove()}return Mt}var ri=4,ni=.001,ii=1e-7,oi=10,ot=11,St=1/(ot-1),ai=typeof Float32Array=="function";function rn(i,e){return 1-3*e+3*i}function nn(i,e){return 3*e-6*i}function on(i){return 3*i}function Pt(i,e,r){return((rn(e,r)*i+nn(e,r))*i+on(e))*i}function an(i,e,r){return 3*rn(e,r)*i*i+2*nn(e,r)*i+on(e)}function si(i,e,r,t,n){let o,a,v=0;do a=e+(r-e)/2,o=Pt(a,t,n)-i,o>0?r=a:e=a;while(Math.abs(o)>ii&&++v<oi);return a}function li(i,e,r,t){for(let n=0;n<ri;++n){let o=an(e,r,t);if(o===0)return e;let a=Pt(e,r,t)-i;e-=a/o}return e}function ci(i){return i}function pi(i,e,r,t){if(!(0<=i&&i<=1&&0<=r&&r<=1))throw new Error("bezier x values must be in [0, 1] range");if(i===e&&r===t)return ci;let n=ai?new Float32Array(ot):new Array(ot);for(let a=0;a<ot;++a)n[a]=Pt(a*St,i,r);function o(a){let v=0,O=1,te=ot-1;for(;O!==te&&n[O]<=a;++O)v+=St;--O;let X=(a-n[O])/(n[O+1]-n[O]),zt=v+X*St,Qt=an(zt,i,r);return Qt>=ni?li(a,zt,i,r):Qt===0?zt:si(a,v,v+St,i,r)}return function(v){return v===0?0:v===1?1:Pt(o(v),e,t)}}var di=new He("SMOOTH_SCROLL_OPTIONS"),sn=(()=>{let e=class e{get _w(){return this._document.defaultView}get _now(){return this._w.performance&&this._w.performance.now?this._w.performance.now.bind(this._w.performance):Date.now}constructor(t,n,o){this._document=t,this._platform=n,this._onGoingScrolls=new Map,this._defaultOptions=E({duration:468,easing:{x1:.42,y1:0,x2:.58,y2:1}},o)}_scrollElement(t,n,o){t.scrollLeft=n,t.scrollTop=o}_getElement(t,n){return typeof t=="string"?(n||this._document).querySelector(t):At(t)}_initSmoothScroll(t){return this._onGoingScrolls.has(t)&&this._onGoingScrolls.get(t).next(),this._onGoingScrolls.set(t,new B).get(t)}_isFinished(t,n,o){return t.currentX!==t.x||t.currentY!==t.y?!0:(n.next(),o(),!1)}_interrupted(t,n){return ae(k(t,"wheel",{passive:!0,capture:!0}),k(t,"touchmove",{passive:!0,capture:!0}),n).pipe(tr(1))}_destroy(t,n){n.complete(),this._onGoingScrolls.delete(t)}_step(t){return new Be(n=>{let o=(this._now()-t.startTime)/t.duration;o=o>1?1:o;let a=t.easing(o);t.currentX=t.startX+(t.x-t.startX)*a,t.currentY=t.startY+(t.y-t.startY)*a,this._scrollElement(t.scrollable,t.currentX,t.currentY),ct.schedule(()=>n.next(t))})}_applyScrollToOptions(t,n){if(!n.duration)return this._scrollElement(t,n.left,n.top),Promise.resolve();let o=this._initSmoothScroll(t),a={scrollable:t,startTime:this._now(),startX:t.scrollLeft,startY:t.scrollTop,x:n.left==null?t.scrollLeft:~~n.left,y:n.top==null?t.scrollTop:~~n.top,duration:n.duration,easing:pi(n.easing.x1,n.easing.y1,n.easing.x2,n.easing.y2)};return new Promise(v=>{Je(null).pipe(rr(()=>this._step(a).pipe(or(O=>this._isFinished(O,o,v)))),H(this._interrupted(t,o)),nr(()=>this._destroy(t,o))).subscribe()})}scrollTo(t,n){if(Mr(this._platform)){let o=this._getElement(t),a=getComputedStyle(o).direction==="rtl",v=it(),O=re(E(E({},this._defaultOptions),n),{left:n.left==null?a?n.end:n.start:n.left,right:n.right==null?a?n.start:n.end:n.right});return O.bottom!=null&&(O.top=o.scrollHeight-o.clientHeight-O.bottom),a&&v!==0?(O.left!=null&&(O.right=o.scrollWidth-o.clientWidth-O.left),v===2?O.left=O.right:v===1&&(O.left=O.right?-O.right:O.right)):O.right!=null&&(O.left=o.scrollWidth-o.clientWidth-O.right),this._applyScrollToOptions(o,O)}return Promise.resolve()}scrollToElement(t,n,o={}){let a=this._getElement(t),v=this._getElement(n,a),O=re(E({},o),{left:v.offsetLeft+(o.left||0),top:v.offsetTop+(o.top||0)});return v?this.scrollTo(a,O):Promise.resolve()}};e.\u0275fac=function(n){return new(n||e)(g(W),g(lr),g(di,8))},e.\u0275prov=d({token:e,factory:e.\u0275fac,providedIn:"root"});let i=e;return i})();var gi=["scrollbarY"],mi=["scrollbarX"],hi=["*"];function vi(i,e){if(i&1&&u(0,"scrollbar-x",null,0),i&2){let r=K(2);Ut("scrollable",r.state.isHorizontallyScrollable)("fit",r.state.verticalUsed)}}function bi(i,e){if(i&1&&u(0,"scrollbar-y",null,1),i&2){let r=K(2);Ut("scrollable",r.state.isVerticallyScrollable)("fit",r.state.horizontalUsed)}}function fi(i,e){if(i&1&&(dt(0),ie(1,vi,2,2,"scrollbar-x",5)(2,bi,2,2,"scrollbar-y",5),ut()),i&2){let r=K();m(),h("ngIf",r.state.horizontalUsed),m(),h("ngIf",r.state.verticalUsed)}}var yi=(()=>{let e=class e{constructor(t){this.el=t}set ngAttr(t){for(let[n,o]of Object.entries(t))this.el.nativeElement.setAttribute(n,o)}};e.\u0275fac=function(n){return new(n||e)(c(j))},e.\u0275dir=$({type:e,selectors:[["","ngAttr",""]],inputs:{ngAttr:"ngAttr"}});let i=e;return i})();function dn(i){return z(()=>{i.onselectstart=()=>!1})}function un(i){return z(()=>{i.onselectstart=null})}function pe(){return z(i=>i.stopPropagation())}function Ht(i,e){return i.clientX>=e.left&&i.clientX<=e.left+e.width&&i.clientY>=e.top&&i.clientY<=e.top+e.height}var jt=(()=>{let e=class e{get clientHeight(){return this.nativeElement.clientHeight}get clientWidth(){return this.nativeElement.clientWidth}get scrollHeight(){return this.nativeElement.scrollHeight}get scrollWidth(){return this.nativeElement.scrollWidth}get scrollTop(){return this.nativeElement.scrollTop}get scrollLeft(){return this.nativeElement.scrollLeft}get scrollMaxX(){return this.scrollWidth-this.clientWidth}get scrollMaxY(){return this.scrollHeight-this.clientHeight}get contentHeight(){return this.contentWrapperElement?.clientHeight||0}get contentWidth(){return this.contentWrapperElement?.clientWidth||0}constructor(t){this.viewPort=t,this.nativeElement=t.nativeElement}activatePointerEvents(t,n){this.hovered=new Be(o=>{let a=k(this.nativeElement,"mousemove",{passive:!0}),v=t?a:a.pipe(pe()),O=k(this.nativeElement,"mouseleave",{passive:!0}).pipe(T(()=>!1));ae(v,O).pipe(z(te=>o.next(te)),H(n)).subscribe()}),this.clicked=new Be(o=>{let a=k(this.nativeElement,"mousedown",{passive:!0}).pipe(z(O=>o.next(O))),v=k(this.nativeElement,"mouseup",{passive:!0}).pipe(z(()=>o.next(!1)));a.pipe(et(()=>v),H(n)).subscribe()})}setAsWrapper(){this.nativeElement.className="ng-native-scrollbar-hider ng-scroll-layer",this.nativeElement.firstElementChild&&(this.nativeElement.firstElementChild.className="ng-scroll-layer")}setAsViewport(t){this.nativeElement.className+=` ng-native-scrollbar-hider ng-scroll-viewport ${t}`,this.nativeElement.firstElementChild&&(this.contentWrapperElement=this.nativeElement.firstElementChild,this.contentWrapperElement.classList.add("ng-scroll-content"))}scrollYTo(t){this.nativeElement.scrollTop=t}scrollXTo(t){this.nativeElement.scrollLeft=t}};e.\u0275fac=function(n){return new(n||e)(c(j))},e.\u0275dir=$({type:e,selectors:[["","scrollViewport",""]]});let i=e;return i})(),J=(()=>{let e=class e{};e.\u0275fac=function(n){return new(n||e)},e.\u0275dir=$({type:e,standalone:!1});let i=e;return i})(),Vt=(()=>{let e=class e{get clicked(){let t=k(this.trackElement,"mousedown",{passive:!0}).pipe(pe(),dn(this.document)),n=k(this.document,"mouseup",{passive:!0}).pipe(pe(),un(this.document),et(()=>Kt));return ae(t,n)}get clientRect(){return this.trackElement.getBoundingClientRect()}constructor(t,n,o){this.cmp=t,this.trackElement=n,this.document=o}onTrackClicked(t,n,o){return Je(t).pipe(T(a=>a[this.pageProperty]),T(a=>(a-this.offset-n/2)/this.size*o),z(a=>{this.cmp.scrollTo(re(E({},this.mapToScrollToOption(a)),{duration:bt(this.cmp.trackClickScrollDuration)}))}))}};e.\u0275fac=function(n){return new(n||e)(c(J),c(HTMLElement),c(Document))},e.\u0275dir=$({type:e,standalone:!1});let i=e;return i})(),$t=(()=>{let e=class e extends Vt{get pageProperty(){return"pageX"}get offset(){return this.clientRect.left}get size(){return this.trackElement.clientWidth}constructor(t,n,o){super(t,n.nativeElement,o),this.cmp=t,this.document=o}mapToScrollToOption(t){return{left:t}}};e.\u0275fac=function(n){return new(n||e)(c(J),c(j),c(W))},e.\u0275dir=$({type:e,selectors:[["","scrollbarTrackX",""]],features:[q]});let i=e;return i})(),Ft=(()=>{let e=class e extends Vt{get pageProperty(){return"pageY"}get offset(){return this.clientRect.top}get size(){return this.trackElement.clientHeight}constructor(t,n,o){super(t,n.nativeElement,o),this.cmp=t,this.document=o}mapToScrollToOption(t){return{top:t}}};e.\u0275fac=function(n){return new(n||e)(c(J),c(j),c(W))},e.\u0275dir=$({type:e,selectors:[["","scrollbarTrackY",""]],features:[q]});let i=e;return i})(),gn=(()=>{let e=class e{get trackMax(){return this.track.size-this.size}get clientRect(){return this.thumbElement.getBoundingClientRect()}get clicked(){return k(this.thumbElement,"mousedown",{passive:!0}).pipe(pe())}constructor(t,n,o,a){this.cmp=t,this.track=n,this.thumbElement=o,this.document=a,this._dragging=new B,this.dragging=this._dragging.pipe(pt())}update(){let t=Ci(this.track.size,this.viewportScrollSize,this.cmp.minThumbSize),n=Oi(this.viewportScrollOffset,this.viewportScrollMax,this.trackMax);ct.schedule(()=>this.updateStyles(this.handleDirection(n,this.trackMax),t))}dragged(t){let n,o,a=Je(t).pipe(dn(this.document),z(()=>{n=this.trackMax,o=this.viewportScrollMax,this.setDragging(!0)})),v=k(this.document,"mousemove",{capture:!0,passive:!0}).pipe(pe()),O=k(this.document,"mouseup",{capture:!0}).pipe(pe(),un(this.document),z(()=>this.setDragging(!1)));return a.pipe(T(te=>te[this.pageProperty]),T(te=>te-this.dragStartOffset),Zt(te=>v.pipe(T(X=>X[this.clientProperty]),T(X=>X-this.track.offset),T(X=>o*(X-te)/n),T(X=>this.handleDrag(X,o)),z(X=>this.scrollTo(X)),H(O))))}};e.\u0275fac=function(n){return new(n||e)(c(J),c(Vt),c(HTMLElement),c(Document))},e.\u0275dir=$({type:e,outputs:{dragging:"dragging"},standalone:!1});let i=e;return i})();function Ci(i,e,r){let n=i/e*i;return Math.max(~~n,r)}function Oi(i,e,r){return i*r/e}var ln=(()=>{let e=class e extends gn{get clientProperty(){return"clientX"}get pageProperty(){return"pageX"}get viewportScrollSize(){return this.cmp.viewport.scrollWidth}get viewportScrollOffset(){return this.cmp.viewport.scrollLeft}get viewportScrollMax(){return this.cmp.viewport.scrollMaxX}get dragStartOffset(){return this.clientRect.left+this.document.defaultView.pageXOffset||0}get size(){return this.thumbElement.clientWidth}constructor(t,n,o,a,v){super(t,n,o.nativeElement,a),this.cmp=t,this.track=n,this.element=o,this.document=a,this.dir=v}updateStyles(t,n){this.thumbElement.style.width=`${n}px`,this.thumbElement.style.transform=`translate3d(${t}px, 0, 0)`}handleDrag(t,n){if(this.dir.value==="rtl"){if(this.cmp.manager.rtlScrollAxisType===1)return t-n;if(this.cmp.manager.rtlScrollAxisType===2)return n-t}return t}handleDirection(t,n){if(this.dir.value==="rtl"){if(this.cmp.manager.rtlScrollAxisType===2)return-t;if(this.cmp.manager.rtlScrollAxisType===0)return t-n}return t}setDragging(t){this.cmp.setDragging({horizontalDragging:t})}scrollTo(t){this.cmp.viewport.scrollXTo(t)}};e.\u0275fac=function(n){return new(n||e)(c(J),c($t),c(j),c(W),c(_t))},e.\u0275dir=$({type:e,selectors:[["","scrollbarThumbX",""]],features:[q]});let i=e;return i})(),cn=(()=>{let e=class e extends gn{get pageProperty(){return"pageY"}get viewportScrollSize(){return this.cmp.viewport.scrollHeight}get viewportScrollOffset(){return this.cmp.viewport.scrollTop}get viewportScrollMax(){return this.cmp.viewport.scrollMaxY}get clientProperty(){return"clientY"}get dragStartOffset(){return this.clientRect.top+this.document.defaultView.pageYOffset||0}get size(){return this.thumbElement.clientHeight}constructor(t,n,o,a){super(t,n,o.nativeElement,a),this.cmp=t,this.track=n,this.element=o,this.document=a}updateStyles(t,n){this.thumbElement.style.height=`${n}px`,this.thumbElement.style.transform=`translate3d(0px, ${t}px, 0)`}handleDrag(t){return t}handleDirection(t){return t}setDragging(t){this.cmp.setDragging({verticalDragging:t})}scrollTo(t){this.cmp.viewport.scrollYTo(t)}};e.\u0275fac=function(n){return new(n||e)(c(J),c(Ft),c(j),c(W))},e.\u0275dir=$({type:e,selectors:[["","scrollbarThumbY",""]],features:[q]});let i=e;return i})(),mn=(()=>{let e=class e{constructor(t,n,o,a,v){this.el=t,this.cmp=n,this.platform=o,this.document=a,this.zone=v,this.destroyed=new B}activatePointerEvents(){let t,n,o;return this.cmp.pointerEventsMethod==="viewport"?(this.viewportTrackClicked=new B,this.viewportThumbClicked=new B,this.cmp.viewport.activatePointerEvents(this.cmp.viewportPropagateMouseMove,this.destroyed),t=this.viewportThumbClicked,n=this.viewportTrackClicked,o=this.cmp.viewport.hovered.pipe(T(a=>a?Ht(a,this.el.getBoundingClientRect()):!1),pt(),z(a=>this.document.onselectstart=a?()=>!1:null)),this.cmp.viewport.clicked.pipe(z(a=>{a?Ht(a,this.thumb.clientRect)?this.viewportThumbClicked.next(a):Ht(a,this.track.clientRect)&&(this.cmp.setClicked(!0),this.viewportTrackClicked.next(a)):this.cmp.setClicked(!1)}),H(this.destroyed)).subscribe()):(t=this.thumb.clicked,n=this.track.clicked,o=this.hovered),ae(o.pipe(z(a=>this.setHovered(a))),t.pipe(et(a=>this.thumb.dragged(a))),n.pipe(et(a=>this.track.onTrackClicked(a,this.thumb.size,this.viewportScrollSize))))}get hovered(){let t=k(this.el,"mouseenter",{passive:!0}).pipe(pe(),T(()=>!0)),n=k(this.el,"mouseleave",{passive:!0}).pipe(pe(),T(()=>!1));return ae(t,n)}ngOnInit(){this.zone.runOutsideAngular(()=>{!(this.platform.IOS||this.platform.ANDROID)&&!this.cmp.pointerEventsDisabled&&this.activatePointerEvents().pipe(H(this.destroyed)).subscribe(),ae(this.cmp.scrolled,this.cmp.updated).pipe(z(()=>this.thumb?.update()),H(this.destroyed)).subscribe()})}ngOnDestroy(){this.destroyed.next(),this.destroyed.complete(),this.viewportThumbClicked&&this.viewportTrackClicked&&(this.viewportTrackClicked.complete(),this.viewportThumbClicked.complete())}};e.\u0275fac=function(n){return new(n||e)(c(HTMLElement),c(J),c(he),c(Document),c($e))},e.\u0275dir=$({type:e,standalone:!1});let i=e;return i})(),wi=(()=>{let e=class e extends mn{get viewportScrollSize(){return this.cmp.viewport.scrollHeight}constructor(t,n,o,a,v){super(t.nativeElement,n,o,a,v),this.cmp=n,this.platform=o,this.document=a,this.zone=v}setHovered(t){this.cmp.setHovered({verticalHovered:t})}};e.\u0275fac=function(n){return new(n||e)(c(j),c(J),c(he),c(W),c($e))},e.\u0275cmp=Y({type:e,selectors:[["scrollbar-y"]],viewQuery:function(n,o){if(n&1&&(G(Ft,7),G(cn,7)),n&2){let a;V(a=N())&&(o.track=a.first),V(a=N())&&(o.thumb=a.first)}},hostVars:2,hostBindings:function(n,o){n&2&&Fe("scrollbar-control",!0)},features:[q],decls:2,vars:6,consts:[["scrollbarTrackY",""],["scrollbarThumbY",""]],template:function(n,o){n&1&&(s(0,"div",0),u(1,"div",1),l()),n&2&&(me(rt("ng-scrollbar-track ",o.cmp.trackClass)),m(),me(rt("ng-scrollbar-thumb ",o.cmp.thumbClass)))},dependencies:[Ft,cn],styles:[".ng-scrollbar-wrapper>scrollbar-y.scrollbar-control{width:var(--vertical-scrollbar-total-size)}  .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track{width:var(--vertical-scrollbar-size);height:calc(100% - var(--scrollbar-padding) * 2)}  .ng-scrollbar-wrapper>scrollbar-y.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{height:0;width:100%}  .ng-scrollbar-wrapper[verticalHovered=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb,   .ng-scrollbar-wrapper[verticalDragging=true]>scrollbar-y.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}  .ng-scrollbar-wrapper[deactivated=false]>scrollbar-y.scrollbar-control{top:0;bottom:0}  .ng-scrollbar-wrapper[deactivated=false][dir=ltr]>scrollbar-y.scrollbar-control{right:0;left:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertY]>scrollbar-y.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][dir=ltr][position=invertAll]>scrollbar-y.scrollbar-control{left:0;right:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=rtl]>scrollbar-y.scrollbar-control{left:0;right:unset}  .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertY]>scrollbar-y.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][dir=rtl][position=invertAll]>scrollbar-y.scrollbar-control{left:unset;right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all]>scrollbar-y.scrollbar-control[fit=true]{bottom:var(--scrollbar-total-size);top:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertX]>scrollbar-y.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][position=invertAll]>scrollbar-y.scrollbar-control[fit=true]{top:var(--scrollbar-total-size);bottom:0}"],changeDetection:0});let i=e;return i})(),_i=(()=>{let e=class e extends mn{get viewportScrollSize(){return this.cmp.viewport.scrollWidth}constructor(t,n,o,a,v){super(t.nativeElement,n,o,a,v),this.cmp=n,this.platform=o,this.document=a,this.zone=v}setHovered(t){this.cmp.setHovered({horizontalHovered:t})}};e.\u0275fac=function(n){return new(n||e)(c(j),c(J),c(he),c(W),c($e))},e.\u0275cmp=Y({type:e,selectors:[["scrollbar-x"]],viewQuery:function(n,o){if(n&1&&(G($t,7),G(ln,7)),n&2){let a;V(a=N())&&(o.track=a.first),V(a=N())&&(o.thumb=a.first)}},hostVars:2,hostBindings:function(n,o){n&2&&Fe("scrollbar-control",!0)},features:[q],decls:2,vars:6,consts:[["scrollbarTrackX",""],["scrollbarThumbX",""]],template:function(n,o){n&1&&(s(0,"div",0),u(1,"div",1),l()),n&2&&(me(rt("ng-scrollbar-track ",o.cmp.trackClass)),m(),me(rt("ng-scrollbar-thumb ",o.cmp.thumbClass)))},dependencies:[$t,ln],styles:[".ng-scrollbar-wrapper>scrollbar-x.scrollbar-control{height:var(--horizontal-scrollbar-total-size)}  .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track{height:var(--horizontal-scrollbar-size);width:calc(100% - var(--scrollbar-padding) * 2)}  .ng-scrollbar-wrapper>scrollbar-x.scrollbar-control>.ng-scrollbar-track>.ng-scrollbar-thumb{width:0;height:100%}  .ng-scrollbar-wrapper[horizontalHovered=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb,   .ng-scrollbar-wrapper[horizontalDragging=true]>scrollbar-x.scrollbar-control .ng-scrollbar-thumb{background-color:var(--scrollbar-thumb-hover-color)}  .ng-scrollbar-wrapper[position=invertX]>scrollbar-x.scrollbar-control,   .ng-scrollbar-wrapper[position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}  .ng-scrollbar-wrapper[deactivated=false]>scrollbar-x.scrollbar-control{left:0;right:0;bottom:0;top:unset}  .ng-scrollbar-wrapper[deactivated=false][position=invertX]>scrollbar-x.scrollbar-control,   .ng-scrollbar-wrapper[deactivated=false][position=invertAll]>scrollbar-x.scrollbar-control{top:0;bottom:unset}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertY]>scrollbar-x.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][dir=ltr][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl]>scrollbar-x.scrollbar-control[fit=true]{left:var(--scrollbar-total-size);right:0}  .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertY]>scrollbar-x.scrollbar-control[fit=true],   .ng-scrollbar-wrapper[deactivated=false][track=all][dir=rtl][position=invertAll]>scrollbar-x.scrollbar-control[fit=true]{right:var(--scrollbar-total-size);left:0}"],changeDetection:0});let i=e;return i})(),Mi=new He("NG_SCROLLBAR_OPTIONS"),pn={viewClass:"",trackClass:"",thumbClass:"",track:"vertical",appearance:"compact",visibility:"native",position:"native",pointerEventsMethod:"viewport",trackClickScrollDuration:300,minThumbSize:20,windowResizeDebounce:0,sensorDebounce:0,scrollAuditTime:0,viewportPropagateMouseMove:!0,autoHeightDisabled:!0,autoWidthDisabled:!0,sensorDisabled:!1,pointerEventsDisabled:!1},hn=(()=>{let e=class e{constructor(t){this.globalOptions=t?E(E({},pn),t):pn,this.rtlScrollAxisType=it()}};e.\u0275fac=function(n){return new(n||e)(g(Mi,8))},e.\u0275prov=d({token:e,factory:e.\u0275fac,providedIn:"root"});let i=e;return i})(),Si=(()=>{let e=class e{constructor(t,n,o){this.document=t,this.manager=n,this.platform=o,this._scrollbarSize=new lt(this.getNativeScrollbarSize()),this.scrollbarSize=this._scrollbarSize.asObservable(),o.isBrowser&&k(this.document.defaultView,"resize",{passive:!0}).pipe(xt(this.manager.globalOptions.windowResizeDebounce),T(()=>this.getNativeScrollbarSize()),pt(),z(a=>this._scrollbarSize.next(a))).subscribe()}getNativeScrollbarSize(){if(!this.platform.isBrowser)return 0;if(this.platform.IOS)return 6;let t=this.document.createElement("div");t.className="ng-scrollbar-measure",t.style.left="0px",t.style.overflow="scroll",t.style.position="fixed",t.style.top="-9999px",this.document.body.appendChild(t);let n=t.getBoundingClientRect().right;return this.document.body.removeChild(t),n}};e.\u0275fac=function(n){return new(n||e)(g(W),g(hn),g(he))},e.\u0275prov=d({token:e,factory:e.\u0275fac,providedIn:"root"});let i=e;return i})(),Pi=(()=>{let e=class e{constructor(t,n,o){this.renderer=n,this.hideNativeScrollbar=o,this._subscriber=st.EMPTY,this._subscriber=o.scrollbarSize.subscribe(a=>{this.renderer.setStyle(t.nativeElement,"--native-scrollbar-size",`-${a}px`,pr.DashCase)})}ngOnDestroy(){this._subscriber.unsubscribe()}};e.\u0275fac=function(n){return new(n||e)(c(j),c(je),c(Si))},e.\u0275dir=$({type:e,selectors:[["","hideNativeScrollbar",""]]});let i=e;return i})(),Ei=(()=>{let e=class e{get debounce(){return this._debounce}set debounce(t){this._debounce=bt(t),this._subscribe()}get disabled(){return this._disabled}set disabled(t){this._disabled=ce(t),this._disabled?this._unsubscribe():this._subscribe()}constructor(t,n,o){if(this.zone=t,this.platform=n,this.scrollbar=o,this._disabled=!1,this._currentSubscription=null,this.event=new ue,!o)throw new Error("[NgScrollbar Resize Sensor Directive]: Host element must be an NgScrollbar component.")}ngAfterContentInit(){!this._currentSubscription&&!this._disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){if(this._unsubscribe(),this.platform.isBrowser){let t=new Be(n=>{this._resizeObserver=new ResizeObserver(o=>n.next(o)),this._resizeObserver.observe(this.scrollbar.viewport.nativeElement),this.scrollbar.viewport.contentWrapperElement&&this._resizeObserver.observe(this.scrollbar.viewport.contentWrapperElement)});this.zone.runOutsideAngular(()=>{this._currentSubscription=(this._debounce?t.pipe(xt(this._debounce)):t).subscribe(this.event)})}}_unsubscribe(){this._resizeObserver?.disconnect(),this._currentSubscription?.unsubscribe()}};e.\u0275fac=function(n){return new(n||e)(c($e),c(he),c(J))},e.\u0275dir=$({type:e,selectors:[["","resizeSensor",""]],inputs:{debounce:[0,"sensorDebounce","debounce"],disabled:[0,"sensorDisabled","disabled"]},outputs:{event:"resizeSensor"}});let i=e;return i})(),vn=(()=>{let e=class e{get disabled(){return this._disabled}set disabled(t){this._disabled=ce(t)}get sensorDisabled(){return this._sensorDisabled}set sensorDisabled(t){this._sensorDisabled=ce(t)}get pointerEventsDisabled(){return this._pointerEventsDisabled}set pointerEventsDisabled(t){this._pointerEventsDisabled=ce(t)}get viewportPropagateMouseMove(){return this._viewportPropagateMouseMove}set viewportPropagateMouseMove(t){this._viewportPropagateMouseMove=ce(t)}get autoHeightDisabled(){return this._autoHeightDisabled}set autoHeightDisabled(t){this._autoHeightDisabled=ce(t)}get autoWidthDisabled(){return this._autoWidthDisabled}set autoWidthDisabled(t){this._autoWidthDisabled=ce(t)}get nativeElement(){return this.el.nativeElement}constructor(t,n,o,a,v,O){this.el=t,this.zone=n,this.changeDetectorRef=o,this.dir=a,this.smoothScroll=v,this.manager=O,this._disabled=!1,this._sensorDisabled=this.manager.globalOptions.sensorDisabled,this._pointerEventsDisabled=this.manager.globalOptions.pointerEventsDisabled,this._autoHeightDisabled=this.manager.globalOptions.autoHeightDisabled,this._autoWidthDisabled=this.manager.globalOptions.autoWidthDisabled,this._viewportPropagateMouseMove=this.manager.globalOptions.viewportPropagateMouseMove,this.viewClass=this.manager.globalOptions.viewClass,this.trackClass=this.manager.globalOptions.trackClass,this.thumbClass=this.manager.globalOptions.thumbClass,this.minThumbSize=this.manager.globalOptions.minThumbSize,this.trackClickScrollDuration=this.manager.globalOptions.trackClickScrollDuration,this.pointerEventsMethod=this.manager.globalOptions.pointerEventsMethod,this.track=this.manager.globalOptions.track,this.visibility=this.manager.globalOptions.visibility,this.appearance=this.manager.globalOptions.appearance,this.position=this.manager.globalOptions.position,this.sensorDebounce=this.manager.globalOptions.sensorDebounce,this.scrollAuditTime=this.manager.globalOptions.scrollAuditTime,this.updated=new ue,this.state={},this.destroyed=new B}updateState(){let t=!1,n=!1,o=!1,a=!1;(this.track==="all"||this.track==="vertical")&&(o=this.viewport.scrollHeight>this.viewport.clientHeight,t=this.visibility==="always"||o),(this.track==="all"||this.track==="horizontal")&&(a=this.viewport.scrollWidth>this.viewport.clientWidth,n=this.visibility==="always"||a),this.setState({position:this.position,track:this.track,appearance:this.appearance,visibility:this.visibility,deactivated:this.disabled,dir:this.dir.value,pointerEventsMethod:this.pointerEventsMethod,verticalUsed:t,horizontalUsed:n,isVerticallyScrollable:o,isHorizontallyScrollable:a})}setState(t){this.state=E(E({},this.state),t),this.changeDetectorRef.detectChanges()}getScrolledByDirection(t){let n;return this.scrolled.pipe(z(o=>n=o),T(o=>o.target[t]),ir(),Jt(([o,a])=>o!==a),T(()=>n))}setHovered(t){this.zone.run(()=>this.setState(E({},t)))}setDragging(t){this.zone.run(()=>this.setState(E({},t)))}setClicked(t){this.zone.run(()=>this.setState({scrollbarClicked:t}))}ngOnInit(){this.zone.runOutsideAngular(()=>{this.customViewPort?(this.viewport=this.customViewPort,this.defaultViewPort.setAsWrapper()):this.viewport=this.defaultViewPort,this.viewport.setAsViewport(this.viewClass);let t=k(this.viewport.nativeElement,"scroll",{passive:!0});t=this.scrollAuditTime?t.pipe(er(this.scrollAuditTime)):t,this.scrolled=t.pipe(H(this.destroyed)),this.verticalScrolled=this.getScrolledByDirection("scrollTop"),this.horizontalScrolled=this.getScrolledByDirection("scrollLeft")})}ngOnChanges(t){this.viewport&&this.update()}ngAfterViewInit(){this.update(),this.dir.change.pipe(z(()=>this.update()),H(this.destroyed)).subscribe()}ngOnDestroy(){this.destroyed.next(),this.destroyed.complete()}update(){this.autoHeightDisabled||this.updateHeight(),this.autoWidthDisabled||this.updateWidth(),this.updateState(),this.updated.next()}scrollTo(t){return this.smoothScroll.scrollTo(this.viewport.nativeElement,t)}scrollToElement(t,n){return this.smoothScroll.scrollToElement(this.viewport.nativeElement,t,n)}updateHeight(){this.appearance==="standard"&&this.scrollbarX?this.nativeElement.style.height=`${this.viewport.contentHeight+this.scrollbarX.nativeElement.clientHeight}px`:this.nativeElement.style.height=`${this.viewport.contentHeight}px`}updateWidth(){this.appearance==="standard"&&this.scrollbarY?this.nativeElement.style.width=`${this.viewport.contentWidth+this.scrollbarY.nativeElement.clientWidth}px`:this.nativeElement.style.width=`${this.viewport.contentWidth}px`}};e.\u0275fac=function(n){return new(n||e)(c(j),c($e),c(Or),c(_t),c(sn),c(hn))},e.\u0275cmp=Y({type:e,selectors:[["ng-scrollbar"]],contentQueries:function(n,o,a){if(n&1&&br(a,jt,7),n&2){let v;V(v=N())&&(o.customViewPort=v.first)}},viewQuery:function(n,o){if(n&1&&(G(gi,5,j),G(mi,5,j),G(jt,7)),n&2){let a;V(a=N())&&(o.scrollbarY=a.first),V(a=N())&&(o.scrollbarX=a.first),V(a=N())&&(o.defaultViewPort=a.first)}},hostVars:2,hostBindings:function(n,o){n&2&&Fe("ng-scrollbar",!0)},inputs:{disabled:"disabled",sensorDisabled:"sensorDisabled",pointerEventsDisabled:"pointerEventsDisabled",viewportPropagateMouseMove:"viewportPropagateMouseMove",autoHeightDisabled:"autoHeightDisabled",autoWidthDisabled:"autoWidthDisabled",viewClass:"viewClass",trackClass:"trackClass",thumbClass:"thumbClass",minThumbSize:"minThumbSize",trackClickScrollDuration:"trackClickScrollDuration",pointerEventsMethod:"pointerEventsMethod",track:"track",visibility:"visibility",appearance:"appearance",position:"position",sensorDebounce:"sensorDebounce",scrollAuditTime:"scrollAuditTime"},outputs:{updated:"updated"},exportAs:["ngScrollbar"],features:[fr([{provide:J,useExisting:e}]),sr],ngContentSelectors:hi,decls:6,vars:4,consts:[["scrollbarX",""],["scrollbarY",""],[1,"ng-scrollbar-wrapper",3,"ngAttr"],[1,"ng-scroll-viewport-wrapper",3,"resizeSensor","sensorDebounce","sensorDisabled"],["scrollViewport","","hideNativeScrollbar",""],[4,"ngIf"]],template:function(n,o){n&1&&(hr(),s(0,"div",2)(1,"div",3),M("resizeSensor",function(){return o.update()}),s(2,"div",4)(3,"div"),vr(4),l()()(),ie(5,fi,3,2,"ng-container",5),l()),n&2&&(h("ngAttr",o.state),m(),h("sensorDebounce",o.sensorDebounce)("sensorDisabled",o.sensorDisabled),m(4),h("ngIf",!o.disabled))},dependencies:[mt,yi,Ei,jt,Pi,_i,wi],styles:[".ng-scrollbar-measure{scrollbar-width:none;-ms-overflow-style:none}  .ng-scrollbar-measure::-webkit-scrollbar{display:none}[_nghost-%COMP%]{--scrollbar-border-radius: 7px;--scrollbar-padding: 4px;--scrollbar-track-color: transparent;--scrollbar-thumb-color: rgba(0, 0, 0, .2);--scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);--scrollbar-size: 5px;--scrollbar-hover-size: var(--scrollbar-size);--scrollbar-overscroll-behavior: initial;--scrollbar-transition-duration: .4s;--scrollbar-transition-delay: .8s;--scrollbar-thumb-transition: height ease-out .15s, width ease-out .15s;--scrollbar-track-transition: height ease-out .15s, width ease-out .15s;display:block;position:relative;height:100%;max-height:100%;max-width:100%;box-sizing:content-box!important}[_nghost-%COMP%] > .ng-scrollbar-wrapper[_ngcontent-%COMP%]{--scrollbar-total-size: calc(var(--scrollbar-size) + var(--scrollbar-padding) * 2);--vertical-scrollbar-size: var(--scrollbar-size);--horizontal-scrollbar-size: var(--scrollbar-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalHovered=true][_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalDragging=true][_ngcontent-%COMP%]{--vertical-scrollbar-size: var(--scrollbar-hover-size);--vertical-scrollbar-total-size: calc(var(--vertical-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}[_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalHovered=true][_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalDragging=true][_ngcontent-%COMP%]{--horizontal-scrollbar-size: var(--scrollbar-hover-size);--horizontal-scrollbar-total-size: calc(var(--horizontal-scrollbar-size) + var(--scrollbar-padding) * 2);cursor:default}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:0;right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:var(--scrollbar-total-size);right:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:var(--scrollbar-total-size);right:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=ltr][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-left:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{left:0;right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertY][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][verticalUsed=true][position=invertAll][dir=rtl][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-right:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{top:0;bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-bottom:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=scrollbar][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{top:var(--scrollbar-total-size);bottom:0}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{padding-top:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertX][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%] > .ng-scroll-content[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][appearance=standard][horizontalUsed=true][position=invertAll][pointerEventsMethod=viewport][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport>.ng-scroll-content{padding-top:var(--scrollbar-total-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{scrollbar-width:none;-ms-overflow-style:none}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%]::-webkit-scrollbar, [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport::-webkit-scrollbar{display:none}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{bottom:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{left:0;right:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-native-scrollbar-hider[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][verticalUsed=true][dir=rtl][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-native-scrollbar-hider{right:0;left:var(--native-scrollbar-size)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%] > .scrollbar-control[_ngcontent-%COMP%]{opacity:0;transition-property:opacity;transition-duration:var(--scrollbar-transition-duration);transition-delay:var(--scrollbar-transition-delay)}[_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:hover > .scrollbar-control[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:active > .scrollbar-control[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[deactivated=false][visibility=hover][_ngcontent-%COMP%]:focus > .scrollbar-control[_ngcontent-%COMP%]{opacity:1;transition-duration:var(--scrollbar-transition-duration);transition-delay:0ms}[_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow-x:auto;overflow-y:hidden}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow-y:auto;overflow-x:hidden}[_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > .ng-scroll-viewport[_ngcontent-%COMP%], [_nghost-%COMP%] > .ng-scrollbar-wrapper[verticalUsed=true][horizontalUsed=true][_ngcontent-%COMP%] > .ng-scroll-viewport-wrapper[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] > *[_ngcontent-%COMP%] >   .ng-scroll-viewport{overflow:auto}.ng-scroll-viewport-wrapper[_ngcontent-%COMP%]{overflow:hidden}.ng-scroll-viewport[_ngcontent-%COMP%]{-webkit-overflow-scrolling:touch;contain:strict;will-change:scroll-position;overscroll-behavior:var(--scrollbar-overscroll-behavior)}  .ng-scroll-content{display:inline-block;min-width:100%}.ng-scrollbar-wrapper[_ngcontent-%COMP%], .ng-scroll-viewport-wrapper[_ngcontent-%COMP%], .ng-scroll-layer[_ngcontent-%COMP%],   .ng-scroll-viewport{position:absolute;inset:0}",".ng-scrollbar-wrapper[pointerEventsMethod=viewport]>.scrollbar-control{pointer-events:none}  .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[horizontalDragging=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport,   .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[verticalDragging=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport,   .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>.ng-scroll-viewport,   .ng-scrollbar-wrapper[scrollbarClicked=true]>.ng-scroll-viewport-wrapper>*>*>  .ng-scroll-viewport{-webkit-user-select:none;-moz-user-select:none;user-select:none}  .ng-scrollbar-wrapper>.scrollbar-control{position:absolute;display:flex;justify-content:center;align-items:center;transition:var(--scrollbar-track-transition)}  .ng-scrollbar-wrapper>.scrollbar-control[scrollable=false] .ng-scrollbar-thumb{display:none}  .ng-scrollbar-track{height:100%;width:100%;z-index:1;border-radius:var(--scrollbar-border-radius);background-color:var(--scrollbar-track-color);overflow:hidden;transition:var(--scrollbar-track-transition);cursor:default}  .ng-scrollbar-thumb{box-sizing:border-box;position:relative;border-radius:inherit;background-color:var(--scrollbar-thumb-color);transform:translateZ(0);transition:var(--scrollbar-thumb-transition)}"],changeDetection:0});let i=e;return i})();var bn=[{name:"Dashboard",url:"/dashboard",iconComponent:{name:"cil-speedometer"},badge:{color:"info",text:"NEW"}},{title:!0,name:"My Pages"},{name:"Commandes",url:"/commandes",children:[{name:"Commandes \xE0 exp\xE9dier",url:"/commandes/commandes-aexpedier",icon:"nav-icon-bullet"},{name:"Commandes \xE0 r\xE9server",url:"/commandes/commandes-areserver",icon:"nav-icon-bullet"}]},{name:"Inspection",url:"/inspection",iconComponent:{name:"cil-check-circle"},children:[{name:"Inspection des colis",url:"/inspection/inspection-colis",icon:"nav-icon-bullet"},{name:"Inspection des livraisons",url:"/inspection/inspection-livraison",icon:"nav-icon-bullet"}]},{title:!0,name:"Theme"},{name:"Colors",url:"/theme/colors",iconComponent:{name:"cil-drop"}},{name:"Typography",url:"/theme/typography",linkProps:{fragment:"headings"},iconComponent:{name:"cil-pencil"}},{name:"Components",title:!0},{name:"Base",url:"/base",iconComponent:{name:"cil-puzzle"},children:[{name:"Accordion",url:"/base/accordion",icon:"nav-icon-bullet"},{name:"Breadcrumbs",url:"/base/breadcrumbs",icon:"nav-icon-bullet"},{name:"Cards",url:"/base/cards",icon:"nav-icon-bullet"},{name:"Carousel",url:"/base/carousel",icon:"nav-icon-bullet"},{name:"Collapse",url:"/base/collapse",icon:"nav-icon-bullet"},{name:"List Group",url:"/base/list-group",icon:"nav-icon-bullet"},{name:"Navs & Tabs",url:"/base/navs",icon:"nav-icon-bullet"},{name:"Pagination",url:"/base/pagination",icon:"nav-icon-bullet"},{name:"Placeholder",url:"/base/placeholder",icon:"nav-icon-bullet"},{name:"Popovers",url:"/base/popovers",icon:"nav-icon-bullet"},{name:"Progress",url:"/base/progress",icon:"nav-icon-bullet"},{name:"Spinners",url:"/base/spinners",icon:"nav-icon-bullet"},{name:"Tables",url:"/base/tables",icon:"nav-icon-bullet"},{name:"Tabs",url:"/base/tabs",icon:"nav-icon-bullet"},{name:"Tooltips",url:"/base/tooltips",icon:"nav-icon-bullet"}]},{name:"Buttons",url:"/buttons",iconComponent:{name:"cil-cursor"},children:[{name:"Buttons",url:"/buttons/buttons",icon:"nav-icon-bullet"},{name:"Button groups",url:"/buttons/button-groups",icon:"nav-icon-bullet"},{name:"Dropdowns",url:"/buttons/dropdowns",icon:"nav-icon-bullet"}]},{name:"Forms",url:"/forms",iconComponent:{name:"cil-notes"},children:[{name:"Form Control",url:"/forms/form-control",icon:"nav-icon-bullet"},{name:"Select",url:"/forms/select",icon:"nav-icon-bullet"},{name:"Checks & Radios",url:"/forms/checks-radios",icon:"nav-icon-bullet"},{name:"Range",url:"/forms/range",icon:"nav-icon-bullet"},{name:"Input Group",url:"/forms/input-group",icon:"nav-icon-bullet"},{name:"Floating Labels",url:"/forms/floating-labels",icon:"nav-icon-bullet"},{name:"Layout",url:"/forms/layout",icon:"nav-icon-bullet"},{name:"Validation",url:"/forms/validation",icon:"nav-icon-bullet"}]},{name:"Charts",iconComponent:{name:"cil-chart-pie"},url:"/charts"},{name:"Icons",iconComponent:{name:"cil-star"},url:"/icons",children:[{name:"CoreUI Free",url:"/icons/coreui-icons",icon:"nav-icon-bullet",badge:{color:"success",text:"FREE"}},{name:"CoreUI Flags",url:"/icons/flags",icon:"nav-icon-bullet"},{name:"CoreUI Brands",url:"/icons/brands",icon:"nav-icon-bullet"}]},{name:"Notifications",url:"/notifications",iconComponent:{name:"cil-bell"},children:[{name:"Alerts",url:"/notifications/alerts",icon:"nav-icon-bullet"},{name:"Badges",url:"/notifications/badges",icon:"nav-icon-bullet"},{name:"Modal",url:"/notifications/modal",icon:"nav-icon-bullet"},{name:"Toast",url:"/notifications/toasts",icon:"nav-icon-bullet"}]},{name:"Widgets",url:"/widgets",iconComponent:{name:"cil-calculator"},badge:{color:"info",text:"NEW"}},{title:!0,name:"Extras"},{name:"Pages",url:"/login",iconComponent:{name:"cil-star"},children:[{name:"Login",url:"/login",icon:"nav-icon-bullet"},{name:"Register",url:"/register",icon:"nav-icon-bullet"},{name:"Error 404",url:"/404",icon:"nav-icon-bullet"},{name:"Error 500",url:"/500",icon:"nav-icon-bullet"}]},{title:!0,name:"Links",class:"mt-auto"},{name:"Docs",url:"https://coreui.io/angular/docs/",iconComponent:{name:"cil-description"},attributes:{target:"_blank"}}];var oe={BLACK:"black",POMEGRANATE:"pomegranate",KING_YNA:"king-yna",IBIZA_SUNSET:"ibiza-sunset",FLICKR:"flickr",PURPLE_BLISS:"purple-bliss",MAN_OF_STEEL:"man-of-steel",PURPLE_LOVE:"purple-love",PRIMARY:"primary",WHITE:"white"},Qe={LARGE:"sidebar-lg",MEDIUM:"sidebar-md",SMALL:"sidebar-sm"};var x={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},be=class be{constructor(e,r){this.http=e;this.router=r;this.apiURL=C.apiURL}getAllCustomers(){return this.http.get(this.apiURL+"customers",x)}addCustomer(e){return this.http.post(this.apiURL+"customers",e,x)}findCustomerById(e){return this.http.get(this.apiURL+"customers/"+e,x).pipe(p(r=>((r.status===401||r.status===403||r.status===0)&&this.router.navigate(["pages/login"]),I(()=>r))))}updateCustomer(e,r){return this.http.put(this.apiURL+"cust/"+r,e,x)}updateCustomerByKey(e,r){return this.http.put(this.apiURL+"customers/update/"+r,e,x)}updateCustomerTransporter(e,r){return this.http.put(this.apiURL+"custumm/"+r,e,x)}updateTransporter(e,r){return this.http.put(this.apiURL+"custom/"+r,e,x)}deleteCustomer(e){return this.http.get(this.apiURL+"customers/delate/"+e,x)}getAllTransporters(){return this.http.get(this.apiURL+"customer/Transporteur",x)}getAllExpeditors(){return this.http.get(this.apiURL+"customer/Expediteur",x)}getClientsByExpeditor(e){return this.http.get(this.apiURL+"getClientByExp/"+e,x)}getAllClients(){return this.http.get(this.apiURL+"getAllClients",x)}setClientToExpeditor(e,r){let t={idExp:r,idClient:e};return this.http.post(this.apiURL+"customers/setclient",t,x)}removeClientFromExpeditor(e,r){let t={idExp:r,idClient:e};return this.http.post(this.apiURL+"customers/removeClient",t,x)}disableCustomer(e){return this.http.get(this.apiURL+`customers/disable/${e}`,x)}updateCustomerColor(e,r){return this.http.put(`${this.apiURL}updateClientColor/${e}`,r,x).pipe(p(t=>(console.error("Error updating customer color:",t),I(()=>t))))}getDischargeLocationsByCustomer(e){let r={user_id:e};return this.http.post(this.apiURL+"getLienuxDecharByCustomer",r,x)}addDischargeLocation(e,r){let t={user_id:e,libelle:r};return this.http.post(this.apiURL+"addLieuxDechar",t,x)}};be.\u0275fac=function(r){return new(r||be)(g(y),g(ht))},be.\u0275prov=d({token:be,factory:be.\u0275fac,providedIn:"root"});var Et=be;var fe=class fe{constructor(){this.setConfigValue()}setConfigValue(){this.templateConf={layout:{variant:"Light",dir:"ltr",customizer:{hidden:!1},sidebar:{collapsed:!1,size:Qe.MEDIUM,backgroundColor:oe.WHITE,backgroundImage:!0,backgroundImageURL:"assets/img/sidebar-bg/01.jpg"}}}}updateConfig(e){this.templateConf=E(E({},this.templateConf),e)}getConfig(){return this.templateConf}resetConfig(){this.setConfigValue()}};fe.\u0275fac=function(r){return new(r||fe)},fe.\u0275prov=d({token:fe,factory:fe.\u0275fac,providedIn:"root"});var It=fe;var S={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},ye=class ye{constructor(e){this.http=e;this.apiURL=C.apiURL}addOrder(e){return this.http.post(this.apiURL+"commande",e,S)}addOrderObservable(e){return this.http.post(this.apiURL+"commande",e,S).pipe(p(this.handleError))}getAllRegisteredOrders(e){return this.http.get(this.apiURL+"getAllCommande/"+e,S)}getAllOrders(){return this.http.get(this.apiURL+"commande",S)}findOrder(e){return this.http.get(this.apiURL+"commande/"+e,S)}findRegisteredOrder(e){return this.http.get(this.apiURL+"commande/"+e,S)}updateOrder(e,r){return this.http.put(this.apiURL+"comm/update/"+r,e,S)}updateOrderNote(e,r){return this.http.put(this.apiURL+"comm/updatenote/"+r,e,S)}updateOrderVolume(e,r){let t=`${this.apiURL}commande/updateVolume/${e}`,n={volume:r};return this.http.put(t,n,S).pipe(p(this.handleError))}getAllOrdersByUser(e){return this.http.get(this.apiURL+"comm/"+e,S)}updateOrderExpeditor(e,r){return this.http.put(this.apiURL+"comm/up/"+r,e,S)}getAllOrdersByUserWithErrorHandling(e){return this.http.get(this.apiURL+"com/"+e,S).pipe(p(this.handleError))}getAllValidatedOrdersByTransporter(e){return this.http.get(this.apiURL+"comTrpValide/"+e,S).pipe(p(this.handleError))}getReservedOrders(){return this.http.get(this.apiURL+"comareserve/",S).pipe(p(this.handleError))}getAllReservedOrders(){return this.http.get(this.apiURL+"commande/Reserved/All",S)}findOrdersByExpeditor(e){return this.http.get(this.apiURL+"commande/exp/"+e,S)}updateOrderTransporter(e,r){return this.http.put(this.apiURL+"comande/"+r,e,S)}findAllReservedOrdersByTransporter(e){return this.http.get(this.apiURL+"commande/Reservees/"+e,S)}findReservedOrdersByExpeditor(e){return this.http.get(this.apiURL+"commande/Res/"+e,S)}deleteRegisteredOrder(e){return this.http.put(`${this.apiURL}comm/deleteEnr/${e}`,null,S).pipe(p(this.handleError))}deleteOrderWithLines(e){return this.http.delete(`${this.apiURL}commande/${e}`,S).pipe(p(this.handleError))}setCurrentOrderId(e){this.currentOrderId=e}getCurrentOrderId(){return this.currentOrderId}getAllOrdersByChef(e){return this.http.get(this.apiURL+`getAllCommandeByChef/${e}`,S).pipe(p(this.handleError))}getOrdersToValidateByChef(e){return this.http.get(this.apiURL+`getCommandeToValidateByChef/${e}`,S).pipe(p(this.handleError))}findRegisteredOrdersByChef(e){return this.http.get(this.apiURL+`findEnrgBychef/${e}`,S).pipe(p(this.handleError))}getAllValidatedOrdersByChef(e){return this.http.get(this.apiURL+`getAllComValidByChef/${e}`,S).pipe(p(this.handleError))}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};ye.\u0275fac=function(r){return new(r||ye)(g(y))},ye.\u0275prov=d({token:ye,factory:ye.\u0275fac,providedIn:"root"});var fn=ye;var w={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Ce=class Ce{constructor(e){this.http=e;this.apiURL=C.apiURL}addLine(e){let r=`${this.apiURL}ptchargement`;return this.http.post(r,e,w).pipe(p(this.handleError))}updateVolume(e,r){let t=`${this.apiURL}ptchargement/${e}`,n={volume:r};return this.http.put(t,n,w).pipe(p(this.handleError))}updateStatusValid(e){let r=`${this.apiURL}ptchargement/valid/${e}`;return this.http.put(r,null,w).pipe(p(this.handleError))}updateStatusNoPickup(e){let r=`${this.apiURL}ptchargement/updateStatusNoPickup/${e}`;return this.http.put(r,null,w).pipe(p(this.handleError))}updateStatusReserved(e){let r=`${this.apiURL}ptchargement/reserved`;return this.http.post(r,e,w).pipe(p(this.handleError))}findAllValid(){return this.http.get(this.apiURL+"ptchargement/valid/",w)}findReservedByDateAndTruck(e){let r=new Z;e.id_camion&&(r=r.set("id_camion",e.id_camion)),e.date&&(r=r.set("date",e.date)),e.id_conducteur&&(r=r.set("id_conducteur",e.id_conducteur));let t=this.apiURL+"pt/reserved";return this.http.get(t,{headers:w.headers,params:r})}findExpeditedByDateAndTruck(e){let r=new Z;e.id_camion&&(r=r.set("id_camion",e.id_camion)),e.date&&(r=r.set("date",e.date)),e.id_conducteur&&(r=r.set("id_conducteur",e.id_conducteur));let t=this.apiURL+"pt/expedied";return this.http.get(t,{headers:w.headers,params:r})}findLinesToAdjust(e){let r=new Z;e.id_camion&&(r=r.set("id_camion",e.id_camion)),e.date&&(r=r.set("date",e.date)),e.id_conducteur&&(r=r.set("id_conducteur",e.id_conducteur));let t=this.apiURL+"pt/findLineToAdjust";return this.http.get(t,{headers:w.headers,params:r})}updateReservationStatus(e,r){let t=`${this.apiURL}ptchargement/reservation/${e}`;return this.http.put(t,r,w).pipe(p(this.handleError))}updateExpeditionStatus(e){let r=`${this.apiURL}ptchargement/expedition/${e.id}`;return this.http.put(r,e,w).pipe(p(this.handleError))}updateDeliveryStatus(e){let r=`${this.apiURL}ptchargement/livred/${e}`;return this.http.put(r,null,w).pipe(p(this.handleError))}findDeliveredByDateAndClient(e){let r=new Z;e.date_debut&&(r=r.set("date_debut",e.date_debut)),e.date_fin&&(r=r.set("date_fin",e.date_fin)),e.id_client&&(r=r.set("id_client",e.id_client));let t=this.apiURL+"ligneCmd/toFac";return this.http.get(t,{headers:w.headers,params:r})}findDeliveredByDateAndType(e){let r=new Z;e.date_debut&&(r=r.set("date_debut",e.date_debut)),e.date_fin&&(r=r.set("date_fin",e.date_fin)),e.type_ligne&&(r=r.set("type_ligne",e.type_ligne));let t=this.apiURL+"ligneCmd/type";return this.http.get(t,{headers:w.headers,params:r})}updateClient(e){let r=`${this.apiURL}ptchargement/client/${e.id}`;return this.http.put(r,e,w).pipe(p(this.handleError))}findReservedByUser(e){return this.http.get(this.apiURL+`ligneCmdByDemand/${e}`,w)}findExpeditedByUser(e){return this.http.get(this.apiURL+`ligneCmdExpByDemand/${e}`,w)}findDeliveredByUser(e){return this.http.get(this.apiURL+`ligneCmdLivredByDemand/${e}`,w)}countValid(){return this.http.get(this.apiURL+"countValid",w)}countReserved(){return this.http.get(this.apiURL+"countReserved",w)}countExpedited(){return this.http.get(this.apiURL+"countExpedied",w)}countDelivered(){return this.http.get(this.apiURL+"countDelivred",w)}findVoyageListReserved(){return this.http.get(this.apiURL+"findVoyageList",w)}findVoyageListExpedited(){return this.http.get(this.apiURL+"findVoyageListExp",w)}findVoyageListToAdjust(){return this.http.get(this.apiURL+"findVoyageListToAdjust",w)}findToInspection(){return this.http.get(`${this.apiURL}findInspection`,w).pipe(p(this.handleError))}findAllToInspection(){return this.http.get(this.apiURL+"findAllToInspection",w).pipe(p(this.handleError))}updatePieceCegid(e,r){let t=`${this.apiURL}updatePieceCegid/${e}`,n={comment:r};return this.http.put(t,n,w).pipe(p(this.handleError))}findLinesPdfNotUpdated(){return this.http.get(this.apiURL+"FindLignepdfNotUpdated",w)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Ce.\u0275fac=function(r){return new(r||Ce)(g(y))},Ce.\u0275prov=d({token:Ce,factory:Ce.\u0275fac,providedIn:"root"});var yn=Ce;var F={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Oe=class Oe{constructor(e){this.http=e;this.apiURL=C.apiURL}getInvoice(e){return this.http.get(this.apiURL+"facture/"+e,F)}getInvoiceByExpeditor(e){return this.http.get(this.apiURL+"facturebyexpediteur/"+e,F)}getAllInvoices(e,r,t,n){let o=new Z().set("page",e.toString()).set("pageSize",r.toString());return t&&(o=o.set("id_client",t.toString())),n&&(o=o.set("invoice_date",n)),this.http.get(`${this.apiURL}allfacture/`,re(E({},F),{params:o}))}generateInvoice(e){return this.http.post(this.apiURL+"facture",e,F)}addInvoice(e){return this.http.post(this.apiURL+"facture",e,F)}searchInvoiceLines(e){return this.http.post(this.apiURL+"facture/search",e,F)}convertNumberToText(e){return this.http.post(this.apiURL+`convert/${e}`,null,F)}sendInvoiceEmail(e){let r=new FormData;return r.append("base64PNG",e),this.http.post(this.apiURL+"facture/sendFacture",r,F)}sendInvoice(e){return new Promise((r,t)=>{this.http.post(this.apiURL+"facture/sendFacture",e,F).toPromise().then(n=>{r(n)}).catch(n=>{t(n)})})}createInvoiceFlux(e){return this.http.post(this.apiURL+"createFactureFlux",e,F)}createInvoiceStorage(e){return this.http.post(this.apiURL+"createFactureEntreposage",e,F)}sendToSage(e){return this.http.post(this.apiURL+"sendToSage",e,F)}findInvoiceByDate(e){return this.http.post(this.apiURL+"findInvoiceByDate",e,F)}findInvoicesByClientAndDate(e,r,t,n){let o=new Z().set("id_client",e.toString()).set("invoiced_date",r).set("page",t.toString()).set("pageSize",n.toString());return this.http.get(`${this.apiURL}findFactureByClientAndDate`,re(E({},F),{params:o}))}};Oe.\u0275fac=function(r){return new(r||Oe)(g(y))},Oe.\u0275prov=d({token:Oe,factory:Oe.\u0275fac,providedIn:"root"});var Cn=Oe;var Xe={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},we=class we{constructor(e){this.http=e;this.apiURL=C.apiURL}addDriver(e){return this.http.post(this.apiURL+"conducteur",e,Xe)}getAllDriversByTransporter(e){return this.http.get(this.apiURL+"conducteurTr/"+e,Xe).pipe(p(this.handleError))}getAllDriversAdmin(){return this.http.get(this.apiURL+"conducteur",Xe)}blockDriver(e){return this.http.get(this.apiURL+"conducteur/conduceurBloqued/"+e,Xe)}getAllDrivers(){return this.http.get(this.apiURL+"conducteur/",Xe)}findDriverById(e){let r=`${this.apiURL}conducteur/${e}`;return this.http.get(r,Xe).pipe(p(this.handleError))}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};we.\u0275fac=function(r){return new(r||we)(g(y))},we.\u0275prov=d({token:we,factory:we.\u0275fac,providedIn:"root"});var On=we;var qe={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},_e=class _e{constructor(e){this.http=e;this.apiURL=C.apiURL}addTruck(e){return this.http.post(this.apiURL+"camion",e,qe)}getAllTrucks(){return this.http.get(this.apiURL+"camion",qe)}getTrucksByTransporter(e){return this.http.get(this.apiURL+"camionTr/"+e,qe).pipe(p(this.handleError))}deleteTruck(e){return this.http.get(this.apiURL+"camion/delate/"+e,qe)}findTruckById(e){return this.http.get(this.apiURL+"camion/"+e,qe)}getAllTrucksAdmin(){return this.http.get(this.apiURL+"camion",qe)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};_e.\u0275fac=function(r){return new(r||_e)(g(y))},_e.\u0275prov=d({token:_e,factory:_e.\u0275fac,providedIn:"root"});var wn=_e;var at={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Me=class Me{constructor(e){this.http=e;this.apiURL=C.apiURL}getAllCities(){return this.http.get(this.apiURL+"ville",at).pipe(p(this.handleError))}findRegionByCityId(e){return this.http.get(this.apiURL+"region/"+e,at)}addLoadingPoint(e){return this.http.post(this.apiURL+"ptchargement",e,at)}addChargingPoint(e){return this.http.post(this.apiURL+"ptchargement",e,at)}addChargingDischargingByOrderId(e){return this.http.get(this.apiURL+"ptchargDecharg/"+e,at)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Me.\u0275fac=function(r){return new(r||Me)(g(y))},Me.\u0275prov=d({token:Me,factory:Me.\u0275fac,providedIn:"root"});var _n=Me;var A={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Se=class Se{constructor(e){this.http=e;this.apiURL=C.apiURL}addDestination(e){return this.http.post(this.apiURL+"destination",e,A)}getDestinationByCityAndSupplier(e,r,t){return this.http.get(this.apiURL+`destination/${e}/${r}/${t}`,A)}findAllDestinationsWithType(){return this.http.get(this.apiURL+"findAllDestinationHaveType",A)}findByClientId(e){return this.http.get(this.apiURL+`destination/${e}`,A)}updateDestination(e,r){let t=`${this.apiURL}updateDestination/${e}`;return this.http.put(t,r,A).pipe(p(this.handleError))}deleteDestination(e){let r=`${this.apiURL}delete/${e}`;return this.http.delete(r,A).pipe(p(this.handleError))}setDestination(e){this.destination=e,console.log("Destination updated:",this.destination)}getDestination(){return console.log("Destination retrieved:",this.destination),this.destination}updateDestinationTypes(e,r){let t=`${this.apiURL}updateDestinationType/${e}`;return this.http.put(t,r,A).pipe(p(this.handleError))}findDestinationById(e){return this.http.get(this.apiURL+`findDestinationById/${e}`,A).pipe(p(this.handleError))}findDestinationsByCompany(e){return this.http.get(this.apiURL+`findDestinationByCompany/${e}`,A)}assignDestinationToUser(e,r){return this.http.post(this.apiURL+`assignDestinationToUser/${e}`,r,A)}findDestinationsByUser(e){return this.http.get(this.apiURL+`findDestinationByUser/${e}`,A)}deleteDestinationByUser(e){return this.http.get(this.apiURL+`deleteDestinationByUser/${e}`,A)}findAllWarehouses(){return this.http.get(this.apiURL+"findAllWarhouses",A)}findWarehousesByBrand(e,r){return this.http.get(this.apiURL+`findWarhousesByBrand/${e}/${r}`,A)}addWarehouse(e){return this.http.post(this.apiURL+"addWarehouse",e,A)}disableWarehouse(e){return this.http.put(this.apiURL+`disabledDepot/${e}`,null,A)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Se.\u0275fac=function(r){return new(r||Se)(g(y))},Se.\u0275prov=d({token:Se,factory:Se.\u0275fac,providedIn:"root"});var Mn=Se;var Nt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Pe=class Pe{constructor(e){this.http=e;this.apiURL=C.apiURL}cancelVoyage(e){return this.http.put(`${this.apiURL}cancelVoyage/${e}`,null,Nt)}searchVoyageByDate(e){return this.http.post(`${this.apiURL}voyages`,e,Nt)}updateVoyageToAdjust(e){return this.http.get(`${this.apiURL}updateVoyageToAdjust?id_voyage=${e}`,Nt)}};Pe.\u0275fac=function(r){return new(r||Pe)(g(y))},Pe.\u0275prov=d({token:Pe,factory:Pe.\u0275fac,providedIn:"root"});var Sn=Pe;var Rt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Ee=class Ee{constructor(e){this.http=e;this.apiURL=C.apiURL}addCircuit(e){return this.http.post(this.apiURL+"circuit",e,Rt).pipe(p(this.handleError))}getAllCircuits(){return this.http.get(this.apiURL+"circuit",Rt).pipe(p(this.handleError))}adjustVoyage(e){return this.http.post(this.apiURL+"circuit/adjustVoyage",e,Rt).pipe(p(this.handleError))}updateCircuitById(e,r){let t=`${this.apiURL}circuit/${e}`;return this.http.put(t,r,Rt).pipe(p(this.handleError))}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Ee.\u0275fac=function(r){return new(r||Ee)(g(y))},Ee.\u0275prov=d({token:Ee,factory:Ee.\u0275fac,providedIn:"root"});var Pn=Ee;var de={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Ie=class Ie{constructor(e){this.http=e;this.apiURL=C.apiURL}sendExpeditionMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailExpedition`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendDeliveryMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailLivraison`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendReservationMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailReservation`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendMessage(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}messages`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendCancellationMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailAnnulation`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendReservationDepartureUpdateMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailUpdateReservationDepart`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendReservationArrivalUpdateMail(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}mailUpdateReservationArrivee`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}sendMailsFromFrontend(e){return new Promise((r,t)=>{this.http.post(`${this.apiURL}sendMailsFromFront`,e,de).toPromise().then(n=>r(n)).catch(n=>t(n))})}};Ie.\u0275fac=function(r){return new(r||Ie)(g(y))},Ie.\u0275prov=d({token:Ie,factory:Ie.\u0275fac,providedIn:"root"});var En=Ie;var Lt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Re=class Re{constructor(e){this.http=e;this.apiURL=C.apiURL}getNotificationsByUser(e){return this.http.get(`${this.apiURL}notifications/${e}`,Lt)}markAsRead(e){return this.http.put(`${this.apiURL}notifications/read/${e}`,null,Lt)}createNotification(e){return this.http.post(`${this.apiURL}notifications`,e,Lt)}deleteNotification(e){return this.http.delete(`${this.apiURL}notifications/${e}`,Lt)}};Re.\u0275fac=function(r){return new(r||Re)(g(y))},Re.\u0275prov=d({token:Re,factory:Re.\u0275fac,providedIn:"root"});var In=Re;var Wt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Le=class Le{constructor(e){this.http=e;this.apiURL=C.apiURL}addComment(e){return this.http.post(this.apiURL+"commentaire",e,Wt).pipe(p(this.handleError))}findCommentsByLine(e){return this.http.get(this.apiURL+`commentaire/${e}`,Wt).pipe(p(this.handleError))}findAllComments(e,r){let t=new Z().set("page",e.toString()).set("limit",r.toString());return this.http.get(this.apiURL+"findAllComments",E({params:t},Wt))}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Le.\u0275fac=function(r){return new(r||Le)(g(y))},Le.\u0275prov=d({token:Le,factory:Le.\u0275fac,providedIn:"root"});var Rn=Le;var Yt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},De=class De{constructor(e){this.http=e;this.apiURL=C.apiURL}uploadPdf(e,r){let t=new FormData;return t.append("pdfAttachment",e,r),this.http.post(`${this.apiURL}pdf/upload-pdf`,t,Yt)}generatePdfFromHtml(e){let r={htmlContent:e};return this.http.post(`${this.apiURL}pdf/generate`,r,Yt)}downloadPdf(e){return this.http.get(`${this.apiURL}pdf/download/${e}`,re(E({},Yt),{responseType:"blob"}))}};De.\u0275fac=function(r){return new(r||De)(g(y))},De.\u0275prov=d({token:De,factory:De.\u0275fac,providedIn:"root"});var Ln=De;var ze={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},xe=class xe{constructor(e){this.http=e;this.apiURL=C.apiURL}addMerchandiseType(e){let r={nom_marchandise:e};return this.http.post(this.apiURL+"marchandise",r,ze)}getAllMerchandiseTypes(){return this.http.get(this.apiURL+"marchandise",ze)}deleteMerchandiseType(e){let r=`${this.apiURL}marchandise/${e}`;return this.http.delete(r,ze).pipe(p(this.handleError))}addConditionType(e){let r={nom_condition:e};return this.http.post(this.apiURL+"condition",r,ze)}getAllConditionTypes(){return this.http.get(this.apiURL+"condition",ze)}deleteConditionType(e){let r=`${this.apiURL}condition/${e}`;return this.http.delete(r,ze).pipe(p(this.handleError))}getAllOrderTypes(){return this.http.get(this.apiURL+"typecmd",ze)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};xe.\u0275fac=function(r){return new(r||xe)(g(y))},xe.\u0275prov=d({token:xe,factory:xe.\u0275fac,providedIn:"root"});var Dn=xe;var Ii={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Ue=class Ue{constructor(e){this.http=e;this.apiURL=C.apiURL}getAllCompanies(){return this.http.get(this.apiURL+"entreprises/",Ii)}};Ue.\u0275fac=function(r){return new(r||Ue)(g(y))},Ue.\u0275prov=d({token:Ue,factory:Ue.\u0275fac,providedIn:"root"});var zn=Ue;var Gt={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},ke=class ke{constructor(e){this.http=e;this.apiURL=C.apiURL}getComplaintsByExpeditor(e){return this.http.get(this.apiURL+"reclamation/expediteur/"+e,Gt)}getComplaintsByTransporter(e){return this.http.get(this.apiURL+"reclamation/transporteur/"+e,Gt)}getAllComplaints(){return this.http.get(this.apiURL+"reclamations",Gt)}};ke.\u0275fac=function(r){return new(r||ke)(g(y))},ke.\u0275prov=d({token:ke,factory:ke.\u0275fac,providedIn:"root"});var xn=ke;var ee={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Te=class Te{constructor(e){this.http=e;this.apiURL=C.apiURL}addSupplier(e){return this.http.post(this.apiURL+"fournisseur",e,ee)}getAllSuppliers(){return this.http.get(this.apiURL+"fournisseur",ee)}findSupplierByMatricule(e){let r=encodeURIComponent(e),t=`${this.apiURL}fournisseur/${r}`;return this.http.get(t,ee)}findSupplierByType(e){let r=`${this.apiURL}getFou/${e}`;return this.http.get(r,ee)}updateSupplier(e,r){let t=`${this.apiURL}fournisseur/${e}`;return this.http.put(t,r,ee).pipe(p(this.handleError))}findSuppliersWithStorage(){return this.http.get(this.apiURL+"findFournisseurWhithEntreposage",ee)}findSuppliersWithFlux(){return this.http.get(this.apiURL+"findFournisseurWhithFLux",ee)}findSupplierById(e){return this.http.get(this.apiURL+`fournisseurById/${e}`,ee)}findClientFromSageByMatricule(e){return this.http.get(this.apiURL+`findClientFromSageByMat/${e}`,ee)}findClientsToInvoiceTransport(){return this.http.get(this.apiURL+"findClientToInvoice",ee)}findInvoicedClients(){return this.http.get(this.apiURL+"findClientInvoiced",ee)}handleError(e){return console.error("An error occurred:",e),I(()=>new Error(e.message||"Server error"))}};Te.\u0275fac=function(r){return new(r||Te)(g(y))},Te.\u0275prov=d({token:Te,factory:Te.\u0275fac,providedIn:"root"});var Un=Te;var Ri={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},Ae=class Ae{constructor(e){this.http=e;this.apiURL=C.apiURL}getAllBillingTypes(){return this.http.get(`${this.apiURL}typeFacturation`,Ri)}};Ae.\u0275fac=function(r){return new(r||Ae)(g(y))},Ae.\u0275prov=d({token:Ae,factory:Ae.\u0275fac,providedIn:"root"});var kn=Ae;var Li=["customizer"],Di=i=>({open:i}),ne=i=>({selected:i}),Ke=class Ke{constructor(e,r,t,n,o){this.renderer=e;this.layoutService=r;this.configService=t;this.customerService=n;this.sessionStorageService=o;this.directionEvent=new ue;this.isOpenSubject=new lt(!1);this.isOpen$=this.isOpenSubject.asObservable();this.options={direction:"ltr",bgColor:oe.BLACK,bgImage:"assets/img/sidebar-bg/01.jpg",bgImageDisplay:!0,compactMenu:!1,sidebarSize:Qe.MEDIUM};this.size=Qe.MEDIUM;this.isOpen=!0;this.config={};this.isBgImageDisplay=!0;this.selectedBgColor=oe.BLACK;this.selectedBgImage="assets/img/sidebar-bg/01.jpg";this.SIDEBAR_COLORS=oe;this.SIDEBAR_SIZES=Qe;this.layoutSub=this.layoutService.customizerChangeEmitted$.subscribe(a=>{typeof a=="object"&&a&&a.bgColor&&(this.selectedBgColor=a.bgColor,this.selectedBgImage=a.bgImage)})}ngOnInit(){let e=sessionStorage.getItem("color")!=="null"?sessionStorage.getItem("color"):oe.PRIMARY;e&&this.changeSidebarBgColor(e),this.layoutService.customizerChangeEmitted$.subscribe(r=>{typeof r=="string"&&r==="toggle"&&(this.isOpen=!this.isOpen)}),this.config=this.configService.getConfig(),this.isOpen=!this.config.layout.customizer.hidden,this.config.layout.sidebar.size&&(this.options.sidebarSize=this.config.layout.sidebar.size,this.size=this.config.layout.sidebar.size),setTimeout(()=>{e&&this.changeSidebarBgColor(e)},100)}ngAfterViewInit(){setTimeout(()=>{this.options.bgColor=oe.BLACK,this.selectedBgColor=oe.BLACK,this.isBgImageDisplay&&(this.options.bgImageDisplay=!0)},0)}ngOnDestroy(){this.layoutSub&&this.layoutSub.unsubscribe()}sendOptions(){this.directionEvent.emit(this.options),this.layoutService.emitChange(this.options)}bgImageDisplay(e){e.target.checked?(this.options.bgImageDisplay=!0,this.isBgImageDisplay=!0):(this.options.bgImageDisplay=!1,this.isBgImageDisplay=!1),this.layoutService.emitCustomizerChange(this.options)}toggleCompactMenu(e){e.target.checked?this.options.compactMenu=!0:this.options.compactMenu=!1,this.layoutService.emitCustomizerChange(this.options)}changeSidebarWidth(e){this.options.sidebarSize=e,this.size=e,this.layoutService.emitCustomizerChange(this.options)}toggleCustomizer(){this.isOpen?(this.renderer.removeClass(this.customizer.nativeElement,"open"),this.isOpen=!1):(this.renderer.addClass(this.customizer.nativeElement,"open"),this.isOpen=!0)}closeCustomizer(){this.renderer.removeClass(this.customizer.nativeElement,"open"),this.updateClientColor(),this.isOpen=!1}changeSidebarBgColor(e){this.selectedBgColor=e,this.options.bgColor=e,this.isBgImageDisplay&&(this.options.bgImageDisplay=!0),this.layoutService.emitCustomizerChange(this.options)}changeSidebarBgImage(e){this.selectedBgImage=e,this.options.bgImage=e,this.isBgImageDisplay&&(this.options.bgImageDisplay=!0),this.layoutService.emitCustomizerChange(this.options)}onClickOutside(e){!this.isOpen||this.isDescendant(e.target,"customizer")||(this.updateClientColor(),this.isOpen=!1)}isDescendant(e,r){for(;e;){if(e.id===r)return!0;e=e.parentNode}return!1}toggle(){this.isOpenSubject.next(!this.isOpenSubject.value)}updateClientColor(){let e=sessionStorage.getItem("color");if(this.options.bgColor!==e){sessionStorage.setItem("color",this.options.bgColor);let r=this.sessionStorageService.getSession();if(r?.iduser){let t={color:this.options.bgColor};this.customerService.updateCustomerColor(r.iduser.toString(),t).subscribe({next:n=>{console.log("Color updated successfully:",n)},error:n=>{console.error("Error updating color:",n)}})}}}};Ke.\u0275fac=function(r){return new(r||Ke)(c(je),c(le),c(It),c(Et),c(qr))},Ke.\u0275cmp=Y({type:Ke,selectors:[["app-customizer-theme"]],viewQuery:function(r,t){if(r&1&&G(Li,5),r&2){let n;V(n=N())&&(t.customizer=n.first)}},hostBindings:function(r,t){r&1&&M("dblclick",function(o){return t.onClickOutside(o)},cr)},outputs:{directionEvent:"directionEvent"},decls:67,vars:38,consts:[["customizer",""],["width",""],[1,"customizer","border-left-blue-grey","border-left-lighten-4","d-none","d-sm-none","d-md-block",3,"ngClass"],[1,"customizer-close",3,"click"],["cIcon","","name","cilX","size","lg"],["id","customizer-toggle-icon",1,"customizer-toggle","bg-danger","d-none",3,"click"],["cIcon","","name","cilSettings","size","lg",1,"fa-spin"],[1,"customizer-content","p-3","ps-container","ps-theme-dark","text-left"],[1,"text-uppercase","mb-0","text-bold-400"],[1,"text-center","text-bold-500","mb-3","text-uppercase","sb-options"],[1,"cz-bg-color","sb-color-options"],[1,"row","p-1"],[1,"col"],["data-bg-color","pomegranate",1,"gradient-pomegranate","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","king-yna",1,"gradient-king-yna","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","ibiza-sunset",1,"gradient-ibiza-sunset","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","flickr",1,"gradient-flickr","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","purple-bliss",1,"gradient-purple-bliss","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","man-of-steel",1,"gradient-man-of-steel","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","purple-love",1,"gradient-purple-love","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","black",1,"bg-black","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","white",1,"bg-grey","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],["data-bg-color","primary",1,"bg-primary","d-block","rounded-circle",2,"width","20px","height","20px",3,"click","ngClass"],[1,"togglebutton","toggle-sb-bg-img"],[1,"switch","switch","border-0","d-flex","justify-content-between","w-100"],[1,"float-right"],[1,"custom-control","custom-checkbox","mb-2","mr-sm-2","mb-sm-0"],["type","checkbox","id","sidebar-bg-img",1,"custom-control-input","cz-bg-image-display",3,"change","checked"],["for","sidebar-bg-img",1,"custom-control-label","d-block"],[1,"togglebutton"],["type","checkbox","id","cz-compact-menu",1,"custom-control-input","cz-compact-menu",3,"change","checked"],["for","cz-compact-menu",1,"custom-control-label","d-block"],["for","cz-sidebar-width"],["id","cz-sidebar-width",1,"custom-select","cz-sidebar-width","float-right",3,"change"],["value","sidebar-sm",3,"selected"],["value","sidebar-md",3,"selected"],["value","sidebar-lg",3,"selected"]],template:function(r,t){if(r&1){let n=se();s(0,"div",2,0)(2,"a",3),M("click",function(){return R(n),L(t.closeCustomizer())}),P(),u(3,"svg",4),l(),D(),s(4,"a",5),M("click",function(){return R(n),L(t.toggleCustomizer())}),P(),u(5,"svg",6),l(),D(),s(6,"div",7)(7,"h4",8),b(8,"Theme Customizer"),l(),s(9,"p"),b(10,"Customize & Preview in Real Time"),l(),u(11,"hr"),s(12,"h6",9),b(13,"Sidebar Color Options"),l(),s(14,"div",10)(15,"div",11)(16,"div",12)(17,"span",13),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.POMEGRANATE))}),l()(),s(18,"div",12)(19,"span",14),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.KING_YNA))}),l()(),s(20,"div",12)(21,"span",15),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.IBIZA_SUNSET))}),l()(),s(22,"div",12)(23,"span",16),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.FLICKR))}),l()(),s(24,"div",12)(25,"span",17),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.PURPLE_BLISS))}),l()(),s(26,"div",12)(27,"span",18),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.MAN_OF_STEEL))}),l()(),s(28,"div",12)(29,"span",19),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.PURPLE_LOVE))}),l()()(),s(30,"div",11)(31,"div",12)(32,"span",20),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.BLACK))}),l()(),s(33,"div",12)(34,"span",21),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.WHITE))}),l()(),s(35,"div",12)(36,"span",22),M("click",function(){return R(n),L(t.changeSidebarBgColor(t.SIDEBAR_COLORS.PRIMARY))}),l()()()(),u(37,"hr"),s(38,"div",23)(39,"div",24)(40,"span"),b(41,"Sidebar Bg Image"),l(),s(42,"div",25)(43,"div",26)(44,"input",27),M("change",function(a){return R(n),L(t.bgImageDisplay(a))}),l(),u(45,"label",28),l()()(),u(46,"hr"),l(),s(47,"div",29)(48,"div",24)(49,"span"),b(50,"Compact Menu"),l(),s(51,"div",25)(52,"div",26)(53,"input",30),M("change",function(a){return R(n),L(t.toggleCompactMenu(a))}),l(),u(54,"label",31),l()()()(),u(55,"hr"),s(56,"div")(57,"label",32),b(58,"Sidebar Width"),l(),s(59,"select",33,1),M("change",function(){R(n);let a=ge(60);return L(t.changeSidebarWidth(a.value))}),s(61,"option",34),b(62,"Small"),l(),s(63,"option",35),b(64,"Medium"),l(),s(65,"option",36),b(66,"Large"),l()()()()()}r&2&&(h("ngClass",Q(16,Di,t.isOpen)),m(17),h("ngClass",Q(18,ne,t.selectedBgColor===t.SIDEBAR_COLORS.POMEGRANATE)),m(2),h("ngClass",Q(20,ne,t.selectedBgColor===t.SIDEBAR_COLORS.KING_YNA)),m(2),h("ngClass",Q(22,ne,t.selectedBgColor===t.SIDEBAR_COLORS.IBIZA_SUNSET)),m(2),h("ngClass",Q(24,ne,t.selectedBgColor===t.SIDEBAR_COLORS.FLICKR)),m(2),h("ngClass",Q(26,ne,t.selectedBgColor===t.SIDEBAR_COLORS.PURPLE_BLISS)),m(2),h("ngClass",Q(28,ne,t.selectedBgColor===t.SIDEBAR_COLORS.MAN_OF_STEEL)),m(2),h("ngClass",Q(30,ne,t.selectedBgColor===t.SIDEBAR_COLORS.PURPLE_LOVE)),m(3),h("ngClass",Q(32,ne,t.selectedBgColor===t.SIDEBAR_COLORS.BLACK)),m(2),h("ngClass",Q(34,ne,t.selectedBgColor===t.SIDEBAR_COLORS.WHITE)),m(2),h("ngClass",Q(36,ne,t.selectedBgColor===t.SIDEBAR_COLORS.PRIMARY)),m(8),h("checked",t.isBgImageDisplay),m(9),h("checked",t.config.layout.sidebar.collapsed),m(8),h("selected",t.size==="sidebar-sm"),m(2),h("selected",t.size==="sidebar-md"),m(2),h("selected",t.size==="sidebar-lg"))},dependencies:[Ve,wr,en,Zr,Jr,Ne],styles:[".customizer[_ngcontent-%COMP%]{width:400px;right:-400px;padding:0;background-color:var(--cui-body-bg, #fff);color:var(--cui-body-color, #000);z-index:1051;position:fixed;top:0;bottom:0;height:100vh;transition:right .4s cubic-bezier(.05,.74,.2,.99);backface-visibility:hidden;border-left:1px solid var(--cui-border-color, rgba(0, 0, 0, .05));box-shadow:0 0 8px #0000001a}.customizer.open[_ngcontent-%COMP%]{right:0}.customizer[_ngcontent-%COMP%]   .customizer-content[_ngcontent-%COMP%]{position:relative;height:100%;overflow-y:auto}.customizer[_ngcontent-%COMP%]   a.customizer-toggle[_ngcontent-%COMP%]{background:#fff;color:var(--cui-primary);display:block;box-shadow:-3px 0 8px #0000001a}.customizer[_ngcontent-%COMP%]   a.customizer-close[_ngcontent-%COMP%]{color:var(--cui-body-color, #000)}.customizer[_ngcontent-%COMP%]   .customizer-close[_ngcontent-%COMP%]{position:absolute;right:10px;top:10px;padding:7px;width:auto;z-index:10;cursor:pointer}.customizer[_ngcontent-%COMP%]   .customizer-toggle[_ngcontent-%COMP%]{position:absolute;top:35%;width:54px;height:50px;left:-54px;text-align:center;line-height:50px;cursor:pointer}.customizer[_ngcontent-%COMP%]   .color-options[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{white-space:pre}.customizer[_ngcontent-%COMP%]   .cz-bg-color[_ngcontent-%COMP%]{margin:0 auto}.customizer[_ngcontent-%COMP%]   .cz-bg-color[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover{cursor:pointer}.customizer[_ngcontent-%COMP%]   .cz-bg-color[_ngcontent-%COMP%]   span.white[_ngcontent-%COMP%]{color:#ddd!important}.customizer[_ngcontent-%COMP%]   .cz-bg-color[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%], .customizer[_ngcontent-%COMP%]   .cz-tl-bg-color[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]{box-shadow:0 0 10px 3px var(--cui-primary);border:3px solid #fff}.customizer[_ngcontent-%COMP%]   .cz-bg-image[_ngcontent-%COMP%]:hover{cursor:pointer}.customizer[_ngcontent-%COMP%]   .cz-bg-image[_ngcontent-%COMP%]   img.rounded[_ngcontent-%COMP%]{border-radius:1rem!important;border:2px solid #e6e6e6;height:100px;width:50px}.customizer[_ngcontent-%COMP%]   .cz-bg-image[_ngcontent-%COMP%]   img.rounded.selected[_ngcontent-%COMP%]{border:2px solid var(--cui-success)}.customizer[_ngcontent-%COMP%]   .tl-color-options[_ngcontent-%COMP%]{display:none}.customizer[_ngcontent-%COMP%]   .cz-tl-bg-image[_ngcontent-%COMP%]   img.rounded[_ngcontent-%COMP%]{border-radius:1rem!important;border:2px solid #e6e6e6;height:100px;width:70px}.customizer[_ngcontent-%COMP%]   .cz-tl-bg-image[_ngcontent-%COMP%]   img.rounded.selected[_ngcontent-%COMP%]{border:2px solid var(--cui-success)}.customizer[_ngcontent-%COMP%]   .cz-tl-bg-image[_ngcontent-%COMP%]   img.rounded[_ngcontent-%COMP%]:hover, .customizer[_ngcontent-%COMP%]   .cz-bg-color[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]   span.rounded-circle[_ngcontent-%COMP%]:hover, .customizer[_ngcontent-%COMP%]   .cz-tl-bg-color[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]   span.rounded-circle[_ngcontent-%COMP%]:hover{cursor:pointer}.customizer[_ngcontent-%COMP%]   .togglebutton[_ngcontent-%COMP%]   .switch[_ngcontent-%COMP%]   .custom-control[_ngcontent-%COMP%]   .custom-control-input[_ngcontent-%COMP%]:checked ~ .custom-control-label[_ngcontent-%COMP%]:before{background-color:var(--cui-primary);border-color:var(--cui-primary)}.customizer[_ngcontent-%COMP%]   .custom-select[_ngcontent-%COMP%]{border:1px solid #ced4da;border-radius:.25rem}.customizer[_ngcontent-%COMP%]   .custom-select[_ngcontent-%COMP%]:focus{border-color:var(--cui-primary);box-shadow:0 0 0 .2rem rgba(var(--cui-primary-rgb),.25)}.customizer[_ngcontent-%COMP%]   .layout-switch[_ngcontent-%COMP%]   .custom-control-inline[_ngcontent-%COMP%]{margin-right:1rem}[dir=rtl]   [_nghost-%COMP%]     .customizer{left:-400px;right:auto;border-right:1px solid rgba(0,0,0,.05);border-left:0px}[dir=rtl]   [_nghost-%COMP%]     .customizer.open{left:0;right:auto}[dir=rtl]   [_nghost-%COMP%]     .customizer .customizer-close{left:10px;right:auto}[dir=rtl]   [_nghost-%COMP%]     .customizer .customizer-toggle{right:-54px;left:auto}@media (max-width: 768px){.customizer[_ngcontent-%COMP%]{display:none!important}}.gradient-pomegranate[_ngcontent-%COMP%]{background:linear-gradient(45deg,#c0392b,#e74c3c)}.gradient-king-yna[_ngcontent-%COMP%]{background:linear-gradient(45deg,#f39c12,#f1c40f)}.gradient-ibiza-sunset[_ngcontent-%COMP%]{background:linear-gradient(45deg,#e67e22,#f39c12)}.gradient-flickr[_ngcontent-%COMP%]{background:linear-gradient(45deg,#e91e63,#9c27b0)}.gradient-purple-bliss[_ngcontent-%COMP%]{background:linear-gradient(45deg,#9b59b6,#8e44ad)}.gradient-man-of-steel[_ngcontent-%COMP%]{background:linear-gradient(45deg,#34495e,#2c3e50)}.gradient-purple-love[_ngcontent-%COMP%]{background:linear-gradient(45deg,#8e44ad,#9b59b6)}"]});var Dt=Ke;var zi=["sidebarBgImage"],xi=()=>[],Ui=()=>({icon:!1});function ki(i,e){i&1&&(s(0,"c-sidebar-footer",9),u(1,"button",17),l())}var Ze=class Ze{constructor(e,r){this.layoutService=e;this.renderer=r;this.navItems=[...bn];this.layoutSubscription=new st}ngOnInit(){this.layoutSubscription.add(this.layoutService.customizerChangeEmitted$.subscribe(e=>{typeof e=="object"&&e&&this.applyThemeChanges(e)}))}ngOnDestroy(){this.layoutSubscription.unsubscribe()}toggleCustomizer(){this.layoutService.emitCustomizerChange("toggle")}applyThemeChanges(e){let r=document.body,t=document.getElementById("sidebar1");t&&(e.bgColor&&(t.setAttribute("data-coreui-theme","dark"),t.setAttribute("data-background-color",e.bgColor),t.style.setProperty("--cui-sidebar-bg",e.bgColor)),e.bgImage&&e.bgImageDisplay?this.applySidebarBackgroundImage(t,e.bgImage):this.removeSidebarBackgroundImage(t),e.sidebarSize&&(r.classList.remove("sidebar-sm","sidebar-md","sidebar-lg"),r.classList.add(e.sidebarSize)),e.compactMenu!==void 0&&(e.compactMenu?t.classList.add("sidebar-narrow"):t.classList.remove("sidebar-narrow")))}applySidebarBackgroundImage(e,r){let t=e.querySelector(".sidebar-background");t&&t.remove();let n=this.renderer.createElement("div");this.renderer.addClass(n,"sidebar-background"),this.renderer.setStyle(n,"background-image",`url("${r}")`),this.renderer.setStyle(n,"position","absolute"),this.renderer.setStyle(n,"top","0"),this.renderer.setStyle(n,"left","0"),this.renderer.setStyle(n,"width","100%"),this.renderer.setStyle(n,"height","100%"),this.renderer.setStyle(n,"background-size","cover"),this.renderer.setStyle(n,"background-position","center"),this.renderer.setStyle(n,"background-repeat","no-repeat"),this.renderer.setStyle(n,"z-index","-1"),this.renderer.setStyle(n,"opacity","0.1"),this.renderer.appendChild(e,n)}removeSidebarBackgroundImage(e){let r=e.querySelector(".sidebar-background");r&&r.remove()}};Ze.\u0275fac=function(r){return new(r||Ze)(c(le),c(je))},Ze.\u0275cmp=Y({type:Ze,selectors:[["app-dashboard"]],viewQuery:function(r,t){if(r&1&&G(zi,5),r&2){let n;V(n=N())&&(t.sidebarBgImage=n.first)}},decls:20,vars:8,consts:[["sidebar1","cSidebar"],["sidebarBgImage",""],["colorScheme","dark","id","sidebar1","visible","",1,"d-print-none","sidebar","sidebar-fixed","border-end"],[1,"border-bottom"],[3,"routerLink"],["cIcon","","height","32","name","logo","title","CoreUI Logo",1,"sidebar-brand-full"],["cIcon","","height","32","name","signet","title","CoreUI Logo",1,"sidebar-brand-narrow"],["pointerEventsMethod","scrollbar","visibility","hover"],["dropdownMode","close","compact","",3,"navItems"],["cSidebarToggle","sidebar1","toggle","unfoldable",1,"border-top","d-none","d-lg-flex",2,"cursor","pointer"],[1,"sidebar-background"],[1,"wrapper","d-flex","flex-column","min-vh-100"],["position","sticky","sidebarId","sidebar1",1,"mb-4","d-print-none","header","header-sticky","p-0","shadow-sm",3,"cShadowOnScroll"],[1,"body","flex-grow-1"],["breakpoint","lg",1,"h-auto","px-4"],[1,"customizer-toggle-btn",3,"click"],["cIcon","","name","cilSettings","size","lg",1,"fa-spin"],["cSidebarToggler","","aria-label","Toggle sidebar fold"]],template:function(r,t){if(r&1){let n=se();s(0,"c-sidebar",2,0)(2,"c-sidebar-header",3)(3,"c-sidebar-brand",4),P(),u(4,"svg",5)(5,"svg",6),l()(),D(),s(6,"ng-scrollbar",7),u(7,"c-sidebar-nav",8),l(),dr(8,ki,2,0,"c-sidebar-footer",9),u(9,"div",10,1),l(),s(11,"div",11),u(12,"app-default-header",12),s(13,"div",13)(14,"c-container",14),u(15,"router-outlet"),l()(),u(16,"app-default-footer"),l(),u(17,"app-customizer-theme"),s(18,"div",15),M("click",function(){return R(n),L(t.toggleCustomizer())}),P(),u(19,"svg",16),l()}if(r&2){let n=ge(1);m(3),h("routerLink",nt(6,xi)),m(),me(nt(7,Ui)),m(3),h("navItems",t.navItems),m(),ur(n.narrow?-1:8),m(4),h("cShadowOnScroll","sm")}},dependencies:[Nr,Gr,Wr,Xr,Qr,yt,Yr,ft,Ct,Ot,Ne,vn,Sr,vt,Er,Ve,Dt],styles:["[_nghost-%COMP%]  .ng-scrollbar{--scrollbar-padding: 1px;--scrollbar-size: 5px;--scrollbar-thumb-color: var(--cui-gray-500, #aab3c5);--scrollbar-thumb-hover-color: var(--cui-gray-400, #cfd4de);--scrollbar-hover-size: calc(var(--scrollbar-size) * 1.5);--scrollbar-border-radius: 5px}[_nghost-%COMP%]  .ng-scroll-content{display:flex;min-height:100%}.customizer-toggle-btn[_ngcontent-%COMP%]{position:fixed;top:50%;right:0;transform:translateY(-50%);background:var(--cui-primary);color:#fff;width:50px;height:50px;border-radius:50% 0 0 50%;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:1050;box-shadow:-2px 0 8px #00000026;transition:all .3s ease}.customizer-toggle-btn[_ngcontent-%COMP%]:hover{background:var(--cui-primary-dark, #0056b3);transform:translateY(-50%) translate(-5px)}.customizer-toggle-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:18px}[_nghost-%COMP%]  .layout-dark{--cui-body-bg: #1e1e2f;--cui-body-color: #ffffff}[_nghost-%COMP%]  .layout-transparent{--cui-body-bg: transparent}[_nghost-%COMP%]  .sidebar-sm .sidebar{width:200px}[_nghost-%COMP%]  .sidebar-lg .sidebar{width:300px}@media (max-width: 768px){.customizer-toggle-btn[_ngcontent-%COMP%]{display:none}}"]});var An=Ze;export{Dt as CustomizerThemeComponent,Ct as DefaultFooterComponent,Ot as DefaultHeaderComponent,An as DefaultLayoutComponent};
