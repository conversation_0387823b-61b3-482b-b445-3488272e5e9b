{"version": 3, "file": "applicationTokenCertificateCredentials.d.ts", "sourceRoot": "", "sources": ["../../../lib/credentials/applicationTokenCertificateCredentials.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,+BAA+B,EAAE,MAAM,mCAAmC,CAAC;AACpF,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAC;AACvD,OAAO,EAAiB,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACrE,OAAO,EAAE,aAAa,EAAiB,UAAU,EAAE,MAAM,WAAW,CAAC;AACrE,OAAO,EAAE,4BAA4B,EAAE,MAAM,UAAU,CAAC;AAExD,qBAAa,sCAAuC,SAAQ,+BAA+B;IACzF,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAC7B,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAE5B;;;;;;;;;;;;;OAaG;gBAED,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,WAAW,EAAE,MAAM,EACnB,UAAU,EAAE,MAAM,EAClB,aAAa,CAAC,EAAE,aAAa,EAC7B,WAAW,CAAC,EAAE,WAAW,EACzB,UAAU,CAAC,EAAE,UAAU;IAczB;;;OAGG;IACU,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC;IA6B/C;;;;;;;;;;;;;OAaG;WACW,MAAM,CAClB,QAAQ,EAAE,MAAM,EAChB,2BAA2B,EAAE,MAAM,EACnC,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,4BAA4B,GACpC,sCAAsC;CA4B1C"}