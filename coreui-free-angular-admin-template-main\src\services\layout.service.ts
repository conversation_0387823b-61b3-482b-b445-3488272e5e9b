import { Injectable } from '@angular/core';
import { Subject, Observable } from 'rxjs';
import { CustomizerOptions } from '../models/theme.interfaces';

/**
 * Layout Service
 * Handles theme customization events and communication between components
 * Based on zen-logistic implementation with RxJS best practices
 */
@Injectable({
  providedIn: 'root'
})
export class LayoutService {

  // General layout changes
  private emitChangeSource = new Subject<any>();
  changeEmitted$: Observable<any> = this.emitChangeSource.asObservable();

  // Customizer specific changes
  private emitCustomizerSource = new Subject<CustomizerOptions | string>();
  customizerChangeEmitted$: Observable<CustomizerOptions | string> = this.emitCustomizerSource.asObservable();

  // Compact menu changes
  private emitCustomizerCMSource = new Subject<any>();
  customizerCMChangeEmitted$: Observable<any> = this.emitCustomizerCMSource.asObservable();

  // Notification sidebar changes
  private emitNotiSidebarSource = new Subject<any>();
  notiSidebarChangeEmitted$: Observable<any> = this.emitNotiSidebarSource.asObservable();

  constructor() {}

  /**
   * Emit general layout change
   * @param change - Layout change data
   */
  emitChange(change: any): void {
    this.emitChangeSource.next(change);
  }

  /**
   * Emit customizer change
   * @param change - Customizer options or string command
   */
  emitCustomizerChange(change: CustomizerOptions | string): void {
    this.emitCustomizerSource.next(change);
  }

  /**
   * Emit compact menu change
   * @param change - Compact menu change data
   */
  emitCustomizerCMChange(change: any): void {
    this.emitCustomizerCMSource.next(change);
  }

  /**
   * Emit notification sidebar change
   * @param change - Notification sidebar change data
   */
  emitNotiSidebarChange(change: any): void {
    this.emitNotiSidebarSource.next(change);
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.emitChangeSource.complete();
    this.emitCustomizerSource.complete();
    this.emitCustomizerCMSource.complete();
    this.emitNotiSidebarSource.complete();
  }
}
