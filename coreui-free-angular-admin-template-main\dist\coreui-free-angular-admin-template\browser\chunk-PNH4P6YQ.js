import{b as j}from"./chunk-3MARWV4R.js";import{e as P,l as V,w as W}from"./chunk-LKW33VZJ.js";import{Aa as G,B as D,Ba as $,Ca as q,Da as O,E as y,F as k,I as f,V as A,W as C,X as T,Y as L,Z as B,_ as N,ba as F,ea as z,j as w,ma as I,n as v,oa as _,qa as M,xa as H,ya as R}from"./chunk-Y7QKHPW3.js";import{y as g}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Db as u,Eb as s,Fb as S,Gb as o,Hb as t,Ib as n,Jb as r,Va as i,ac as h,eb as E,fc as e,hc as x,tc as l}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var d=()=>[];function Q(c,p){if(c&1&&(t(0,"c-dropdown",11)(1,"button",52),e(2),n(),t(3,"ul",6)(4,"li")(5,"button",8),e(6,"Action"),n()(),t(7,"li")(8,"button",8),e(9,"Another action"),n()(),t(10,"li")(11,"button",8),e(12,"Something else here"),n()(),t(13,"li"),r(14,"hr",10),n(),t(15,"li")(16,"button",16),e(17,"Separated link"),n()()()()),c&2){let a=p.$implicit;i(),o("color",a),i(),x(" ",a," "),i(3),o("routerLink",l(5,d)),i(3),o("routerLink",l(6,d)),i(3),o("routerLink",l(7,d))}}function U(c,p){if(c&1&&(t(0,"c-dropdown",13)(1,"button",53),e(2),n(),t(3,"button",54)(4,"span",19),e(5,"Toggle Dropdown"),n()(),t(6,"ul",6)(7,"li")(8,"button",8),e(9,"Action"),n()(),t(10,"li")(11,"button",16),e(12,"Another action"),n()(),t(13,"li")(14,"button",16),e(15,"Something else here"),n()(),t(16,"li"),r(17,"hr",10),n(),t(18,"li")(19,"button",16),e(20,"Separated link"),n()()()()),c&2){let a=p.$implicit;i(),o("color",a),i(),x(" ",a," "),i(),o("color",a),i(5),o("routerLink",l(4,d))}}var m=class m{constructor(){this.colors=["primary","secondary","success","info","warning","danger"]}};m.\u0275fac=function(a){return new(a||m)},m.\u0275cmp=E({type:m,selectors:[["app-dropdowns"]],decls:448,vars:23,consts:[["collapseRef","cCollapse"],["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/dropdown#single-button"],["cButton","","cDropdownToggle","","color","secondary"],["cDropdownMenu",""],["cDropdownHeader",""],["cDropdownItem","",3,"routerLink"],["cDropdownItem","","disabled","",3,"routerLink"],["cDropdownDivider",""],["variant","btn-group"],["href","components/dropdown#split-button"],["placement","bottom-start","variant","btn-group"],["href","components/dropdown#sizing"],["cButton","","cDropdownToggle","","color","secondary","size","lg"],["cDropdownItem",""],["cButton","","color","secondary","size","lg"],["cButton","","cDropdownToggle","","color","secondary","size","lg","split",""],[1,"visually-hidden"],["cButton","","cDropdownToggle","","color","secondary","size","sm"],["cButton","","color","secondary","size","sm"],["cButton","","cDropdownToggle","","color","secondary","size","sm","split",""],["href","components/dropdown#dark-dropdowns"],["dark",""],["cDropdownMenu","","cdkTrapFocus",""],["colorScheme","dark","expand","lg",1,"bg-dark"],[3,"fluid"],["cNavbarBrand","","href","https://coreui.io/angular/","target","_blank"],[3,"cNavbarToggler"],["cCollapse","",3,"navbar"],[1,"me-auto","mb-2","mb-lg-0"],["cNavLink","",3,"active","routerLink"],["cNavLink","",3,"routerLink"],["variant","nav-item",3,"popper"],["cNavLink","","cDropdownToggle",""],["cDropdownMenu","","dark",""],["cNavLink","","disabled",""],["cForm","",1,"d-flex"],["aria-label","Search","cFormControl","","placeholder","Search","type","search",1,"me-2"],["cButton","","color","success","type","submit","variant","outline"],["href","components/dropdown#dropup"],["direction","dropup","variant","btn-group"],["cButton","","color","secondary"],["cButton","","cDropdownToggle","","color","secondary",3,"split"],["href","components/dropdown#dropright"],["direction","dropend","variant","btn-group"],["href","components/dropdown#dropleft"],["direction","dropstart","variant","input-group"],["href","components/dropdown#centered"],["direction","center","variant","btn-group"],["direction","dropup-center","variant","btn-group",1,"dropup"],["cButton","","cDropdownToggle","",3,"color"],["cButton","",3,"color"],["cButton","","cDropdownToggle","","split","",3,"color"]],template:function(a,b){if(a&1&&(t(0,"c-row")(1,"c-col",1)(2,"c-card",2)(3,"c-card-header"),e(4,`
        `),t(5,"strong"),e(6,"Angular Dropdown"),n(),e(7," "),t(8,"small"),e(9,"Single button"),n(),e(10,`
      `),n(),t(11,"c-card-body")(12,"p",3),e(13," Here's how you can put them to work with either "),t(14,"code"),e(15,"<button>"),n(),e(16," elements: "),n(),t(17,"app-docs-example",4)(18,"c-dropdown")(19,"button",5),e(20," Dropdown button "),n(),t(21,"ul",6)(22,"li")(23,"h6",7),e(24,"Header"),n()(),t(25,"li")(26,"a",8),e(27,"Action"),n()(),t(28,"li")(29,"a",8),e(30,"Another action"),n()(),t(31,"li")(32,"a",9),e(33,"Something else here"),n()(),t(34,"li"),r(35,"hr",10),n(),t(36,"li")(37,"button",8),e(38,"Separated link"),n()()()()(),t(39,"p",3),e(40," The best part is you can do this with any button variant, too: "),n(),t(41,"app-docs-example",4),s(42,Q,18,8,"c-dropdown",11,u),n()()()(),t(44,"c-col",1)(45,"c-card",2)(46,"c-card-header"),e(47,`
        `),t(48,"strong"),e(49,"Angular Dropdown"),n(),e(50," "),t(51,"small"),e(52,"Split button"),n(),e(53,`
      `),n(),t(54,"c-card-body")(55,"p",3),e(56," Similarly, create split button dropdowns with virtually the same markup as single button dropdowns, but with the addition of boolean prop "),t(57,"code"),e(58,"split"),n(),e(59," for proper spacing around the dropdown caret. "),n(),t(60,"p",3),e(61," We use this extra class to reduce the horizontal "),t(62,"code"),e(63,"padding"),n(),e(64," on either side of the caret by 25% and remove the "),t(65,"code"),e(66,"margin-left"),n(),e(67," that's attached for normal button dropdowns. Those additional changes hold the caret centered in the split button and implement a more properly sized hit area next to the main button. "),n(),t(68,"app-docs-example",12),s(69,U,21,5,"c-dropdown",13,u),n()()()(),t(71,"c-col",1)(72,"c-card",2)(73,"c-card-header"),e(74,`
        `),t(75,"strong"),e(76,"Angular Dropdown"),n(),e(77," "),t(78,"small"),e(79,"Sizing"),n(),e(80,`
      `),n(),t(81,"c-card-body")(82,"p",3),e(83," Button dropdowns work with buttons of all sizes, including default and split dropdown buttons. "),n(),t(84,"app-docs-example",14)(85,"c-dropdown",11)(86,"button",15),e(87," Large button "),n(),t(88,"ul",6)(89,"li")(90,"button",16),e(91,"Action"),n()(),t(92,"li")(93,"button",16),e(94,"Another action"),n()(),t(95,"li")(96,"button",16),e(97,"Something else here"),n()(),t(98,"li"),r(99,"hr",10),n(),t(100,"li")(101,"button",16),e(102,"Separated link"),n()()()(),t(103,"c-dropdown",11)(104,"button",17),e(105," Large split button "),n(),t(106,"button",18)(107,"span",19),e(108,"Toggle Dropdown"),n()(),t(109,"ul",6)(110,"li")(111,"button",8),e(112,"Action"),n()(),t(113,"li")(114,"button",16),e(115,"Another action"),n()(),t(116,"li")(117,"button",16),e(118,"Something else here"),n()(),t(119,"li"),r(120,"hr",10),n(),t(121,"li")(122,"button",16),e(123,"Separated link"),n()()()()(),t(124,"app-docs-example",14)(125,"c-dropdown",11)(126,"button",20),e(127," Small button "),n(),t(128,"ul",6)(129,"li")(130,"button",16),e(131,"Action"),n()(),t(132,"li")(133,"button",16),e(134,"Another action"),n()(),t(135,"li")(136,"button",16),e(137,"Something else here"),n()(),t(138,"li"),r(139,"hr",10),n(),t(140,"li")(141,"button",16),e(142,"Separated link"),n()()()(),t(143,"c-dropdown",11)(144,"button",21),e(145," Small split button "),n(),t(146,"button",22)(147,"span",19),e(148,"Toggle Dropdown"),n()(),t(149,"ul",6)(150,"li")(151,"button",8),e(152,"Action"),n()(),t(153,"li")(154,"button",16),e(155,"Another action"),n()(),t(156,"li")(157,"button",16),e(158,"Something else here"),n()(),t(159,"li"),r(160,"hr",10),n(),t(161,"li")(162,"button",16),e(163,"Separated link"),n()()()()()()()(),t(164,"c-col",1)(165,"c-card",2)(166,"c-card-header"),e(167,`
        `),t(168,"strong"),e(169,"Angular Dropdown"),n(),e(170," "),t(171,"small"),e(172,"dark"),n(),e(173,`
      `),n(),t(174,"c-card-body")(175,"p",3),e(176," Opt into darker dropdowns to match a dark navbar or custom style by set "),t(177,"code"),e(178,"dark"),n(),e(179," property. No changes are required to the dropdown items. "),n(),t(180,"app-docs-example",23)(181,"c-dropdown",24)(182,"button",5),e(183," Dropdown button "),n(),t(184,"ul",25)(185,"li")(186,"button",16),e(187,"Action"),n()(),t(188,"li")(189,"button",16),e(190,"Another action"),n()(),t(191,"li")(192,"button",16),e(193,"Something else here"),n()(),t(194,"li"),r(195,"hr",10),n(),t(196,"li")(197,"button",16),e(198,"Separated link"),n()()()()(),t(199,"p",3),e(200,"And putting it to use in a navbar:"),n(),t(201,"app-docs-example",23)(202,"c-navbar",26)(203,"c-container",27)(204,"a",28),e(205," Navbar "),n(),r(206,"button",29),t(207,"div",30,0)(209,"c-navbar-nav",31)(210,"c-nav-item")(211,"a",32),e(212,"Home"),n()(),t(213,"c-nav-item")(214,"a",33),e(215,"Link"),n()(),t(216,"c-nav-item")(217,"c-dropdown",34)(218,"a",35),e(219," Dropdown "),n(),t(220,"ul",36)(221,"li")(222,"button",16),e(223,"Action"),n()(),t(224,"li")(225,"button",16),e(226,"Another action"),n()(),t(227,"li")(228,"button",16),e(229,"Something else here"),n()(),t(230,"li"),r(231,"hr",10),n(),t(232,"li")(233,"button",16),e(234,"Separated link"),n()()()()(),t(235,"c-nav-item")(236,"a",37),e(237,"Disabled"),n()()(),t(238,"form",38),r(239,"input",39),t(240,"button",40),e(241,"Search"),n()()()()()()()()(),t(242,"c-col",1)(243,"c-card",2)(244,"c-card-header"),e(245,`
        `),t(246,"strong"),e(247,"Angular Dropdown"),n(),e(248," "),t(249,"small"),e(250,"Dropup"),n(),e(251,`
      `),n(),t(252,"c-card-body")(253,"p",3),e(254," Trigger dropdown menus above elements by adding "),t(255,"code"),e(256,'direction="dropup"'),n(),e(257," to the "),t(258,"code"),e(259,"<c-dropdown>"),n(),e(260," component. "),n(),t(261,"app-docs-example",41)(262,"c-dropdown",42)(263,"button",5),e(264," Dropup "),n(),t(265,"ul",6)(266,"li")(267,"button",16),e(268,"Action"),n()(),t(269,"li")(270,"button",16),e(271,"Another action"),n()(),t(272,"li")(273,"button",16),e(274,"Something else here"),n()(),t(275,"li"),r(276,"hr",10),n(),t(277,"li")(278,"button",16),e(279,"Separated link"),n()()()(),t(280,"c-dropdown",42)(281,"button",43),e(282," Split Dropup "),n(),t(283,"button",44)(284,"span",19),e(285,"Toggle Dropdown"),n()(),t(286,"ul",6)(287,"li")(288,"button",16),e(289,"Action"),n()(),t(290,"li")(291,"button",16),e(292,"Another action"),n()(),t(293,"li")(294,"button",16),e(295,"Something else here"),n()(),t(296,"li"),r(297,"hr",10),n(),t(298,"li")(299,"button",16),e(300,"Separated link"),n()()()()()()()(),t(301,"c-col",1)(302,"c-card",2)(303,"c-card-header"),e(304,`
        `),t(305,"strong"),e(306,"Angular Dropdown"),n(),e(307," "),t(308,"small"),e(309,"Dropright"),n(),e(310,`
      `),n(),t(311,"c-card-body")(312,"p",3),e(313," Trigger dropdown menus at the right of the elements by adding "),t(314,"code"),e(315,'direction="dropend"'),n(),e(316," to the "),t(317,"code"),e(318,"<c-dropdown>"),n(),e(319," component. "),n(),t(320,"app-docs-example",45)(321,"c-dropdown",46)(322,"button",5),e(323," Dropend "),n(),t(324,"ul",6)(325,"li")(326,"button",16),e(327,"Action"),n()(),t(328,"li")(329,"button",16),e(330,"Another action"),n()(),t(331,"li")(332,"button",16),e(333,"Something else here"),n()(),t(334,"li"),r(335,"hr",10),n(),t(336,"li")(337,"button",16),e(338,"Separated link"),n()()()(),t(339,"c-dropdown",46)(340,"button",43),e(341," Split Right "),n(),t(342,"button",44)(343,"span",19),e(344,"Toggle Dropdown"),n()(),t(345,"ul",6)(346,"li")(347,"button",16),e(348,"Action"),n()(),t(349,"li")(350,"button",16),e(351,"Another action"),n()(),t(352,"li")(353,"button",16),e(354,"Something else here"),n()(),t(355,"li"),r(356,"hr",10),n(),t(357,"li")(358,"button",16),e(359,"Separated link"),n()()()()()()()(),t(360,"c-col",1)(361,"c-card",2)(362,"c-card-header"),e(363,`
        `),t(364,"strong"),e(365,"Angular Dropdown"),n(),e(366," "),t(367,"small"),e(368,"Dropleft"),n(),e(369,`
      `),n(),t(370,"c-card-body")(371,"p",3),e(372," Trigger dropdown menus at the left of the elements by adding "),t(373,"code"),e(374,'direction="dropstart"'),n(),e(375," to the "),t(376,"code"),e(377,"<c-dropdown>"),n(),e(378," component. "),n(),t(379,"app-docs-example",47)(380,"c-button-group")(381,"c-dropdown",48)(382,"button",44)(383,"span",19),e(384,"Toggle Dropdown"),n()(),t(385,"ul",6)(386,"li")(387,"button",16),e(388,"Action"),n()(),t(389,"li")(390,"button",16),e(391,"Another action"),n()(),t(392,"li")(393,"button",16),e(394,"Something else here"),n()(),t(395,"li"),r(396,"hr",10),n(),t(397,"li")(398,"button",16),e(399,"Separated link"),n()()(),t(400,"button",43),e(401," Split Left "),n()()()()()()(),t(402,"c-col",1)(403,"c-card",2)(404,"c-card-header"),e(405,`
        `),t(406,"strong"),e(407,"Angular Dropdown"),n(),e(408," "),t(409,"small"),e(410,"Centered"),n(),e(411,`
      `),n(),t(412,"c-card-body")(413,"p",3),e(414," Trigger dropdown menus centered below the toggle by adding "),t(415,"code"),e(416,'direction="center"'),n(),e(417," to the "),t(418,"code"),e(419,"c-dropdown"),n(),e(420," component. "),n(),t(421,"app-docs-example",49)(422,"c-dropdown",50)(423,"button",5),e(424,"Centered dropdown"),n(),t(425,"ul",6)(426,"li")(427,"button",16),e(428,"Action one"),n()(),t(429,"li")(430,"button",16),e(431,"Action two"),n()(),t(432,"li")(433,"button",16),e(434,"Action three"),n()()()(),t(435,"c-dropdown",51)(436,"button",5),e(437,"Centered dropdup"),n(),t(438,"ul",6)(439,"li")(440,"button",16),e(441,"Action one"),n()(),t(442,"li")(443,"button",16),e(444,"Action two"),n()(),t(445,"li")(446,"button",16),e(447,"Action three"),n()()()()()()()()()),a&2){let K=h(208);i(26),o("routerLink",l(16,d)),i(3),o("routerLink",l(17,d)),i(3),o("routerLink",l(18,d)),i(5),o("routerLink","/buttons"),i(5),S(b.colors),i(27),S(b.colors),i(42),o("routerLink",l(19,d)),i(40),o("routerLink",l(20,d)),i(52),o("fluid",!0),i(3),o("cNavbarToggler",K),i(),o("navbar",!0),i(4),o("active",!0)("routerLink",l(21,d)),i(3),o("routerLink",l(22,d)),i(3),o("popper",!1),i(66),o("split",!0),i(59),o("split",!0),i(40),o("split",!0)}},dependencies:[M,_,y,f,k,j,B,v,L,T,C,N,g,A,G,I,$,O,w,q,R,H,W,V,P,F,z,D],encapsulation:2});var J=m;export{J as DropdownsComponent};
