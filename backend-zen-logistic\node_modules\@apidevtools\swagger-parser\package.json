{"_from": "@apidevtools/swagger-parser@10.0.3", "_id": "@apidevtools/swagger-parser@10.0.3", "_inBundle": false, "_integrity": "sha512-sNiLY51vZOmSPFZA5TF35KZ2HbgYklQnTSDnkghamzLb3EkNtcQnrBQEj5AOCxHpTtXpqMCRM1CrmV2rG6nw4g==", "_location": "/@apidevtools/swagger-parser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@apidevtools/swagger-parser@10.0.3", "name": "@apidevtools/swagger-parser", "escapedName": "@apidevtools%2fswagger-parser", "scope": "@apidevtools", "rawSpec": "10.0.3", "saveSpec": null, "fetchSpec": "10.0.3"}, "_requiredBy": ["/swagger-parser"], "_resolved": "https://registry.npmjs.org/@apidevtools/swagger-parser/-/swagger-parser-10.0.3.tgz", "_shasum": "32057ae99487872c4dd96b314a1ab4b95d89eaf5", "_spec": "@apidevtools/swagger-parser@10.0.3", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\swagger-parser", "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "bugs": {"url": "https://github.com/APIDevTools/swagger-parser/issues"}, "bundleDependencies": false, "dependencies": {"@apidevtools/json-schema-ref-parser": "^9.0.6", "@apidevtools/openapi-schemas": "^2.0.4", "@apidevtools/swagger-methods": "^3.0.2", "@jsdevtools/ono": "^7.1.3", "call-me-maybe": "^1.0.1", "z-schema": "^5.0.1"}, "deprecated": false, "description": "Swagger 2.0 and OpenAPI 3.0 parser and validator for Node and browsers", "devDependencies": {"@babel/polyfill": "^7.11.5", "@jsdevtools/eslint-config": "^1.1.4", "@jsdevtools/host-environment": "^2.1.2", "@jsdevtools/karma-config": "^3.1.7", "@jsdevtools/version-bump-prompt": "^6.1.0", "@types/node": "^14.6.4", "chai": "^4.2.0", "eslint": "^7.8.1", "js-yaml": "^3.14.0", "karma": "^5.2.1", "karma-cli": "^2.0.0", "mocha": "^8.1.3", "node-fetch": "^2.6.1", "node-sass": "^4.14.1", "npm-check": "^5.9.0", "nyc": "^15.1.0", "openapi-types": "^7.0.1", "shx": "^0.3.2", "simplifyify": "^8.0.3", "typescript": "^4.0.2"}, "files": ["lib"], "homepage": "https://apitools.dev/swagger-parser/", "keywords": ["swagger", "openapi", "open-api", "json", "yaml", "parse", "parser", "validate", "validator", "validation", "spec", "specification", "schema", "reference", "dereference"], "license": "MIT", "main": "lib/index.js", "name": "@apidevtools/swagger-parser", "peerDependencies": {"openapi-types": ">=7"}, "repository": {"type": "git", "url": "git+https://github.com/APIDevTools/swagger-parser.git"}, "scripts": {"build": "npm run build:website && npm run build:sass", "build:sass": "node-sass --source-map true --output-style compressed online/src/scss/style.scss online/css/style.min.css", "build:website": "simplifyify online/src/js/index.js --outfile online/js/bundle.js --bundle --debug --minify", "bump": "bump --tag --push --all", "clean": "shx rm -rf .nyc_output coverage", "coverage": "npm run coverage:node && npm run coverage:browser", "coverage:browser": "npm run test:browser -- --coverage", "coverage:node": "nyc node_modules/mocha/bin/mocha", "lint": "eslint lib test online/src/js", "release": "npm run upgrade && npm run clean && npm run build && npm test && npm run bump", "test": "npm run test:node && npm run test:typescript && npm run test:browser && npm run lint", "test:browser": "karma start --single-run", "test:node": "mocha", "test:typescript": "tsc --noEmit --strict --lib esnext,dom test/specs/typescript-definition.spec.ts", "upgrade": "npm-check -u && npm audit fix"}, "typings": "lib/index.d.ts", "version": "10.0.3"}