{"version": 3, "file": "applicationTokenCredentialsBase.js", "sourceRoot": "", "sources": ["../../../lib/credentials/applicationTokenCredentialsBase.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,iEAA8D;AAE9D,yDAAqE;AAGrE,MAAsB,+BAAgC,SAAQ,2CAAoB;IAChF;;;;;;;;;;;OAWG;IACH,YACE,QAAgB,EAChB,MAAc,EACd,aAA6B,EAC7B,WAAyB,EACzB,UAAuB;QAEvB,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IAClE,CAAC;IAEe,iBAAiB;;;;;YAC/B,iHAAiH;YACjH,IAAI;gBACF,OAAO,MAAM,OAAM,iBAAiB,YAAC,SAAS,CAAC,CAAC;aACjD;YAAC,OAAO,KAAK,EAAE;gBACd,4GAA4G;gBAC5G,0HAA0H;gBAC1H,qDAAqD;gBACrD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,2BAA2B,CAAC;oBACpD,SAAS,EAAE,IAAI,CAAC,QAAQ;iBACzB,CAAC,CAAC;gBAEH,IAAI,MAAM,CAAC,MAAM,EAAE;oBACjB,MAAM,KAAK,CAAC;iBACb;gBAED,MAAM,OAAO,GACX,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO;oBAChD,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;oBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;gBAErB,MAAM,IAAI,KAAK,CACb,6BAAa,CAAC,kBAAkB;oBAC9B,KAAK;oBACL,wFAAwF;oBACxF,OAAO,CACV,CAAC;aACH;QACH,CAAC;KAAA;IAED;;;;;;;OAOG;IACK,2BAA2B,CACjC,KAAa;QAEb,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,OAAO,CAAuC,CAAC,OAAO,EAAE,EAAE;YACnE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,KAAY,EAAE,OAAc,EAAE,EAAE;gBAC3D,IAAI,KAAK,EAAE;oBACT,OAAO,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;iBACnD;gBAED,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;oBACjC,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;wBACpD,IAAI,GAAG,EAAE;4BACP,OAAO,OAAO,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;yBACjD;wBACD,OAAO,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;oBACnC,CAAC,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,OAAO,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;iBAClC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AApFD,0EAoFC"}