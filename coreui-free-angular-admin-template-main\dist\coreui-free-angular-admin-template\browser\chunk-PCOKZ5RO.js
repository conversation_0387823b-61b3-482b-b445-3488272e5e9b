import{a as b}from"./chunk-5AY4JT2K.js";import{a as D}from"./chunk-3MARWV4R.js";import{E as s,F as m,I as p,oa as h,qa as C}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as i,Hb as a,Ib as t,Jb as r,Va as n,eb as c,fc as o}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var d=class d{constructor(){this.options={maintainAspectRatio:!1};this.months=["January","February","March","April","May","June","July","August","September","October","November","December"];this.chartBarData={labels:[...this.months].slice(0,7),datasets:[{label:"GitHub Commits",backgroundColor:"#f87979",data:[40,20,12,39,17,42,79]}]};this.chartLineData={labels:[...this.months].slice(0,7),datasets:[{label:"My First dataset",backgroundColor:"rgba(220, 220, 220, 0.2)",borderColor:"rgba(220, 220, 220, 1)",pointBackgroundColor:"rgba(220, 220, 220, 1)",pointBorderColor:"#fff",data:[this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData]},{label:"My Second dataset",backgroundColor:"rgba(151, 187, 205, 0.2)",borderColor:"rgba(151, 187, 205, 1)",pointBackgroundColor:"rgba(151, 187, 205, 1)",pointBorderColor:"#fff",data:[this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData]}]};this.chartLineOptions={maintainAspectRatio:!1};this.chartDoughnutData={labels:["VueJs","EmberJs","ReactJs","Angular"],datasets:[{backgroundColor:["#41B883","#E46651","#00D8FF","#DD1B16"],data:[40,20,80,10]}]};this.chartPieData={labels:["Red","Green","Yellow"],datasets:[{data:[300,50,100],backgroundColor:["#FF6384","#36A2EB","#FFCE56"],hoverBackgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]};this.chartPolarAreaData={labels:["Red","Green","Yellow","Grey","Blue"],datasets:[{data:[11,16,7,3,14],backgroundColor:["#FF6384","#4BC0C0","#FFCE56","#E7E9ED","#36A2EB"]}]};this.chartRadarData={labels:["Eating","Drinking","Sleeping","Designing","Coding","Cycling","Running"],datasets:[{label:"2020",backgroundColor:"rgba(179,181,198,0.2)",borderColor:"rgba(179,181,198,1)",pointBackgroundColor:"rgba(179,181,198,1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(179,181,198,1)",data:[65,59,90,81,56,55,40]},{label:"2021",backgroundColor:"rgba(255,99,132,0.2)",borderColor:"rgba(255,99,132,1)",pointBackgroundColor:"rgba(255,99,132,1)",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"rgba(255,99,132,1)",data:[this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData,this.randomData]}]}}get randomData(){return Math.round(Math.random()*100)}};d.\u0275fac=function(l){return new(l||d)},d.\u0275cmp=c({type:d,selectors:[["app-charts"]],decls:43,vars:12,consts:[["xs","12"],["href","charts"],["xs","12","lg","6"],[1,"mb-4"],["type","bar",3,"data","options"],["type","line",3,"data","options"],["type","doughnut",3,"data","options"],["type","pie",3,"data","options"],["type","polarArea",3,"data","options"],["type","radar",3,"data","options"]],template:function(l,e){l&1&&(a(0,"c-row")(1,"c-col",0)(2,"app-docs-callout",1),o(3," CoreUI Angular wrapper component for Chart.js 4.x, the most popular charting library. "),r(4,"br"),t()(),a(5,"c-col",2)(6,"c-card",3)(7,"c-card-header"),o(8," Bar Chart "),t(),a(9,"c-card-body"),r(10,"c-chart",4),t()()(),a(11,"c-col",2)(12,"c-card",3)(13,"c-card-header"),o(14," Line Chart "),t(),a(15,"c-card-body"),r(16,"c-chart",5),t()()()(),a(17,"c-row")(18,"c-col",2)(19,"c-card",3)(20,"c-card-header"),o(21," Doughnut Chart "),t(),a(22,"c-card-body"),r(23,"c-chart",6),t()()(),a(24,"c-col",2)(25,"c-card",3)(26,"c-card-header"),o(27," Pie Chart "),t(),a(28,"c-card-body"),r(29,"c-chart",7),t()()()(),a(30,"c-row")(31,"c-col",2)(32,"c-card",3)(33,"c-card-header"),o(34," Polar Area Chart "),t(),a(35,"c-card-body"),r(36,"c-chart",8),t()()(),a(37,"c-col",2)(38,"c-card",3)(39,"c-card-header"),o(40," Radar Chart "),t(),a(41,"c-card-body"),r(42,"c-chart",9),t()()()()),l&2&&(n(10),i("data",e.chartBarData)("options",e.options),n(6),i("data",e.chartLineData)("options",e.options),n(7),i("data",e.chartDoughnutData)("options",e.options),n(6),i("data",e.chartPieData)("options",e.options),n(7),i("data",e.chartPolarAreaData)("options",e.options),n(6),i("data",e.chartRadarData)("options",e.options))},dependencies:[C,h,D,s,p,m,b],encapsulation:2});var g=d;export{g as ChartsComponent};
