{"_args": [["tslib@2.7.0", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "tslib@2.7.0", "_id": "tslib@2.7.0", "_inBundle": false, "_integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==", "_location": "/@azure/core-auth/tslib", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tslib@2.7.0", "name": "tslib", "escapedName": "tslib", "rawSpec": "2.7.0", "saveSpec": null, "fetchSpec": "2.7.0"}, "_requiredBy": ["/@azure/core-auth"], "_resolved": "https://registry.npmjs.org/tslib/-/tslib-2.7.0.tgz", "_spec": "2.7.0", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "Microsoft Corp."}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "description": "Runtime library for TypeScript helper functions", "exports": {".": {"module": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}, "import": {"node": "./modules/index.js", "default": {"types": "./modules/index.d.ts", "default": "./tslib.es6.mjs"}}, "default": "./tslib.js"}, "./*": "./*", "./": "./"}, "homepage": "https://www.typescriptlang.org/", "jsnext:main": "tslib.es6.js", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "license": "0BSD", "main": "tslib.js", "module": "tslib.es6.js", "name": "tslib", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/tslib.git"}, "sideEffects": false, "typings": "tslib.d.ts", "version": "2.7.0"}