import{E as l,F as r,I as m}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as t,Ib as i,eb as d,fc as e}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var n=class n{constructor(){}};n.\u0275fac=function(a){return new(a||n)},n.\u0275cmp=d({type:n,selectors:[["ng-component"]],decls:190,vars:0,consts:[[1,"mb-4"],[1,"table"],[1,"c-highlighter-rouge"],[1,"bd-example"],[1,"h1"],[1,"h2"],[1,"h3"],[1,"h4"],[1,"h5"],[1,"h6"],["id","headings",1,"mb-4"],[1,"bd-example","bd-example-type"],[1,"display-1"],[1,"display-2"],[1,"display-3"],[1,"display-4"],[1,"row"],[1,"col-sm-3"],[1,"col-sm-9"],[1,"col-sm-3","c-d-block","c-text-truncate"],[1,"col-sm-9","c-d-block","c-text-truncate"],[1,"col-sm-4"],[1,"col-sm-8"]],template:function(a,c){a&1&&(t(0,"c-card",0)(1,"c-card-header"),e(2," Headings "),i(),t(3,"c-card-body")(4,"p"),e(5,"Documentation and examples for Bootstrap typography, including global settings, headings, body text, lists, and more."),i(),t(6,"table",1)(7,"thead")(8,"tr")(9,"th"),e(10,"Heading"),i(),t(11,"th"),e(12,"Example"),i()()(),t(13,"tbody")(14,"tr")(15,"td")(16,"p")(17,"code",2),e(18,"<h1></h1>"),i()()(),t(19,"td")(20,"h1"),e(21,"h1. Bootstrap heading"),i()()(),t(22,"tr")(23,"td")(24,"p")(25,"code",2),e(26,"<h2></h2>"),i()()(),t(27,"td")(28,"h2"),e(29,"h2. Bootstrap heading"),i()()(),t(30,"tr")(31,"td")(32,"p")(33,"code",2),e(34,"<h3></h3>"),i()()(),t(35,"td")(36,"h3"),e(37,"h3. Bootstrap heading"),i()()(),t(38,"tr")(39,"td")(40,"p")(41,"code",2),e(42,"<h4></h4>"),i()()(),t(43,"td")(44,"h4"),e(45,"h4. Bootstrap heading"),i()()(),t(46,"tr")(47,"td")(48,"p")(49,"code",2),e(50,"<h5></h5>"),i()()(),t(51,"td")(52,"h5"),e(53,"h5. Bootstrap heading"),i()()(),t(54,"tr")(55,"td")(56,"p")(57,"code",2),e(58,"<h6></h6>"),i()()(),t(59,"td")(60,"h6"),e(61,"h6. Bootstrap heading"),i()()()()()()(),t(62,"c-card",0)(63,"c-card-header"),e(64," Headings "),i(),t(65,"c-card-body")(66,"p")(67,"code",2),e(68,".h1"),i(),e(69," through "),t(70,"code",2),e(71,".h6"),i(),e(72," classes are also available, for when you want to match the font styling of a heading but cannot use the associated HTML element."),i(),t(73,"div",3)(74,"p",4),e(75,"h1. Bootstrap heading"),i(),t(76,"p",5),e(77,"h2. Bootstrap heading"),i(),t(78,"p",6),e(79,"h3. Bootstrap heading"),i(),t(80,"p",7),e(81,"h4. Bootstrap heading"),i(),t(82,"p",8),e(83,"h5. Bootstrap heading"),i(),t(84,"p",9),e(85,"h6. Bootstrap heading"),i()()()(),t(86,"c-card",10)(87,"c-card-header"),e(88," Display headings "),i(),t(89,"c-card-body")(90,"p"),e(91,"Traditional heading elements are designed to work best in the meat of your page content. When you need a heading to stand out, consider using a "),t(92,"strong"),e(93,"display heading"),i(),e(94,"\u2014a larger, slightly more opinionated heading style. "),i(),t(95,"div",11)(96,"table",1)(97,"tbody")(98,"tr")(99,"td")(100,"span",12),e(101,"Display 1"),i()()(),t(102,"tr")(103,"td")(104,"span",13),e(105,"Display 2"),i()()(),t(106,"tr")(107,"td")(108,"span",14),e(109,"Display 3"),i()()(),t(110,"tr")(111,"td")(112,"span",15),e(113,"Display 4"),i()()()()()()()(),t(114,"c-card",0)(115,"c-card-header"),e(116," Inline text elements "),i(),t(117,"c-card-body")(118,"p"),e(119,"Traditional heading elements are designed to work best in the meat of your page content. When you need a heading to stand out, consider using a "),t(120,"strong"),e(121,"display heading"),i(),e(122,"\u2014a larger, slightly more opinionated heading style. "),i(),t(123,"div",3)(124,"p"),e(125,"You can use the mark tag to "),t(126,"mark"),e(127,"highlight"),i(),e(128," text. "),i(),t(129,"p")(130,"del"),e(131,"This line of text is meant to be treated as deleted text."),i()(),t(132,"p")(133,"s"),e(134,"This line of text is meant to be treated as no longer accurate."),i()(),t(135,"p")(136,"ins"),e(137,"This line of text is meant to be treated as an addition to the document."),i()(),t(138,"p")(139,"u"),e(140,"This line of text will render as underlined"),i()(),t(141,"p")(142,"small"),e(143,"This line of text is meant to be treated as fine print."),i()(),t(144,"p")(145,"strong"),e(146,"This line rendered as bold text."),i()(),t(147,"p")(148,"em"),e(149,"This line rendered as italicized text."),i()()()()(),t(150,"c-card",0)(151,"c-card-header"),e(152," Description list alignment "),i(),t(153,"c-card-body")(154,"p"),e(155,"Align terms and descriptions horizontally by using our grid system\u2019s predefined classes (or semantic mixins). For longer terms, you can optionally add a "),t(156,"code",2),e(157,".text-truncate"),i(),e(158," class to truncate the text with an ellipsis."),i(),t(159,"div",3)(160,"dl",16)(161,"dt",17),e(162,"Description lists"),i(),t(163,"dd",18),e(164,"A description list is perfect for defining terms."),i(),t(165,"dt",17),e(166,"Euismod"),i(),t(167,"dd",18)(168,"p"),e(169,"Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit."),i(),t(170,"p"),e(171,"Donec id elit non mi porta gravida at eget metus."),i()(),t(172,"dt",17),e(173,"Malesuada porta"),i(),t(174,"dd",18),e(175,"Etiam porta sem malesuada magna mollis euismod."),i(),t(176,"dt",19),e(177,"Truncated term is truncated with "),t(178,"code"),e(179,"d-block"),i()(),t(180,"dd",20),e(181,"Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus. "),i(),t(182,"dt",17),e(183,"Nesting"),i(),t(184,"dd",18)(185,"dl",16)(186,"dt",21),e(187,"Nested definition list"),i(),t(188,"dd",22),e(189,"Aenean posuere, tortor sed cursus feugiat, nunc augue blandit nunc."),i()()()()()()())},dependencies:[l,m,r],encapsulation:2});var o=n;export{o as TypographyComponent};
