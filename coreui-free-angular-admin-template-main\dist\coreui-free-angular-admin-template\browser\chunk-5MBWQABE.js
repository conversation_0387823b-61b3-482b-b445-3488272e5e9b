import{b as d,c as g}from"./chunk-EN2VGXKU.js";import{x as u}from"./chunk-VLR5A2CC.js";import{A as c,E as l,ba as e,l as s,p as a}from"./chunk-J5YWIVYY.js";var I=(v,r)=>{let m=e(g),t=e(d),o=e(u),i=t.validateSession();return i.isValid?m.isAuthenticated$.pipe(l(1),a(n=>n?(t.updateLastActivity(),!0):(console.log("Access denied. User not authenticated. Redirecting to login..."),sessionStorage.setItem("redirectUrl",r.url),o.navigate(["/login"]),!1)),c(n=>(console.error("Auth guard error:",n),sessionStorage.setItem("redirectUrl",r.url),o.navigate(["/login"]),s(!1)))):(console.log("Session invalid:",i.reason,i.message),t.clearSession(),sessionStorage.setItem("redirectUrl",r.url),o.navigate(["/login"]),!1)};export{I as a};
