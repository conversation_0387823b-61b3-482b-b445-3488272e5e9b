{"compilerOptions": {"alwaysStrict": true, "module": "commonjs", "noImplicitAny": true, "preserveConstEnums": true, "sourceMap": true, "newLine": "LF", "target": "es2015", "moduleResolution": "node", "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "dist", "declaration": true, "declarationMap": true, "allowJs": false, "strict": true, "strictNullChecks": true, "lib": ["dom", "dom.iterable", "es5", "es6", "es7", "esnext", "esnext.asynciterable", "es2015.iterable"]}, "compileOnSave": true, "exclude": ["node_modules"], "include": ["./lib/**/*.ts", "./test/**/*.ts", "./samples/*.ts"]}