{"_args": [["@azure/ms-rest-js@2.7.0", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "@azure/ms-rest-js@2.7.0", "_id": "@azure/ms-rest-js@2.7.0", "_inBundle": false, "_integrity": "sha512-ngbzWbqF+NmztDOpLBVDxYM+XLcUj7nKhxGbSU9WtIsXfRB//cf2ZbAG5HkOrhU9/wd/ORRB6lM/d69RKVjiyA==", "_location": "/@azure/ms-rest-js", "_phantomChildren": {"asynckit": "0.4.0", "combined-stream": "1.0.8", "mime-types": "2.1.35"}, "_requested": {"type": "version", "registry": true, "raw": "@azure/ms-rest-js@2.7.0", "name": "@azure/ms-rest-js", "escapedName": "@azure%2fms-rest-js", "scope": "@azure", "rawSpec": "2.7.0", "saveSpec": null, "fetchSpec": "2.7.0"}, "_requiredBy": ["/@azure/ms-rest-nodeauth"], "_resolved": "https://registry.npmjs.org/@azure/ms-rest-js/-/ms-rest-js-2.7.0.tgz", "_spec": "2.7.0", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "Microsoft Corporation", "email": "<EMAIL>", "url": "https://github.com/Azure/ms-rest-js"}, "browser": {"./es/lib/policies/msRestUserAgentPolicy.js": "./es/lib/policies/msRestUserAgentPolicy.browser.js", "./es/lib/policies/agentPolicy.js": "./es/lib/policies/agentPolicy.browser.js", "./es/lib/policies/proxyPolicy.js": "./es/lib/policies/proxyPolicy.browser.js", "./es/lib/util/base64.js": "./es/lib/util/base64.browser.js", "./es/lib/util/xml.js": "./es/lib/util/xml.browser.js", "./es/lib/defaultHttpClient.js": "./es/lib/defaultHttpClient.browser.js"}, "bugs": {"url": "http://github.com/Azure/ms-rest-js/issues"}, "dependencies": {"@azure/core-auth": "^1.1.4", "abort-controller": "^3.0.0", "form-data": "^2.5.0", "node-fetch": "^2.6.7", "tslib": "^1.10.0", "tunnel": "0.0.6", "uuid": "^8.3.2", "xml2js": "^0.5.0"}, "description": "Isomorphic client Runtime for Typescript/node.js/browser javascript client libraries generated using AutoRest", "devDependencies": {"@azure/logger-js": "^1.1.0", "@microsoft/api-extractor": "^7.18.11", "@ts-common/azure-js-dev-tools": "^19.4.0", "@types/bluebird": "3.5.36", "@types/chai": "^4.1.7", "@types/express": "4.17.0", "@types/express-serve-static-core": "4.17.0", "@types/fetch-mock": "^7.3.1", "@types/form-data": "^2.2.1", "@types/glob": "^7.1.1", "@types/karma": "^3.0.3", "@types/mocha": "^5.2.7", "@types/node": "^12.0.12", "@types/node-fetch": "^2.3.7", "@types/semver": "^6.0.1", "@types/sinon": "^7.0.13", "@types/trusted-types": "^2.0.0", "@types/tunnel": "0.0.1", "@types/uuid": "^8.3.2", "@types/webpack": "^4.4.34", "@types/webpack-dev-middleware": "^2.0.3", "@types/xml2js": "^0.4.4", "abortcontroller-polyfill": "^1.3.0", "chai": "4.3.4", "cross-env": "^7.0.3", "express": "^4.17.1", "fetch-mock": "^7.3.3", "glob": "^7.1.4", "karma": "^4.1.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.1.0", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^7.0.0", "karma-sourcemap-loader": "^0.3.7", "karma-typescript-es6-transform": "^4.1.1", "karma-webpack": "^4.0.2", "mocha": "^6.1.4", "mocha-chrome": "^2.0.0", "mocha-junit-reporter": "^1.23.0", "mocha-multi-reporters": "^1.1.7", "npm-run-all": "^4.1.5", "nyc": "^14.1.1", "prettier": "2.2.1", "rollup": "^1.16.6", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-json": "^4.0.0", "rollup-plugin-multi-entry": "^2.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-resolve": "0.0.1-predev.1", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-visualizer": "^2.4.4", "semver": "^6.2.0", "shx": "^0.3.2", "sinon": "^7.3.2", "terser": "^4.0.2", "ts-loader": "^6.0.4", "ts-node": "^8.3.0", "tslint": "^5.18.0", "tslint-eslint-rules": "^5.4.0", "typescript": "^3.5.2", "webpack": "^4.35.2", "webpack-cli": "^3.3.5", "webpack-dev-middleware": "^3.7.0", "xhr-mock": "^2.4.1", "yarn": "^1.16.0"}, "files": ["dist/**/*.js", "dist/**/*.js.map", "es/lib/**/*.js", "es/lib/**/*.js.map", "es/lib/**/*.d.ts", "es/lib/**/*.d.ts.map", "lib/**/!(dom.d).ts", "dom-shim.d.ts", "LICENSE", "README.md", "ThirdPartyNotices.txt"], "homepage": "https://github.com/Azure/ms-rest-js", "keywords": ["isomorphic", "browser", "javascript", "node", "microsoft", "autorest", "clientruntime"], "license": "MIT", "main": "./dist/msRest.node.js", "module": "./es/lib/msRest.js", "name": "@azure/ms-rest-js", "nyc": {"extension": [".ts"], "exclude": ["coverage/**/*", "**/*.d.ts", "**/*.js"], "reporter": ["text", "html", "cobertura"], "all": true}, "repository": {"type": "git", "url": "git+ssh://**************/Azure/ms-rest-js.git"}, "scripts": {"build": "run-p build:scripts build:lib", "build:lib": "run-s build:tsc build:rollup build:minify-browser extract-api", "build:minify-browser": "terser -c -m --comments --source-map \"content='./dist/msRest.browser.js.map'\" -o ./dist/msRest.browser.min.js ./dist/msRest.browser.js", "build:rollup": "rollup -c rollup.config.ts", "build:scripts": "tsc -p ./.scripts/", "build:test-browser": "webpack --config webpack.testconfig.ts", "build:tsc": "tsc -p tsconfig.es.json", "check:everything": "ts-node ./.scripts/checkEverything.ts", "check:foronlycalls": "ts-node ./.scripts/checkForOnlyCalls.ts", "check:packagejsonversion": "ts-node ./.scripts/checkPackageJsonVersion.ts", "dep:autorest.typescript": "npx ts-node .scripts/testDependentProjects.ts autorest.typescript 'gulp build' 'gulp regenerate' 'npm run local'", "dep:ms-rest-azure-js": "npx ts-node .scripts/testDependentProjects.ts ms-rest-azure-js", "extract-api": "api-extractor run --local", "format": "prettier --write \"./**/*.ts\"  *.json", "latest": "ts-node ./.scripts/latest.ts", "local": "ts-node ./.scripts/local.ts", "prepack": "npm i && npm run build", "publish-preview": "mocha --no-colors && shx rm -rf dist/test && node ./.scripts/publish", "test": "run-p test:tslint test:unit test:karma", "test:karma": "npm run build:test-browser && node ./node_modules/karma/bin/karma start karma.conf.ts --browsers ChromeNoSecurity --single-run ", "test:karma:debug": "npm run build:test-browser && node ./node_modules/karma/bin/karma start karma.conf.ts  --log-level debug --browsers ChromeDebugging --debug --auto-watch", "test:karma:debugff": "npm run build:test-browser && node ./node_modules/karma/bin/karma start karma.conf.ts  --log-level debug --browsers FirefoxDebugging --debug --auto-watch", "test:tslint": "tslint -p .", "test:unit": "cross-env TS_NODE_FILES=true nyc mocha"}, "sideEffects": false, "tags": ["isomorphic", "browser", "javascript", "node", "microsoft", "autorest", "clientruntime"], "types": "./es/lib/msRest.d.ts", "version": "2.7.0"}