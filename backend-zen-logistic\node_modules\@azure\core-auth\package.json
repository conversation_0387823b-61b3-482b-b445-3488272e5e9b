{"//metadata": {"migrationDate": "2023-03-08T18:36:03.000Z"}, "_args": [["@azure/core-auth@1.8.0", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "@azure/core-auth@1.8.0", "_id": "@azure/core-auth@1.8.0", "_inBundle": false, "_integrity": "sha512-YvFMowkXzLbXNM11yZtVLhUCmuG0ex7JKOH366ipjmHBhL3vpDcPAeWF+jf0X+jVXwFqo3UhsWUq4kH0ZPdu/g==", "_location": "/@azure/core-auth", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@azure/core-auth@1.8.0", "name": "@azure/core-auth", "escapedName": "@azure%2fcore-auth", "scope": "@azure", "rawSpec": "1.8.0", "saveSpec": null, "fetchSpec": "1.8.0"}, "_requiredBy": ["/@azure/ms-rest-js"], "_resolved": "https://registry.npmjs.org/@azure/core-auth/-/core-auth-1.8.0.tgz", "_spec": "1.8.0", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "Microsoft Corporation"}, "browser": "./dist/browser/index.js", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "dependencies": {"@azure/abort-controller": "^2.0.0", "@azure/core-util": "^1.1.0", "tslib": "^2.6.2"}, "description": "Provides low-level interfaces and helper methods for authentication in Azure SDK", "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.40.3", "@types/node": "^18.0.0", "@vitest/browser": "^2.0.5", "@vitest/coverage-istanbul": "^2.0.5", "eslint": "^9.9.0", "playwright": "^1.41.2", "rimraf": "^5.0.5", "tshy": "^2.0.0", "typescript": "~5.5.3", "vitest": "^2.0.5"}, "engines": {"node": ">=18.0.0"}, "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "react-native": {"types": "./dist/react-native/index.d.ts", "default": "./dist/react-native/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist/", "README.md", "LICENSE"], "homepage": "https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/core/core-auth/README.md", "keywords": ["azure", "authentication", "cloud"], "license": "MIT", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "name": "@azure/core-auth", "react-native": "./dist/react-native/index.js", "repository": {"type": "git", "url": "git+https://github.com/Azure/azure-sdk-for-js.git"}, "scripts": {"build": "npm run clean && tshy && dev-tool run extract-api", "build:samples": "echo Obsolete", "build:test": "echo skipped. actual commands inlined in browser test scripts", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,cjs,mjs,json}\"", "clean": "rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "tshy && dev-tool run extract-api", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,cjs,mjs,json}\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "lint": "eslint package.json api-extractor.json src test", "lint:fix": "eslint package.json api-extractor.json src test --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run clean && tshy && npm run unit-test:node && dev-tool run bundle && npm run unit-test:browser && npm run integration-test", "test:browser": "npm run clean && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && tshy && npm run unit-test:node && npm run integration-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "npm run clean && tshy && dev-tool run build-test && dev-tool run test:vitest --no-test-proxy --browser", "unit-test:node": "dev-tool run test:vitest --no-test-proxy"}, "sdk-type": "client", "sideEffects": false, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser", "react-native"], "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "1.8.0"}