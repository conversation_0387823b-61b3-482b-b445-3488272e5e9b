{"_from": "@apidevtools/json-schema-ref-parser@^9.0.6", "_id": "@apidevtools/json-schema-ref-parser@9.1.2", "_inBundle": false, "_integrity": "sha512-r1w81DpR+KyRWd3f+rk6TNqMgedmAxZP5v5KWlXQWlgMUUtyEJch0DKEci1SorPMiSeM8XPl7MZ3miJ60JIpQg==", "_location": "/@apidevtools/json-schema-ref-parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@apidevtools/json-schema-ref-parser@^9.0.6", "name": "@apidevtools/json-schema-ref-parser", "escapedName": "@apidevtools%2fjson-schema-ref-parser", "scope": "@apidevtools", "rawSpec": "^9.0.6", "saveSpec": null, "fetchSpec": "^9.0.6"}, "_requiredBy": ["/@apidevtools/swagger-parser"], "_resolved": "https://registry.npmjs.org/@apidevtools/json-schema-ref-parser/-/json-schema-ref-parser-9.1.2.tgz", "_shasum": "8ff5386b365d4c9faa7c8b566ff16a46a577d9b8", "_spec": "@apidevtools/json-schema-ref-parser@^9.0.6", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\@apidevtools\\swagger-parser", "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "browser": {"fs": false}, "bugs": {"url": "https://github.com/APIDevTools/json-schema-ref-parser/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"@jsdevtools/ono": "^7.1.3", "@types/json-schema": "^7.0.6", "call-me-maybe": "^1.0.1", "js-yaml": "^4.1.0"}, "deprecated": false, "description": "Parse, Resolve, and Dereference JSON Schema $ref pointers", "devDependencies": {"@babel/polyfill": "^7.12.1", "@jsdevtools/eslint-config": "^1.0.7", "@jsdevtools/host-environment": "^2.1.2", "@jsdevtools/karma-config": "^3.1.7", "@types/node": "^14.14.21", "chai": "^4.2.0", "chai-subset": "^1.6.0", "eslint": "^7.18.0", "karma": "^5.0.2", "karma-cli": "^2.0.0", "mocha": "^8.2.1", "npm-check": "^5.9.0", "nyc": "^15.0.1", "semantic-release-plugin-update-version-in-files": "^1.1.0", "shx": "^0.3.2", "typescript": "^4.0.5"}, "files": ["lib"], "homepage": "https://apitools.dev/json-schema-ref-parser/", "keywords": ["json", "schema", "jsonschema", "json-schema", "json-pointer", "$ref", "dereference", "resolve"], "license": "MIT", "main": "lib/index.js", "name": "@apidevtools/json-schema-ref-parser", "release": {"branches": ["main", "v9", {"name": "v9.1.x", "range": "9.1.x"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["semantic-release-plugin-update-version-in-files", {"files": ["dist/package.json"], "placeholder": "X.X.X"}], "@semantic-release/npm", "@semantic-release/github"]}, "repository": {"type": "git", "url": "git+https://github.com/APIDevTools/json-schema-ref-parser.git"}, "scripts": {"build": "cp LICENSE *.md dist", "clean": "shx rm -rf .nyc_output coverage", "coverage": "npm run coverage:node && npm run coverage:browser", "coverage:browser": "npm run test:browser -- --coverage", "coverage:node": "nyc node_modules/mocha/bin/mocha", "lint": "eslint lib test/fixtures test/specs", "test": "npm run test:node && npm run test:typescript && npm run test:browser && npm run lint", "test:browser": "karma start --single-run", "test:node": "mocha", "test:typescript": "tsc --noEmit --strict --lib esnext,dom test/specs/typescript-definition.spec.ts", "upgrade": "npm-check -u && npm audit fix"}, "typings": "lib/index.d.ts", "version": "9.1.2"}