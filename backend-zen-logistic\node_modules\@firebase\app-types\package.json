{"_from": "@firebase/app-types@0.6.3", "_id": "@firebase/app-types@0.6.3", "_inBundle": false, "_integrity": "sha512-/M13DPPati7FQHEQ9Minjk1HGLm/4K4gs9bR4rzLCWJg64yGtVC0zNg9gDpkw9yc2cvol/mNFxqTtd4geGrwdw==", "_location": "/@firebase/app-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@firebase/app-types@0.6.3", "name": "@firebase/app-types", "escapedName": "@firebase%2fapp-types", "scope": "@firebase", "rawSpec": "0.6.3", "saveSpec": null, "fetchSpec": "0.6.3"}, "_requiredBy": ["/@firebase/database-types"], "_resolved": "https://registry.npmjs.org/@firebase/app-types/-/app-types-0.6.3.tgz", "_shasum": "3f10514786aad846d74cd63cb693556309918f4b", "_spec": "@firebase/app-types@0.6.3", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\@firebase\\database-types", "author": {"name": "Firebase", "email": "<EMAIL>", "url": "https://firebase.google.com/"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "bundleDependencies": false, "dependency": {"@firebase/logger": "0.2.6"}, "deprecated": false, "description": "@firebase/app Types", "devDependencies": {"typescript": "4.2.2"}, "files": ["index.d.ts", "private.d.ts"], "homepage": "https://github.com/firebase/firebase-js-sdk#readme", "license": "Apache-2.0", "name": "@firebase/app-types", "repository": {"directory": "packages/app-types", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "scripts": {"test": "tsc", "test:ci": "node ../../scripts/run_tests_in_ci.js"}, "version": "0.6.3"}