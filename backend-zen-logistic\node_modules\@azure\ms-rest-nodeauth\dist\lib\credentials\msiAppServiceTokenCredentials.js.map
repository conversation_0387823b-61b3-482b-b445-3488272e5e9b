{"version": 3, "file": "msiAppServiceTokenCredentials.js", "sourceRoot": "", "sources": ["../../../lib/credentials/msiAppServiceTokenCredentials.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,+DAA0F;AAC1F,kDAAuE;AA2BvE;;GAEG;AACH,MAAa,6BAA8B,SAAQ,yCAAmB;IAqBpE;;;;;;;;;;;;;OAaG;IACH,YAAY,OAA8B;QACxC,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAE,CAAC;QAC3B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,OAAO,CAAC,WAAW;YACjB,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACzF,OAAO,CAAC,SAAS;YACf,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACnF,IACE,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,EAC1E;YACA,MAAM,IAAI,KAAK,CACb,qEAAqE;gBACnE,wGAAwG,CAC3G,CAAC;SACH;QAED,IACE,CAAC,OAAO,CAAC,SAAS;YAClB,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,QAAQ,CAAC,EACtE;YACA,MAAM,IAAI,KAAK,CACb,mEAAmE;gBACjE,oGAAoG,CACvG,CAAC;SACH;QAED,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC1B,OAAO,CAAC,aAAa,GAAG,YAAY,CAAC;SACtC;aAAM,IAAI,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC9D,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QAED,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;;OAGG;IACG,QAAQ;;YACZ,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEhD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC7D,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,IAAI,KAAK,CAAC,UAAW,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC1F,MAAM,IAAI,KAAK,CACb,yCAAyC,UAAU,CAAC,GAAG,oBAAoB,KAAK,CAAC,UAAU,EAAE,CAC9F,CAAC;aACH;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,UAAW,CAAqB,CAAC;YAC9E,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBACrB,MAAM,IAAI,KAAK,CACb,qEAAqE,KAAK,CAAC,UAAU,EAAE,CACxF,CAAC;aACH;iBAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBAC9B,MAAM,IAAI,KAAK,CACb,uEAAuE,KAAK,CAAC,UAAU,EAAE,CAC1F,CAAC;aACH;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;KAAA;IAES,qBAAqB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,CAAC;QAC5F,MAAM,UAAU,GAA0B;YACxC,GAAG,EAAE,QAAQ;YACb,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI,CAAC,SAAS;aACvB;YACD,eAAe,EAAE;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB;YACD,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,MAAM,WAAW,GAAG,IAAI,wBAAW,EAAE,CAAC;QACtC,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;CACF;AAxHD,sEAwHC"}