{"_from": "@apidevtools/openapi-schemas@^2.0.4", "_id": "@apidevtools/openapi-schemas@2.1.0", "_inBundle": false, "_integrity": "sha512-Zc1AlqrJlX3SlpupFGpiLi2EbteyP7fXmUOGup6/DnkRgjP9bgMM/ag+n91rsv0U1Gpz0H3VILA/o3bW7Ua6BQ==", "_location": "/@apidevtools/openapi-schemas", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@apidevtools/openapi-schemas@^2.0.4", "name": "@apidevtools/openapi-schemas", "escapedName": "@apidevtools%2fopenapi-schemas", "scope": "@apidevtools", "rawSpec": "^2.0.4", "saveSpec": null, "fetchSpec": "^2.0.4"}, "_requiredBy": ["/@apidevtools/swagger-parser"], "_resolved": "https://registry.npmjs.org/@apidevtools/openapi-schemas/-/openapi-schemas-2.1.0.tgz", "_shasum": "9fa08017fb59d80538812f03fc7cac5992caaa17", "_spec": "@apidevtools/openapi-schemas@^2.0.4", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\@apidevtools\\swagger-parser", "author": {"name": "<PERSON>", "url": "https://jamesmessinger.com"}, "bugs": {"url": "https://github.com/APIDevTools/openapi-schemas/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JSON Schemas for every version of the OpenAPI Specification", "devDependencies": {"@jsdevtools/eslint-config": "^1.1.4", "@jsdevtools/version-bump-prompt": "^6.1.0", "@types/chai": "^4.2.17", "@types/command-line-args": "^5.0.0", "@types/mocha": "^8.2.2", "@types/node": "^15.0.1", "chai": "^4.3.4", "eslint": "^7.25.0", "mocha": "^8.3.2", "npm-check": "^5.9.2", "nyc": "^15.1.0", "shx": "^0.3.3", "typescript": "^4.2.4"}, "engines": {"node": ">=10"}, "files": ["lib", "schemas"], "homepage": "https://apitools.dev/openapi-schemas", "keywords": ["openapi", "open-api", "swagger", "oas", "api", "rest", "json", "specification", "definition", "schema"], "license": "MIT", "main": "lib/index.js", "name": "@apidevtools/openapi-schemas", "repository": {"type": "git", "url": "git+https://github.com/APIDevTools/openapi-schemas.git"}, "scripts": {"build": "npm run build:schemas && npm run build:typescript", "build:schemas": "npm run clean && npm run clone && npm run copy", "build:typescript": "tsc", "bump": "bump --tag --push --all", "clean": "shx rm -rf .nyc_output coverage lib .tmp schemas", "clone": "git clone https://github.com/OAI/OpenAPI-Specification.git .tmp", "copy": "shx cp -r .tmp/schemas schemas", "coverage": "nyc node_modules/mocha/bin/mocha", "lint": "eslint src test", "release": "npm run upgrade && npm run clean && npm run build && npm test && npm run bump", "test": "mocha && npm run lint", "upgrade": "npm-check -u && npm audit fix", "watch": "tsc --watch"}, "types": "lib/index.d.ts", "version": "2.1.0"}