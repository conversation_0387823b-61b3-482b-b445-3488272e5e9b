import{a as ke,b as Ee,d as Be,e as Ie,g as De,j as u,l as _e,n as Ne,p as Ve,q as Ae,r as Pe,s as Me,w as Oe}from"./chunk-LKW33VZJ.js";import{E as pe,F as me,I as de,Sa as Fe,a as se,ba as ue,ca as fe,da as he,ea as ye,fa as Ce,ib as C,ja as ve,jb as F,ka as ge,kb as we,la as Se,lb as w,ma as Te,mb as k,n as le,nb as E,oa as be,qa as xe,v as ce}from"./chunk-Y7QKHPW3.js";import{b as ie,d as ne,g as ae,h as re}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{$b as z,Db as S,Eb as T,Fb as b,Gb as s,Hb as e,Hc as D,Ib as t,Jb as c,Jc as oe,Kb as $,Lb as H,Nb as Q,Qb as U,Tb as q,Ub as J,Va as i,X as M,_b as Y,ac as x,eb as g,fc as a,gc as K,ha as O,hc as y,ia as G,ib as L,ic as W,ja as R,ka as j,qc as X,sc as Z,tc as B,wc as I,xc as ee,y as P,yc as te}from"./chunk-J5YWIVYY.js";import{a as N,b as V,c as A}from"./chunk-L3UST63Y.js";var $e=["*"],f=class f extends C{constructor(){super();this.closeButton=D(!0);this.title=D("")}};f.\u0275fac=function(r){return new(r||f)},f.\u0275cmp=g({type:f,selectors:[["app-toast-simple"]],inputs:{closeButton:[1,"closeButton"],title:[1,"title"]},features:[Z([{provide:C,useExisting:M(()=>f)}]),L],ngContentSelectors:$e,decls:12,vars:6,consts:[["toastBody",""],[3,"closeButton"],["focusable","false","height","20","preserveAspectRatio","xMidYMid slice","role","img","width","20","xmlns","http://www.w3.org/2000/svg",1,"rounded","me-2"],["fill","#007aff","height","100%","width","100%"],[3,"cToastClose"],[1,"mb-1"],["thin","",3,"value"]],template:function(r,d){if(r&1&&(q(),$(0),e(1,"c-toast-header",1),R(),e(2,"svg",2),c(3,"rect",3),t(),j(),e(4,"strong"),a(5),t()(),e(6,"c-toast-body",4,0)(8,"p",5),a(9),t(),J(10),c(11,"c-progress",6),t(),H()),r&2){let p=x(7);i(),s("closeButton",d.closeButton()),i(4),K(d.title()),i(),s("cToastClose",p.toast??void 0),i(3),W("This is a dynamic toast no ",p.toast==null?null:p.toast.index()," ",p.toast==null?null:p.toast.clock),i(2),s("value",25*((p.toast==null?null:p.toast.clock)??1))}},dependencies:[w,F,we,Fe],styles:["[_nghost-%COMP%]{display:block;overflow:hidden}"]});var v=f;var He=()=>({position:"relative"}),Qe=()=>({display:"contents"});function Ue(m,n){if(m&1&&c(0,"c-toaster",2),m&2){let o=n.$implicit;s("placement",X(o))("ngClass","p-3")}}function qe(m,n){if(m&1&&(e(0,"option",12),a(1),t()),m&2){let o=n.$implicit;s("ngValue",o),i(),y(" ",o," ")}}function Je(m,n){if(m&1&&(e(0,"option",12),a(1),t()),m&2){let o=n.$implicit;s("ngValue",o),i(),y(" ",o," ")}}var Re=(l=>(l[""]="",l.primary="primary",l.secondary="secondary",l.success="success",l.info="info",l.warning="warning",l.danger="danger",l.dark="dark",l.light="light",l))(Re||{}),h=class h{constructor(){this.positions=Object.values(k);this.position=k.TopEnd;this.positionStatic=k.Static;this.colors=Object.keys(Re);this.autohide=!0;this.delay=5e3;this.fade=!0;this.toasterForm=new De({autohide:new u(this.autohide),delay:new u({value:this.delay,disabled:!this.autohide}),position:new u(this.position),fade:new u({value:!0,disabled:!1}),closeButton:new u(!0),color:new u("")});this.formChanges=this.toasterForm.valueChanges.pipe(se(),P(n=>n.autohide!==this.autohide));this.toasterComponents=oe(E)}ngOnInit(){this.formChanges.subscribe(n=>{this.autohide=n.autohide,this.position=n.position,this.fade=n.fade;let o=this.toasterForm?.get("delay");this.autohide?o?.enable():o?.disable(),this.delay=o?.enabled?n.timeout:this.delay})}addToast(){let n=this.toasterForm.value;this.toasterComponents().filter(r=>r.placement===this.toasterForm.value.position).forEach(r=>{let d=`Toast ${n.color} ${n.position}`,l=V(N({},n),{title:d,position:n.position}),{position:p}=l,_=A(l,["position"]);r.addToast(v,_,{}).setInput("closeButton",_.closeButton)})}};h.\u0275fac=function(o){return new(o||h)},h.\u0275cmp=g({type:h,selectors:[["app-toasters"]],viewQuery:function(o,r){o&1&&Y(r.toasterComponents,E,5),o&2&&z()},decls:68,vars:24,consts:[["toast",""],["xs","12"],["position","fixed",3,"ngClass","placement"],["lg","6","sm","12"],["cForm","",3,"formGroup"],[1,"my-2","mt-4"],["cFormCheckInput","","formControlName","autohide","id","autohide","type","checkbox"],["cFormCheckLabel","","for","autohide"],["cInputGroupText",""],["cFormControl","","formControlName","delay","id","delay",3,"type"],[1,"my-2"],["cSelect","","formControlName","position"],[3,"ngValue"],["cSelect","","formControlName","color"],["cFormCheckInput","","formControlName","fade","id","fade","type","checkbox"],["cFormCheckLabel","","for","fade"],["cFormCheckInput","","formControlName","closeButton","id","close","type","checkbox"],["cFormCheckLabel","","for","close"],["cButton","","color","success",1,"m-1",3,"click"],["lg","6","sm","12",1,"mt-3",3,"ngStyle"],[3,"placement","ngStyle"],["color","danger",3,"autohide","fade","visible"],["color","dark",3,"fade","visible"],[3,"closeButton"],["cTextColor","secondary"],["color","secondary",3,"autohide","fade","visible","title"],[1,"mt-3"]],template:function(o,r){if(o&1){let d=Q();e(0,"c-row")(1,"c-col",1),T(2,Ue,1,3,"c-toaster",2,S),I(4,"slice"),e(5,"c-card")(6,"c-card-header"),a(7," Toaster "),t(),e(8,"c-card-body")(9,"c-container")(10,"c-row")(11,"c-col",3)(12,"form",4)(13,"h5"),a(14,"Add toast with following props:"),t(),e(15,"c-form-check",5),c(16,"input",6),e(17,"label",7),a(18," Toast autohide "),t()(),e(19,"c-input-group")(20,"span",8),a(21,"Delay"),t(),c(22,"input",9),t(),e(23,"c-input-group",10)(24,"span",8),a(25,"Position"),t(),e(26,"select",11),T(27,qe,2,2,"option",12,S),t()(),e(29,"c-input-group",10)(30,"span",8),a(31,"Color"),t(),e(32,"select",13),T(33,Je,2,2,"option",12,S),t()(),e(35,"c-form-check",10),c(36,"input",14),e(37,"label",15),a(38,"Fade"),t()(),e(39,"c-form-check",10),c(40,"input",16),e(41,"label",17),a(42,"Close Button"),t()(),c(43,"hr"),e(44,"button",18),U("click",function(){return O(d),G(r.addToast())}),a(45," Add toast "),t()()(),e(46,"c-col",19)(47,"c-toaster",20)(48,"c-toast",21)(49,"c-toast-header"),a(50,"Toast title"),t(),e(51,"c-toast-body"),a(52,"This is a static toast in a static toaster"),t()(),e(53,"c-toast",22)(54,"c-toast-header",23),a(55,"Toast title"),t(),e(56,"c-toast-body",24),a(57,"This is a static toast in a static toaster"),t()(),e(58,"app-toast-simple",25,0),a(60),t()()()()()()(),e(61,"c-card",26)(62,"c-card-body")(63,"c-row")(64,"c-col")(65,"p"),a(66),I(67,"json"),t()()()()()()()}if(o&2){let d=x(59);i(2),b(te(4,17,r.positions,1)),i(10),s("formGroup",r.toasterForm),i(10),s("type","number"),i(5),b(r.positions),i(6),b(r.colors),i(13),s("ngStyle",B(22,He)),i(),s("placement",r.positionStatic)("ngStyle",B(23,Qe)),i(),s("autohide",!1)("fade",!0)("visible",!0),i(5),s("fade",!0)("visible",!0),i(),s("closeButton",!1),i(4),s("autohide",!1)("fade",!0)("visible",!0)("title","App Toast"),i(2),y(" This is a toast in static positioned App toaster! ",d.index()," "),i(6),y("Form value: ",ee(67,20,r.toasterForm.value))}},dependencies:[xe,be,E,ie,ce,pe,de,me,Te,Oe,_e,Pe,Me,Ee,ke,Ae,Be,Ie,Ne,Ve,ue,he,Ce,fe,ge,Se,ye,ve,le,ne,C,w,F,v,ae,re],encapsulation:2});var Ge=h;export{Re as Colors,Ge as ToastersComponent};
