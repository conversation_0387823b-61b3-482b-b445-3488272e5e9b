// Here you can add other styles

// custom .chartjs-tooltip-body-item padding
@use "charts";

// custom tweaks for scrollbar styling (wip)
@use "scrollbar";

 // custom calendar today cell color
.calendar-cell.today {
  --cui-calendar-cell-today-color: var(--cui-info) !important;
}

// Theme Customizer Gradient Variables
$bg-hibiscus: linear-gradient(to right bottom, #f05f57, #c83d5c, #99245a, #671351, #360940);
$bg-purple-pizzazz: linear-gradient(to right bottom, #662d86, #8b2a8a, #ae2389, #cf1d83, #ed1e79);
$bg-blue-lagoon: linear-gradient(to right bottom, #144e68, #006d83, #008d92, #00ad91, #57ca85);
$bg-electric-violet: linear-gradient(to left top, #4a00e0, #600de0, #7119e1, #8023e1, #8e2de2);
$bg-portage: linear-gradient(to left top, #97abff, #798ce5, #5b6ecb, #3b51b1, #123597);
$bg-tundora: linear-gradient(to left top, #474747, #4a4a4a, #4c4d4d, #4f5050, #525352);

// Theme Background Classes
.bg-hibiscus {
  background-image: $bg-hibiscus;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-purple-pizzazz {
  background-image: $bg-purple-pizzazz;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-blue-lagoon {
  background-image: $bg-blue-lagoon;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-electric-violet {
  background-image: $bg-electric-violet;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-portage {
  background-image: $bg-portage;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}

.bg-tundora {
  background-image: $bg-tundora;
  background-size: cover;
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  transition: background 0.3s;
}



// Icon animation class
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// custom select week cursor pointer
.select-week .calendar-row.current {
  cursor: pointer;
}
