var k=Object.defineProperty,q=Object.defineProperties;var r=Object.getOwnPropertyDescriptors;var f=Object.getOwnPropertySymbols;var l=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var n=a=>{throw TypeError(a)};var j=(a,b,c)=>b in a?k(a,b,{enumerable:!0,configurable:!0,writable:!0,value:c}):a[b]=c,t=(a,b)=>{for(var c in b||={})l.call(b,c)&&j(a,c,b[c]);if(f)for(var c of f(b))m.call(b,c)&&j(a,c,b[c]);return a},u=(a,b)=>q(a,r(b));var v=(a,b)=>{var c={};for(var d in a)l.call(a,d)&&b.indexOf(d)<0&&(c[d]=a[d]);if(a!=null&&f)for(var d of f(a))b.indexOf(d)<0&&m.call(a,d)&&(c[d]=a[d]);return c};var w=(a,b)=>{for(var c in b)k(a,c,{get:b[c],enumerable:!0})};var s=(a,b,c)=>b.has(a)||n("Cannot "+c);var x=(a,b,c)=>(s(a,b,"read from private field"),c?c.call(a):b.get(a)),y=(a,b,c)=>b.has(a)?n("Cannot add the same private member more than once"):b instanceof WeakSet?b.add(a):b.set(a,c);var z=(a,b,c)=>new Promise((d,i)=>{var o=e=>{try{g(c.next(e))}catch(h){i(h)}},p=e=>{try{g(c.throw(e))}catch(h){i(h)}},g=e=>e.done?d(e.value):Promise.resolve(e.value).then(o,p);g((c=c.apply(a,b)).next())});export{t as a,u as b,v as c,w as d,x as e,y as f,z as g};
