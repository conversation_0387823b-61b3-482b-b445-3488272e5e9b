var n=function(t,e){if(!(typeof window>"u")&&!(typeof document>"u")){var r=e??document.body;return window.getComputedStyle(r,null).getPropertyValue(t).replace(/^\s/,"")}};var f=function(t){if(typeof t>"u")throw new TypeError("Hex color is not defined");if(t==="transparent")return"#00000000";var e=t.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);if(!e)throw new Error("".concat(t," is not a valid rgb color"));var r="0".concat(parseInt(e[1],10).toString(16)),o="0".concat(parseInt(e[2],10).toString(16)),a="0".concat(parseInt(e[3],10).toString(16));return"#".concat(r.slice(-2)).concat(o.slice(-2)).concat(a.slice(-2))};export{n as a,f as b};
