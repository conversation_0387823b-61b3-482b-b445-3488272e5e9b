import{n as f,p as I,x as v}from"./chunk-VLR5A2CC.js";import{A as r,I as g,Y as S,aa as h,c as E,g as l,m as i,p as m}from"./chunk-J5YWIVYY.js";import{a as d}from"./chunk-L3UST63Y.js";var b={production:!1,apiURL:"http://localhost:3000/api/",urlFront:"http://localhost:4200",apiPdf:"http://localhost:8080/api/",urlChat:"chat.zenlogistic-test.tn"};var a=class a{constructor(){this.SESSION_KEYS={USER_ID:"iduser",EMAIL:"userEmail",ROLE:"userRole",TYPE_UTILISATEUR:"type_utilisateur",NOM:"nom",PRENOM:"prenom",STATUT:"statut",LOGIN_TIME:"loginTime",EXPIRES_AT:"expiresAt",LAST_ACTIVITY:"lastActivity"};this.defaultConfig={sessionTimeoutMinutes:480,inactivityTimeoutMinutes:60,autoRefresh:!0};this.sessionValidSubject=new l(!1);this.sessionValid$=this.sessionValidSubject.asObservable();this.validateCurrentSession(),this.defaultConfig.autoRefresh&&this.setupPeriodicValidation()}storeSession(e,t){let s=d(d({},this.defaultConfig),t),o=new Date,y=new Date(o.getTime()+s.sessionTimeoutMinutes*6e4),R={iduser:e.id,email:e.email,userRole:e.type_utilisateur||e.role||"user",type_utilisateur:e.type_utilisateur,nom:e.nom,prenom:e.prenom,statut:e.statut,loginTime:o.toISOString(),expiresAt:y.toISOString(),lastActivity:o.toISOString()};Object.entries(R).forEach(([O,p])=>{if(p!=null){let U=this.getStorageKey(O);sessionStorage.setItem(U,p.toString())}}),this.sessionValidSubject.next(!0)}getSession(){try{let e=sessionStorage.getItem(this.SESSION_KEYS.USER_ID),t=sessionStorage.getItem(this.SESSION_KEYS.EMAIL),s=sessionStorage.getItem(this.SESSION_KEYS.ROLE);return!e||!t||!s?null:{iduser:parseInt(e),email:t,userRole:s,type_utilisateur:sessionStorage.getItem(this.SESSION_KEYS.TYPE_UTILISATEUR)||void 0,nom:sessionStorage.getItem(this.SESSION_KEYS.NOM)||void 0,prenom:sessionStorage.getItem(this.SESSION_KEYS.PRENOM)||void 0,statut:sessionStorage.getItem(this.SESSION_KEYS.STATUT)||void 0,loginTime:sessionStorage.getItem(this.SESSION_KEYS.LOGIN_TIME)||new Date().toISOString(),expiresAt:sessionStorage.getItem(this.SESSION_KEYS.EXPIRES_AT)||void 0,lastActivity:sessionStorage.getItem(this.SESSION_KEYS.LAST_ACTIVITY)||void 0}}catch(e){return console.error("Error retrieving session data:",e),null}}updateLastActivity(){let e=new Date().toISOString();sessionStorage.setItem(this.SESSION_KEYS.LAST_ACTIVITY,e)}validateSession(){let e=this.getSession();if(!e)return{isValid:!1,reason:"MISSING_DATA",message:"No session data found"};let t=new Date;if(e.expiresAt){let s=new Date(e.expiresAt);if(t>s)return{isValid:!1,reason:"EXPIRED",message:"Session has expired"}}if(e.lastActivity){let s=new Date(e.lastActivity),o=new Date(s.getTime()+this.defaultConfig.inactivityTimeoutMinutes*6e4);if(t>o)return{isValid:!1,reason:"INACTIVE_TOO_LONG",message:"Session expired due to inactivity"}}return{isValid:!0}}clearSession(){Object.values(this.SESSION_KEYS).forEach(e=>{sessionStorage.removeItem(e)}),this.sessionValidSubject.next(!1)}hasValidSession(){return this.validateSession().isValid}getSessionValue(e){let t=this.getStorageKey(e);return sessionStorage.getItem(t)}setSessionValue(e,t){let s=this.getStorageKey(e);sessionStorage.setItem(s,t),e!=="lastActivity"&&this.updateLastActivity()}extendSession(e=60){let t=sessionStorage.getItem(this.SESSION_KEYS.EXPIRES_AT);if(t){let s=new Date(new Date(t).getTime()+e*6e4);sessionStorage.setItem(this.SESSION_KEYS.EXPIRES_AT,s.toISOString())}}getStorageKey(e){return{iduser:this.SESSION_KEYS.USER_ID,email:this.SESSION_KEYS.EMAIL,userRole:this.SESSION_KEYS.ROLE,type_utilisateur:this.SESSION_KEYS.TYPE_UTILISATEUR,nom:this.SESSION_KEYS.NOM,prenom:this.SESSION_KEYS.PRENOM,statut:this.SESSION_KEYS.STATUT,loginTime:this.SESSION_KEYS.LOGIN_TIME,expiresAt:this.SESSION_KEYS.EXPIRES_AT,lastActivity:this.SESSION_KEYS.LAST_ACTIVITY}[e]}validateCurrentSession(){let e=this.validateSession();this.sessionValidSubject.next(e.isValid)}setupPeriodicValidation(){setInterval(()=>{this.validateCurrentSession()},5*60*1e3)}};a.\u0275fac=function(t){return new(t||a)},a.\u0275prov=S({token:a,factory:a.\u0275fac,providedIn:"root"});var c=a;var n={headers:new f({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})},u=class u{constructor(e,t,s){this.http=e;this.router=t;this.sessionStorageService=s;this.apiURL=b.apiURL;this.isAuthenticatedSubject=new l(!1);this.isAuthenticated$=this.isAuthenticatedSubject.asObservable();this.userSubject=new l(null);this.currentUser$=this.userSubject.asObservable();this.loadingSubject=new l(!1);this.loading$=this.loadingSubject.asObservable();this.checkExistingSession(),this.sessionStorageService.sessionValid$.subscribe(o=>{!o&&this.isAuthenticatedSubject.value&&this.handleSessionExpired()})}login(e){return this.loadingSubject.next(!0),this.http.post(this.apiURL+"customers/login",e,n).pipe(m(t=>{if(console.log("Login response:",t),t&&t.message&&t.message.includes("Not found Customer"))throw{type:"INVALID_CREDENTIALS",message:"Invalid email or password",details:t.message};if(t&&t.id)return this.sessionStorageService.storeSession(t),this.isAuthenticatedSubject.next(!0),this.userSubject.next({iduser:t.id,email:t.email,role:t.type_utilisateur||t.role||"user",type_utilisateur:t.type_utilisateur,nom:t.nom,prenom:t.prenom,statut:t.statut}),t;throw{type:"INVALID_RESPONSE",message:"Invalid response format from server",details:"Server response does not contain expected user data"}}),r(t=>(console.error("Login error:",t),this.isAuthenticatedSubject.next(!1),this.userSubject.next(null),i(()=>this.handleAuthError(t)))),g(()=>this.loadingSubject.next(!1)))}logout(){return this.loadingSubject.next(!0),this.sessionStorageService.clearSession(),this.isAuthenticatedSubject.next(!1),this.userSubject.next(null),this.loadingSubject.next(!1),new E(e=>{e.next(),e.complete()})}isAuthenticated(){return this.isAuthenticatedSubject.value}getUser(){return this.userSubject.value}checkExistingSession(){let e=this.sessionStorageService.getSession();if(e&&this.sessionStorageService.hasValidSession()){let t={iduser:e.iduser,email:e.email,role:e.userRole,type_utilisateur:e.type_utilisateur,nom:e.nom,prenom:e.prenom,statut:e.statut};this.isAuthenticatedSubject.next(!0),this.userSubject.next(t),this.sessionStorageService.updateLastActivity()}else e&&this.sessionStorageService.clearSession()}handleSessionExpired(){console.log("Session expired, logging out user"),this.sessionStorageService.clearSession(),this.isAuthenticatedSubject.next(!1),this.userSubject.next(null),this.router.navigate(["/login"])}extendSession(e=60){this.isAuthenticated()&&this.sessionStorageService.extendSession(e)}getSessionValidation(){return this.sessionStorageService.validateSession()}handleAuthError(e){let t;return e.status===0?t={type:"NETWORK_ERROR",message:"Network error. Please check your internet connection and try again.",status:0}:e.status===401?t={type:"INVALID_CREDENTIALS",message:"Invalid email or password. Please check your credentials and try again.",status:401}:e.status===400?t={type:"VALIDATION_ERROR",message:"Invalid request format. Please check your input and try again.",status:400}:e.status>=500?t={type:"SERVER_ERROR",message:"Server error. Please try again later or contact support.",status:e.status}:t={type:"UNKNOWN_ERROR",message:e.error?.message||"An unexpected error occurred. Please try again.",status:e.status},t}register(e){return this.loadingSubject.next(!0),this.http.post(this.apiURL+"customers",e,n).pipe(r(t=>(console.error("Registration error:",t),i(()=>this.handleAuthError(t)))),g(()=>this.loadingSubject.next(!1)))}updateLastConnection(e){return this.http.put(this.apiURL+"lastConnexion/"+e,null,n).pipe(r(t=>(console.error("Update last connection error:",t),i(()=>this.handleAuthError(t)))))}sendActivationEmail(e){return this.http.post(this.apiURL+"email",e,n).pipe(r(t=>(console.error("Send activation email error:",t),i(()=>this.handleAuthError(t)))))}findUserByEmail(e){return this.http.get(this.apiURL+`getUserByMail/${e}`,n).pipe(r(t=>(console.error("Find user by email error:",t),i(()=>this.handleAuthError(t)))))}verifyUsername(e){return this.http.get(this.apiURL+"custo/"+e,n).pipe(r(t=>(console.error("Verify username error:",t),i(()=>this.handleAuthError(t)))))}handleAuthenticationError(e){(e.status===401||e.status===403||e.status===0)&&(this.sessionStorageService.clearSession(),this.isAuthenticatedSubject.next(!1),this.userSubject.next(null),this.router.navigate(["/login"]))}getAllRoles(){return this.http.get(this.apiURL+"roles",n).pipe(r(e=>(console.error("Get all roles error:",e),i(()=>this.handleAuthError(e)))))}updateUserInfo(e){return this.http.put(this.apiURL+"updateUserById/"+e.id,e,n).pipe(r(t=>(console.error("Update user info error:",t),i(()=>this.handleAuthError(t)))))}createChefUser(e){return this.http.post(this.apiURL+"createChefUser",e,n).pipe(r(t=>(console.error("Create chef user error:",t),i(()=>this.handleAuthError(t)))))}findUsersByChef(e){return this.http.get(this.apiURL+`findUsersByChef/${e}`,n).pipe(r(t=>(console.error("Find users by chef error:",t),i(()=>this.handleAuthError(t)))))}findAllChefs(){return this.http.get(this.apiURL+"findAllChef",n).pipe(r(e=>(console.error("Find all chefs error:",e),i(()=>this.handleAuthError(e)))))}findAllClients(){return this.http.get(this.apiURL+"findAllClient",n).pipe(r(e=>(console.error("Find all clients error:",e),i(()=>this.handleAuthError(e)))))}};u.\u0275fac=function(t){return new(t||u)(h(I),h(v),h(c))},u.\u0275prov=S({token:u,factory:u.\u0275fac,providedIn:"root"});var A=u;export{b as a,c as b,A as c};
