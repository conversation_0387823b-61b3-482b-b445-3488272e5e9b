{"version": 3, "file": "tokenCredentialsBase.js", "sourceRoot": "", "sources": ["../../../lib/credentials/tokenCredentialsBase.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,kDAA8E;AAC9E,gEAAuD;AAGvD,yCAMmB;AAEnB,MAAsB,oBAAoB;IAGxC,YACkB,QAAgB,EACzB,MAAc,EACL,aAA6B,EAC7B,cAA2B,+BAAW,CAAC,UAAU,EAC1D,aAAyB,IAAI,uBAAW,EAAE;QAJjC,aAAQ,GAAR,QAAQ,CAAQ;QACzB,WAAM,GAAN,MAAM,CAAQ;QACL,kBAAa,GAAb,aAAa,CAAgB;QAC7B,gBAAW,GAAX,WAAW,CAAsC;QAC1D,eAAU,GAAV,UAAU,CAAgC;QAEjD,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACnD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QAED,IAAI,IAAI,CAAC,aAAa,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;YAC5E,MAAM,IAAI,KAAK,CACb,GAAG;6EACkE,EAAE,CACxE,CAAC;SACH;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/E,IAAI,CAAC,WAAW,GAAG,IAAI,iCAAqB,CAC1C,YAAY,EACZ,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAClC,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAEM,SAAS,CAAC,MAAc;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/E,IAAI,CAAC,WAAW,GAAG,IAAI,iCAAqB,CAC1C,YAAY,EACZ,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAClC,IAAI,CAAC,UAAU,CAChB,CAAC;IACJ,CAAC;IAES,4BAA4B;QACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC;QAC1D,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;YAC9B,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE;gBAChD,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,8BAAwC,CAAC;aACtE;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE;gBACvD,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,eAAyB,CAAC;aACvD;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAES,iBAAiB,CAAC,QAAiB;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;QAErD,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpD,IAAI,CAAC,WAAW,CAAC,YAAY,CAC3B,QAAQ,EACR,QAAS,EACT,IAAI,CAAC,QAAQ,EACb,CAAC,KAAY,EAAE,aAA4C,EAAE,EAAE;gBAC7D,IAAI,KAAK,EAAE;oBACT,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;iBACtB;gBAED,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,gBAAgB,EAAE;oBACzD,OAAO,MAAM,CAAC,aAAa,CAAC,CAAC;iBAC9B;gBAED,OAAO,OAAO,CAAC,aAA8B,CAAC,CAAC;YACjD,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IASD;;;;OAIG;IACU,WAAW,CAAC,WAAwB;;YAC/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,sBAAe,CAAC,eAAe,CAAC,aAAa,EAC7C,GAAG,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,WAAW,EAAE,CAC1D,CAAC;YACF,OAAO,WAAW,CAAC;QACrB,CAAC;KAAA;CACF;AApGD,oDAoGC"}