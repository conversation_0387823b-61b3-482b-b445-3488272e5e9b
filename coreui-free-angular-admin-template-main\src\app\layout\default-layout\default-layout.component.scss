:host::ng-deep {
  .ng-scrollbar {
    --scrollbar-padding: 1px;
    --scrollbar-size: 5px;
    --scrollbar-thumb-color: var(--cui-gray-500, #aab3c5);
    --scrollbar-thumb-hover-color: var(--cui-gray-400, #cfd4de);
    --scrollbar-hover-size: calc(var(--scrollbar-size) * 1.5);
    --scrollbar-border-radius: 5px;
  }

  .ng-scroll-content {
    display: flex;
    min-height: 100%;
  }

  //.sidebar-nav {
  //  scrollbar-color: var(--cui-gray-500, #444) transparent;
  //}
}

// ng-scrollbar css variables
//.cui-scrollbar {
//  --scrollbar-border-radius: 7px;
//  --scrollbar-padding: 1px;
//  --scrollbar-viewport-margin: 0;
//  --scrollbar-track-color: transparent;
//  --scrollbar-wrapper-color: transparent;
//  --scrollbar-thumb-color: rgba(0, 0, 0, 0.2);
//  --scrollbar-thumb-hover-color: var(--scrollbar-thumb-color);
//  --scrollbar-size: 5px;
//  --scrollbar-hover-size: var(--scrollbar-size);
//  --scrollbar-thumb-transition: height ease-out 150ms, width ease-out 150ms;
//  --scrollbar-track-transition: height ease-out 150ms, width ease-out 150ms;
//}

// Customizer toggle button
.customizer-toggle-btn {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background: var(--cui-primary);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50% 0 0 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1050;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    background: var(--cui-primary-dark, #0056b3);
    transform: translateY(-50%) translateX(-5px);
  }

  i {
    font-size: 18px;
  }
}

// Theme layout classes
:host::ng-deep {
  .layout-dark {
    --cui-body-bg: #1e1e2f;
    --cui-body-color: #ffffff;
  }

  .layout-transparent {
    --cui-body-bg: transparent;
  }

  // Sidebar size variations
  .sidebar-sm {
    .sidebar {
      width: 200px;
    }
  }

  .sidebar-lg {
    .sidebar {
      width: 300px;
    }
  }
}

// Responsive behavior
@media (max-width: 768px) {
  .customizer-toggle-btn {
    display: none;
  }
}
