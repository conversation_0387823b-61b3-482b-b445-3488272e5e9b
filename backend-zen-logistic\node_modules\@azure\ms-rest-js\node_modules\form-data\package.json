{"_args": [["form-data@2.5.1", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "form-data@2.5.1", "_id": "form-data@2.5.1", "_inBundle": false, "_integrity": "sha512-m21N3WOmEEURgk6B9GLOE4RuWOFf28Lhh9qGYeNlGq4VDXUlJy2th2slBNU8Gp8EzloYZOibZJ7t5ecIrFSjVA==", "_location": "/@azure/ms-rest-js/form-data", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "form-data@2.5.1", "name": "form-data", "escapedName": "form-data", "rawSpec": "2.5.1", "saveSpec": null, "fetchSpec": "2.5.1"}, "_requiredBy": ["/@azure/ms-rest-js"], "_resolved": "https://registry.npmjs.org/form-data/-/form-data-2.5.1.tgz", "_spec": "2.5.1", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "browser": "./lib/browser", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "devDependencies": {"@types/node": "^12.0.10", "browserify": "^13.1.1", "browserify-istanbul": "^2.0.0", "coveralls": "^3.0.4", "cross-spawn": "^6.0.5", "eslint": "^6.0.1", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "in-publish": "^2.0.0", "is-node-modern": "^1.0.0", "istanbul": "^0.4.5", "obake": "^0.1.2", "phantomjs-prebuilt": "^2.1.13", "pkgfiles": "^2.3.0", "pre-commit": "^1.1.3", "request": "^2.88.0", "rimraf": "^2.7.1", "tape": "^4.6.2", "typescript": "^3.5.2"}, "engines": {"node": ">= 0.12"}, "homepage": "https://github.com/form-data/form-data#readme", "license": "MIT", "main": "./lib/form_data", "name": "form-data", "pre-commit": ["lint", "ci-test", "check"], "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "scripts": {"browser": "browserify -t browserify-istanbul test/run-browser.js | obake --coverage", "check": "istanbul check-coverage coverage/coverage*.json", "ci-lint": "is-node-modern 8 && npm run lint || is-node-not-modern 8", "ci-test": "npm run test && npm run browser && npm run report", "debug": "verbose=1 ./test/run.js", "files": "pkgfiles --sort=name", "get-version": "node -e \"console.log(require('./package.json').version)\"", "lint": "eslint lib/*.js test/*.js test/integration/*.js", "postpublish": "npm run restore-readme", "posttest": "istanbul report lcov text", "predebug": "rimraf coverage test/tmp", "prepublish": "in-publish && npm run update-readme || not-in-publish", "pretest": "rimraf coverage test/tmp", "report": "istanbul report lcov text", "restore-readme": "mv README.md.bak README.md", "test": "istanbul cover test/run.js", "update-readme": "sed -i.bak 's/\\/master\\.svg/\\/v'$(npm --silent run get-version)'.svg/g' README.md"}, "typings": "./index.d.ts", "version": "2.5.1"}