import { Compo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef, Renderer2 } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';
import { NgScrollbar } from 'ngx-scrollbar';
import { Subscription } from 'rxjs';

import { IconDirective } from '@coreui/icons-angular';
import {
  ContainerComponent,
  ShadowOnScrollDirective,
  SidebarBrandComponent,
  SidebarComponent,
  SidebarFooterComponent,
  SidebarHeaderComponent,
  SidebarNavComponent,
  SidebarToggleDirective,
  SidebarTogglerDirective
} from '@coreui/angular';

import { DefaultFooterComponent, DefaultHeaderComponent } from './';
import { navItems } from './_nav';
import { CommonModule } from '@angular/common';
import { CustomizerThemeComponent } from './customizer-theme/customizer-theme.component';
import { LayoutService } from '../../../services';
import { CustomizerOptions } from '../../../models';

function isOverflown(element: HTMLElement) {
  return (
    element.scrollHeight > element.clientHeight ||
    element.scrollWidth > element.clientWidth
  );
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './default-layout.component.html',
  styleUrls: ['./default-layout.component.scss'],
  imports: [
    SidebarComponent,
    SidebarHeaderComponent,
    SidebarBrandComponent,
    SidebarNavComponent,
    SidebarFooterComponent,
    SidebarToggleDirective,
    SidebarTogglerDirective,
    ContainerComponent,
    DefaultFooterComponent,
    DefaultHeaderComponent,
    IconDirective,
    NgScrollbar,
    RouterOutlet,
    RouterLink,
    ShadowOnScrollDirective,
    CommonModule,
    CustomizerThemeComponent
  ]
})
export class DefaultLayoutComponent implements OnInit, OnDestroy {
  @ViewChild('sidebarBgImage', { static: false }) sidebarBgImage!: ElementRef;

  public navItems = [...navItems];
  private layoutSubscription: Subscription = new Subscription();

  constructor(
    private layoutService: LayoutService,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    // Subscribe to customizer changes to apply theme modifications
    this.layoutSubscription.add(
      this.layoutService.customizerChangeEmitted$.subscribe((change: CustomizerOptions | string) => {
        if (typeof change === 'object' && change) {
          this.applyThemeChanges(change);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.layoutSubscription.unsubscribe();
  }

  toggleCustomizer(): void {
    this.layoutService.emitCustomizerChange('toggle');
  }

  private applyThemeChanges(options: CustomizerOptions): void {
    const body = document.body;
    const sidebar = document.getElementById('sidebar1');

    if (sidebar) {
      // Apply sidebar background color using data attributes (CoreUI approach)
      if (options.bgColor) {
        sidebar.setAttribute('data-coreui-theme', 'dark');
        sidebar.setAttribute('data-background-color', options.bgColor);

        // Also apply as CSS custom property for more control
        sidebar.style.setProperty('--cui-sidebar-bg', options.bgColor);
      }

      // Apply sidebar background image
      if (options.bgImage && options.bgImageDisplay) {
        this.applySidebarBackgroundImage(sidebar, options.bgImage);
      } else {
        this.removeSidebarBackgroundImage(sidebar);
      }

      // Apply sidebar size classes to body
      if (options.sidebarSize) {
        body.classList.remove('sidebar-sm', 'sidebar-md', 'sidebar-lg');
        body.classList.add(options.sidebarSize);
      }

      // Apply compact menu (narrow sidebar)
      if (options.compactMenu !== undefined) {
        if (options.compactMenu) {
          sidebar.classList.add('sidebar-narrow');
        } else {
          sidebar.classList.remove('sidebar-narrow');
        }
      }


    }
  }

  private applySidebarBackgroundImage(sidebar: HTMLElement, imageUrl: string): void {
    // Remove existing background image element
    const existingBgImage = sidebar.querySelector('.sidebar-background');
    if (existingBgImage) {
      existingBgImage.remove();
    }

    // Create new background image element
    const bgImageElement = this.renderer.createElement('div');
    this.renderer.addClass(bgImageElement, 'sidebar-background');
    this.renderer.setStyle(bgImageElement, 'background-image', `url("${imageUrl}")`);
    this.renderer.setStyle(bgImageElement, 'position', 'absolute');
    this.renderer.setStyle(bgImageElement, 'top', '0');
    this.renderer.setStyle(bgImageElement, 'left', '0');
    this.renderer.setStyle(bgImageElement, 'width', '100%');
    this.renderer.setStyle(bgImageElement, 'height', '100%');
    this.renderer.setStyle(bgImageElement, 'background-size', 'cover');
    this.renderer.setStyle(bgImageElement, 'background-position', 'center');
    this.renderer.setStyle(bgImageElement, 'background-repeat', 'no-repeat');
    this.renderer.setStyle(bgImageElement, 'z-index', '-1');
    this.renderer.setStyle(bgImageElement, 'opacity', '0.1');

    // Append to sidebar
    this.renderer.appendChild(sidebar, bgImageElement);
  }

  private removeSidebarBackgroundImage(sidebar: HTMLElement): void {
    const existingBgImage = sidebar.querySelector('.sidebar-background');
    if (existingBgImage) {
      existingBgImage.remove();
    }
  }
}
