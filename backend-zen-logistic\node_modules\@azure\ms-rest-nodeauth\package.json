{"_args": [["@azure/ms-rest-nodeauth@3.1.1", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "@azure/ms-rest-nodeauth@3.1.1", "_id": "@azure/ms-rest-nodeauth@3.1.1", "_inBundle": false, "_integrity": "sha512-UA/8dgLy3+ZiwJjAZHxL4MUB14fFQPkaAOZ94jsTW/Z6WmoOeny2+cLk0+dyIX/iH6qSrEWKwbStEeB970B9pA==", "_location": "/@azure/ms-rest-nodeauth", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@azure/ms-rest-nodeauth@3.1.1", "name": "@azure/ms-rest-nodeauth", "escapedName": "@azure%2fms-rest-nodeauth", "scope": "@azure", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/tedious"], "_resolved": "https://registry.npmjs.org/@azure/ms-rest-nodeauth/-/ms-rest-nodeauth-3.1.1.tgz", "_spec": "3.1.1", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "Microsoft Corporation", "email": "<EMAIL>", "url": "https://github.com/Azure/ms-rest-nodeauth"}, "bugs": {"url": "http://github.com/Azure/ms-rest-nodeauth/issues"}, "dependencies": {"@azure/ms-rest-azure-env": "^2.0.0", "@azure/ms-rest-js": "^2.0.4", "adal-node": "^0.2.2"}, "description": "Azure Authentication library in node.js with type definitions.", "devDependencies": {"@azure/arm-subscriptions": "^2.0.0", "@ts-common/azure-js-dev-tools": "^22.2.0", "@types/chai": "^4.1.7", "@types/dotenv": "^6.1.1", "@types/mocha": "^5.2.7", "@types/node": "^10.12.0", "chai": "^4.2.0", "dotenv": "^8.0.0", "mocha": "^6.2.0", "nock": "^10.0.6", "npm-run-all": "^4.1.5", "nyc": "^14.1.1", "prettier": "2.2.1", "rollup": "^1.18.0", "rollup-plugin-sourcemaps": "^0.4.2", "ts-node": "^8.3.0", "tslint": "^5.18.0", "typescript": "~3.5.3"}, "files": ["dist/lib/**/*.js", "dist/lib/**/*.js.map", "dist/lib/**/*.d.ts", "dist/lib/**/*.d.ts.map", "lib/**/*.ts", "LICENSE", "README.md", "tsconfig.json"], "homepage": "https://github.com/Azure/ms-rest-nodeauth", "keywords": ["node", "azure", "autorest", "authentication", "environment", "adal"], "license": "MIT", "main": "./dist/lib/msRestNodeAuth.js", "name": "@azure/ms-rest-nodeauth", "repository": {"type": "git", "url": "git+ssh://**************/Azure/ms-rest-nodeauth.git"}, "scripts": {"build": "run-s build:tsc build:rollup", "build:rollup": "rollup -c rollup.config.js", "build:tsc": "tsc -p tsconfig.json", "check:packagejsonversion": "ts-node ./.scripts/checkPackageJsonVersion.ts", "format": "prettier --write \"./**/*.ts\"", "prepack": "npm install && npm run build", "test": "npm run build && run-p test:tslint test:unit", "test:tslint": "tslint -p . -c tslint.json", "test:unit": "mocha"}, "types": "./dist/lib/msRestNodeAuth.d.ts", "version": "3.1.1"}