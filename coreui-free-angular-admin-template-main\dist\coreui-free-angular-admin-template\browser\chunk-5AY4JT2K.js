import{$b as zn,Bc as Nn,Cc as Ce,Ec as Vn,Gb as An,Gc as Pe,Hb as Dn,Hc as at,Ib as Tn,Ic as Hn,Nb as Ln,Nc as Wn,Qb as En,Qc as si,Rc as es,Sc as $n,Tb as Rn,Ub as In,Za as Cn,_b as Fn,ba as ii,bc as Bn,eb as Pn,ec as jn,ha as wn,ia as Sn,nb as On,va as kn}from"./chunk-J5YWIVYY.js";import{a as ht,b as Ct}from"./chunk-L3UST63Y.js";function Ll(){this.__data__=[],this.size=0}var Yn=Ll;function El(e,i){return e===i||e!==e&&i!==i}var Pt=El;function Rl(e,i){for(var t=e.length;t--;)if(Pt(e[t][0],i))return t;return-1}var Ot=Rl;var Il=Array.prototype,Fl=Il.splice;function zl(e){var i=this.__data__,t=Ot(i,e);if(t<0)return!1;var s=i.length-1;return t==s?i.pop():Fl.call(i,t,1),--this.size,!0}var Un=zl;function Bl(e){var i=this.__data__,t=Ot(i,e);return t<0?void 0:i[t][1]}var Xn=Bl;function jl(e){return Ot(this.__data__,e)>-1}var Gn=jl;function Nl(e,i){var t=this.__data__,s=Ot(t,e);return s<0?(++this.size,t.push([e,i])):t[s][1]=i,this}var Kn=Nl;function te(e){var i=-1,t=e==null?0:e.length;for(this.clear();++i<t;){var s=e[i];this.set(s[0],s[1])}}te.prototype.clear=Yn;te.prototype.delete=Un;te.prototype.get=Xn;te.prototype.has=Gn;te.prototype.set=Kn;var At=te;function Vl(){this.__data__=new At,this.size=0}var qn=Vl;function Hl(e){var i=this.__data__,t=i.delete(e);return this.size=i.size,t}var Zn=Hl;function Wl(e){return this.__data__.get(e)}var Jn=Wl;function $l(e){return this.__data__.has(e)}var Qn=$l;var Yl=typeof global=="object"&&global&&global.Object===Object&&global,ni=Yl;var Ul=typeof self=="object"&&self&&self.Object===Object&&self,Xl=ni||Ul||Function("return this")(),st=Xl;var Gl=st.Symbol,ee=Gl;var to=Object.prototype,Kl=to.hasOwnProperty,ql=to.toString,Oe=ee?ee.toStringTag:void 0;function Zl(e){var i=Kl.call(e,Oe),t=e[Oe];try{e[Oe]=void 0;var s=!0}catch{}var n=ql.call(e);return s&&(i?e[Oe]=t:delete e[Oe]),n}var eo=Zl;var Jl=Object.prototype,Ql=Jl.toString;function tc(e){return Ql.call(e)}var io=tc;var ec="[object Null]",ic="[object Undefined]",so=ee?ee.toStringTag:void 0;function sc(e){return e==null?e===void 0?ic:ec:so&&so in Object(e)?eo(e):io(e)}var Dt=sc;function nc(e){var i=typeof e;return e!=null&&(i=="object"||i=="function")}var J=nc;var oc="[object AsyncFunction]",rc="[object Function]",ac="[object GeneratorFunction]",lc="[object Proxy]";function cc(e){if(!J(e))return!1;var i=Dt(e);return i==rc||i==ac||i==oc||i==lc}var ie=cc;var hc=st["__core-js_shared__"],oi=hc;var no=function(){var e=/[^.]+$/.exec(oi&&oi.keys&&oi.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function dc(e){return!!no&&no in e}var oo=dc;var fc=Function.prototype,uc=fc.toString;function pc(e){if(e!=null){try{return uc.call(e)}catch{}try{return e+""}catch{}}return""}var ro=pc;var gc=/[\\^$.*+?()[\]{}|]/g,mc=/^\[object .+?Constructor\]$/,bc=Function.prototype,xc=Object.prototype,_c=bc.toString,yc=xc.hasOwnProperty,vc=RegExp("^"+_c.call(yc).replace(gc,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Mc(e){if(!J(e)||oo(e))return!1;var i=ie(e)?vc:mc;return i.test(ro(e))}var ao=Mc;function wc(e,i){return e?.[i]}var lo=wc;function Sc(e,i){var t=lo(e,i);return ao(t)?t:void 0}var se=Sc;var kc=se(st,"Map"),ri=kc;var Cc=se(Object,"create"),xt=Cc;function Pc(){this.__data__=xt?xt(null):{},this.size=0}var co=Pc;function Oc(e){var i=this.has(e)&&delete this.__data__[e];return this.size-=i?1:0,i}var ho=Oc;var Ac="__lodash_hash_undefined__",Dc=Object.prototype,Tc=Dc.hasOwnProperty;function Lc(e){var i=this.__data__;if(xt){var t=i[e];return t===Ac?void 0:t}return Tc.call(i,e)?i[e]:void 0}var fo=Lc;var Ec=Object.prototype,Rc=Ec.hasOwnProperty;function Ic(e){var i=this.__data__;return xt?i[e]!==void 0:Rc.call(i,e)}var uo=Ic;var Fc="__lodash_hash_undefined__";function zc(e,i){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=xt&&i===void 0?Fc:i,this}var po=zc;function ne(e){var i=-1,t=e==null?0:e.length;for(this.clear();++i<t;){var s=e[i];this.set(s[0],s[1])}}ne.prototype.clear=co;ne.prototype.delete=ho;ne.prototype.get=fo;ne.prototype.has=uo;ne.prototype.set=po;var is=ne;function Bc(){this.size=0,this.__data__={hash:new is,map:new(ri||At),string:new is}}var go=Bc;function jc(e){var i=typeof e;return i=="string"||i=="number"||i=="symbol"||i=="boolean"?e!=="__proto__":e===null}var mo=jc;function Nc(e,i){var t=e.__data__;return mo(i)?t[typeof i=="string"?"string":"hash"]:t.map}var Tt=Nc;function Vc(e){var i=Tt(this,e).delete(e);return this.size-=i?1:0,i}var bo=Vc;function Hc(e){return Tt(this,e).get(e)}var xo=Hc;function Wc(e){return Tt(this,e).has(e)}var _o=Wc;function $c(e,i){var t=Tt(this,e),s=t.size;return t.set(e,i),this.size+=t.size==s?0:1,this}var yo=$c;function oe(e){var i=-1,t=e==null?0:e.length;for(this.clear();++i<t;){var s=e[i];this.set(s[0],s[1])}}oe.prototype.clear=go;oe.prototype.delete=bo;oe.prototype.get=xo;oe.prototype.has=_o;oe.prototype.set=yo;var vo=oe;var Yc=200;function Uc(e,i){var t=this.__data__;if(t instanceof At){var s=t.__data__;if(!ri||s.length<Yc-1)return s.push([e,i]),this.size=++t.size,this;t=this.__data__=new vo(s)}return t.set(e,i),this.size=t.size,this}var Mo=Uc;function re(e){var i=this.__data__=new At(e);this.size=i.size}re.prototype.clear=qn;re.prototype.delete=Zn;re.prototype.get=Jn;re.prototype.has=Qn;re.prototype.set=Mo;var wo=re;var Xc=function(){try{var e=se(Object,"defineProperty");return e({},"",{}),e}catch{}}(),ae=Xc;function Gc(e,i,t){i=="__proto__"&&ae?ae(e,i,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[i]=t}var le=Gc;function Kc(e,i,t){(t!==void 0&&!Pt(e[i],t)||t===void 0&&!(i in e))&&le(e,i,t)}var Ae=Kc;function qc(e){return function(i,t,s){for(var n=-1,o=Object(i),r=s(i),a=r.length;a--;){var l=r[e?a:++n];if(t(o[l],l,o)===!1)break}return i}}var So=qc;var Zc=So(),ko=Zc;var Ao=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Co=Ao&&typeof module=="object"&&module&&!module.nodeType&&module,Jc=Co&&Co.exports===Ao,Po=Jc?st.Buffer:void 0,Oo=Po?Po.allocUnsafe:void 0;function Qc(e,i){if(i)return e.slice();var t=e.length,s=Oo?Oo(t):new e.constructor(t);return e.copy(s),s}var Do=Qc;var th=st.Uint8Array,ss=th;function eh(e){var i=new e.constructor(e.byteLength);return new ss(i).set(new ss(e)),i}var To=eh;function ih(e,i){var t=i?To(e.buffer):e.buffer;return new e.constructor(t,e.byteOffset,e.length)}var Lo=ih;function sh(e,i){var t=-1,s=e.length;for(i||(i=Array(s));++t<s;)i[t]=e[t];return i}var Eo=sh;var Ro=Object.create,nh=function(){function e(){}return function(i){if(!J(i))return{};if(Ro)return Ro(i);e.prototype=i;var t=new e;return e.prototype=void 0,t}}(),Io=nh;function oh(e,i){return function(t){return e(i(t))}}var Fo=oh;var rh=Fo(Object.getPrototypeOf,Object),ai=rh;var ah=Object.prototype;function lh(e){var i=e&&e.constructor,t=typeof i=="function"&&i.prototype||ah;return e===t}var li=lh;function ch(e){return typeof e.constructor=="function"&&!li(e)?Io(ai(e)):{}}var zo=ch;function hh(e){return e!=null&&typeof e=="object"}var dt=hh;var dh="[object Arguments]";function fh(e){return dt(e)&&Dt(e)==dh}var ns=fh;var Bo=Object.prototype,uh=Bo.hasOwnProperty,ph=Bo.propertyIsEnumerable,gh=ns(function(){return arguments}())?ns:function(e){return dt(e)&&uh.call(e,"callee")&&!ph.call(e,"callee")},De=gh;var mh=Array.isArray,Te=mh;var bh=9007199254740991;function xh(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=bh}var ci=xh;function _h(e){return e!=null&&ci(e.length)&&!ie(e)}var ce=_h;function yh(e){return dt(e)&&ce(e)}var jo=yh;function vh(){return!1}var No=vh;var Wo=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Vo=Wo&&typeof module=="object"&&module&&!module.nodeType&&module,Mh=Vo&&Vo.exports===Wo,Ho=Mh?st.Buffer:void 0,wh=Ho?Ho.isBuffer:void 0,Sh=wh||No,hi=Sh;var kh="[object Object]",Ch=Function.prototype,Ph=Object.prototype,$o=Ch.toString,Oh=Ph.hasOwnProperty,Ah=$o.call(Object);function Dh(e){if(!dt(e)||Dt(e)!=kh)return!1;var i=ai(e);if(i===null)return!0;var t=Oh.call(i,"constructor")&&i.constructor;return typeof t=="function"&&t instanceof t&&$o.call(t)==Ah}var Yo=Dh;var Th="[object Arguments]",Lh="[object Array]",Eh="[object Boolean]",Rh="[object Date]",Ih="[object Error]",Fh="[object Function]",zh="[object Map]",Bh="[object Number]",jh="[object Object]",Nh="[object RegExp]",Vh="[object Set]",Hh="[object String]",Wh="[object WeakMap]",$h="[object ArrayBuffer]",Yh="[object DataView]",Uh="[object Float32Array]",Xh="[object Float64Array]",Gh="[object Int8Array]",Kh="[object Int16Array]",qh="[object Int32Array]",Zh="[object Uint8Array]",Jh="[object Uint8ClampedArray]",Qh="[object Uint16Array]",td="[object Uint32Array]",B={};B[Uh]=B[Xh]=B[Gh]=B[Kh]=B[qh]=B[Zh]=B[Jh]=B[Qh]=B[td]=!0;B[Th]=B[Lh]=B[$h]=B[Eh]=B[Yh]=B[Rh]=B[Ih]=B[Fh]=B[zh]=B[Bh]=B[jh]=B[Nh]=B[Vh]=B[Hh]=B[Wh]=!1;function ed(e){return dt(e)&&ci(e.length)&&!!B[Dt(e)]}var Uo=ed;function id(e){return function(i){return e(i)}}var Xo=id;var Go=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Le=Go&&typeof module=="object"&&module&&!module.nodeType&&module,sd=Le&&Le.exports===Go,os=sd&&ni.process,nd=function(){try{var e=Le&&Le.require&&Le.require("util").types;return e||os&&os.binding&&os.binding("util")}catch{}}(),rs=nd;var Ko=rs&&rs.isTypedArray,od=Ko?Xo(Ko):Uo,di=od;function rd(e,i){if(!(i==="constructor"&&typeof e[i]=="function")&&i!="__proto__")return e[i]}var Ee=rd;var ad=Object.prototype,ld=ad.hasOwnProperty;function cd(e,i,t){var s=e[i];(!(ld.call(e,i)&&Pt(s,t))||t===void 0&&!(i in e))&&le(e,i,t)}var qo=cd;function hd(e,i,t,s){var n=!t;t||(t={});for(var o=-1,r=i.length;++o<r;){var a=i[o],l=s?s(t[a],e[a],a,t,e):void 0;l===void 0&&(l=e[a]),n?le(t,a,l):qo(t,a,l)}return t}var Zo=hd;function dd(e,i){for(var t=-1,s=Array(e);++t<e;)s[t]=i(t);return s}var Jo=dd;var fd=9007199254740991,ud=/^(?:0|[1-9]\d*)$/;function pd(e,i){var t=typeof e;return i=i??fd,!!i&&(t=="number"||t!="symbol"&&ud.test(e))&&e>-1&&e%1==0&&e<i}var fi=pd;var gd=Object.prototype,md=gd.hasOwnProperty;function bd(e,i){var t=Te(e),s=!t&&De(e),n=!t&&!s&&hi(e),o=!t&&!s&&!n&&di(e),r=t||s||n||o,a=r?Jo(e.length,String):[],l=a.length;for(var c in e)(i||md.call(e,c))&&!(r&&(c=="length"||n&&(c=="offset"||c=="parent")||o&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||fi(c,l)))&&a.push(c);return a}var Qo=bd;function xd(e){var i=[];if(e!=null)for(var t in Object(e))i.push(t);return i}var tr=xd;var _d=Object.prototype,yd=_d.hasOwnProperty;function vd(e){if(!J(e))return tr(e);var i=li(e),t=[];for(var s in e)s=="constructor"&&(i||!yd.call(e,s))||t.push(s);return t}var er=vd;function Md(e){return ce(e)?Qo(e,!0):er(e)}var ui=Md;function wd(e){return Zo(e,ui(e))}var ir=wd;function Sd(e,i,t,s,n,o,r){var a=Ee(e,t),l=Ee(i,t),c=r.get(l);if(c){Ae(e,t,c);return}var h=o?o(a,l,t+"",e,i,r):void 0,d=h===void 0;if(d){var f=Te(l),u=!f&&hi(l),p=!f&&!u&&di(l);h=l,f||u||p?Te(a)?h=a:jo(a)?h=Eo(a):u?(d=!1,h=Do(l,!0)):p?(d=!1,h=Lo(l,!0)):h=[]:Yo(l)||De(l)?(h=a,De(a)?h=ir(a):(!J(a)||ie(a))&&(h=zo(l))):d=!1}d&&(r.set(l,h),n(h,l,s,o,r),r.delete(l)),Ae(e,t,h)}var sr=Sd;function nr(e,i,t,s,n){e!==i&&ko(i,function(o,r){if(n||(n=new wo),J(o))sr(e,i,r,t,nr,s,n);else{var a=s?s(Ee(e,r),o,r+"",e,i,n):void 0;a===void 0&&(a=o),Ae(e,r,a)}},ui)}var or=nr;function kd(e){return e}var pi=kd;function Cd(e,i,t){switch(t.length){case 0:return e.call(i);case 1:return e.call(i,t[0]);case 2:return e.call(i,t[0],t[1]);case 3:return e.call(i,t[0],t[1],t[2])}return e.apply(i,t)}var rr=Cd;var ar=Math.max;function Pd(e,i,t){return i=ar(i===void 0?e.length-1:i,0),function(){for(var s=arguments,n=-1,o=ar(s.length-i,0),r=Array(o);++n<o;)r[n]=s[i+n];n=-1;for(var a=Array(i+1);++n<i;)a[n]=s[n];return a[i]=t(r),rr(e,this,a)}}var lr=Pd;function Od(e){return function(){return e}}var cr=Od;var Ad=ae?function(e,i){return ae(e,"toString",{configurable:!0,enumerable:!1,value:cr(i),writable:!0})}:pi,hr=Ad;var Dd=800,Td=16,Ld=Date.now;function Ed(e){var i=0,t=0;return function(){var s=Ld(),n=Td-(s-t);if(t=s,n>0){if(++i>=Dd)return arguments[0]}else i=0;return e.apply(void 0,arguments)}}var dr=Ed;var Rd=dr(hr),fr=Rd;function Id(e,i){return fr(lr(e,i,pi),e+"")}var ur=Id;function Fd(e,i,t){if(!J(t))return!1;var s=typeof i;return(s=="number"?ce(t)&&fi(i,t.length):s=="string"&&i in t)?Pt(t[i],e):!1}var pr=Fd;function zd(e){return ur(function(i,t){var s=-1,n=t.length,o=n>1?t[n-1]:void 0,r=n>2?t[2]:void 0;for(o=e.length>3&&typeof o=="function"?(n--,o):void 0,r&&pr(t[0],t[1],r)&&(o=n<3?void 0:o,n=1),i=Object(i);++s<n;){var a=t[s];a&&e(i,a,s,o)}return i})}var gr=zd;var Bd=gr(function(e,i,t){or(e,i,t)}),mr=Bd;function Fe(e){return e+.5|0}var Lt=(e,i,t)=>Math.max(Math.min(e,t),i);function Re(e){return Lt(Fe(e*2.55),0,255)}function Et(e){return Lt(Fe(e*255),0,255)}function _t(e){return Lt(Fe(e/2.55)/100,0,1)}function br(e){return Lt(Fe(e*100),0,100)}var nt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ls=[..."0123456789ABCDEF"],jd=e=>ls[e&15],Nd=e=>ls[(e&240)>>4]+ls[e&15],gi=e=>(e&240)>>4===(e&15),Vd=e=>gi(e.r)&&gi(e.g)&&gi(e.b)&&gi(e.a);function Hd(e){var i=e.length,t;return e[0]==="#"&&(i===4||i===5?t={r:255&nt[e[1]]*17,g:255&nt[e[2]]*17,b:255&nt[e[3]]*17,a:i===5?nt[e[4]]*17:255}:(i===7||i===9)&&(t={r:nt[e[1]]<<4|nt[e[2]],g:nt[e[3]]<<4|nt[e[4]],b:nt[e[5]]<<4|nt[e[6]],a:i===9?nt[e[7]]<<4|nt[e[8]]:255})),t}var Wd=(e,i)=>e<255?i(e):"";function $d(e){var i=Vd(e)?jd:Nd;return e?"#"+i(e.r)+i(e.g)+i(e.b)+Wd(e.a,i):void 0}var Yd=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function vr(e,i,t){let s=i*Math.min(t,1-t),n=(o,r=(o+e/30)%12)=>t-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function Ud(e,i,t){let s=(n,o=(n+e/60)%6)=>t-t*i*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Xd(e,i,t){let s=vr(e,1,.5),n;for(i+t>1&&(n=1/(i+t),i*=n,t*=n),n=0;n<3;n++)s[n]*=1-i-t,s[n]+=i;return s}function Gd(e,i,t,s,n){return e===n?(i-t)/s+(i<t?6:0):i===n?(t-e)/s+2:(e-i)/s+4}function cs(e){let t=e.r/255,s=e.g/255,n=e.b/255,o=Math.max(t,s,n),r=Math.min(t,s,n),a=(o+r)/2,l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Gd(t,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function hs(e,i,t,s){return(Array.isArray(i)?e(i[0],i[1],i[2]):e(i,t,s)).map(Et)}function ds(e,i,t){return hs(vr,e,i,t)}function Kd(e,i,t){return hs(Xd,e,i,t)}function qd(e,i,t){return hs(Ud,e,i,t)}function Mr(e){return(e%360+360)%360}function Zd(e){let i=Yd.exec(e),t=255,s;if(!i)return;i[5]!==s&&(t=i[6]?Re(+i[5]):Et(+i[5]));let n=Mr(+i[2]),o=+i[3]/100,r=+i[4]/100;return i[1]==="hwb"?s=Kd(n,o,r):i[1]==="hsv"?s=qd(n,o,r):s=ds(n,o,r),{r:s[0],g:s[1],b:s[2],a:t}}function Jd(e,i){var t=cs(e);t[0]=Mr(t[0]+i),t=ds(t),e.r=t[0],e.g=t[1],e.b=t[2]}function Qd(e){if(!e)return;let i=cs(e),t=i[0],s=br(i[1]),n=br(i[2]);return e.a<255?`hsla(${t}, ${s}%, ${n}%, ${_t(e.a)})`:`hsl(${t}, ${s}%, ${n}%)`}var xr={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},_r={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function tf(){let e={},i=Object.keys(_r),t=Object.keys(xr),s,n,o,r,a;for(s=0;s<i.length;s++){for(r=a=i[s],n=0;n<t.length;n++)o=t[n],a=a.replace(o,xr[o]);o=parseInt(_r[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}var mi;function ef(e){mi||(mi=tf(),mi.transparent=[0,0,0,0]);let i=mi[e.toLowerCase()];return i&&{r:i[0],g:i[1],b:i[2],a:i.length===4?i[3]:255}}var sf=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function nf(e){let i=sf.exec(e),t=255,s,n,o;if(i){if(i[7]!==s){let r=+i[7];t=i[8]?Re(r):Lt(r*255,0,255)}return s=+i[1],n=+i[3],o=+i[5],s=255&(i[2]?Re(s):Lt(s,0,255)),n=255&(i[4]?Re(n):Lt(n,0,255)),o=255&(i[6]?Re(o):Lt(o,0,255)),{r:s,g:n,b:o,a:t}}}function of(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${_t(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}var as=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,he=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function rf(e,i,t){let s=he(_t(e.r)),n=he(_t(e.g)),o=he(_t(e.b));return{r:Et(as(s+t*(he(_t(i.r))-s))),g:Et(as(n+t*(he(_t(i.g))-n))),b:Et(as(o+t*(he(_t(i.b))-o))),a:e.a+t*(i.a-e.a)}}function bi(e,i,t){if(e){let s=cs(e);s[i]=Math.max(0,Math.min(s[i]+s[i]*t,i===0?360:1)),s=ds(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function wr(e,i){return e&&Object.assign(i||{},e)}function yr(e){var i={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(i={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(i.a=Et(e[3]))):(i=wr(e,{r:0,g:0,b:0,a:1}),i.a=Et(i.a)),i}function af(e){return e.charAt(0)==="r"?nf(e):Zd(e)}var Ie=class e{constructor(i){if(i instanceof e)return i;let t=typeof i,s;t==="object"?s=yr(i):t==="string"&&(s=Hd(i)||ef(i)||af(i)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var i=wr(this._rgb);return i&&(i.a=_t(i.a)),i}set rgb(i){this._rgb=yr(i)}rgbString(){return this._valid?of(this._rgb):void 0}hexString(){return this._valid?$d(this._rgb):void 0}hslString(){return this._valid?Qd(this._rgb):void 0}mix(i,t){if(i){let s=this.rgb,n=i.rgb,o,r=t===o?.5:t,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(i,t){return i&&(this._rgb=rf(this._rgb,i._rgb,t)),this}clone(){return new e(this.rgb)}alpha(i){return this._rgb.a=Et(i),this}clearer(i){let t=this._rgb;return t.a*=1-i,this}greyscale(){let i=this._rgb,t=Fe(i.r*.3+i.g*.59+i.b*.11);return i.r=i.g=i.b=t,this}opaquer(i){let t=this._rgb;return t.a*=1+i,this}negate(){let i=this._rgb;return i.r=255-i.r,i.g=255-i.g,i.b=255-i.b,this}lighten(i){return bi(this._rgb,2,i),this}darken(i){return bi(this._rgb,2,-i),this}saturate(i){return bi(this._rgb,1,i),this}desaturate(i){return bi(this._rgb,1,-i),this}rotate(i){return Jd(this._rgb,i),this}};function pt(){}var Rr=(()=>{let e=0;return()=>e++})();function A(e){return e==null}function F(e){if(Array.isArray&&Array.isArray(e))return!0;let i=Object.prototype.toString.call(e);return i.slice(0,7)==="[object"&&i.slice(-6)==="Array]"}function D(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function V(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function Q(e,i){return V(e)?e:i}function C(e,i){return typeof e>"u"?i:e}var Ir=(e,i)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/i,gs=(e,i)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*i:+e;function I(e,i,t){if(e&&typeof e.call=="function")return e.apply(t,i)}function E(e,i,t,s){let n,o,r;if(F(e))if(o=e.length,s)for(n=o-1;n>=0;n--)i.call(t,e[n],n);else for(n=0;n<o;n++)i.call(t,e[n],n);else if(D(e))for(r=Object.keys(e),o=r.length,n=0;n<o;n++)i.call(t,e[r[n]],r[n])}function je(e,i){let t,s,n,o;if(!e||!i||e.length!==i.length)return!1;for(t=0,s=e.length;t<s;++t)if(n=e[t],o=i[t],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Mi(e){if(F(e))return e.map(Mi);if(D(e)){let i=Object.create(null),t=Object.keys(e),s=t.length,n=0;for(;n<s;++n)i[t[n]]=Mi(e[t[n]]);return i}return e}function Fr(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function lf(e,i,t,s){if(!Fr(e))return;let n=i[e],o=t[e];D(n)&&D(o)?fe(n,o,s):i[e]=Mi(o)}function fe(e,i,t){let s=F(i)?i:[i],n=s.length;if(!D(e))return e;t=t||{};let o=t.merger||lf,r;for(let a=0;a<n;++a){if(r=s[a],!D(r))continue;let l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],e,r,t)}return e}function pe(e,i){return fe(e,i,{merger:cf})}function cf(e,i,t){if(!Fr(e))return;let s=i[e],n=t[e];D(s)&&D(n)?pe(s,n):Object.prototype.hasOwnProperty.call(i,e)||(i[e]=Mi(n))}var Sr={"":e=>e,x:e=>e.x,y:e=>e.y};function hf(e){let i=e.split("."),t=[],s="";for(let n of i)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(t.push(s),s="");return t}function df(e){let i=hf(e);return t=>{for(let s of i){if(s==="")break;t=t&&t[s]}return t}}function Mt(e,i){return(Sr[i]||(Sr[i]=df(i)))(e)}function Ci(e){return e.charAt(0).toUpperCase()+e.slice(1)}var ge=e=>typeof e<"u",yt=e=>typeof e=="function",ms=(e,i)=>{if(e.size!==i.size)return!1;for(let t of e)if(!i.has(t))return!1;return!0};function zr(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}var L=Math.PI,z=2*L,ff=z+L,wi=Number.POSITIVE_INFINITY,uf=L/180,H=L/2,$t=L/4,kr=L*2/3,vt=Math.log10,lt=Math.sign;function me(e,i,t){return Math.abs(e-i)<t}function bs(e){let i=Math.round(e);e=me(e,i,e/1e3)?i:e;let t=Math.pow(10,Math.floor(vt(e))),s=e/t;return(s<=1?1:s<=2?2:s<=5?5:10)*t}function Br(e){let i=[],t=Math.sqrt(e),s;for(s=1;s<t;s++)e%s===0&&(i.push(s),i.push(e/s));return t===(t|0)&&i.push(t),i.sort((n,o)=>n-o).pop(),i}function pf(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function Xt(e){return!pf(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function jr(e,i){let t=Math.round(e);return t-i<=e&&t+i>=e}function xs(e,i,t){let s,n,o;for(s=0,n=e.length;s<n;s++)o=e[s][t],isNaN(o)||(i.min=Math.min(i.min,o),i.max=Math.max(i.max,o))}function ot(e){return e*(L/180)}function Pi(e){return e*(180/L)}function _s(e){if(!V(e))return;let i=1,t=0;for(;Math.round(e*i)/i!==e;)i*=10,t++;return t}function ys(e,i){let t=i.x-e.x,s=i.y-e.y,n=Math.sqrt(t*t+s*s),o=Math.atan2(s,t);return o<-.5*L&&(o+=z),{angle:o,distance:n}}function Si(e,i){return Math.sqrt(Math.pow(i.x-e.x,2)+Math.pow(i.y-e.y,2))}function gf(e,i){return(e-i+ff)%z-L}function X(e){return(e%z+z)%z}function be(e,i,t,s){let n=X(e),o=X(i),r=X(t),a=X(o-n),l=X(r-n),c=X(n-o),h=X(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function Y(e,i,t){return Math.max(i,Math.min(t,e))}function Nr(e){return Y(e,-32768,32767)}function gt(e,i,t,s=1e-6){return e>=Math.min(i,t)-s&&e<=Math.max(i,t)+s}function Oi(e,i,t){t=t||(r=>e[r]<i);let s=e.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,t(o)?n=o:s=o;return{lo:n,hi:s}}var ft=(e,i,t,s)=>Oi(e,t,s?n=>{let o=e[n][i];return o<t||o===t&&e[n+1][i]===t}:n=>e[n][i]<t),Vr=(e,i,t)=>Oi(e,t,s=>e[s][i]>=t);function Hr(e,i,t){let s=0,n=e.length;for(;s<n&&e[s]<i;)s++;for(;n>s&&e[n-1]>t;)n--;return s>0||n<e.length?e.slice(s,n):e}var Wr=["push","pop","shift","splice","unshift"];function $r(e,i){if(e._chartjs){e._chartjs.listeners.push(i);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[i]}}),Wr.forEach(t=>{let s="_onData"+Ci(t),n=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value(...o){let r=n.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function vs(e,i){let t=e._chartjs;if(!t)return;let s=t.listeners,n=s.indexOf(i);n!==-1&&s.splice(n,1),!(s.length>0)&&(Wr.forEach(o=>{delete e[o]}),delete e._chartjs)}function Ms(e){let i=new Set(e);return i.size===e.length?e:Array.from(i)}var ws=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function Ss(e,i){let t=[],s=!1;return function(...n){t=n,s||(s=!0,ws.call(window,()=>{s=!1,e.apply(i,t)}))}}function Yr(e,i){let t;return function(...s){return i?(clearTimeout(t),t=setTimeout(e,i,s)):e.apply(this,s),i}}var Ai=e=>e==="start"?"left":e==="end"?"right":"center",G=(e,i,t)=>e==="start"?i:e==="end"?t:(i+t)/2,Ur=(e,i,t,s)=>e===(s?"left":"right")?t:e==="center"?(i+t)/2:i;function ks(e,i,t){let s=i.length,n=0,o=s;if(e._sorted){let{iScale:r,vScale:a,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,h=r.axis,{min:d,max:f,minDefined:u,maxDefined:p}=r.getUserBounds();if(u){if(n=Math.min(ft(l,h,d).lo,t?s:ft(i,h,r.getPixelForValue(d)).lo),c){let g=l.slice(0,n+1).reverse().findIndex(m=>!A(m[a.axis]));n-=Math.max(0,g)}n=Y(n,0,s-1)}if(p){let g=Math.max(ft(l,r.axis,f,!0).hi+1,t?0:ft(i,h,r.getPixelForValue(f),!0).hi+1);if(c){let m=l.slice(g-1).findIndex(b=>!A(b[a.axis]));g+=Math.max(0,m)}o=Y(g,n,s)-n}else o=s-n}return{start:n,count:o}}function Cs(e){let{xScale:i,yScale:t,_scaleRanges:s}=e,n={xmin:i.min,xmax:i.max,ymin:t.min,ymax:t.max};if(!s)return e._scaleRanges=n,!0;let o=s.xmin!==i.min||s.xmax!==i.max||s.ymin!==t.min||s.ymax!==t.max;return Object.assign(s,n),o}var xi=e=>e===0||e===1,Cr=(e,i,t)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-i)*z/t)),Pr=(e,i,t)=>Math.pow(2,-10*e)*Math.sin((e-i)*z/t)+1,de={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*H)+1,easeOutSine:e=>Math.sin(e*H),easeInOutSine:e=>-.5*(Math.cos(L*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>xi(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>xi(e)?e:Cr(e,.075,.3),easeOutElastic:e=>xi(e)?e:Pr(e,.075,.3),easeInOutElastic(e){return xi(e)?e:e<.5?.5*Cr(e*2,.1125,.45):.5+.5*Pr(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let i=1.70158;return(e/=.5)<1?.5*(e*e*(((i*=1.525)+1)*e-i)):.5*((e-=2)*e*(((i*=1.525)+1)*e+i)+2)},easeInBounce:e=>1-de.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?de.easeInBounce(e*2)*.5:de.easeOutBounce(e*2-1)*.5+.5};function Ps(e){if(e&&typeof e=="object"){let i=e.toString();return i==="[object CanvasPattern]"||i==="[object CanvasGradient]"}return!1}function Os(e){return Ps(e)?e:new Ie(e)}function fs(e){return Ps(e)?e:new Ie(e).saturate(.5).darken(.1).hexString()}var mf=["x","y","borderWidth","radius","tension"],bf=["color","borderColor","backgroundColor"];function xf(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:i=>i!=="onProgress"&&i!=="onComplete"&&i!=="fn"}),e.set("animations",{colors:{type:"color",properties:bf},numbers:{type:"number",properties:mf}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:i=>i|0}}}})}function _f(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}var Or=new Map;function yf(e,i){i=i||{};let t=e+JSON.stringify(i),s=Or.get(t);return s||(s=new Intl.NumberFormat(e,i),Or.set(t,s)),s}function xe(e,i,t){return yf(i,t).format(e)}var Xr={values(e){return F(e)?e:""+e},numeric(e,i,t){if(e===0)return"0";let s=this.chart.options.locale,n,o=e;if(t.length>1){let c=Math.max(Math.abs(t[0].value),Math.abs(t[t.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=vf(e,t)}let r=vt(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),xe(e,s,l)},logarithmic(e,i,t){if(e===0)return"0";let s=t[i].significand||e/Math.pow(10,Math.floor(vt(e)));return[1,2,3,5,10,15].includes(s)||i>.8*t.length?Xr.numeric.call(this,e,i,t):""}};function vf(e,i){let t=i.length>3?i[2].value-i[1].value:i[1].value-i[0].value;return Math.abs(t)>=1&&e!==Math.floor(e)&&(t=e-Math.floor(e)),t}var Ne={formatters:Xr};function Mf(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(i,t)=>t.lineWidth,tickColor:(i,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ne.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:i=>!i.startsWith("before")&&!i.startsWith("after")&&i!=="callback"&&i!=="parser",_indexable:i=>i!=="borderDash"&&i!=="tickBorderDash"&&i!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:i=>i!=="backdropPadding"&&i!=="callback",_indexable:i=>i!=="backdropPadding"})}var It=Object.create(null),Di=Object.create(null);function ze(e,i){if(!i)return e;let t=i.split(".");for(let s=0,n=t.length;s<n;++s){let o=t[s];e=e[o]||(e[o]=Object.create(null))}return e}function us(e,i,t){return typeof i=="string"?fe(ze(e,i),t):fe(ze(e,""),i)}var ps=class{constructor(i,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>fs(n.backgroundColor),this.hoverBorderColor=(s,n)=>fs(n.borderColor),this.hoverColor=(s,n)=>fs(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(i),this.apply(t)}set(i,t){return us(this,i,t)}get(i){return ze(this,i)}describe(i,t){return us(Di,i,t)}override(i,t){return us(It,i,t)}route(i,t,s,n){let o=ze(this,i),r=ze(this,s),a="_"+t;Object.defineProperties(o,{[a]:{value:o[t],writable:!0},[t]:{enumerable:!0,get(){let l=this[a],c=r[n];return D(l)?Object.assign({},c,l):C(l,c)},set(l){this[a]=l}}})}apply(i){i.forEach(t=>t(this))}},j=new ps({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[xf,_f,Mf]);function wf(e){return!e||A(e.size)||A(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function Be(e,i,t,s,n){let o=i[n];return o||(o=i[n]=e.measureText(n).width,t.push(n)),o>s&&(s=o),s}function Gr(e,i,t,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==i&&(n=s.data={},o=s.garbageCollect=[],s.font=i),e.save(),e.font=i;let r=0,a=t.length,l,c,h,d,f;for(l=0;l<a;l++)if(d=t[l],d!=null&&!F(d))r=Be(e,n,o,r,d);else if(F(d))for(c=0,h=d.length;c<h;c++)f=d[c],f!=null&&!F(f)&&(r=Be(e,n,o,r,f));e.restore();let u=o.length/2;if(u>t.length){for(l=0;l<u;l++)delete n[o[l]];o.splice(0,u)}return r}function Ft(e,i,t){let s=e.currentDevicePixelRatio,n=t!==0?Math.max(t/2,.5):0;return Math.round((i-n)*s)/s+n}function As(e,i){!i&&!e||(i=i||e.getContext("2d"),i.save(),i.resetTransform(),i.clearRect(0,0,e.width,e.height),i.restore())}function Ti(e,i,t,s){Ds(e,i,t,s,null)}function Ds(e,i,t,s,n){let o,r,a,l,c,h,d,f,u=i.pointStyle,p=i.rotation,g=i.radius,m=(p||0)*uf;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(t,s),e.rotate(m),e.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),e.restore();return}if(!(isNaN(g)||g<=0)){switch(e.beginPath(),u){default:n?e.ellipse(t,s,n/2,g,0,0,z):e.arc(t,s,g,0,z),e.closePath();break;case"triangle":h=n?n/2:g,e.moveTo(t+Math.sin(m)*h,s-Math.cos(m)*g),m+=kr,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*g),m+=kr,e.lineTo(t+Math.sin(m)*h,s-Math.cos(m)*g),e.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(m+$t)*l,d=Math.cos(m+$t)*(n?n/2-c:l),a=Math.sin(m+$t)*l,f=Math.sin(m+$t)*(n?n/2-c:l),e.arc(t-d,s-a,c,m-L,m-H),e.arc(t+f,s-r,c,m-H,m),e.arc(t+d,s+a,c,m,m+H),e.arc(t-f,s+r,c,m+H,m+L),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,h=n?n/2:l,e.rect(t-h,s-l,2*h,2*l);break}m+=$t;case"rectRot":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,f=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-a),e.lineTo(t+f,s-r),e.lineTo(t+d,s+a),e.lineTo(t-f,s+r),e.closePath();break;case"crossRot":m+=$t;case"cross":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,f=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-a),e.lineTo(t+d,s+a),e.moveTo(t+f,s-r),e.lineTo(t-f,s+r);break;case"star":d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,f=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-a),e.lineTo(t+d,s+a),e.moveTo(t+f,s-r),e.lineTo(t-f,s+r),m+=$t,d=Math.cos(m)*(n?n/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,f=Math.sin(m)*(n?n/2:g),e.moveTo(t-d,s-a),e.lineTo(t+d,s+a),e.moveTo(t+f,s-r),e.lineTo(t-f,s+r);break;case"line":r=n?n/2:Math.cos(m)*g,a=Math.sin(m)*g,e.moveTo(t-r,s-a),e.lineTo(t+r,s+a);break;case"dash":e.moveTo(t,s),e.lineTo(t+Math.cos(m)*(n?n/2:g),s+Math.sin(m)*g);break;case!1:e.closePath();break}e.fill(),i.borderWidth>0&&e.stroke()}}function ut(e,i,t){return t=t||.5,!i||e&&e.x>i.left-t&&e.x<i.right+t&&e.y>i.top-t&&e.y<i.bottom+t}function Ve(e,i){e.save(),e.beginPath(),e.rect(i.left,i.top,i.right-i.left,i.bottom-i.top),e.clip()}function He(e){e.restore()}function Kr(e,i,t,s,n){if(!i)return e.lineTo(t.x,t.y);if(n==="middle"){let o=(i.x+t.x)/2;e.lineTo(o,i.y),e.lineTo(o,t.y)}else n==="after"!=!!s?e.lineTo(i.x,t.y):e.lineTo(t.x,i.y);e.lineTo(t.x,t.y)}function qr(e,i,t,s){if(!i)return e.lineTo(t.x,t.y);e.bezierCurveTo(s?i.cp1x:i.cp2x,s?i.cp1y:i.cp2y,s?t.cp2x:t.cp1x,s?t.cp2y:t.cp1y,t.x,t.y)}function Sf(e,i){i.translation&&e.translate(i.translation[0],i.translation[1]),A(i.rotation)||e.rotate(i.rotation),i.color&&(e.fillStyle=i.color),i.textAlign&&(e.textAlign=i.textAlign),i.textBaseline&&(e.textBaseline=i.textBaseline)}function kf(e,i,t,s,n){if(n.strikethrough||n.underline){let o=e.measureText(s),r=i-o.actualBoundingBoxLeft,a=i+o.actualBoundingBoxRight,l=t-o.actualBoundingBoxAscent,c=t+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=n.decorationWidth||2,e.moveTo(r,h),e.lineTo(a,h),e.stroke()}}function Cf(e,i){let t=e.fillStyle;e.fillStyle=i.color,e.fillRect(i.left,i.top,i.width,i.height),e.fillStyle=t}function zt(e,i,t,s,n,o={}){let r=F(i)?i:[i],a=o.strokeWidth>0&&o.strokeColor!=="",l,c;for(e.save(),e.font=n.string,Sf(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Cf(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),A(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,t,s,o.maxWidth)),e.fillText(c,t,s,o.maxWidth),kf(e,t,s,c,o),s+=Number(n.lineHeight);e.restore()}function _e(e,i){let{x:t,y:s,w:n,h:o,radius:r}=i;e.arc(t+r.topLeft,s+r.topLeft,r.topLeft,1.5*L,L,!0),e.lineTo(t,s+o-r.bottomLeft),e.arc(t+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,L,H,!0),e.lineTo(t+n-r.bottomRight,s+o),e.arc(t+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,H,0,!0),e.lineTo(t+n,s+r.topRight),e.arc(t+n-r.topRight,s+r.topRight,r.topRight,0,-H,!0),e.lineTo(t+r.topLeft,s)}var Pf=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Of=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Af(e,i){let t=(""+e).match(Pf);if(!t||t[1]==="normal")return i*1.2;switch(e=+t[2],t[3]){case"px":return e;case"%":e/=100;break}return i*e}var Df=e=>+e||0;function Li(e,i){let t={},s=D(i),n=s?Object.keys(i):i,o=D(e)?s?r=>C(e[r],e[i[r]]):r=>e[r]:()=>e;for(let r of n)t[r]=Df(o(r));return t}function Ts(e){return Li(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Bt(e){return Li(e,["topLeft","topRight","bottomLeft","bottomRight"])}function K(e){let i=Ts(e);return i.width=i.left+i.right,i.height=i.top+i.bottom,i}function $(e,i){e=e||{},i=i||j.font;let t=C(e.size,i.size);typeof t=="string"&&(t=parseInt(t,10));let s=C(e.style,i.style);s&&!(""+s).match(Of)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);let n={family:C(e.family,i.family),lineHeight:Af(C(e.lineHeight,i.lineHeight),t),size:t,style:s,weight:C(e.weight,i.weight),string:""};return n.string=wf(n),n}function ye(e,i,t,s){let n=!0,o,r,a;for(o=0,r=e.length;o<r;++o)if(a=e[o],a!==void 0&&(i!==void 0&&typeof a=="function"&&(a=a(i),n=!1),t!==void 0&&F(a)&&(a=a[t%a.length],n=!1),a!==void 0))return s&&!n&&(s.cacheable=!1),a}function Zr(e,i,t){let{min:s,max:n}=e,o=gs(i,(n-s)/2),r=(a,l)=>t&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function wt(e,i){return Object.assign(Object.create(e),i)}function Ei(e,i=[""],t,s,n=()=>e[0]){let o=t||e;typeof s>"u"&&(s=ta("_fallback",e));let r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>Ei([a,...e],i,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return Jr(a,l,()=>Bf(l,i,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return Dr(a).includes(l)},ownKeys(a){return Dr(a)},set(a,l,c){let h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Ut(e,i,t,s){let n={_cacheable:!1,_proxy:e,_context:i,_subProxy:t,_stack:new Set,_descriptors:Ls(e,s),setContext:o=>Ut(e,o,t,s),override:o=>Ut(e.override(o),i,t,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return Jr(o,r,()=>Lf(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function Ls(e,i={scriptable:!0,indexable:!0}){let{_scriptable:t=i.scriptable,_indexable:s=i.indexable,_allKeys:n=i.allKeys}=e;return{allKeys:n,scriptable:t,indexable:s,isScriptable:yt(t)?t:()=>t,isIndexable:yt(s)?s:()=>s}}var Tf=(e,i)=>e?e+Ci(i):i,Es=(e,i)=>D(i)&&e!=="adapters"&&(Object.getPrototypeOf(i)===null||i.constructor===Object);function Jr(e,i,t){if(Object.prototype.hasOwnProperty.call(e,i)||i==="constructor")return e[i];let s=t();return e[i]=s,s}function Lf(e,i,t){let{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=e,a=s[i];return yt(a)&&r.isScriptable(i)&&(a=Ef(i,a,e,t)),F(a)&&a.length&&(a=Rf(i,a,e,r.isIndexable)),Es(i,a)&&(a=Ut(a,n,o&&o[i],r)),a}function Ef(e,i,t,s){let{_proxy:n,_context:o,_subProxy:r,_stack:a}=t;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=i(o,r||s);return a.delete(e),Es(e,l)&&(l=Rs(n._scopes,n,e,l)),l}function Rf(e,i,t,s){let{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=t;if(typeof o.index<"u"&&s(e))return i[o.index%i.length];if(D(i[0])){let l=i,c=n._scopes.filter(h=>h!==l);i=[];for(let h of l){let d=Rs(c,n,e,h);i.push(Ut(d,o,r&&r[e],a))}}return i}function Qr(e,i,t){return yt(e)?e(i,t):e}var If=(e,i)=>e===!0?i:typeof e=="string"?Mt(i,e):void 0;function Ff(e,i,t,s,n){for(let o of i){let r=If(t,o);if(r){e.add(r);let a=Qr(r._fallback,t,n);if(typeof a<"u"&&a!==t&&a!==s)return a}else if(r===!1&&typeof s<"u"&&t!==s)return null}return!1}function Rs(e,i,t,s){let n=i._rootScopes,o=Qr(i._fallback,t,s),r=[...e,...n],a=new Set;a.add(s);let l=Ar(a,r,t,o||t,s);return l===null||typeof o<"u"&&o!==t&&(l=Ar(a,r,o,l,s),l===null)?!1:Ei(Array.from(a),[""],n,o,()=>zf(i,t,s))}function Ar(e,i,t,s,n){for(;t;)t=Ff(e,i,t,s,n);return t}function zf(e,i,t){let s=e._getTarget();i in s||(s[i]={});let n=s[i];return F(n)&&D(t)?t:n||{}}function Bf(e,i,t,s){let n;for(let o of i)if(n=ta(Tf(o,e),t),typeof n<"u")return Es(e,n)?Rs(t,s,e,n):n}function ta(e,i){for(let t of i){if(!t)continue;let s=t[e];if(typeof s<"u")return s}}function Dr(e){let i=e._keys;return i||(i=e._keys=jf(e._scopes)),i}function jf(e){let i=new Set;for(let t of e)for(let s of Object.keys(t).filter(n=>!n.startsWith("_")))i.add(s);return Array.from(i)}function Is(e,i,t,s){let{iScale:n}=e,{key:o="r"}=this._parsing,r=new Array(s),a,l,c,h;for(a=0,l=s;a<l;++a)c=a+t,h=i[c],r[a]={r:n.parse(Mt(h,o),c)};return r}var Nf=Number.EPSILON||1e-14,ue=(e,i)=>i<e.length&&!e[i].skip&&e[i],ea=e=>e==="x"?"y":"x";function Vf(e,i,t,s){let n=e.skip?i:e,o=i,r=t.skip?i:t,a=Si(o,n),l=Si(r,o),c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;let d=s*c,f=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+f*(r.x-n.x),y:o.y+f*(r.y-n.y)}}}function Hf(e,i,t){let s=e.length,n,o,r,a,l,c=ue(e,0);for(let h=0;h<s-1;++h)if(l=c,c=ue(e,h+1),!(!l||!c)){if(me(i[h],0,Nf)){t[h]=t[h+1]=0;continue}n=t[h]/i[h],o=t[h+1]/i[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),t[h]=n*r*i[h],t[h+1]=o*r*i[h])}}function Wf(e,i,t="x"){let s=ea(t),n=e.length,o,r,a,l=ue(e,0);for(let c=0;c<n;++c){if(r=a,a=l,l=ue(e,c+1),!a)continue;let h=a[t],d=a[s];r&&(o=(h-r[t])/3,a[`cp1${t}`]=h-o,a[`cp1${s}`]=d-o*i[c]),l&&(o=(l[t]-h)/3,a[`cp2${t}`]=h+o,a[`cp2${s}`]=d+o*i[c])}}function $f(e,i="x"){let t=ea(i),s=e.length,n=Array(s).fill(0),o=Array(s),r,a,l,c=ue(e,0);for(r=0;r<s;++r)if(a=l,l=c,c=ue(e,r+1),!!l){if(c){let h=c[i]-l[i];n[r]=h!==0?(c[t]-l[t])/h:0}o[r]=a?c?lt(n[r-1])!==lt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}Hf(e,n,o),Wf(e,o,i)}function _i(e,i,t){return Math.max(Math.min(e,t),i)}function Yf(e,i){let t,s,n,o,r,a=ut(e[0],i);for(t=0,s=e.length;t<s;++t)r=o,o=a,a=t<s-1&&ut(e[t+1],i),o&&(n=e[t],r&&(n.cp1x=_i(n.cp1x,i.left,i.right),n.cp1y=_i(n.cp1y,i.top,i.bottom)),a&&(n.cp2x=_i(n.cp2x,i.left,i.right),n.cp2y=_i(n.cp2y,i.top,i.bottom)))}function ia(e,i,t,s,n){let o,r,a,l;if(i.spanGaps&&(e=e.filter(c=>!c.skip)),i.cubicInterpolationMode==="monotone")$f(e,n);else{let c=s?e[e.length-1]:e[0];for(o=0,r=e.length;o<r;++o)a=e[o],l=Vf(c,a,e[Math.min(o+1,r-(s?0:1))%r],i.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}i.capBezierPoints&&Yf(e,t)}function Ri(){return typeof window<"u"&&typeof document<"u"}function Ii(e){let i=e.parentNode;return i&&i.toString()==="[object ShadowRoot]"&&(i=i.host),i}function ki(e,i,t){let s;return typeof e=="string"?(s=parseInt(e,10),e.indexOf("%")!==-1&&(s=s/100*i.parentNode[t])):s=e,s}var Fi=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function Uf(e,i){return Fi(e).getPropertyValue(i)}var Xf=["top","right","bottom","left"];function Yt(e,i,t){let s={};t=t?"-"+t:"";for(let n=0;n<4;n++){let o=Xf[n];s[o]=parseFloat(e[i+"-"+o+t])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}var Gf=(e,i,t)=>(e>0||i>0)&&(!t||!t.shadowRoot);function Kf(e,i){let t=e.touches,s=t&&t.length?t[0]:e,{offsetX:n,offsetY:o}=s,r=!1,a,l;if(Gf(n,o,e.target))a=n,l=o;else{let c=i.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function jt(e,i){if("native"in e)return e;let{canvas:t,currentDevicePixelRatio:s}=i,n=Fi(t),o=n.boxSizing==="border-box",r=Yt(n,"padding"),a=Yt(n,"border","width"),{x:l,y:c,box:h}=Kf(e,t),d=r.left+(h&&a.left),f=r.top+(h&&a.top),{width:u,height:p}=i;return o&&(u-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-d)/u*t.width/s),y:Math.round((c-f)/p*t.height/s)}}function qf(e,i,t){let s,n;if(i===void 0||t===void 0){let o=e&&Ii(e);if(!o)i=e.clientWidth,t=e.clientHeight;else{let r=o.getBoundingClientRect(),a=Fi(o),l=Yt(a,"border","width"),c=Yt(a,"padding");i=r.width-c.width-l.width,t=r.height-c.height-l.height,s=ki(a.maxWidth,o,"clientWidth"),n=ki(a.maxHeight,o,"clientHeight")}}return{width:i,height:t,maxWidth:s||wi,maxHeight:n||wi}}var yi=e=>Math.round(e*10)/10;function sa(e,i,t,s){let n=Fi(e),o=Yt(n,"margin"),r=ki(n.maxWidth,e,"clientWidth")||wi,a=ki(n.maxHeight,e,"clientHeight")||wi,l=qf(e,i,t),{width:c,height:h}=l;if(n.boxSizing==="content-box"){let f=Yt(n,"border","width"),u=Yt(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=yi(Math.min(c,r,l.maxWidth)),h=yi(Math.min(h,a,l.maxHeight)),c&&!h&&(h=yi(c/2)),(i!==void 0||t!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=yi(Math.floor(h*s))),{width:c,height:h}}function Fs(e,i,t){let s=i||1,n=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);let r=e.canvas;return r.style&&(t||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(e.currentDevicePixelRatio=s,r.height=n,r.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0):!1}var na=function(){let e=!1;try{let i={get passive(){return e=!0,!1}};Ri()&&(window.addEventListener("test",null,i),window.removeEventListener("test",null,i))}catch{}return e}();function zs(e,i){let t=Uf(e,i),s=t&&t.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Rt(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:e.y+t*(i.y-e.y)}}function oa(e,i,t,s){return{x:e.x+t*(i.x-e.x),y:s==="middle"?t<.5?e.y:i.y:s==="after"?t<1?e.y:i.y:t>0?i.y:e.y}}function ra(e,i,t,s){let n={x:e.cp2x,y:e.cp2y},o={x:i.cp1x,y:i.cp1y},r=Rt(e,n,t),a=Rt(n,o,t),l=Rt(o,i,t),c=Rt(r,a,t),h=Rt(a,l,t);return Rt(c,h,t)}var Zf=function(e,i){return{x(t){return e+e+i-t},setWidth(t){i=t},textAlign(t){return t==="center"?t:t==="right"?"left":"right"},xPlus(t,s){return t-s},leftForLtr(t,s){return t-s}}},Jf=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,i){return e+i},leftForLtr(e,i){return e}}};function Gt(e,i,t){return e?Zf(i,t):Jf()}function Bs(e,i){let t,s;(i==="ltr"||i==="rtl")&&(t=e.canvas.style,s=[t.getPropertyValue("direction"),t.getPropertyPriority("direction")],t.setProperty("direction",i,"important"),e.prevTextDirection=s)}function js(e,i){i!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",i[0],i[1]))}function aa(e){return e==="angle"?{between:be,compare:gf,normalize:X}:{between:gt,compare:(i,t)=>i-t,normalize:i=>i}}function Tr({start:e,end:i,count:t,loop:s,style:n}){return{start:e%t,end:i%t,loop:s&&(i-e+1)%t===0,style:n}}function Qf(e,i,t){let{property:s,start:n,end:o}=t,{between:r,normalize:a}=aa(s),l=i.length,{start:c,end:h,loop:d}=e,f,u;if(d){for(c+=l,h+=l,f=0,u=l;f<u&&r(a(i[c%l][s]),n,o);++f)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:e.style}}function Ns(e,i,t){if(!t)return[e];let{property:s,start:n,end:o}=t,r=i.length,{compare:a,between:l,normalize:c}=aa(s),{start:h,end:d,loop:f,style:u}=Qf(e,i,t),p=[],g=!1,m=null,b,x,y,M=()=>l(n,y,b)&&a(n,y)!==0,_=()=>a(o,b)===0||l(o,y,b),v=()=>g||M(),S=()=>!g||_();for(let w=h,k=h;w<=d;++w)x=i[w%r],!x.skip&&(b=c(x[s]),b!==y&&(g=l(b,n,o),m===null&&v()&&(m=a(b,n)===0?w:k),m!==null&&S()&&(p.push(Tr({start:m,end:w,loop:f,count:r,style:u})),m=null),k=w,y=b));return m!==null&&p.push(Tr({start:m,end:d,loop:f,count:r,style:u})),p}function Vs(e,i){let t=[],s=e.segments;for(let n=0;n<s.length;n++){let o=Ns(s[n],e.points,i);o.length&&t.push(...o)}return t}function tu(e,i,t,s){let n=0,o=i-1;if(t&&!s)for(;n<i&&!e[n].skip;)n++;for(;n<i&&e[n].skip;)n++;for(n%=i,t&&(o+=n);o>n&&e[o%i].skip;)o--;return o%=i,{start:n,end:o}}function eu(e,i,t,s){let n=e.length,o=[],r=i,a=e[i],l;for(l=i+1;l<=t;++l){let c=e[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:i%n,end:(l-1)%n,loop:s}),i=r=c.stop?l:null):(r=l,a.skip&&(i=l)),a=c}return r!==null&&o.push({start:i%n,end:r%n,loop:s}),o}function la(e,i){let t=e.points,s=e.options.spanGaps,n=t.length;if(!n)return[];let o=!!e._loop,{start:r,end:a}=tu(t,n,o,s);if(s===!0)return Lr(e,[{start:r,end:a,loop:o}],t,i);let l=a<r?a+n:a,c=!!e._fullLoop&&r===0&&a===n-1;return Lr(e,eu(t,r,l,c),t,i)}function Lr(e,i,t,s){return!s||!s.setContext||!t?i:iu(e,i,t,s)}function iu(e,i,t,s){let n=e._chart.getContext(),o=Er(e.options),{_datasetIndex:r,options:{spanGaps:a}}=e,l=t.length,c=[],h=o,d=i[0].start,f=d;function u(p,g,m,b){let x=a?-1:1;if(p!==g){for(p+=l;t[p%l].skip;)p-=x;for(;t[g%l].skip;)g+=x;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:m,style:b}),h=b,d=g%l)}}for(let p of i){d=a?d:p.start;let g=t[d%l],m;for(f=d+1;f<=p.end;f++){let b=t[f%l];m=Er(s.setContext(wt(n,{type:"segment",p0:g,p1:b,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:r}))),su(m,h)&&u(d,f-1,p.loop,h),g=b,h=m}d<f-1&&u(d,f-1,p.loop,h)}return c}function Er(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function su(e,i){if(!i)return!1;let t=[],s=function(n,o){return Ps(o)?(t.includes(o)||t.push(o),t.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(i,s)}function vi(e,i,t){return e.options.clip?e[t]:i[t]}function nu(e,i){let{xScale:t,yScale:s}=e;return t&&s?{left:vi(t,i,"left"),right:vi(t,i,"right"),top:vi(s,i,"top"),bottom:vi(s,i,"bottom")}:i}function Hs(e,i){let t=i._clip;if(t.disabled)return!1;let s=nu(i,e.chartArea);return{left:t.left===!1?0:s.left-(t.left===!0?0:t.left),right:t.right===!1?e.width:s.right+(t.right===!0?0:t.right),top:t.top===!1?0:s.top-(t.top===!0?0:t.top),bottom:t.bottom===!1?e.height:s.bottom+(t.bottom===!0?0:t.bottom)}}var tn=class{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(i,t,s,n){let o=t.listeners[n],r=t.duration;o.forEach(a=>a({chart:i,initial:t.initial,numSteps:r,currentStep:Math.min(s-t.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=ws.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(i=Date.now()){let t=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;let o=s.items,r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(i),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,i,"progress")),o.length||(s.running=!1,this._notify(n,s,i,"complete"),s.initial=!1),t+=o.length}),this._lastDate=i,t===0&&(this._running=!1)}_getAnims(i){let t=this._charts,s=t.get(i);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(i,s)),s}listen(i,t,s){this._getAnims(i).listeners[t].push(s)}add(i,t){!t||!t.length||this._getAnims(i).items.push(...t)}has(i){return this._getAnims(i).items.length>0}start(i){let t=this._charts.get(i);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(i){if(!this._running)return!1;let t=this._charts.get(i);return!(!t||!t.running||!t.items.length)}stop(i){let t=this._charts.get(i);if(!t||!t.items.length)return;let s=t.items,n=s.length-1;for(;n>=0;--n)s[n].cancel();t.items=[],this._notify(i,t,Date.now(),"complete")}remove(i){return this._charts.delete(i)}},St=new tn,ca="transparent",ou={boolean(e,i,t){return t>.5?i:e},color(e,i,t){let s=Os(e||ca),n=s.valid&&Os(i||ca);return n&&n.valid?n.mix(s,t).hexString():i},number(e,i,t){return e+(i-e)*t}},en=class{constructor(i,t,s,n){let o=t[s];n=ye([i.to,n,o,i.from]);let r=ye([i.from,o,n]);this._active=!0,this._fn=i.fn||ou[i.type||typeof r],this._easing=de[i.easing]||de.linear,this._start=Math.floor(Date.now()+(i.delay||0)),this._duration=this._total=Math.floor(i.duration),this._loop=!!i.loop,this._target=t,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(i,t,s){if(this._active){this._notify(!1);let n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,i.duration)),this._total+=o,this._loop=!!i.loop,this._to=ye([i.to,t,n,i.from]),this._from=ye([i.from,n,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(i){let t=i-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to,l;if(this._active=o!==a&&(r||t<s),!this._active){this._target[n]=a,this._notify(!0);return}if(t<0){this._target[n]=o;return}l=t/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){let i=this._promises||(this._promises=[]);return new Promise((t,s)=>{i.push({res:t,rej:s})})}_notify(i){let t=i?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][t]()}},Yi=class{constructor(i,t){this._chart=i,this._properties=new Map,this.configure(t)}configure(i){if(!D(i))return;let t=Object.keys(j.animation),s=this._properties;Object.getOwnPropertyNames(i).forEach(n=>{let o=i[n];if(!D(o))return;let r={};for(let a of t)r[a]=o[a];(F(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(i,t){let s=t.options,n=au(i,s);if(!n)return[];let o=this._createAnimations(n,s);return s.$shared&&ru(i.options.$animations,s).then(()=>{i.options=s},()=>{}),o}_createAnimations(i,t){let s=this._properties,n=[],o=i.$animations||(i.$animations={}),r=Object.keys(t),a=Date.now(),l;for(l=r.length-1;l>=0;--l){let c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(i,t));continue}let h=t[c],d=o[c],f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,a);continue}else d.cancel();if(!f||!f.duration){i[c]=h;continue}o[c]=d=new en(f,i,c,h),n.push(d)}return n}update(i,t){if(this._properties.size===0){Object.assign(i,t);return}let s=this._createAnimations(i,t);if(s.length)return St.add(this._chart,s),!0}};function ru(e,i){let t=[],s=Object.keys(i);for(let n=0;n<s.length;n++){let o=e[s[n]];o&&o.active()&&t.push(o.wait())}return Promise.all(t)}function au(e,i){if(!i)return;let t=e.options;if(!t){e.options=i;return}return t.$shared&&(e.options=t=Object.assign({},t,{$shared:!1,$animations:{}})),t}function ha(e,i){let t=e&&e.options||{},s=t.reverse,n=t.min===void 0?i:0,o=t.max===void 0?i:0;return{start:s?o:n,end:s?n:o}}function lu(e,i,t){if(t===!1)return!1;let s=ha(e,t),n=ha(i,t);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function cu(e){let i,t,s,n;return D(e)?(i=e.top,t=e.right,s=e.bottom,n=e.left):i=t=s=n=e,{top:i,right:t,bottom:s,left:n,disabled:e===!1}}function cl(e,i){let t=[],s=e._getSortedDatasetMetas(i),n,o;for(n=0,o=s.length;n<o;++n)t.push(s[n].index);return t}function da(e,i,t,s={}){let n=e.keys,o=s.mode==="single",r,a,l,c;if(i===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===t){if(h=!0,s.all)continue;break}c=e.values[l],V(c)&&(o||i===0||lt(i)===lt(c))&&(i+=c)}return!h&&!s.all?0:i}function hu(e,i){let{iScale:t,vScale:s}=i,n=t.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length),l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:e[h]};return a}function Ws(e,i){let t=e&&e.options.stacked;return t||t===void 0&&i.stack!==void 0}function du(e,i,t){return`${e.id}.${i.id}.${t.stack||t.type}`}function fu(e){let{min:i,max:t,minDefined:s,maxDefined:n}=e.getUserBounds();return{min:s?i:Number.NEGATIVE_INFINITY,max:n?t:Number.POSITIVE_INFINITY}}function uu(e,i,t){let s=e[i]||(e[i]={});return s[t]||(s[t]={})}function fa(e,i,t,s){for(let n of i.getMatchingVisibleMetas(s).reverse()){let o=e[n.index];if(t&&o>0||!t&&o<0)return n.index}return null}function ua(e,i){let{chart:t,_cachedMeta:s}=e,n=t._stacks||(t._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=du(o,r,s),d=i.length,f;for(let u=0;u<d;++u){let p=i[u],{[l]:g,[c]:m}=p,b=p._stacks||(p._stacks={});f=b[c]=uu(n,h,g),f[a]=m,f._top=fa(f,r,!0,s.type),f._bottom=fa(f,r,!1,s.type);let x=f._visualValues||(f._visualValues={});x[a]=m}}function $s(e,i){let t=e.scales;return Object.keys(t).filter(s=>t[s].axis===i).shift()}function pu(e,i){return wt(e,{active:!1,dataset:void 0,datasetIndex:i,index:i,mode:"default",type:"dataset"})}function gu(e,i,t){return wt(e,{active:!1,dataIndex:i,parsed:void 0,raw:void 0,element:t,index:i,mode:"default",type:"data"})}function We(e,i){let t=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){i=i||e._parsed;for(let n of i){let o=n._stacks;if(!o||o[s]===void 0||o[s][t]===void 0)return;delete o[s][t],o[s]._visualValues!==void 0&&o[s]._visualValues[t]!==void 0&&delete o[s]._visualValues[t]}}}var Ys=e=>e==="reset"||e==="none",pa=(e,i)=>i?e:Object.assign({},e),mu=(e,i,t)=>e&&!i.hidden&&i._stacked&&{keys:cl(t,!0),values:null},Vt=(()=>{class e{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,s){this.chart=t,this._ctx=t.ctx,this.index=s,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){let t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ws(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&We(this._cachedMeta),this.index=t}linkScales(){let t=this.chart,s=this._cachedMeta,n=this.getDataset(),o=(f,u,p,g)=>f==="x"?u:f==="r"?g:p,r=s.xAxisID=C(n.xAxisID,$s(t,"x")),a=s.yAxisID=C(n.yAxisID,$s(t,"y")),l=s.rAxisID=C(n.rAxisID,$s(t,"r")),c=s.indexAxis,h=s.iAxisID=o(c,r,a,l),d=s.vAxisID=o(c,a,r,l);s.xScale=this.getScaleForId(r),s.yScale=this.getScaleForId(a),s.rScale=this.getScaleForId(l),s.iScale=this.getScaleForId(h),s.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){let s=this._cachedMeta;return t===s.iScale?s.vScale:s.iScale}reset(){this._update("reset")}_destroy(){let t=this._cachedMeta;this._data&&vs(this._data,this),t._stacked&&We(t)}_dataCheck(){let t=this.getDataset(),s=t.data||(t.data=[]),n=this._data;if(D(s)){let o=this._cachedMeta;this._data=hu(s,o)}else if(n!==s){if(n){vs(n,this);let o=this._cachedMeta;We(o),o._parsed=[]}s&&Object.isExtensible(s)&&$r(s,this),this._syncList=[],this._data=s}}addElements(){let t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){let s=this._cachedMeta,n=this.getDataset(),o=!1;this._dataCheck();let r=s._stacked;s._stacked=Ws(s.vScale,s),s.stack!==n.stack&&(o=!0,We(s),s.stack=n.stack),this._resyncElements(t),(o||r!==s._stacked)&&(ua(this,s._parsed),s._stacked=Ws(s.vScale,s))}configure(){let t=this.chart.config,s=t.datasetScopeKeys(this._type),n=t.getOptionScopes(this.getDataset(),s,!0);this.options=t.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,s){let{_cachedMeta:n,_data:o}=this,{iScale:r,_stacked:a}=n,l=r.axis,c=t===0&&s===o.length?!0:n._sorted,h=t>0&&n._parsed[t-1],d,f,u;if(this._parsing===!1)n._parsed=o,n._sorted=!0,u=o;else{F(o[t])?u=this.parseArrayData(n,o,t,s):D(o[t])?u=this.parseObjectData(n,o,t,s):u=this.parsePrimitiveData(n,o,t,s);let p=()=>f[l]===null||h&&f[l]<h[l];for(d=0;d<s;++d)n._parsed[d+t]=f=u[d],c&&(p()&&(c=!1),h=f);n._sorted=c}a&&ua(this,u)}parsePrimitiveData(t,s,n,o){let{iScale:r,vScale:a}=t,l=r.axis,c=a.axis,h=r.getLabels(),d=r===a,f=new Array(o),u,p,g;for(u=0,p=o;u<p;++u)g=u+n,f[u]={[l]:d||r.parse(h[g],g),[c]:a.parse(s[g],g)};return f}parseArrayData(t,s,n,o){let{xScale:r,yScale:a}=t,l=new Array(o),c,h,d,f;for(c=0,h=o;c<h;++c)d=c+n,f=s[d],l[c]={x:r.parse(f[0],d),y:a.parse(f[1],d)};return l}parseObjectData(t,s,n,o){let{xScale:r,yScale:a}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=new Array(o),d,f,u,p;for(d=0,f=o;d<f;++d)u=d+n,p=s[u],h[d]={x:r.parse(Mt(p,l),u),y:a.parse(Mt(p,c),u)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,s,n){let o=this.chart,r=this._cachedMeta,a=s[t.axis],l={keys:cl(o,!0),values:s._stacks[t.axis]._visualValues};return da(l,a,r.index,{mode:n})}updateRangeFromParsed(t,s,n,o){let r=n[s.axis],a=r===null?NaN:r,l=o&&n._stacks[s.axis];o&&l&&(o.values=l,a=da(o,r,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,s){let n=this._cachedMeta,o=n._parsed,r=n._sorted&&t===n.iScale,a=o.length,l=this._getOtherScale(t),c=mu(s,n,this.chart),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:f}=fu(l),u,p;function g(){p=o[u];let m=p[l.axis];return!V(p[t.axis])||d>m||f<m}for(u=0;u<a&&!(!g()&&(this.updateRangeFromParsed(h,t,p,c),r));++u);if(r){for(u=a-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(h,t,p,c);break}}return h}getAllParsedValues(t){let s=this._cachedMeta._parsed,n=[],o,r,a;for(o=0,r=s.length;o<r;++o)a=s[o][t.axis],V(a)&&n.push(a);return n}getMaxOverflow(){return!1}getLabelAndValue(t){let s=this._cachedMeta,n=s.iScale,o=s.vScale,r=this.getParsed(t);return{label:n?""+n.getLabelForValue(r[n.axis]):"",value:o?""+o.getLabelForValue(r[o.axis]):""}}_update(t){let s=this._cachedMeta;this.update(t||"default"),s._clip=cu(C(this.options.clip,lu(s.xScale,s.yScale,this.getMaxOverflow())))}update(t){}draw(){let t=this._ctx,s=this.chart,n=this._cachedMeta,o=n.data||[],r=s.chartArea,a=[],l=this._drawStart||0,c=this._drawCount||o.length-l,h=this.options.drawActiveElementsOnTop,d;for(n.dataset&&n.dataset.draw(t,r,l,c),d=l;d<l+c;++d){let f=o[d];f.hidden||(f.active&&h?a.push(f):f.draw(t,r))}for(d=0;d<a.length;++d)a[d].draw(t,r)}getStyle(t,s){let n=s?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(t||0,n)}getContext(t,s,n){let o=this.getDataset(),r;if(t>=0&&t<this._cachedMeta.data.length){let a=this._cachedMeta.data[t];r=a.$context||(a.$context=gu(this.getContext(),t,a)),r.parsed=this.getParsed(t),r.raw=o.data[t],r.index=r.dataIndex=t}else r=this.$context||(this.$context=pu(this.chart.getContext(),this.index)),r.dataset=o,r.index=r.datasetIndex=this.index;return r.active=!!s,r.mode=n,r}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,s){return this._resolveElementOptions(this.dataElementType.id,s,t)}_resolveElementOptions(t,s="default",n){let o=s==="active",r=this._cachedDataOpts,a=t+"-"+s,l=r[a],c=this.enableOptionSharing&&ge(n);if(l)return pa(l,c);let h=this.chart.config,d=h.datasetElementScopeKeys(this._type,t),f=o?[`${t}Hover`,"hover",t,""]:[t,""],u=h.getOptionScopes(this.getDataset(),d),p=Object.keys(j.elements[t]),g=()=>this.getContext(n,o,s),m=h.resolveNamedOptions(u,p,g,f);return m.$shared&&(m.$shared=c,r[a]=Object.freeze(pa(m,c))),m}_resolveAnimations(t,s,n){let o=this.chart,r=this._cachedDataOpts,a=`animation-${s}`,l=r[a];if(l)return l;let c;if(o.options.animation!==!1){let d=this.chart.config,f=d.datasetAnimationScopeKeys(this._type,s),u=d.getOptionScopes(this.getDataset(),f);c=d.createResolver(u,this.getContext(t,n,s))}let h=new Yi(o,c&&c.animations);return c&&c._cacheable&&(r[a]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,s){return!s||Ys(t)||this.chart._animationsDisabled}_getSharedOptions(t,s){let n=this.resolveDataElementOptions(t,s),o=this._sharedOptions,r=this.getSharedOptions(n),a=this.includeOptions(s,r)||r!==o;return this.updateSharedOptions(r,s,n),{sharedOptions:r,includeOptions:a}}updateElement(t,s,n,o){Ys(o)?Object.assign(t,n):this._resolveAnimations(s,o).update(t,n)}updateSharedOptions(t,s,n){t&&!Ys(s)&&this._resolveAnimations(void 0,s).update(t,n)}_setStyle(t,s,n,o){t.active=o;let r=this.getStyle(s,o);this._resolveAnimations(s,n,o).update(t,{options:!o&&this.getSharedOptions(r)||r})}removeHoverStyle(t,s,n){this._setStyle(t,n,"active",!1)}setHoverStyle(t,s,n){this._setStyle(t,n,"active",!0)}_removeDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){let t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){let s=this._data,n=this._cachedMeta.data;for(let[l,c,h]of this._syncList)this[l](c,h);this._syncList=[];let o=n.length,r=s.length,a=Math.min(r,o);a&&this.parse(0,a),r>o?this._insertElements(o,r-o,t):r<o&&this._removeElements(r,o-r)}_insertElements(t,s,n=!0){let o=this._cachedMeta,r=o.data,a=t+s,l,c=h=>{for(h.length+=s,l=h.length-1;l>=a;l--)h[l]=h[l-s]};for(c(r),l=t;l<a;++l)r[l]=new this.dataElementType;this._parsing&&c(o._parsed),this.parse(t,s),n&&this.updateElements(r,t,s,"reset")}updateElements(t,s,n,o){}_removeElements(t,s){let n=this._cachedMeta;if(this._parsing){let o=n._parsed.splice(t,s);n._stacked&&We(n,o)}n.data.splice(t,s)}_sync(t){if(this._parsing)this._syncList.push(t);else{let[s,n,o]=t;this[s](n,o)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){let t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,s){s&&this._sync(["_removeElements",t,s]);let n=arguments.length-2;n&&this._sync(["_insertElements",t,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}return e})();function bu(e,i){if(!e._cache.$bar){let t=e.getMatchingVisibleMetas(i),s=[];for(let n=0,o=t.length;n<o;n++)s=s.concat(t[n].controller.getAllParsedValues(e));e._cache.$bar=Ms(s.sort((n,o)=>n-o))}return e._cache.$bar}function xu(e){let i=e.iScale,t=bu(i,e.type),s=i._length,n,o,r,a,l=()=>{r===32767||r===-32768||(ge(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=t.length;n<o;++n)r=i.getPixelForValue(t[n]),l();for(a=void 0,n=0,o=i.ticks.length;n<o;++n)r=i.getPixelForTick(n),l();return s}function _u(e,i,t,s){let n=t.barThickness,o,r;return A(n)?(o=i.min*t.categoryPercentage,r=t.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:i.pixels[e]-o/2}}function yu(e,i,t,s){let n=i.pixels,o=n[e],r=e>0?n[e-1]:null,a=e<n.length-1?n[e+1]:null,l=t.categoryPercentage;r===null&&(r=o-(a===null?i.end-i.start:a-o)),a===null&&(a=o+o-r);let c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:t.barPercentage,start:c}}function vu(e,i,t,s){let n=t.parse(e[0],s),o=t.parse(e[1],s),r=Math.min(n,o),a=Math.max(n,o),l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),i[t.axis]=c,i._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function hl(e,i,t,s){return F(e)?vu(e,i,t,s):i[t.axis]=t.parse(e,s),i}function ga(e,i,t,s){let n=e.iScale,o=e.vScale,r=n.getLabels(),a=n===o,l=[],c,h,d,f;for(c=t,h=t+s;c<h;++c)f=i[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(hl(f,d,o,c));return l}function Us(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function Mu(e,i,t){return e!==0?lt(e):(i.isHorizontal()?1:-1)*(i.min>=t?1:-1)}function wu(e){let i,t,s,n,o;return e.horizontal?(i=e.base>e.x,t="left",s="right"):(i=e.base<e.y,t="bottom",s="top"),i?(n="end",o="start"):(n="start",o="end"),{start:t,end:s,reverse:i,top:n,bottom:o}}function Su(e,i,t,s){let n=i.borderSkipped,o={};if(!n){e.borderSkipped=o;return}if(n===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}let{start:r,end:a,reverse:l,top:c,bottom:h}=wu(e);n==="middle"&&t&&(e.enableBorderRadius=!0,(t._top||0)===s?n=c:(t._bottom||0)===s?n=h:(o[ma(h,r,a,l)]=!0,n=c)),o[ma(n,r,a,l)]=!0,e.borderSkipped=o}function ma(e,i,t,s){return s?(e=ku(e,i,t),e=ba(e,t,i)):e=ba(e,i,t),e}function ku(e,i,t){return e===i?t:e===t?i:e}function ba(e,i,t){return e==="start"?i:e==="end"?t:e}function Cu(e,{inflateAmount:i},t){e.inflateAmount=i==="auto"?t===1?.33:0:i}var Pu=(()=>{class e extends Vt{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,s,n,o){return ga(t,s,n,o)}parseArrayData(t,s,n,o){return ga(t,s,n,o)}parseObjectData(t,s,n,o){let{iScale:r,vScale:a}=t,{xAxisKey:l="x",yAxisKey:c="y"}=this._parsing,h=r.axis==="x"?l:c,d=a.axis==="x"?l:c,f=[],u,p,g,m;for(u=n,p=n+o;u<p;++u)m=s[u],g={},g[r.axis]=r.parse(Mt(m,h),u),f.push(hl(Mt(m,d),g,a,u));return f}updateRangeFromParsed(t,s,n,o){super.updateRangeFromParsed(t,s,n,o);let r=n._custom;r&&s===this._cachedMeta.vScale&&(t.min=Math.min(t.min,r.min),t.max=Math.max(t.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(t){let s=this._cachedMeta,{iScale:n,vScale:o}=s,r=this.getParsed(t),a=r._custom,l=Us(a)?"["+a.start+", "+a.end+"]":""+o.getLabelForValue(r[o.axis]);return{label:""+n.getLabelForValue(r[n.axis]),value:l}}initialize(){this.enableOptionSharing=!0,super.initialize();let t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){let s=this._cachedMeta;this.updateElements(s.data,0,s.data.length,t)}updateElements(t,s,n,o){let r=o==="reset",{index:a,_cachedMeta:{vScale:l}}=this,c=l.getBasePixel(),h=l.isHorizontal(),d=this._getRuler(),{sharedOptions:f,includeOptions:u}=this._getSharedOptions(s,o);for(let p=s;p<s+n;p++){let g=this.getParsed(p),m=r||A(g[l.axis])?{base:c,head:c}:this._calculateBarValuePixels(p),b=this._calculateBarIndexPixels(p,d),x=(g._stacks||{})[l.axis],y={horizontal:h,base:m.base,enableBorderRadius:!x||Us(g._custom)||a===x._top||a===x._bottom,x:h?m.head:b.center,y:h?b.center:m.head,height:h?b.size:Math.abs(m.size),width:h?Math.abs(m.size):b.size};u&&(y.options=f||this.resolveDataElementOptions(p,t[p].active?"active":o));let M=y.options||t[p].options;Su(y,M,x,a),Cu(y,M,d.ratio),this.updateElement(t[p],p,y,o)}}_getStacks(t,s){let{iScale:n}=this._cachedMeta,o=n.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),r=n.options.stacked,a=[],l=this._cachedMeta.controller.getParsed(s),c=l&&l[n.axis],h=d=>{let f=d._parsed.find(p=>p[n.axis]===c),u=f&&f[d.vScale.axis];if(A(u)||isNaN(u))return!0};for(let d of o)if(!(s!==void 0&&h(d))&&((r===!1||a.indexOf(d.stack)===-1||r===void 0&&d.stack===void 0)&&a.push(d.stack),d.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){let t=this.chart.scales,s=this.chart.options.indexAxis;return Object.keys(t).filter(n=>t[n].axis===s).shift()}_getAxis(){let t={},s=this.getFirstScaleIdForIndexAxis();for(let n of this.chart.data.datasets)t[C(this.chart.options.indexAxis==="x"?n.xAxisID:n.yAxisID,s)]=!0;return Object.keys(t)}_getStackIndex(t,s,n){let o=this._getStacks(t,n),r=s!==void 0?o.indexOf(s):-1;return r===-1?o.length-1:r}_getRuler(){let t=this.options,s=this._cachedMeta,n=s.iScale,o=[],r,a;for(r=0,a=s.data.length;r<a;++r)o.push(n.getPixelForValue(this.getParsed(r)[n.axis],r));let l=t.barThickness;return{min:l||xu(s),pixels:o,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:t.grouped,ratio:l?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){let{_cachedMeta:{vScale:s,_stacked:n,index:o},options:{base:r,minBarLength:a}}=this,l=r||0,c=this.getParsed(t),h=c._custom,d=Us(h),f=c[s.axis],u=0,p=n?this.applyStack(s,c,n):f,g,m;p!==f&&(u=p-f,p=f),d&&(f=h.barStart,p=h.barEnd-h.barStart,f!==0&&lt(f)!==lt(h.barEnd)&&(u=0),u+=f);let b=!A(r)&&!d?r:u,x=s.getPixelForValue(b);if(this.chart.getDataVisibility(t)?g=s.getPixelForValue(u+p):g=x,m=g-x,Math.abs(m)<a){m=Mu(m,s,l)*a,f===l&&(x-=m/2);let y=s.getPixelForDecimal(0),M=s.getPixelForDecimal(1),_=Math.min(y,M),v=Math.max(y,M);x=Math.max(Math.min(x,v),_),g=x+m,n&&!d&&(c._stacks[s.axis]._visualValues[o]=s.getValueForPixel(g)-s.getValueForPixel(x))}if(x===s.getPixelForValue(l)){let y=lt(m)*s.getLineWidthForValue(l)/2;x+=y,m-=y}return{size:m,base:x,head:g,center:g+m/2}}_calculateBarIndexPixels(t,s){let n=s.scale,o=this.options,r=o.skipNull,a=C(o.maxBarThickness,1/0),l,c,h=this._getAxisCount();if(s.grouped){let d=r?this._getStackCount(t):s.stackCount,f=o.barThickness==="flex"?yu(t,s,o,d*h):_u(t,s,o,d*h),u=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,p=this._getAxis().indexOf(C(u,this.getFirstScaleIdForIndexAxis())),g=this._getStackIndex(this.index,this._cachedMeta.stack,r?t:void 0)+p;l=f.start+f.chunk*g+f.chunk/2,c=Math.min(a,f.chunk*f.ratio)}else l=n.getPixelForValue(this.getParsed(t)[n.axis],t),c=Math.min(a,s.min*s.ratio);return{base:l-c/2,head:l+c/2,center:l,size:c}}draw(){let t=this._cachedMeta,s=t.vScale,n=t.data,o=n.length,r=0;for(;r<o;++r)this.getParsed(r)[s.axis]!==null&&!n[r].hidden&&n[r].draw(this._ctx)}}return e})(),Ou=(()=>{class e extends Vt{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,s,n,o){let r=super.parsePrimitiveData(t,s,n,o);for(let a=0;a<r.length;a++)r[a]._custom=this.resolveDataElementOptions(a+n).radius;return r}parseArrayData(t,s,n,o){let r=super.parseArrayData(t,s,n,o);for(let a=0;a<r.length;a++){let l=s[n+a];r[a]._custom=C(l[2],this.resolveDataElementOptions(a+n).radius)}return r}parseObjectData(t,s,n,o){let r=super.parseObjectData(t,s,n,o);for(let a=0;a<r.length;a++){let l=s[n+a];r[a]._custom=C(l&&l.r&&+l.r,this.resolveDataElementOptions(a+n).radius)}return r}getMaxOverflow(){let t=this._cachedMeta.data,s=0;for(let n=t.length-1;n>=0;--n)s=Math.max(s,t[n].size(this.resolveDataElementOptions(n))/2);return s>0&&s}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:r}=s,a=this.getParsed(t),l=o.getLabelForValue(a.x),c=r.getLabelForValue(a.y),h=a._custom;return{label:n[t]||"",value:"("+l+", "+c+(h?", "+h:"")+")"}}update(t){let s=this._cachedMeta.data;this.updateElements(s,0,s.length,t)}updateElements(t,s,n,o){let r=o==="reset",{iScale:a,vScale:l}=this._cachedMeta,{sharedOptions:c,includeOptions:h}=this._getSharedOptions(s,o),d=a.axis,f=l.axis;for(let u=s;u<s+n;u++){let p=t[u],g=!r&&this.getParsed(u),m={},b=m[d]=r?a.getPixelForDecimal(.5):a.getPixelForValue(g[d]),x=m[f]=r?l.getBasePixel():l.getPixelForValue(g[f]);m.skip=isNaN(b)||isNaN(x),h&&(m.options=c||this.resolveDataElementOptions(u,p.active?"active":o),r&&(m.options.radius=0)),this.updateElement(p,u,m,o)}}resolveDataElementOptions(t,s){let n=this.getParsed(t),o=super.resolveDataElementOptions(t,s);o.$shared&&(o=Object.assign({},o,{$shared:!1}));let r=o.radius;return s!=="active"&&(o.radius=0),o.radius+=C(n&&n._custom,r),o}}return e})();function Au(e,i,t){let s=1,n=1,o=0,r=0;if(i<z){let a=e,l=a+i,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),f=Math.sin(l),u=(y,M,_)=>be(y,a,l,!0)?1:Math.max(M,M*t,_,_*t),p=(y,M,_)=>be(y,a,l,!0)?-1:Math.min(M,M*t,_,_*t),g=u(0,c,d),m=u(H,h,f),b=p(L,c,d),x=p(L+H,h,f);s=(g-b)/2,n=(m-x)/2,o=-(g+b)/2,r=-(m+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}var Mn=(()=>{class e extends Vt{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let s=t.data;if(s.labels.length&&s.datasets.length){let{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((r,a)=>{let c=t.getDatasetMeta(0).controller.getStyle(a);return{text:r,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}}};constructor(t,s){super(t,s),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,s){let n=this.getDataset().data,o=this._cachedMeta;if(this._parsing===!1)o._parsed=n;else{let r=c=>+n[c];if(D(n[t])){let{key:c="value"}=this._parsing;r=h=>+Mt(n[h],c)}let a,l;for(a=t,l=t+s;a<l;++a)o._parsed[a]=r(a)}}_getRotation(){return ot(this.options.rotation-90)}_getCircumference(){return ot(this.options.circumference)}_getRotationExtents(){let t=z,s=-z;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){let o=this.chart.getDatasetMeta(n).controller,r=o._getRotation(),a=o._getCircumference();t=Math.min(t,r),s=Math.max(s,r+a)}return{rotation:t,circumference:s-t}}update(t){let s=this.chart,{chartArea:n}=s,o=this._cachedMeta,r=o.data,a=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,l=Math.max((Math.min(n.width,n.height)-a)/2,0),c=Math.min(Ir(this.options.cutout,l),1),h=this._getRingWeight(this.index),{circumference:d,rotation:f}=this._getRotationExtents(),{ratioX:u,ratioY:p,offsetX:g,offsetY:m}=Au(f,d,c),b=(n.width-a)/u,x=(n.height-a)/p,y=Math.max(Math.min(b,x)/2,0),M=gs(this.options.radius,y),_=Math.max(M*c,0),v=(M-_)/this._getVisibleDatasetWeightTotal();this.offsetX=g*M,this.offsetY=m*M,o.total=this.calculateTotal(),this.outerRadius=M-v*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-v*h,0),this.updateElements(r,0,r.length,t)}_circumference(t,s){let n=this.options,o=this._cachedMeta,r=this._getCircumference();return s&&n.animation.animateRotate||!this.chart.getDataVisibility(t)||o._parsed[t]===null||o.data[t].hidden?0:this.calculateCircumference(o._parsed[t]*r/z)}updateElements(t,s,n,o){let r=o==="reset",a=this.chart,l=a.chartArea,h=a.options.animation,d=(l.left+l.right)/2,f=(l.top+l.bottom)/2,u=r&&h.animateScale,p=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:m,includeOptions:b}=this._getSharedOptions(s,o),x=this._getRotation(),y;for(y=0;y<s;++y)x+=this._circumference(y,r);for(y=s;y<s+n;++y){let M=this._circumference(y,r),_=t[y],v={x:d+this.offsetX,y:f+this.offsetY,startAngle:x,endAngle:x+M,circumference:M,outerRadius:g,innerRadius:p};b&&(v.options=m||this.resolveDataElementOptions(y,_.active?"active":o)),x+=M,this.updateElement(_,y,v,o)}}calculateTotal(){let t=this._cachedMeta,s=t.data,n=0,o;for(o=0;o<s.length;o++){let r=t._parsed[o];r!==null&&!isNaN(r)&&this.chart.getDataVisibility(o)&&!s[o].hidden&&(n+=Math.abs(r))}return n}calculateCircumference(t){let s=this._cachedMeta.total;return s>0&&!isNaN(t)?z*(Math.abs(t)/s):0}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart,o=n.data.labels||[],r=xe(s._parsed[t],n.options.locale);return{label:o[t]||"",value:r}}getMaxBorderWidth(t){let s=0,n=this.chart,o,r,a,l,c;if(!t){for(o=0,r=n.data.datasets.length;o<r;++o)if(n.isDatasetVisible(o)){a=n.getDatasetMeta(o),t=a.data,l=a.controller;break}}if(!t)return 0;for(o=0,r=t.length;o<r;++o)c=l.resolveDataElementOptions(o),c.borderAlign!=="inner"&&(s=Math.max(s,c.borderWidth||0,c.hoverBorderWidth||0));return s}getMaxOffset(t){let s=0;for(let n=0,o=t.length;n<o;++n){let r=this.resolveDataElementOptions(n);s=Math.max(s,r.offset||0,r.hoverOffset||0)}return s}_getRingWeightOffset(t){let s=0;for(let n=0;n<t;++n)this.chart.isDatasetVisible(n)&&(s+=this._getRingWeight(n));return s}_getRingWeight(t){return Math.max(C(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}return e})(),Du=(()=>{class e extends Vt{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){let s=this._cachedMeta,{dataset:n,data:o=[],_dataset:r}=s,a=this.chart._animationsDisabled,{start:l,count:c}=ks(s,o,a);this._drawStart=l,this._drawCount=c,Cs(s)&&(l=0,c=o.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!r._decimated,n.points=o;let h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(n,void 0,{animated:!a,options:h},t),this.updateElements(o,l,c,t)}updateElements(t,s,n,o){let r=o==="reset",{iScale:a,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,{sharedOptions:d,includeOptions:f}=this._getSharedOptions(s,o),u=a.axis,p=l.axis,{spanGaps:g,segment:m}=this.options,b=Xt(g)?g:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||r||o==="none",y=s+n,M=t.length,_=s>0&&this.getParsed(s-1);for(let v=0;v<M;++v){let S=t[v],w=x?S:{};if(v<s||v>=y){w.skip=!0;continue}let k=this.getParsed(v),O=A(k[p]),P=w[u]=a.getPixelForValue(k[u],v),T=w[p]=r||O?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,k,c):k[p],v);w.skip=isNaN(P)||isNaN(T)||O,w.stop=v>0&&Math.abs(k[u]-_[u])>b,m&&(w.parsed=k,w.raw=h.data[v]),f&&(w.options=d||this.resolveDataElementOptions(v,S.active?"active":o)),x||this.updateElement(S,v,w,o),_=k}}getMaxOverflow(){let t=this._cachedMeta,s=t.dataset,n=s.options&&s.options.borderWidth||0,o=t.data||[];if(!o.length)return n;let r=o[0].size(this.resolveDataElementOptions(0)),a=o[o.length-1].size(this.resolveDataElementOptions(o.length-1));return Math.max(n,r,a)/2}draw(){let t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}return e})(),dl=(()=>{class e extends Vt{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){let s=t.data;if(s.labels.length&&s.datasets.length){let{labels:{pointStyle:n,color:o}}=t.legend.options;return s.labels.map((r,a)=>{let c=t.getDatasetMeta(0).controller.getStyle(a);return{text:r,fillStyle:c.backgroundColor,strokeStyle:c.borderColor,fontColor:o,lineWidth:c.borderWidth,pointStyle:n,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,s,n){n.chart.toggleDataVisibility(s.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,s){super(t,s),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){let s=this._cachedMeta,n=this.chart,o=n.data.labels||[],r=xe(s._parsed[t].r,n.options.locale);return{label:o[t]||"",value:r}}parseObjectData(t,s,n,o){return Is.bind(this)(t,s,n,o)}update(t){let s=this._cachedMeta.data;this._updateRadius(),this.updateElements(s,0,s.length,t)}getMinMax(){let t=this._cachedMeta,s={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((n,o)=>{let r=this.getParsed(o).r;!isNaN(r)&&this.chart.getDataVisibility(o)&&(r<s.min&&(s.min=r),r>s.max&&(s.max=r))}),s}_updateRadius(){let t=this.chart,s=t.chartArea,n=t.options,o=Math.min(s.right-s.left,s.bottom-s.top),r=Math.max(o/2,0),a=Math.max(n.cutoutPercentage?r/100*n.cutoutPercentage:1,0),l=(r-a)/t.getVisibleDatasetCount();this.outerRadius=r-l*this.index,this.innerRadius=this.outerRadius-l}updateElements(t,s,n,o){let r=o==="reset",a=this.chart,c=a.options.animation,h=this._cachedMeta.rScale,d=h.xCenter,f=h.yCenter,u=h.getIndexAngle(0)-.5*L,p=u,g,m=360/this.countVisibleElements();for(g=0;g<s;++g)p+=this._computeAngle(g,o,m);for(g=s;g<s+n;g++){let b=t[g],x=p,y=p+this._computeAngle(g,o,m),M=a.getDataVisibility(g)?h.getDistanceFromCenterForValue(this.getParsed(g).r):0;p=y,r&&(c.animateScale&&(M=0),c.animateRotate&&(x=y=u));let _={x:d,y:f,innerRadius:0,outerRadius:M,startAngle:x,endAngle:y,options:this.resolveDataElementOptions(g,b.active?"active":o)};this.updateElement(b,g,_,o)}}countVisibleElements(){let t=this._cachedMeta,s=0;return t.data.forEach((n,o)=>{!isNaN(this.getParsed(o).r)&&this.chart.getDataVisibility(o)&&s++}),s}_computeAngle(t,s,n){return this.chart.getDataVisibility(t)?ot(this.resolveDataElementOptions(t,s).angle||n):0}}return e})(),Tu=(()=>{class e extends Mn{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}return e})(),Lu=(()=>{class e extends Vt{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){let s=this._cachedMeta.vScale,n=this.getParsed(t);return{label:s.getLabels()[t],value:""+s.getLabelForValue(n[s.axis])}}parseObjectData(t,s,n,o){return Is.bind(this)(t,s,n,o)}update(t){let s=this._cachedMeta,n=s.dataset,o=s.data||[],r=s.iScale.getLabels();if(n.points=o,t!=="resize"){let a=this.resolveDatasetElementOptions(t);this.options.showLine||(a.borderWidth=0);let l={_loop:!0,_fullLoop:r.length===o.length,options:a};this.updateElement(n,void 0,l,t)}this.updateElements(o,0,o.length,t)}updateElements(t,s,n,o){let r=this._cachedMeta.rScale,a=o==="reset";for(let l=s;l<s+n;l++){let c=t[l],h=this.resolveDataElementOptions(l,c.active?"active":o),d=r.getPointPositionForValue(l,this.getParsed(l).r),f=a?r.xCenter:d.x,u=a?r.yCenter:d.y,p={x:f,y:u,angle:d.angle,skip:isNaN(f)||isNaN(u),options:h};this.updateElement(c,l,p,o)}}}return e})(),Eu=(()=>{class e extends Vt{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){let s=this._cachedMeta,n=this.chart.data.labels||[],{xScale:o,yScale:r}=s,a=this.getParsed(t),l=o.getLabelForValue(a.x),c=r.getLabelForValue(a.y);return{label:n[t]||"",value:"("+l+", "+c+")"}}update(t){let s=this._cachedMeta,{data:n=[]}=s,o=this.chart._animationsDisabled,{start:r,count:a}=ks(s,n,o);if(this._drawStart=r,this._drawCount=a,Cs(s)&&(r=0,a=n.length),this.options.showLine){this.datasetElementType||this.addElements();let{dataset:l,_dataset:c}=s;l._chart=this.chart,l._datasetIndex=this.index,l._decimated=!!c._decimated,l.points=n;let h=this.resolveDatasetElementOptions(t);h.segment=this.options.segment,this.updateElement(l,void 0,{animated:!o,options:h},t)}else this.datasetElementType&&(delete s.dataset,this.datasetElementType=!1);this.updateElements(n,r,a,t)}addElements(){let{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,s,n,o){let r=o==="reset",{iScale:a,vScale:l,_stacked:c,_dataset:h}=this._cachedMeta,d=this.resolveDataElementOptions(s,o),f=this.getSharedOptions(d),u=this.includeOptions(o,f),p=a.axis,g=l.axis,{spanGaps:m,segment:b}=this.options,x=Xt(m)?m:Number.POSITIVE_INFINITY,y=this.chart._animationsDisabled||r||o==="none",M=s>0&&this.getParsed(s-1);for(let _=s;_<s+n;++_){let v=t[_],S=this.getParsed(_),w=y?v:{},k=A(S[g]),O=w[p]=a.getPixelForValue(S[p],_),P=w[g]=r||k?l.getBasePixel():l.getPixelForValue(c?this.applyStack(l,S,c):S[g],_);w.skip=isNaN(O)||isNaN(P)||k,w.stop=_>0&&Math.abs(S[p]-M[p])>x,b&&(w.parsed=S,w.raw=h.data[_]),u&&(w.options=f||this.resolveDataElementOptions(_,v.active?"active":o)),y||this.updateElement(v,_,w,o),M=S}this.updateSharedOptions(f,o,d)}getMaxOverflow(){let t=this._cachedMeta,s=t.data||[];if(!this.options.showLine){let l=0;for(let c=s.length-1;c>=0;--c)l=Math.max(l,s[c].size(this.resolveDataElementOptions(c))/2);return l>0&&l}let n=t.dataset,o=n.options&&n.options.borderWidth||0;if(!s.length)return o;let r=s[0].size(this.resolveDataElementOptions(0)),a=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(o,r,a)/2}}return e})(),Ru=Object.freeze({__proto__:null,BarController:Pu,BubbleController:Ou,DoughnutController:Mn,LineController:Du,PieController:Tu,PolarAreaController:dl,RadarController:Lu,ScatterController:Eu});function Kt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}var sn=class e{static override(i){Object.assign(e.prototype,i)}options;constructor(i){this.options=i||{}}init(){}formats(){return Kt()}parse(){return Kt()}format(){return Kt()}add(){return Kt()}diff(){return Kt()}startOf(){return Kt()}endOf(){return Kt()}},Iu={_date:sn};function Fu(e,i,t,s){let{controller:n,data:o,_sorted:r}=e,a=n._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&i===a.axis&&i!=="r"&&r&&o.length){let c=a._reversePixels?Vr:ft;if(s){if(n._sharedOptions){let h=o[0],d=typeof h.getRange=="function"&&h.getRange(i);if(d){let f=c(o,i,t-d),u=c(o,i,t+d);return{lo:f.lo,hi:u.hi}}}}else{let h=c(o,i,t);if(l){let{vScale:d}=n._cachedMeta,{_parsed:f}=e,u=f.slice(0,h.lo+1).reverse().findIndex(g=>!A(g[d.axis]));h.lo-=Math.max(0,u);let p=f.slice(h.hi).findIndex(g=>!A(g[d.axis]));h.hi+=Math.max(0,p)}return h}}return{lo:0,hi:o.length-1}}function Qe(e,i,t,s,n){let o=e.getSortedVisibleDatasetMetas(),r=t[i];for(let a=0,l=o.length;a<l;++a){let{index:c,data:h}=o[a],{lo:d,hi:f}=Fu(o[a],i,r,n);for(let u=d;u<=f;++u){let p=h[u];p.skip||s(p,c,u)}}}function zu(e){let i=e.indexOf("x")!==-1,t=e.indexOf("y")!==-1;return function(s,n){let o=i?Math.abs(s.x-n.x):0,r=t?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Xs(e,i,t,s,n){let o=[];return!n&&!e.isPointInArea(i)||Qe(e,t,i,function(a,l,c){!n&&!ut(a,e.chartArea,0)||a.inRange(i.x,i.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Bu(e,i,t,s){let n=[];function o(r,a,l){let{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=ys(r,{x:i.x,y:i.y});be(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Qe(e,t,i,o),n}function ju(e,i,t,s,n,o){let r=[],a=zu(t),l=Number.POSITIVE_INFINITY;function c(h,d,f){let u=h.inRange(i.x,i.y,n);if(s&&!u)return;let p=h.getCenterPoint(n);if(!(!!o||e.isPointInArea(p))&&!u)return;let m=a(i,p);m<l?(r=[{element:h,datasetIndex:d,index:f}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:f})}return Qe(e,t,i,c),r}function Gs(e,i,t,s,n,o){return!o&&!e.isPointInArea(i)?[]:t==="r"&&!s?Bu(e,i,t,n):ju(e,i,t,s,n,o)}function xa(e,i,t,s,n){let o=[],r=t==="x"?"inXRange":"inYRange",a=!1;return Qe(e,t,i,(l,c,h)=>{l[r]&&l[r](i[t],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(i.x,i.y,n))}),s&&!a?[]:o}var Nu={evaluateInteractionItems:Qe,modes:{index(e,i,t,s){let n=jt(i,e),o=t.axis||"x",r=t.includeInvisible||!1,a=t.intersect?Xs(e,n,o,s,r):Gs(e,n,o,!1,s,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{let h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(e,i,t,s){let n=jt(i,e),o=t.axis||"xy",r=t.includeInvisible||!1,a=t.intersect?Xs(e,n,o,s,r):Gs(e,n,o,!1,s,r);if(a.length>0){let l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(e,i,t,s){let n=jt(i,e),o=t.axis||"xy",r=t.includeInvisible||!1;return Xs(e,n,o,s,r)},nearest(e,i,t,s){let n=jt(i,e),o=t.axis||"xy",r=t.includeInvisible||!1;return Gs(e,n,o,t.intersect,s,r)},x(e,i,t,s){let n=jt(i,e);return xa(e,n,"x",t.intersect,s)},y(e,i,t,s){let n=jt(i,e);return xa(e,n,"y",t.intersect,s)}}},fl=["left","top","right","bottom"];function $e(e,i){return e.filter(t=>t.pos===i)}function _a(e,i){return e.filter(t=>fl.indexOf(t.pos)===-1&&t.box.axis===i)}function Ye(e,i){return e.sort((t,s)=>{let n=i?s:t,o=i?t:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function Vu(e){let i=[],t,s,n,o,r,a;for(t=0,s=(e||[]).length;t<s;++t)n=e[t],{position:o,options:{stack:r,stackWeight:a=1}}=n,i.push({index:t,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return i}function Hu(e){let i={};for(let t of e){let{stack:s,pos:n,stackWeight:o}=t;if(!s||!fl.includes(n))continue;let r=i[s]||(i[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return i}function Wu(e,i){let t=Hu(e),{vBoxMaxWidth:s,hBoxMaxHeight:n}=i,o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];let{fullSize:l}=a.box,c=t[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&i.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&i.availableHeight)}return t}function $u(e){let i=Vu(e),t=Ye(i.filter(c=>c.box.fullSize),!0),s=Ye($e(i,"left"),!0),n=Ye($e(i,"right")),o=Ye($e(i,"top"),!0),r=Ye($e(i,"bottom")),a=_a(i,"x"),l=_a(i,"y");return{fullSize:t,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:$e(i,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function ya(e,i,t,s){return Math.max(e[t],i[t])+Math.max(e[s],i[s])}function ul(e,i){e.top=Math.max(e.top,i.top),e.left=Math.max(e.left,i.left),e.bottom=Math.max(e.bottom,i.bottom),e.right=Math.max(e.right,i.right)}function Yu(e,i,t,s){let{pos:n,box:o}=t,r=e.maxPadding;if(!D(n)){t.size&&(e[n]-=t.size);let d=s[t.stack]||{size:0,count:1};d.size=Math.max(d.size,t.horizontal?o.height:o.width),t.size=d.size/d.count,e[n]+=t.size}o.getPadding&&ul(r,o.getPadding());let a=Math.max(0,i.outerWidth-ya(r,e,"left","right")),l=Math.max(0,i.outerHeight-ya(r,e,"top","bottom")),c=a!==e.w,h=l!==e.h;return e.w=a,e.h=l,t.horizontal?{same:c,other:h}:{same:h,other:c}}function Uu(e){let i=e.maxPadding;function t(s){let n=Math.max(i[s]-e[s],0);return e[s]+=n,n}e.y+=t("top"),e.x+=t("left"),t("right"),t("bottom")}function Xu(e,i){let t=i.maxPadding;function s(n){let o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(i[r],t[r])}),o}return s(e?["left","right"]:["top","bottom"])}function Ge(e,i,t,s){let n=[],o,r,a,l,c,h;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||i.w,a.height||i.h,Xu(a.horizontal,i));let{same:d,other:f}=Yu(i,t,a,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(a)}return c&&Ge(n,i,t,s)||h}function zi(e,i,t,s,n){e.top=t,e.left=i,e.right=i+s,e.bottom=t+n,e.width=s,e.height=n}function va(e,i,t,s){let n=t.padding,{x:o,y:r}=i;for(let a of e){let l=a.box,c=s[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){let d=i.w*h,f=c.size||l.height;ge(c.start)&&(r=c.start),l.fullSize?zi(l,n.left,r,t.outerWidth-n.right-n.left,f):zi(l,i.left+c.placed,r,d,f),c.start=r,c.placed+=d,r=l.bottom}else{let d=i.h*h,f=c.size||l.width;ge(c.start)&&(o=c.start),l.fullSize?zi(l,o,n.top,f,t.outerHeight-n.bottom-n.top):zi(l,o,i.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}i.x=o,i.y=r}var Z={addBox(e,i){e.boxes||(e.boxes=[]),i.fullSize=i.fullSize||!1,i.position=i.position||"top",i.weight=i.weight||0,i._layers=i._layers||function(){return[{z:0,draw(t){i.draw(t)}}]},e.boxes.push(i)},removeBox(e,i){let t=e.boxes?e.boxes.indexOf(i):-1;t!==-1&&e.boxes.splice(t,1)},configure(e,i,t){i.fullSize=t.fullSize,i.position=t.position,i.weight=t.weight},update(e,i,t,s){if(!e)return;let n=K(e.options.layout.padding),o=Math.max(i-n.width,0),r=Math.max(t-n.height,0),a=$u(e.boxes),l=a.vertical,c=a.horizontal;E(e.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});let h=l.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,d=Object.freeze({outerWidth:i,outerHeight:t,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},n);ul(f,K(s));let u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),p=Wu(l.concat(c),d);Ge(a.fullSize,u,d,p),Ge(l,u,d,p),Ge(c,u,d,p)&&Ge(l,u,d,p),Uu(u),va(a.leftAndTop,u,d,p),u.x+=u.w,u.y+=u.h,va(a.rightAndBottom,u,d,p),e.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},E(a.chartArea,g=>{let m=g.box;Object.assign(m,e.chartArea),m.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}},Ui=class{acquireContext(i,t){}releaseContext(i){return!1}addEventListener(i,t,s){}removeEventListener(i,t,s){}getDevicePixelRatio(){return 1}getMaximumSize(i,t,s,n){return t=Math.max(0,t||i.width),s=s||i.height,{width:t,height:Math.max(0,n?Math.floor(t/n):s)}}isAttached(i){return!0}updateConfig(i){}},nn=class extends Ui{acquireContext(i){return i&&i.getContext&&i.getContext("2d")||null}updateConfig(i){i.options.animation=!1}},Wi="$chartjs",Gu={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Ma=e=>e===null||e==="";function Ku(e,i){let t=e.style,s=e.getAttribute("height"),n=e.getAttribute("width");if(e[Wi]={initial:{height:s,width:n,style:{display:t.display,height:t.height,width:t.width}}},t.display=t.display||"block",t.boxSizing=t.boxSizing||"border-box",Ma(n)){let o=zs(e,"width");o!==void 0&&(e.width=o)}if(Ma(s))if(e.style.height==="")e.height=e.width/(i||2);else{let o=zs(e,"height");o!==void 0&&(e.height=o)}return e}var pl=na?{passive:!0}:!1;function qu(e,i,t){e&&e.addEventListener(i,t,pl)}function Zu(e,i,t){e&&e.canvas&&e.canvas.removeEventListener(i,t,pl)}function Ju(e,i){let t=Gu[e.type]||e.type,{x:s,y:n}=jt(e,i);return{type:t,chart:i,native:e,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Xi(e,i){for(let t of e)if(t===i||t.contains(i))return!0}function Qu(e,i,t){let s=e.canvas,n=new MutationObserver(o=>{let r=!1;for(let a of o)r=r||Xi(a.addedNodes,s),r=r&&!Xi(a.removedNodes,s);r&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}function tp(e,i,t){let s=e.canvas,n=new MutationObserver(o=>{let r=!1;for(let a of o)r=r||Xi(a.removedNodes,s),r=r&&!Xi(a.addedNodes,s);r&&t()});return n.observe(document,{childList:!0,subtree:!0}),n}var qe=new Map,wa=0;function gl(){let e=window.devicePixelRatio;e!==wa&&(wa=e,qe.forEach((i,t)=>{t.currentDevicePixelRatio!==e&&i()}))}function ep(e,i){qe.size||window.addEventListener("resize",gl),qe.set(e,i)}function ip(e){qe.delete(e),qe.size||window.removeEventListener("resize",gl)}function sp(e,i,t){let s=e.canvas,n=s&&Ii(s);if(!n)return;let o=Ss((a,l)=>{let c=n.clientWidth;t(a,l),c<n.clientWidth&&t()},window),r=new ResizeObserver(a=>{let l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),ep(e,o),r}function Ks(e,i,t){t&&t.disconnect(),i==="resize"&&ip(e)}function np(e,i,t){let s=e.canvas,n=Ss(o=>{e.ctx!==null&&t(Ju(o,e))},e);return qu(s,i,n),n}var on=class extends Ui{acquireContext(i,t){let s=i&&i.getContext&&i.getContext("2d");return s&&s.canvas===i?(Ku(i,t),s):null}releaseContext(i){let t=i.canvas;if(!t[Wi])return!1;let s=t[Wi].initial;["height","width"].forEach(o=>{let r=s[o];A(r)?t.removeAttribute(o):t.setAttribute(o,r)});let n=s.style||{};return Object.keys(n).forEach(o=>{t.style[o]=n[o]}),t.width=t.width,delete t[Wi],!0}addEventListener(i,t,s){this.removeEventListener(i,t);let n=i.$proxies||(i.$proxies={}),r={attach:Qu,detach:tp,resize:sp}[t]||np;n[t]=r(i,t,s)}removeEventListener(i,t){let s=i.$proxies||(i.$proxies={}),n=s[t];if(!n)return;({attach:Ks,detach:Ks,resize:Ks}[t]||Zu)(i,t,n),s[t]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(i,t,s,n){return sa(i,t,s,n)}isAttached(i){let t=i&&Ii(i);return!!(t&&t.isConnected)}};function op(e){return!Ri()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?nn:on}var ct=class{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(i){let{x:t,y:s}=this.getProps(["x","y"],i);return{x:t,y:s}}hasValue(){return Xt(this.x)&&Xt(this.y)}getProps(i,t){let s=this.$animations;if(!t||!s)return this;let n={};return i.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}};function rp(e,i){let t=e.options.ticks,s=ap(e),n=Math.min(t.maxTicksLimit||s,s),o=t.major.enabled?cp(i):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return hp(i,c,o,r/n),c;let h=lp(o,i,n);if(r>0){let d,f,u=r>1?Math.round((l-a)/(r-1)):null;for(Bi(i,c,h,A(u)?0:a-u,a),d=0,f=r-1;d<f;d++)Bi(i,c,h,o[d],o[d+1]);return Bi(i,c,h,l,A(u)?i.length:l+u),c}return Bi(i,c,h),c}function ap(e){let i=e.options.offset,t=e._tickSize(),s=e._length/t+(i?0:1),n=e._maxLength/t;return Math.floor(Math.min(s,n))}function lp(e,i,t){let s=dp(e),n=i.length/t;if(!s)return Math.max(n,1);let o=Br(s);for(let r=0,a=o.length-1;r<a;r++){let l=o[r];if(l>n)return l}return Math.max(n,1)}function cp(e){let i=[],t,s;for(t=0,s=e.length;t<s;t++)e[t].major&&i.push(t);return i}function hp(e,i,t,s){let n=0,o=t[0],r;for(s=Math.ceil(s),r=0;r<e.length;r++)r===o&&(i.push(e[r]),n++,o=t[n*s])}function Bi(e,i,t,s,n){let o=C(s,0),r=Math.min(C(n,e.length),e.length),a=0,l,c,h;for(t=Math.ceil(t),n&&(l=n-s,t=l/Math.floor(l/t)),h=o;h<0;)a++,h=Math.round(o+a*t);for(c=Math.max(o,0);c<r;c++)c===h&&(i.push(e[c]),a++,h=Math.round(o+a*t))}function dp(e){let i=e.length,t,s;if(i<2)return!1;for(s=e[0],t=1;t<i;++t)if(e[t]-e[t-1]!==s)return!1;return s}var fp=e=>e==="left"?"right":e==="right"?"left":e,Sa=(e,i,t)=>i==="top"||i==="left"?e[i]+t:e[i]-t,ka=(e,i)=>Math.min(i||e,e);function Ca(e,i){let t=[],s=e.length/i,n=e.length,o=0;for(;o<n;o+=s)t.push(e[Math.floor(o)]);return t}function up(e,i,t){let s=e.ticks.length,n=Math.min(i,s-1),o=e._startPixel,r=e._endPixel,a=1e-6,l=e.getPixelForTick(n),c;if(!(t&&(s===1?c=Math.max(l-o,r-l):i===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(n-1))/2,l+=n<i?c:-c,l<o-a||l>r+a)))return l}function pp(e,i){E(e,t=>{let s=t.gc,n=s.length/2,o;if(n>i){for(o=0;o<n;++o)delete t.data[s[o]];s.splice(0,n)}})}function Ue(e){return e.drawTicks?e.tickLength:0}function Pa(e,i){if(!e.display)return 0;let t=$(e.font,i),s=K(e.padding);return(F(e.text)?e.text.length:1)*t.lineHeight+s.height}function gp(e,i){return wt(e,{scale:i,type:"scale"})}function mp(e,i,t){return wt(e,{tick:t,index:i,type:"tick"})}function bp(e,i,t){let s=Ai(e);return(t&&i!=="right"||!t&&i==="right")&&(s=fp(s)),s}function xp(e,i,t,s){let{top:n,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:h}=l,d=0,f,u,p,g=r-n,m=a-o;if(e.isHorizontal()){if(u=G(s,o,a),D(t)){let b=Object.keys(t)[0],x=t[b];p=h[b].getPixelForValue(x)+g-i}else t==="center"?p=(c.bottom+c.top)/2+g-i:p=Sa(e,t,i);f=a-o}else{if(D(t)){let b=Object.keys(t)[0],x=t[b];u=h[b].getPixelForValue(x)-m+i}else t==="center"?u=(c.left+c.right)/2-m+i:u=Sa(e,t,i);p=G(s,r,n),d=t==="left"?-H:H}return{titleX:u,titleY:p,maxWidth:f,rotation:d}}var Zt=class e extends ct{constructor(i){super(),this.id=i.id,this.type=i.type,this.options=void 0,this.ctx=i.ctx,this.chart=i.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(i){this.options=i.setContext(this.getContext()),this.axis=i.axis,this._userMin=this.parse(i.min),this._userMax=this.parse(i.max),this._suggestedMin=this.parse(i.suggestedMin),this._suggestedMax=this.parse(i.suggestedMax)}parse(i,t){return i}getUserBounds(){let{_userMin:i,_userMax:t,_suggestedMin:s,_suggestedMax:n}=this;return i=Q(i,Number.POSITIVE_INFINITY),t=Q(t,Number.NEGATIVE_INFINITY),s=Q(s,Number.POSITIVE_INFINITY),n=Q(n,Number.NEGATIVE_INFINITY),{min:Q(i,s),max:Q(t,n),minDefined:V(i),maxDefined:V(t)}}getMinMax(i){let{min:t,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:t,max:s};let a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,i),n||(t=Math.min(t,r.min)),o||(s=Math.max(s,r.max));return t=o&&t>s?s:t,s=n&&t>s?t:s,{min:Q(t,Q(s,t)),max:Q(s,Q(t,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){let i=this.chart.data;return this.options.labels||(this.isHorizontal()?i.xLabels:i.yLabels)||i.labels||[]}getLabelItems(i=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(i))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(i,t,s){let{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=i,this.maxHeight=t,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Zr(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();let l=a<this.ticks.length;this._convertTicksToLabels(l?Ca(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=rp(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let i=this.options.reverse,t,s;this.isHorizontal()?(t=this.left,s=this.right):(t=this.top,s=this.bottom,i=!i),this._startPixel=t,this._endPixel=s,this._reversePixels=i,this._length=s-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(i){this.chart.notifyPlugins(i,this.getContext()),I(this.options[i],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(i){let t=this.options.ticks,s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s],o.label=I(t.callback,[o.value,s,i],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){let i=this.options,t=i.ticks,s=ka(this.ticks.length,i.ticks.maxTicksLimit),n=t.minRotation||0,o=t.maxRotation,r=n,a,l,c;if(!this._isVisible()||!t.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}let h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=Y(this.chart.width-d,0,this.maxWidth);a=i.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(i.offset?.5:1)),l=this.maxHeight-Ue(i.grid)-t.padding-Pa(i.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),r=Pi(Math.min(Math.asin(Y((h.highest.height+6)/a,-1,1)),Math.asin(Y(l/c,-1,1))-Math.asin(Y(f/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){let i={width:0,height:0},{chart:t,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){let l=Pa(n,t.options.font);if(a?(i.width=this.maxWidth,i.height=Ue(o)+l):(i.height=this.maxHeight,i.width=Ue(o)+l),s.display&&this.ticks.length){let{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,p=ot(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(a){let b=s.mirror?0:m*d.width+g*f.height;i.height=Math.min(this.maxHeight,i.height+b+u)}else{let b=s.mirror?0:g*d.width+m*f.height;i.width=Math.min(this.maxWidth,i.width+b+u)}this._calculatePadding(c,h,m,g)}}this._handleMargins(),a?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=i.height):(this.width=i.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(i,t,s,n){let{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){let h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1),f=0,u=0;l?c?(f=n*i.width,u=s*t.height):(f=s*i.height,u=n*t.width):o==="start"?u=t.width:o==="end"?f=i.width:o!=="inner"&&(f=i.width/2,u=t.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let h=t.height/2,d=i.height/2;o==="start"?(h=0,d=i.height):o==="end"&&(h=t.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){let{axis:i,position:t}=this.options;return t==="top"||t==="bottom"||i==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(i){this.beforeTickToLabelConversion(),this.generateTickLabels(i);let t,s;for(t=0,s=i.length;t<s;t++)A(i[t].label)&&(i.splice(t,1),s--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let i=this._labelSizes;if(!i){let t=this.options.ticks.sampleSize,s=this.ticks;t<s.length&&(s=Ca(s,t)),this._labelSizes=i=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return i}_computeLabelSizes(i,t,s){let{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(t/ka(t,s)),c=0,h=0,d,f,u,p,g,m,b,x,y,M,_;for(d=0;d<t;d+=l){if(p=i[d].label,g=this._resolveTickFontOptions(d),n.font=m=g.string,b=o[m]=o[m]||{data:{},gc:[]},x=g.lineHeight,y=M=0,!A(p)&&!F(p))y=Be(n,b.data,b.gc,y,p),M=x;else if(F(p))for(f=0,u=p.length;f<u;++f)_=p[f],!A(_)&&!F(_)&&(y=Be(n,b.data,b.gc,y,_),M+=x);r.push(y),a.push(M),c=Math.max(y,c),h=Math.max(M,h)}pp(o,t);let v=r.indexOf(c),S=a.indexOf(h),w=k=>({width:r[k]||0,height:a[k]||0});return{first:w(0),last:w(t-1),widest:w(v),highest:w(S),widths:r,heights:a}}getLabelForValue(i){return i}getPixelForValue(i,t){return NaN}getValueForPixel(i){}getPixelForTick(i){let t=this.ticks;return i<0||i>t.length-1?null:this.getPixelForValue(t[i].value)}getPixelForDecimal(i){this._reversePixels&&(i=1-i);let t=this._startPixel+i*this._length;return Nr(this._alignToPixels?Ft(this.chart,t,0):t)}getDecimalForPixel(i){let t=(i-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){let{min:i,max:t}=this;return i<0&&t<0?t:i>0&&t>0?i:0}getContext(i){let t=this.ticks||[];if(i>=0&&i<t.length){let s=t[i];return s.$context||(s.$context=mp(this.getContext(),i,s))}return this.$context||(this.$context=gp(this.chart.getContext(),this))}_tickSize(){let i=this.options.ticks,t=ot(this.labelRotation),s=Math.abs(Math.cos(t)),n=Math.abs(Math.sin(t)),o=this._getLabelSizes(),r=i.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){let i=this.options.display;return i!=="auto"?!!i:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(i){let t=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Ue(o),u=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,b=function(N){return Ft(s,N,g)},x,y,M,_,v,S,w,k,O,P,T,U;if(r==="top")x=b(this.bottom),S=this.bottom-f,k=x-m,P=b(i.top)+m,U=i.bottom;else if(r==="bottom")x=b(this.top),P=i.top,U=b(i.bottom)-m,S=x+m,k=this.top+f;else if(r==="left")x=b(this.right),v=this.right-f,w=x-m,O=b(i.left)+m,T=i.right;else if(r==="right")x=b(this.left),O=i.left,T=b(i.right)-m,v=x+m,w=this.left+f;else if(t==="x"){if(r==="center")x=b((i.top+i.bottom)/2+.5);else if(D(r)){let N=Object.keys(r)[0],W=r[N];x=b(this.chart.scales[N].getPixelForValue(W))}P=i.top,U=i.bottom,S=x+m,k=S+f}else if(t==="y"){if(r==="center")x=b((i.left+i.right)/2);else if(D(r)){let N=Object.keys(r)[0],W=r[N];x=b(this.chart.scales[N].getPixelForValue(W))}v=x-m,w=v-f,O=i.left,T=i.right}let it=C(n.ticks.maxTicksLimit,d),R=Math.max(1,Math.ceil(d/it));for(y=0;y<d;y+=R){let N=this.getContext(y),W=o.setContext(N),rt=a.setContext(N),q=W.lineWidth,Jt=W.color,ei=rt.dash||[],Qt=rt.dashOffset,Se=W.tickWidth,Ht=W.tickColor,ke=W.tickBorderDash||[],Wt=W.tickBorderDashOffset;M=up(this,y,l),M!==void 0&&(_=Ft(s,M,q),c?v=w=O=T=_:S=k=P=U=_,u.push({tx1:v,ty1:S,tx2:w,ty2:k,x1:O,y1:P,x2:T,y2:U,width:q,color:Jt,borderDash:ei,borderDashOffset:Qt,tickWidth:Se,tickColor:Ht,tickBorderDash:ke,tickBorderDashOffset:Wt}))}return this._ticksLength=d,this._borderValue=x,u}_computeLabelItems(i){let t=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=Ue(s.grid),u=f+h,p=d?-h:u,g=-ot(this.labelRotation),m=[],b,x,y,M,_,v,S,w,k,O,P,T,U="middle";if(n==="top")v=this.bottom-p,S=this._getXAxisLabelAlignment();else if(n==="bottom")v=this.top+p,S=this._getXAxisLabelAlignment();else if(n==="left"){let R=this._getYAxisLabelAlignment(f);S=R.textAlign,_=R.x}else if(n==="right"){let R=this._getYAxisLabelAlignment(f);S=R.textAlign,_=R.x}else if(t==="x"){if(n==="center")v=(i.top+i.bottom)/2+u;else if(D(n)){let R=Object.keys(n)[0],N=n[R];v=this.chart.scales[R].getPixelForValue(N)+u}S=this._getXAxisLabelAlignment()}else if(t==="y"){if(n==="center")_=(i.left+i.right)/2-u;else if(D(n)){let R=Object.keys(n)[0],N=n[R];_=this.chart.scales[R].getPixelForValue(N)}S=this._getYAxisLabelAlignment(f).textAlign}t==="y"&&(l==="start"?U="top":l==="end"&&(U="bottom"));let it=this._getLabelSizes();for(b=0,x=a.length;b<x;++b){y=a[b],M=y.label;let R=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,k=this._resolveTickFontOptions(b),O=k.lineHeight,P=F(M)?M.length:1;let N=P/2,W=R.color,rt=R.textStrokeColor,q=R.textStrokeWidth,Jt=S;r?(_=w,S==="inner"&&(b===x-1?Jt=this.options.reverse?"left":"right":b===0?Jt=this.options.reverse?"right":"left":Jt="center"),n==="top"?c==="near"||g!==0?T=-P*O+O/2:c==="center"?T=-it.highest.height/2-N*O+O:T=-it.highest.height+O/2:c==="near"||g!==0?T=O/2:c==="center"?T=it.highest.height/2-N*O:T=it.highest.height-P*O,d&&(T*=-1),g!==0&&!R.showLabelBackdrop&&(_+=O/2*Math.sin(g))):(v=w,T=(1-P)*O/2);let ei;if(R.showLabelBackdrop){let Qt=K(R.backdropPadding),Se=it.heights[b],Ht=it.widths[b],ke=T-Qt.top,Wt=0-Qt.left;switch(U){case"middle":ke-=Se/2;break;case"bottom":ke-=Se;break}switch(S){case"center":Wt-=Ht/2;break;case"right":Wt-=Ht;break;case"inner":b===x-1?Wt-=Ht:b>0&&(Wt-=Ht/2);break}ei={left:Wt,top:ke,width:Ht+Qt.width,height:Se+Qt.height,color:R.backdropColor}}m.push({label:M,font:k,textOffset:T,options:{rotation:g,color:W,strokeColor:rt,strokeWidth:q,textAlign:Jt,textBaseline:U,translation:[_,v],backdrop:ei}})}return m}_getXAxisLabelAlignment(){let{position:i,ticks:t}=this.options;if(-ot(this.labelRotation))return i==="top"?"left":"right";let n="center";return t.align==="start"?n="left":t.align==="end"?n="right":t.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(i){let{position:t,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=i+o,l=r.widest.width,c,h;return t==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):t==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;let i=this.chart,t=this.options.position;if(t==="left"||t==="right")return{top:0,left:this.left,bottom:i.height,right:this.right};if(t==="top"||t==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:i.width}}drawBackground(){let{ctx:i,options:{backgroundColor:t},left:s,top:n,width:o,height:r}=this;t&&(i.save(),i.fillStyle=t,i.fillRect(s,n,o,r),i.restore())}getLineWidthForValue(i){let t=this.options.grid;if(!this._isVisible()||!t.display)return 0;let n=this.ticks.findIndex(o=>o.value===i);return n>=0?t.setContext(this.getContext(n)).lineWidth:0}drawGrid(i){let t=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(i)),o,r,a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(t.display)for(o=0,r=n.length;o<r;++o){let l=n[o];t.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),t.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){let{chart:i,ctx:t,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;let a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue,c,h,d,f;this.isHorizontal()?(c=Ft(i,this.left,r)-r/2,h=Ft(i,this.right,a)+a/2,d=f=l):(d=Ft(i,this.top,r)-r/2,f=Ft(i,this.bottom,a)+a/2,c=h=l),t.save(),t.lineWidth=o.width,t.strokeStyle=o.color,t.beginPath(),t.moveTo(c,d),t.lineTo(h,f),t.stroke(),t.restore()}drawLabels(i){if(!this.options.ticks.display)return;let s=this.ctx,n=this._computeLabelArea();n&&Ve(s,n);let o=this.getLabelItems(i);for(let r of o){let a=r.options,l=r.font,c=r.label,h=r.textOffset;zt(s,c,0,h,l,a)}n&&He(s)}drawTitle(){let{ctx:i,options:{position:t,title:s,reverse:n}}=this;if(!s.display)return;let o=$(s.font),r=K(s.padding),a=s.align,l=o.lineHeight/2;t==="bottom"||t==="center"||D(t)?(l+=r.bottom,F(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;let{titleX:c,titleY:h,maxWidth:d,rotation:f}=xp(this,l,t,a);zt(i,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:bp(a,t,n),textBaseline:"middle",translation:[c,h]})}draw(i){this._isVisible()&&(this.drawBackground(),this.drawGrid(i),this.drawBorder(),this.drawTitle(),this.drawLabels(i))}_layers(){let i=this.options,t=i.ticks&&i.ticks.z||0,s=C(i.grid&&i.grid.z,-1),n=C(i.border&&i.border.z,0);return!this._isVisible()||this.draw!==e.prototype.draw?[{z:t,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:t,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(i){let t=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[],o,r;for(o=0,r=t.length;o<r;++o){let a=t[o];a[s]===this.id&&(!i||a.type===i)&&n.push(a)}return n}_resolveTickFontOptions(i){let t=this.options.ticks.setContext(this.getContext(i));return $(t.font)}_maxDigits(){let i=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/i}},Me=class{constructor(i,t,s){this.type=i,this.scope=t,this.override=s,this.items=Object.create(null)}isForType(i){return Object.prototype.isPrototypeOf.call(this.type.prototype,i.prototype)}register(i){let t=Object.getPrototypeOf(i),s;vp(t)&&(s=this.register(t));let n=this.items,o=i.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+i);return o in n||(n[o]=i,_p(i,r,s),this.override&&j.override(i.id,i.overrides)),r}get(i){return this.items[i]}unregister(i){let t=this.items,s=i.id,n=this.scope;s in t&&delete t[s],n&&s in j[n]&&(delete j[n][s],this.override&&delete It[s])}};function _p(e,i,t){let s=fe(Object.create(null),[t?j.get(t):{},j.get(i),e.defaults]);j.set(i,s),e.defaultRoutes&&yp(i,e.defaultRoutes),e.descriptors&&j.describe(i,e.descriptors)}function yp(e,i){Object.keys(i).forEach(t=>{let s=t.split("."),n=s.pop(),o=[e].concat(s).join("."),r=i[t].split("."),a=r.pop(),l=r.join(".");j.route(o,n,l,a)})}function vp(e){return"id"in e&&"defaults"in e}var rn=class{constructor(){this.controllers=new Me(Vt,"datasets",!0),this.elements=new Me(ct,"elements"),this.plugins=new Me(Object,"plugins"),this.scales=new Me(Zt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...i){this._each("register",i)}remove(...i){this._each("unregister",i)}addControllers(...i){this._each("register",i,this.controllers)}addElements(...i){this._each("register",i,this.elements)}addPlugins(...i){this._each("register",i,this.plugins)}addScales(...i){this._each("register",i,this.scales)}getController(i){return this._get(i,this.controllers,"controller")}getElement(i){return this._get(i,this.elements,"element")}getPlugin(i){return this._get(i,this.plugins,"plugin")}getScale(i){return this._get(i,this.scales,"scale")}removeControllers(...i){this._each("unregister",i,this.controllers)}removeElements(...i){this._each("unregister",i,this.elements)}removePlugins(...i){this._each("unregister",i,this.plugins)}removeScales(...i){this._each("unregister",i,this.scales)}_each(i,t,s){[...t].forEach(n=>{let o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(i,o,n):E(n,r=>{let a=s||this._getRegistryForType(r);this._exec(i,a,r)})})}_exec(i,t,s){let n=Ci(i);I(s["before"+n],[],s),t[i](s),I(s["after"+n],[],s)}_getRegistryForType(i){for(let t=0;t<this._typedRegistries.length;t++){let s=this._typedRegistries[t];if(s.isForType(i))return s}return this.plugins}_get(i,t,s){let n=t.get(i);if(n===void 0)throw new Error('"'+i+'" is not a registered '+s+".");return n}},bt=new rn,an=class{constructor(){this._init=[]}notify(i,t,s,n){t==="beforeInit"&&(this._init=this._createDescriptors(i,!0),this._notify(this._init,i,"install"));let o=n?this._descriptors(i).filter(n):this._descriptors(i),r=this._notify(o,i,t,s);return t==="afterDestroy"&&(this._notify(o,i,"stop"),this._notify(this._init,i,"uninstall")),r}_notify(i,t,s,n){n=n||{};for(let o of i){let r=o.plugin,a=r[s],l=[t,n,o.options];if(I(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){A(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(i){if(this._cache)return this._cache;let t=this._cache=this._createDescriptors(i);return this._notifyStateChanges(i),t}_createDescriptors(i,t){let s=i&&i.config,n=C(s.options&&s.options.plugins,{}),o=Mp(s);return n===!1&&!t?[]:Sp(i,o,n,t)}_notifyStateChanges(i){let t=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(t,s),i,"stop"),this._notify(n(s,t),i,"start")}};function Mp(e){let i={},t=[],s=Object.keys(bt.plugins.items);for(let o=0;o<s.length;o++)t.push(bt.getPlugin(s[o]));let n=e.plugins||[];for(let o=0;o<n.length;o++){let r=n[o];t.indexOf(r)===-1&&(t.push(r),i[r.id]=!0)}return{plugins:t,localIds:i}}function wp(e,i){return!i&&e===!1?null:e===!0?{}:e}function Sp(e,{plugins:i,localIds:t},s,n){let o=[],r=e.getContext();for(let a of i){let l=a.id,c=wp(s[l],n);c!==null&&o.push({plugin:a,options:kp(e.config,{plugin:a,local:t[l]},c,r)})}return o}function kp(e,{plugin:i,local:t},s,n){let o=e.pluginScopeKeys(i),r=e.getOptionScopes(s,o);return t&&i.defaults&&r.push(i.defaults),e.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ln(e,i){let t=j.datasets[e]||{};return((i.datasets||{})[e]||{}).indexAxis||i.indexAxis||t.indexAxis||"x"}function Cp(e,i){let t=e;return e==="_index_"?t=i:e==="_value_"&&(t=i==="x"?"y":"x"),t}function Pp(e,i){return e===i?"_index_":"_value_"}function Oa(e){if(e==="x"||e==="y"||e==="r")return e}function Op(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function cn(e,...i){if(Oa(e))return e;for(let t of i){let s=t.axis||Op(t.position)||e.length>1&&Oa(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function Aa(e,i,t){if(t[i+"AxisID"]===e)return{axis:i}}function Ap(e,i){if(i.data&&i.data.datasets){let t=i.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(t.length)return Aa(e,"x",t[0])||Aa(e,"y",t[0])}return{}}function Dp(e,i){let t=It[e.type]||{scales:{}},s=i.scales||{},n=ln(e.type,i),o=Object.create(null);return Object.keys(s).forEach(r=>{let a=s[r];if(!D(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);let l=cn(r,a,Ap(r,e),j.scales[a.type]),c=Pp(l,n),h=t.scales||{};o[r]=pe(Object.create(null),[{axis:l},a,h[l],h[c]])}),e.data.datasets.forEach(r=>{let a=r.type||e.type,l=r.indexAxis||ln(a,i),h=(It[a]||{}).scales||{};Object.keys(h).forEach(d=>{let f=Cp(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),pe(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(r=>{let a=o[r];pe(a,[j.scales[a.type],j.scale])}),o}function ml(e){let i=e.options||(e.options={});i.plugins=C(i.plugins,{}),i.scales=Dp(e,i)}function bl(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function Tp(e){return e=e||{},e.data=bl(e.data),ml(e),e}var Da=new Map,xl=new Set;function ji(e,i){let t=Da.get(e);return t||(t=i(),Da.set(e,t),xl.add(t)),t}var Xe=(e,i,t)=>{let s=Mt(i,t);s!==void 0&&e.add(s)},hn=class{constructor(i){this._config=Tp(i),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(i){this._config.type=i}get data(){return this._config.data}set data(i){this._config.data=bl(i)}get options(){return this._config.options}set options(i){this._config.options=i}get plugins(){return this._config.plugins}update(){let i=this._config;this.clearCache(),ml(i)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(i){return ji(i,()=>[[`datasets.${i}`,""]])}datasetAnimationScopeKeys(i,t){return ji(`${i}.transition.${t}`,()=>[[`datasets.${i}.transitions.${t}`,`transitions.${t}`],[`datasets.${i}`,""]])}datasetElementScopeKeys(i,t){return ji(`${i}-${t}`,()=>[[`datasets.${i}.elements.${t}`,`datasets.${i}`,`elements.${t}`,""]])}pluginScopeKeys(i){let t=i.id,s=this.type;return ji(`${s}-plugin-${t}`,()=>[[`plugins.${t}`,...i.additionalOptionScopes||[]]])}_cachedScopes(i,t){let s=this._scopeCache,n=s.get(i);return(!n||t)&&(n=new Map,s.set(i,n)),n}getOptionScopes(i,t,s){let{options:n,type:o}=this,r=this._cachedScopes(i,s),a=r.get(t);if(a)return a;let l=new Set;t.forEach(h=>{i&&(l.add(i),h.forEach(d=>Xe(l,i,d))),h.forEach(d=>Xe(l,n,d)),h.forEach(d=>Xe(l,It[o]||{},d)),h.forEach(d=>Xe(l,j,d)),h.forEach(d=>Xe(l,Di,d))});let c=Array.from(l);return c.length===0&&c.push(Object.create(null)),xl.has(t)&&r.set(t,c),c}chartOptionScopes(){let{options:i,type:t}=this;return[i,It[t]||{},j.datasets[t]||{},{type:t},j,Di]}resolveNamedOptions(i,t,s,n=[""]){let o={$shared:!0},{resolver:r,subPrefixes:a}=Ta(this._resolverCache,i,n),l=r;if(Ep(r,t)){o.$shared=!1,s=yt(s)?s():s;let c=this.createResolver(i,s,a);l=Ut(r,s,c)}for(let c of t)o[c]=l[c];return o}createResolver(i,t,s=[""],n){let{resolver:o}=Ta(this._resolverCache,i,s);return D(t)?Ut(o,t,void 0,n):o}};function Ta(e,i,t){let s=e.get(i);s||(s=new Map,e.set(i,s));let n=t.join(),o=s.get(n);return o||(o={resolver:Ei(i,t),subPrefixes:t.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}var Lp=e=>D(e)&&Object.getOwnPropertyNames(e).some(i=>yt(e[i]));function Ep(e,i){let{isScriptable:t,isIndexable:s}=Ls(e);for(let n of i){let o=t(n),r=s(n),a=(r||o)&&e[n];if(o&&(yt(a)||Lp(a))||r&&F(a))return!0}return!1}var Rp="4.5.0",Ip=["top","bottom","left","right","chartArea"];function La(e,i){return e==="top"||e==="bottom"||Ip.indexOf(e)===-1&&i==="x"}function Ea(e,i){return function(t,s){return t[e]===s[e]?t[i]-s[i]:t[e]-s[e]}}function Ra(e){let i=e.chart,t=i.options.animation;i.notifyPlugins("afterRender"),I(t&&t.onComplete,[e],i)}function Fp(e){let i=e.chart,t=i.options.animation;I(t&&t.onProgress,[e],i)}function _l(e){return Ri()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}var $i={},Ia=e=>{let i=_l(e);return Object.values($i).filter(t=>t.canvas===i).pop()};function zp(e,i,t){let s=Object.keys(e);for(let n of s){let o=+n;if(o>=i){let r=e[n];delete e[n],(t>0||o>i)&&(e[o+t]=r)}}}function Bp(e,i,t,s){return!t||e.type==="mouseout"?null:s?i:e}var Zi=(()=>{class e{static defaults=j;static instances=$i;static overrides=It;static registry=bt;static version=Rp;static getChart=Ia;static register(...t){bt.add(...t),Fa()}static unregister(...t){bt.remove(...t),Fa()}constructor(t,s){let n=this.config=new hn(s),o=_l(t),r=Ia(o);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");let a=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||op(o)),this.platform.updateConfig(n);let l=this.platform.acquireContext(o,a.aspectRatio),c=l&&l.canvas,h=c&&c.height,d=c&&c.width;if(this.id=Rr(),this.ctx=l,this.canvas=c,this.width=d,this.height=h,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new an,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Yr(f=>this.update(f),a.resizeDelay||0),this._dataChanges=[],$i[this.id]=this,!l||!c){console.error("Failed to create chart: can't acquire context from the given item");return}St.listen(this,"complete",Ra),St.listen(this,"progress",Fp),this._initialize(),this.attached&&this.update()}get aspectRatio(){let{options:{aspectRatio:t,maintainAspectRatio:s},width:n,height:o,_aspectRatio:r}=this;return A(t)?s&&r?r:o?n/o:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return bt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Fs(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return As(this.canvas,this.ctx),this}stop(){return St.stop(this),this}resize(t,s){St.running(this)?this._resizeBeforeDraw={width:t,height:s}:this._resize(t,s)}_resize(t,s){let n=this.options,o=this.canvas,r=n.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(o,t,s,r),l=n.devicePixelRatio||this.platform.getDevicePixelRatio(),c=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,Fs(this,l,!0)&&(this.notifyPlugins("resize",{size:a}),I(n.onResize,[this,a],this),this.attached&&this._doResize(c)&&this.render())}ensureScalesHaveIDs(){let s=this.options.scales||{};E(s,(n,o)=>{n.id=o})}buildOrUpdateScales(){let t=this.options,s=t.scales,n=this.scales,o=Object.keys(n).reduce((a,l)=>(a[l]=!1,a),{}),r=[];s&&(r=r.concat(Object.keys(s).map(a=>{let l=s[a],c=cn(a,l),h=c==="r",d=c==="x";return{options:l,dposition:h?"chartArea":d?"bottom":"left",dtype:h?"radialLinear":d?"category":"linear"}}))),E(r,a=>{let l=a.options,c=l.id,h=cn(c,l),d=C(l.type,a.dtype);(l.position===void 0||La(l.position,h)!==La(a.dposition))&&(l.position=a.dposition),o[c]=!0;let f=null;if(c in n&&n[c].type===d)f=n[c];else{let u=bt.getScale(d);f=new u({id:c,type:d,ctx:this.ctx,chart:this}),n[f.id]=f}f.init(l,t)}),E(o,(a,l)=>{a||delete n[l]}),E(n,a=>{Z.configure(this,a,a.options),Z.addBox(this,a)})}_updateMetasets(){let t=this._metasets,s=this.data.datasets.length,n=t.length;if(t.sort((o,r)=>o.index-r.index),n>s){for(let o=s;o<n;++o)this._destroyDatasetMeta(o);t.splice(s,n-s)}this._sortedMetasets=t.slice(0).sort(Ea("order","index"))}_removeUnreferencedMetasets(){let{_metasets:t,data:{datasets:s}}=this;t.length>s.length&&delete this._stacks,t.forEach((n,o)=>{s.filter(r=>r===n._dataset).length===0&&this._destroyDatasetMeta(o)})}buildOrUpdateControllers(){let t=[],s=this.data.datasets,n,o;for(this._removeUnreferencedMetasets(),n=0,o=s.length;n<o;n++){let r=s[n],a=this.getDatasetMeta(n),l=r.type||this.config.type;if(a.type&&a.type!==l&&(this._destroyDatasetMeta(n),a=this.getDatasetMeta(n)),a.type=l,a.indexAxis=r.indexAxis||ln(l,this.options),a.order=r.order||0,a.index=n,a.label=""+r.label,a.visible=this.isDatasetVisible(n),a.controller)a.controller.updateIndex(n),a.controller.linkScales();else{let c=bt.getController(l),{datasetElementType:h,dataElementType:d}=j.datasets[l];Object.assign(c,{dataElementType:bt.getElement(d),datasetElementType:h&&bt.getElement(h)}),a.controller=new c(this,n),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){E(this.data.datasets,(t,s)=>{this.getDatasetMeta(s).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){let s=this.config;s.update();let n=this._options=s.createResolver(s.chartOptionScopes(),this.getContext()),o=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;let r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let h=0,d=this.data.datasets.length;h<d;h++){let{controller:f}=this.getDatasetMeta(h),u=!o&&r.indexOf(f)===-1;f.buildOrUpdateElements(u),a=Math.max(+f.getMaxOverflow(),a)}a=this._minPadding=n.layout.autoPadding?a:0,this._updateLayout(a),o||E(r,h=>{h.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Ea("z","_idx"));let{_active:l,_lastEvent:c}=this;c?this._eventHandler(c,!0):l.length&&this._updateHoverStyles(l,l,!0),this.render()}_updateScales(){E(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){let t=this.options,s=new Set(Object.keys(this._listeners)),n=new Set(t.events);(!ms(s,n)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){let{_hiddenIndices:t}=this,s=this._getUniformDataChanges()||[];for(let{method:n,start:o,count:r}of s){let a=n==="_removeElements"?-r:r;zp(t,o,a)}}_getUniformDataChanges(){let t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];let s=this.data.datasets.length,n=r=>new Set(t.filter(a=>a[0]===r).map((a,l)=>l+","+a.splice(1).join(","))),o=n(0);for(let r=1;r<s;r++)if(!ms(o,n(r)))return;return Array.from(o).map(r=>r.split(",")).map(r=>({method:r[1],start:+r[2],count:+r[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);let s=this.chartArea,n=s.width<=0||s.height<=0;this._layers=[],E(this.boxes,o=>{n&&o.position==="chartArea"||(o.configure&&o.configure(),this._layers.push(...o._layers()))},this),this._layers.forEach((o,r)=>{o._idx=r}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let s=0,n=this.data.datasets.length;s<n;++s)this.getDatasetMeta(s).controller.configure();for(let s=0,n=this.data.datasets.length;s<n;++s)this._updateDataset(s,yt(t)?t({datasetIndex:s}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,s){let n=this.getDatasetMeta(t),o={meta:n,index:t,mode:s,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",o)!==!1&&(n.controller._update(s),o.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",o))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(St.has(this)?this.attached&&!St.running(this)&&St.start(this):(this.draw(),Ra({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){let{width:n,height:o}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,o)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;let s=this._layers;for(t=0;t<s.length&&s[t].z<=0;++t)s[t].draw(this.chartArea);for(this._drawDatasets();t<s.length;++t)s[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){let s=this._sortedMetasets,n=[],o,r;for(o=0,r=s.length;o<r;++o){let a=s[o];(!t||a.visible)&&n.push(a)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;let t=this.getSortedVisibleDatasetMetas();for(let s=t.length-1;s>=0;--s)this._drawDataset(t[s]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){let s=this.ctx,n={meta:t,index:t.index,cancelable:!0},o=Hs(this,t);this.notifyPlugins("beforeDatasetDraw",n)!==!1&&(o&&Ve(s,o),t.controller.draw(),o&&He(s),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(t){return ut(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,s,n,o){let r=Nu.modes[s];return typeof r=="function"?r(this,t,n,o):[]}getDatasetMeta(t){let s=this.data.datasets[t],n=this._metasets,o=n.filter(r=>r&&r._dataset===s).pop();return o||(o={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:s&&s.order||0,index:t,_dataset:s,_parsed:[],_sorted:!1},n.push(o)),o}getContext(){return this.$context||(this.$context=wt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){let s=this.data.datasets[t];if(!s)return!1;let n=this.getDatasetMeta(t);return typeof n.hidden=="boolean"?!n.hidden:!s.hidden}setDatasetVisibility(t,s){let n=this.getDatasetMeta(t);n.hidden=!s}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,s,n){let o=n?"show":"hide",r=this.getDatasetMeta(t),a=r.controller._resolveAnimations(void 0,o);ge(s)?(r.data[s].hidden=!n,this.update()):(this.setDatasetVisibility(t,n),a.update(r,{visible:n}),this.update(l=>l.datasetIndex===t?o:void 0))}hide(t,s){this._updateVisibility(t,s,!1)}show(t,s){this._updateVisibility(t,s,!0)}_destroyDatasetMeta(t){let s=this._metasets[t];s&&s.controller&&s.controller._destroy(),delete this._metasets[t]}_stop(){let t,s;for(this.stop(),St.remove(this),t=0,s=this.data.datasets.length;t<s;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");let{canvas:t,ctx:s}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),As(t,s),this.platform.releaseContext(s),this.canvas=null,this.ctx=null),delete $i[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){let t=this._listeners,s=this.platform,n=(r,a)=>{s.addEventListener(this,r,a),t[r]=a},o=(r,a,l)=>{r.offsetX=a,r.offsetY=l,this._eventHandler(r)};E(this.options.events,r=>n(r,o))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});let t=this._responsiveListeners,s=this.platform,n=(c,h)=>{s.addEventListener(this,c,h),t[c]=h},o=(c,h)=>{t[c]&&(s.removeEventListener(this,c,h),delete t[c])},r=(c,h)=>{this.canvas&&this.resize(c,h)},a,l=()=>{o("attach",l),this.attached=!0,this.resize(),n("resize",r),n("detach",a)};a=()=>{this.attached=!1,o("resize",r),this._stop(),this._resize(0,0),n("attach",l)},s.isAttached(this.canvas)?l():a()}unbindEvents(){E(this._listeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._listeners={},E(this._responsiveListeners,(t,s)=>{this.platform.removeEventListener(this,s,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,s,n){let o=n?"set":"remove",r,a,l,c;for(s==="dataset"&&(r=this.getDatasetMeta(t[0].datasetIndex),r.controller["_"+o+"DatasetHoverStyle"]()),l=0,c=t.length;l<c;++l){a=t[l];let h=a&&this.getDatasetMeta(a.datasetIndex).controller;h&&h[o+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){let s=this._active||[],n=t.map(({datasetIndex:r,index:a})=>{let l=this.getDatasetMeta(r);if(!l)throw new Error("No dataset found at index "+r);return{datasetIndex:r,element:l.data[a],index:a}});!je(n,s)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,s))}notifyPlugins(t,s,n){return this._plugins.notify(this,t,s,n)}isPluginEnabled(t){return this._plugins._cache.filter(s=>s.plugin.id===t).length===1}_updateHoverStyles(t,s,n){let o=this.options.hover,r=(c,h)=>c.filter(d=>!h.some(f=>d.datasetIndex===f.datasetIndex&&d.index===f.index)),a=r(s,t),l=n?t:r(t,s);a.length&&this.updateHoverStyle(a,o.mode,!1),l.length&&o.mode&&this.updateHoverStyle(l,o.mode,!0)}_eventHandler(t,s){let n={event:t,replay:s,cancelable:!0,inChartArea:this.isPointInArea(t)},o=a=>(a.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",n,o)===!1)return;let r=this._handleEvent(t,s,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,o),(r||n.changed)&&this.render(),this}_handleEvent(t,s,n){let{_active:o=[],options:r}=this,a=s,l=this._getActiveElements(t,o,n,a),c=zr(t),h=Bp(t,this._lastEvent,n,c);n&&(this._lastEvent=null,I(r.onHover,[t,l,this],this),c&&I(r.onClick,[t,l,this],this));let d=!je(l,o);return(d||s)&&(this._active=l,this._updateHoverStyles(l,o,s)),this._lastEvent=h,d}_getActiveElements(t,s,n,o){if(t.type==="mouseout")return[];if(!n)return s;let r=this.options.hover;return this.getElementsAtEventForMode(t,r.mode,r,o)}}return e})();function Fa(){return E(Zi.instances,e=>e._plugins.invalidate())}function jp(e,i,t){let{startAngle:s,x:n,y:o,outerRadius:r,innerRadius:a,options:l}=i,{borderWidth:c,borderJoinStyle:h}=l,d=Math.min(c/r,X(s-t));if(e.beginPath(),e.arc(n,o,r-c/2,s+d/2,t-d/2),a>0){let f=Math.min(c/a,X(s-t));e.arc(n,o,a+c/2,t-f/2,s+f/2,!0)}else{let f=Math.min(c/2,r*X(s-t));if(h==="round")e.arc(n,o,f,t-L/2,s+L/2,!0);else if(h==="bevel"){let u=2*f*f,p=-u*Math.cos(t+L/2)+n,g=-u*Math.sin(t+L/2)+o,m=u*Math.cos(s+L/2)+n,b=u*Math.sin(s+L/2)+o;e.lineTo(p,g),e.lineTo(m,b)}}e.closePath(),e.moveTo(0,0),e.rect(0,0,e.canvas.width,e.canvas.height),e.clip("evenodd")}function Np(e,i,t){let{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=i,c=n/a;e.beginPath(),e.arc(o,r,a,s-c,t+c),l>n?(c=n/l,e.arc(o,r,l,t+c,s-c,!0)):e.arc(o,r,n,t+H,s-H),e.closePath(),e.clip()}function Vp(e){return Li(e,["outerStart","outerEnd","innerStart","innerEnd"])}function Hp(e,i,t,s){let n=Vp(e.options.borderRadius),o=(t-i)/2,r=Math.min(o,s*i/2),a=l=>{let c=(t-Math.min(o,l))*s/2;return Y(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:Y(n.innerStart,0,r),innerEnd:Y(n.innerEnd,0,r)}}function ve(e,i,t,s){return{x:t+e*Math.cos(i),y:s+e*Math.sin(i)}}function Gi(e,i,t,s,n,o){let{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=i,d=Math.max(i.outerRadius+s+t-c,0),f=h>0?h+s+t+c:0,u=0,p=n-l;if(s){let R=h>0?h-s:0,N=d>0?d-s:0,W=(R+N)/2,rt=W!==0?p*W/(W+s):p;u=(p-rt)/2}let g=Math.max(.001,p*d-t/L)/d,m=(p-g)/2,b=l+m+u,x=n-m-u,{outerStart:y,outerEnd:M,innerStart:_,innerEnd:v}=Hp(i,f,d,x-b),S=d-y,w=d-M,k=b+y/S,O=x-M/w,P=f+_,T=f+v,U=b+_/P,it=x-v/T;if(e.beginPath(),o){let R=(k+O)/2;if(e.arc(r,a,d,k,R),e.arc(r,a,d,R,O),M>0){let q=ve(w,O,r,a);e.arc(q.x,q.y,M,O,x+H)}let N=ve(T,x,r,a);if(e.lineTo(N.x,N.y),v>0){let q=ve(T,it,r,a);e.arc(q.x,q.y,v,x+H,it+Math.PI)}let W=(x-v/f+(b+_/f))/2;if(e.arc(r,a,f,x-v/f,W,!0),e.arc(r,a,f,W,b+_/f,!0),_>0){let q=ve(P,U,r,a);e.arc(q.x,q.y,_,U+Math.PI,b-H)}let rt=ve(S,b,r,a);if(e.lineTo(rt.x,rt.y),y>0){let q=ve(S,k,r,a);e.arc(q.x,q.y,y,b-H,k)}}else{e.moveTo(r,a);let R=Math.cos(k)*d+r,N=Math.sin(k)*d+a;e.lineTo(R,N);let W=Math.cos(O)*d+r,rt=Math.sin(O)*d+a;e.lineTo(W,rt)}e.closePath()}function Wp(e,i,t,s,n){let{fullCircles:o,startAngle:r,circumference:a}=i,l=i.endAngle;if(o){Gi(e,i,t,s,l,n);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%z||z))}return Gi(e,i,t,s,l,n),e.fill(),l}function $p(e,i,t,s,n){let{fullCircles:o,startAngle:r,circumference:a,options:l}=i,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:f,borderRadius:u}=l,p=l.borderAlign==="inner";if(!c)return;e.setLineDash(d||[]),e.lineDashOffset=f,p?(e.lineWidth=c*2,e.lineJoin=h||"round"):(e.lineWidth=c,e.lineJoin=h||"bevel");let g=i.endAngle;if(o){Gi(e,i,t,s,g,n);for(let m=0;m<o;++m)e.stroke();isNaN(a)||(g=r+(a%z||z))}p&&Np(e,i,g),l.selfJoin&&g-r>=L&&u===0&&h!=="miter"&&jp(e,i,g),o||(Gi(e,i,t,s,g,n),e.stroke())}var dn=class extends ct{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:i=>i!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(i){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,i&&Object.assign(this,i)}inRange(i,t,s){let n=this.getProps(["x","y"],s),{angle:o,distance:r}=ys(n,{x:i,y:t}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),f=(this.options.spacing+this.options.borderWidth)/2,u=C(d,l-a),p=be(o,a,l)&&a!==l,g=u>=z||p,m=gt(r,c+f,h+f);return g&&m}getCenterPoint(i){let{x:t,y:s,startAngle:n,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],i),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(r+a+c+l)/2;return{x:t+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(i){return this.getCenterPoint(i)}draw(i){let{options:t,circumference:s}=this,n=(t.offset||0)/4,o=(t.spacing||0)/2,r=t.circular;if(this.pixelMargin=t.borderAlign==="inner"?.33:0,this.fullCircles=s>z?Math.floor(s/z):0,s===0||this.innerRadius<0||this.outerRadius<0)return;i.save();let a=(this.startAngle+this.endAngle)/2;i.translate(Math.cos(a)*n,Math.sin(a)*n);let l=1-Math.sin(Math.min(L,s||0)),c=n*l;i.fillStyle=t.backgroundColor,i.strokeStyle=t.borderColor,Wp(i,this,c,o,r),$p(i,this,c,o,r),i.restore()}};function yl(e,i,t=i){e.lineCap=C(t.borderCapStyle,i.borderCapStyle),e.setLineDash(C(t.borderDash,i.borderDash)),e.lineDashOffset=C(t.borderDashOffset,i.borderDashOffset),e.lineJoin=C(t.borderJoinStyle,i.borderJoinStyle),e.lineWidth=C(t.borderWidth,i.borderWidth),e.strokeStyle=C(t.borderColor,i.borderColor)}function Yp(e,i,t){e.lineTo(t.x,t.y)}function Up(e){return e.stepped?Kr:e.tension||e.cubicInterpolationMode==="monotone"?qr:Yp}function vl(e,i,t={}){let s=e.length,{start:n=0,end:o=s-1}=t,{start:r,end:a}=i,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:i.loop,ilen:c<l&&!h?s+c-l:c-l}}function Xp(e,i,t,s){let{points:n,options:o}=i,{count:r,start:a,loop:l,ilen:c}=vl(n,t,s),h=Up(o),{move:d=!0,reverse:f}=s||{},u,p,g;for(u=0;u<=c;++u)p=n[(a+(f?c-u:u))%r],!p.skip&&(d?(e.moveTo(p.x,p.y),d=!1):h(e,g,p,f,o.stepped),g=p);return l&&(p=n[(a+(f?c:0))%r],h(e,g,p,f,o.stepped)),!!l}function Gp(e,i,t,s){let n=i.points,{count:o,start:r,ilen:a}=vl(n,t,s),{move:l=!0,reverse:c}=s||{},h=0,d=0,f,u,p,g,m,b,x=M=>(r+(c?a-M:M))%o,y=()=>{g!==m&&(e.lineTo(h,m),e.lineTo(h,g),e.lineTo(h,b))};for(l&&(u=n[x(0)],e.moveTo(u.x,u.y)),f=0;f<=a;++f){if(u=n[x(f)],u.skip)continue;let M=u.x,_=u.y,v=M|0;v===p?(_<g?g=_:_>m&&(m=_),h=(d*h+M)/++d):(y(),e.lineTo(M,_),p=v,d=0,g=m=_),b=_}y()}function fn(e){let i=e.options,t=i.borderDash&&i.borderDash.length;return!e._decimated&&!e._loop&&!i.tension&&i.cubicInterpolationMode!=="monotone"&&!i.stepped&&!t?Gp:Xp}function Kp(e){return e.stepped?oa:e.tension||e.cubicInterpolationMode==="monotone"?ra:Rt}function qp(e,i,t,s){let n=i._path;n||(n=i._path=new Path2D,i.path(n,t,s)&&n.closePath()),yl(e,i.options),e.stroke(n)}function Zp(e,i,t,s){let{segments:n,options:o}=i,r=fn(i);for(let a of n)yl(e,o,a.style),e.beginPath(),r(e,i,a,{start:t,end:t+s-1})&&e.closePath(),e.stroke()}var Jp=typeof Path2D=="function";function Qp(e,i,t,s){Jp&&!i.options.segment?qp(e,i,t,s):Zp(e,i,t,s)}var Ji=(()=>{class e extends ct{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,s){let n=this.options;if((n.tension||n.cubicInterpolationMode==="monotone")&&!n.stepped&&!this._pointsUpdated){let o=n.spanGaps?this._loop:this._fullLoop;ia(this._points,n,t,o,s),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=la(this,this.options.segment))}first(){let t=this.segments,s=this.points;return t.length&&s[t[0].start]}last(){let t=this.segments,s=this.points,n=t.length;return n&&s[t[n-1].end]}interpolate(t,s){let n=this.options,o=t[s],r=this.points,a=Vs(this,{property:s,start:o,end:o});if(!a.length)return;let l=[],c=Kp(n),h,d;for(h=0,d=a.length;h<d;++h){let{start:f,end:u}=a[h],p=r[f],g=r[u];if(p===g){l.push(p);continue}let m=Math.abs((o-p[s])/(g[s]-p[s])),b=c(p,g,m,n.stepped);b[s]=t[s],l.push(b)}return l.length===1?l[0]:l}pathSegment(t,s,n){return fn(this)(t,this,s,n)}path(t,s,n){let o=this.segments,r=fn(this),a=this._loop;s=s||0,n=n||this.points.length-s;for(let l of o)a&=r(t,this,l,{start:s,end:s+n-1});return!!a}draw(t,s,n,o){let r=this.options||{};(this.points||[]).length&&r.borderWidth&&(t.save(),Qp(t,this,n,o),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}return e})();function za(e,i,t,s){let n=e.options,{[t]:o}=e.getProps([t],s);return Math.abs(i-o)<n.radius+n.hitRadius}var tg=(()=>{class e extends ct{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,s,n){let o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(t-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(t,s){return za(this,t,"x",s)}inYRange(t,s){return za(this,t,"y",s)}getCenterPoint(t){let{x:s,y:n}=this.getProps(["x","y"],t);return{x:s,y:n}}size(t){t=t||this.options||{};let s=t.radius||0;s=Math.max(s,s&&t.hoverRadius||0);let n=s&&t.borderWidth||0;return(s+n)*2}draw(t,s){let n=this.options;this.skip||n.radius<.1||!ut(this,s,this.size(n)/2)||(t.strokeStyle=n.borderColor,t.lineWidth=n.borderWidth,t.fillStyle=n.backgroundColor,Ti(t,n,this.x,this.y))}getRange(){let t=this.options||{};return t.radius+t.hitRadius}}return e})();function Ml(e,i){let{x:t,y:s,base:n,width:o,height:r}=e.getProps(["x","y","base","width","height"],i),a,l,c,h,d;return e.horizontal?(d=r/2,a=Math.min(t,n),l=Math.max(t,n),c=s-d,h=s+d):(d=o/2,a=t-d,l=t+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function Nt(e,i,t,s){return e?0:Y(i,t,s)}function eg(e,i,t){let s=e.options.borderWidth,n=e.borderSkipped,o=Ts(s);return{t:Nt(n.top,o.top,0,t),r:Nt(n.right,o.right,0,i),b:Nt(n.bottom,o.bottom,0,t),l:Nt(n.left,o.left,0,i)}}function ig(e,i,t){let{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),n=e.options.borderRadius,o=Bt(n),r=Math.min(i,t),a=e.borderSkipped,l=s||D(n);return{topLeft:Nt(!l||a.top||a.left,o.topLeft,0,r),topRight:Nt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:Nt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:Nt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function sg(e){let i=Ml(e),t=i.right-i.left,s=i.bottom-i.top,n=eg(e,t/2,s/2),o=ig(e,t/2,s/2);return{outer:{x:i.left,y:i.top,w:t,h:s,radius:o},inner:{x:i.left+n.l,y:i.top+n.t,w:t-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function qs(e,i,t,s){let n=i===null,o=t===null,a=e&&!(n&&o)&&Ml(e,s);return a&&(n||gt(i,a.left,a.right))&&(o||gt(t,a.top,a.bottom))}function ng(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function og(e,i){e.rect(i.x,i.y,i.w,i.h)}function Zs(e,i,t={}){let s=e.x!==t.x?-i:0,n=e.y!==t.y?-i:0,o=(e.x+e.w!==t.x+t.w?i:0)-s,r=(e.y+e.h!==t.y+t.h?i:0)-n;return{x:e.x+s,y:e.y+n,w:e.w+o,h:e.h+r,radius:e.radius}}var un=class extends ct{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(i){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,i&&Object.assign(this,i)}draw(i){let{inflateAmount:t,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=sg(this),a=ng(r.radius)?_e:og;i.save(),(r.w!==o.w||r.h!==o.h)&&(i.beginPath(),a(i,Zs(r,t,o)),i.clip(),a(i,Zs(o,-t,r)),i.fillStyle=s,i.fill("evenodd")),i.beginPath(),a(i,Zs(o,t)),i.fillStyle=n,i.fill(),i.restore()}inRange(i,t,s){return qs(this,i,t,s)}inXRange(i,t){return qs(this,i,null,t)}inYRange(i,t){return qs(this,null,i,t)}getCenterPoint(i){let{x:t,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],i);return{x:o?(t+n)/2:t,y:o?s:(s+n)/2}}getRange(i){return i==="x"?this.width/2:this.height/2}},rg=Object.freeze({__proto__:null,ArcElement:dn,BarElement:un,LineElement:Ji,PointElement:tg}),pn=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Ba=pn.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function wl(e){return pn[e%pn.length]}function Sl(e){return Ba[e%Ba.length]}function ag(e,i){return e.borderColor=wl(i),e.backgroundColor=Sl(i),++i}function lg(e,i){return e.backgroundColor=e.data.map(()=>wl(i++)),i}function cg(e,i){return e.backgroundColor=e.data.map(()=>Sl(i++)),i}function hg(e){let i=0;return(t,s)=>{let n=e.getDatasetMeta(s).controller;n instanceof Mn?i=lg(t,i):n instanceof dl?i=cg(t,i):n&&(i=ag(t,i))}}function ja(e){let i;for(i in e)if(e[i].borderColor||e[i].backgroundColor)return!0;return!1}function dg(e){return e&&(e.borderColor||e.backgroundColor)}function fg(){return j.borderColor!=="rgba(0,0,0,0.1)"||j.backgroundColor!=="rgba(0,0,0,0.1)"}var ug={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,i,t){if(!t.enabled)return;let{data:{datasets:s},options:n}=e.config,{elements:o}=n,r=ja(s)||dg(n)||o&&ja(o)||fg();if(!t.forceOverride&&r)return;let a=hg(e);s.forEach(a)}};function pg(e,i,t,s,n){let o=n.samples||s;if(o>=t)return e.slice(i,i+t);let r=[],a=(t-2)/(o-2),l=0,c=i+t-1,h=i,d,f,u,p,g;for(r[l++]=e[h],d=0;d<o-2;d++){let m=0,b=0,x,y=Math.floor((d+1)*a)+1+i,M=Math.min(Math.floor((d+2)*a)+1,t)+i,_=M-y;for(x=y;x<M;x++)m+=e[x].x,b+=e[x].y;m/=_,b/=_;let v=Math.floor(d*a)+1+i,S=Math.min(Math.floor((d+1)*a)+1,t)+i,{x:w,y:k}=e[h];for(u=p=-1,x=v;x<S;x++)p=.5*Math.abs((w-m)*(e[x].y-k)-(w-e[x].x)*(b-k)),p>u&&(u=p,f=e[x],g=x);r[l++]=f,h=g}return r[l++]=e[c],r}function gg(e,i,t,s){let n=0,o=0,r,a,l,c,h,d,f,u,p,g,m=[],b=i+t-1,x=e[i].x,M=e[b].x-x;for(r=i;r<i+t;++r){a=e[r],l=(a.x-x)/M*s,c=a.y;let _=l|0;if(_===h)c<p?(p=c,d=r):c>g&&(g=c,f=r),n=(o*n+a.x)/++o;else{let v=r-1;if(!A(d)&&!A(f)){let S=Math.min(d,f),w=Math.max(d,f);S!==u&&S!==v&&m.push(Ct(ht({},e[S]),{x:n})),w!==u&&w!==v&&m.push(Ct(ht({},e[w]),{x:n}))}r>0&&v!==u&&m.push(e[v]),m.push(a),h=_,o=0,p=g=c,d=f=u=r}}return m}function kl(e){if(e._decimated){let i=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:i})}}function Na(e){e.data.datasets.forEach(i=>{kl(i)})}function mg(e,i){let t=i.length,s=0,n,{iScale:o}=e,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=Y(ft(i,o.axis,r).lo,0,t-1)),c?n=Y(ft(i,o.axis,a).hi+1,s,t)-s:n=t-s,{start:s,count:n}}var bg={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,i,t)=>{if(!t.enabled){Na(e);return}let s=e.width;e.data.datasets.forEach((n,o)=>{let{_data:r,indexAxis:a}=n,l=e.getDatasetMeta(o),c=r||n.data;if(ye([a,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;let h=e.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||e.options.parsing)return;let{start:d,count:f}=mg(l,c),u=t.threshold||4*s;if(f<=u){kl(n);return}A(r)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let p;switch(t.algorithm){case"lttb":p=pg(c,d,f,s,t);break;case"min-max":p=gg(c,d,f,s);break;default:throw new Error(`Unsupported decimation algorithm '${t.algorithm}'`)}n._decimated=p})},destroy(e){Na(e)}};function xg(e,i,t){let s=e.segments,n=e.points,o=i.points,r=[];for(let a of s){let{start:l,end:c}=a;c=Qi(l,c,n);let h=gn(t,n[l],n[c],a.loop);if(!i.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}let d=Vs(i,h);for(let f of d){let u=gn(t,o[f.start],o[f.end],f.loop),p=Ns(a,n,u);for(let g of p)r.push({source:g,target:f,start:{[t]:Va(h,u,"start",Math.max)},end:{[t]:Va(h,u,"end",Math.min)}})}}return r}function gn(e,i,t,s){if(s)return;let n=i[e],o=t[e];return e==="angle"&&(n=X(n),o=X(o)),{property:e,start:n,end:o}}function _g(e,i){let{x:t=null,y:s=null}=e||{},n=i.points,o=[];return i.segments.forEach(({start:r,end:a})=>{a=Qi(r,a,n);let l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):t!==null&&(o.push({x:t,y:l.y}),o.push({x:t,y:c.y}))}),o}function Qi(e,i,t){for(;i>e;i--){let s=t[i];if(!isNaN(s.x)&&!isNaN(s.y))break}return i}function Va(e,i,t,s){return e&&i?s(e[t],i[t]):e?e[t]:i?i[t]:0}function Cl(e,i){let t=[],s=!1;return F(e)?(s=!0,t=e):t=_g(e,i),t.length?new Ji({points:t,options:{tension:0},_loop:s,_fullLoop:s}):null}function Ha(e){return e&&e.fill!==!1}function yg(e,i,t){let n=e[i].fill,o=[i],r;if(!t)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!V(n))return n;if(r=e[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function vg(e,i,t){let s=kg(e);if(D(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return V(n)&&Math.floor(n)===n?Mg(s[0],i,n,t):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Mg(e,i,t,s){return(e==="-"||e==="+")&&(t=i+t),t===i||t<0||t>=s?!1:t}function wg(e,i){let t=null;return e==="start"?t=i.bottom:e==="end"?t=i.top:D(e)?t=i.getPixelForValue(e.value):i.getBasePixel&&(t=i.getBasePixel()),t}function Sg(e,i,t){let s;return e==="start"?s=t:e==="end"?s=i.options.reverse?i.min:i.max:D(e)?s=e.value:s=i.getBaseValue(),s}function kg(e){let i=e.options,t=i.fill,s=C(t&&t.target,t);return s===void 0&&(s=!!i.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Cg(e){let{scale:i,index:t,line:s}=e,n=[],o=s.segments,r=s.points,a=Pg(i,t);a.push(Cl({x:null,y:i.bottom},s));for(let l=0;l<o.length;l++){let c=o[l];for(let h=c.start;h<=c.end;h++)Og(n,r[h],a)}return new Ji({points:n,options:{}})}function Pg(e,i){let t=[],s=e.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){let o=s[n];if(o.index===i)break;o.hidden||t.unshift(o.dataset)}return t}function Og(e,i,t){let s=[];for(let n=0;n<t.length;n++){let o=t[n],{first:r,last:a,point:l}=Ag(o,i,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(e.push(l),!a)break}}e.push(...s)}function Ag(e,i,t){let s=e.interpolate(i,t);if(!s)return{};let n=s[t],o=e.segments,r=e.points,a=!1,l=!1;for(let c=0;c<o.length;c++){let h=o[c],d=r[h.start][t],f=r[h.end][t];if(gt(n,d,f)){a=n===d,l=n===f;break}}return{first:a,last:l,point:s}}var Ki=class{constructor(i){this.x=i.x,this.y=i.y,this.radius=i.radius}pathSegment(i,t,s){let{x:n,y:o,radius:r}=this;return t=t||{start:0,end:z},i.arc(n,o,r,t.end,t.start,!0),!s.bounds}interpolate(i){let{x:t,y:s,radius:n}=this,o=i.angle;return{x:t+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}};function Dg(e){let{chart:i,fill:t,line:s}=e;if(V(t))return Tg(i,t);if(t==="stack")return Cg(e);if(t==="shape")return!0;let n=Lg(e);return n instanceof Ki?n:Cl(n,s)}function Tg(e,i){let t=e.getDatasetMeta(i);return t&&e.isDatasetVisible(i)?t.dataset:null}function Lg(e){return(e.scale||{}).getPointPositionForValue?Rg(e):Eg(e)}function Eg(e){let{scale:i={},fill:t}=e,s=wg(t,i);if(V(s)){let n=i.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function Rg(e){let{scale:i,fill:t}=e,s=i.options,n=i.getLabels().length,o=s.reverse?i.max:i.min,r=Sg(t,i,o),a=[];if(s.grid.circular){let l=i.getPointPositionForValue(0,o);return new Ki({x:l.x,y:l.y,radius:i.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(i.getPointPositionForValue(l,r));return a}function Js(e,i,t){let s=Dg(i),{chart:n,index:o,line:r,scale:a,axis:l}=i,c=r.options,h=c.fill,d=c.backgroundColor,{above:f=d,below:u=d}=h||{},p=n.getDatasetMeta(o),g=Hs(n,p);s&&r.points.length&&(Ve(e,t),Ig(e,{line:r,target:s,above:f,below:u,area:t,scale:a,axis:l,clip:g}),He(e))}function Ig(e,i){let{line:t,target:s,above:n,below:o,area:r,scale:a,clip:l}=i,c=t._loop?"angle":i.axis;e.save();let h=o;o!==n&&(c==="x"?(Wa(e,s,r.top),Qs(e,{line:t,target:s,color:n,scale:a,property:c,clip:l}),e.restore(),e.save(),Wa(e,s,r.bottom)):c==="y"&&($a(e,s,r.left),Qs(e,{line:t,target:s,color:o,scale:a,property:c,clip:l}),e.restore(),e.save(),$a(e,s,r.right),h=n)),Qs(e,{line:t,target:s,color:h,scale:a,property:c,clip:l}),e.restore()}function Wa(e,i,t){let{segments:s,points:n}=i,o=!0,r=!1;e.beginPath();for(let a of s){let{start:l,end:c}=a,h=n[l],d=n[Qi(l,c,n)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(h.x,t),e.lineTo(h.x,h.y)),r=!!i.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(d.x,t)}e.lineTo(i.first().x,t),e.closePath(),e.clip()}function $a(e,i,t){let{segments:s,points:n}=i,o=!0,r=!1;e.beginPath();for(let a of s){let{start:l,end:c}=a,h=n[l],d=n[Qi(l,c,n)];o?(e.moveTo(h.x,h.y),o=!1):(e.lineTo(t,h.y),e.lineTo(h.x,h.y)),r=!!i.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(t,d.y)}e.lineTo(t,i.first().y),e.closePath(),e.clip()}function Qs(e,i){let{line:t,target:s,property:n,color:o,scale:r,clip:a}=i,l=xg(t,s,n);for(let{source:c,target:h,start:d,end:f}of l){let{style:{backgroundColor:u=o}={}}=c,p=s!==!0;e.save(),e.fillStyle=u,Fg(e,r,a,p&&gn(n,d,f)),e.beginPath();let g=!!t.pathSegment(e,c),m;if(p){g?e.closePath():Ya(e,s,f,n);let b=!!s.pathSegment(e,h,{move:g,reverse:!0});m=g&&b,m||Ya(e,s,d,n)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function Fg(e,i,t,s){let n=i.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=r,c=n.top,h=a,d=n.bottom):(l=n.left,c=r,h=n.right,d=a),e.beginPath(),t&&(l=Math.max(l,t.left),h=Math.min(h,t.right),c=Math.max(c,t.top),d=Math.min(d,t.bottom)),e.rect(l,c,h-l,d-c),e.clip()}}function Ya(e,i,t,s){let n=i.interpolate(t,s);n&&e.lineTo(n.x,n.y)}var zg={id:"filler",afterDatasetsUpdate(e,i,t){let s=(e.data.datasets||[]).length,n=[],o,r,a,l;for(r=0;r<s;++r)o=e.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof Ji&&(l={visible:e.isDatasetVisible(r),index:r,fill:vg(a,r,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=yg(n,r,t.propagate))},beforeDraw(e,i,t){let s=t.drawTime==="beforeDraw",n=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let r=n.length-1;r>=0;--r){let a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Js(e.ctx,a,o))}},beforeDatasetsDraw(e,i,t){if(t.drawTime!=="beforeDatasetsDraw")return;let s=e.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){let o=s[n].$filler;Ha(o)&&Js(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,i,t){let s=i.meta.$filler;!Ha(s)||t.drawTime!=="beforeDatasetDraw"||Js(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}},Ua=(e,i)=>{let{boxHeight:t=i,boxWidth:s=i}=e;return e.usePointStyle&&(t=Math.min(t,i),s=e.pointStyleWidth||Math.min(s,i)),{boxWidth:s,boxHeight:t,itemHeight:Math.max(i,t)}},Bg=(e,i)=>e!==null&&i!==null&&e.datasetIndex===i.datasetIndex&&e.index===i.index,qi=class extends ct{constructor(i){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t,s){this.maxWidth=i,this.maxHeight=t,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){let i=this.options.labels||{},t=I(i.generateLabels,[this.chart],this)||[];i.filter&&(t=t.filter(s=>i.filter(s,this.chart.data))),i.sort&&(t=t.sort((s,n)=>i.sort(s,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){let{options:i,ctx:t}=this;if(!i.display){this.width=this.height=0;return}let s=i.labels,n=$(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Ua(s,o),c,h;t.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,i.maxWidth||this.maxWidth),this.height=Math.min(h,i.maxHeight||this.maxHeight)}_fitRows(i,t,s,n){let{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a,d=i;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((p,g)=>{let m=s+t/2+o.measureText(p.text).width;(g===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(g>0?0:1)]=0,u+=h,f++),l[g]={left:0,top:u,row:f,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(i,t,s,n){let{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-i,d=a,f=0,u=0,p=0,g=0;return this.legendItems.forEach((m,b)=>{let{itemWidth:x,itemHeight:y}=jg(s,t,o,m,n);b>0&&u+y+2*a>h&&(d+=f+a,c.push({width:f,height:u}),p+=f+a,g++,f=u=0),l[b]={left:p,top:u,col:g,width:x,height:y},f=Math.max(f,x),u+=y+a}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;let i=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Gt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=G(s,this.left+n,this.right-this.lineWidths[a]);for(let c of t)a!==c.row&&(a=c.row,l=G(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+i+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=G(s,this.top+i+n,this.bottom-this.columnSizes[a].height);for(let c of t)c.col!==a&&(a=c.col,l=G(s,this.top+i+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){let i=this.ctx;Ve(i,this),this._draw(),He(i)}}_draw(){let{options:i,columnSizes:t,lineWidths:s,ctx:n}=this,{align:o,labels:r}=i,a=j.color,l=Gt(i.rtl,this.left,this.width),c=$(r.font),{padding:h}=r,d=c.size,f=d/2,u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;let{boxWidth:p,boxHeight:g,itemHeight:m}=Ua(r,d),b=function(v,S,w){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;n.save();let k=C(w.lineWidth,1);if(n.fillStyle=C(w.fillStyle,a),n.lineCap=C(w.lineCap,"butt"),n.lineDashOffset=C(w.lineDashOffset,0),n.lineJoin=C(w.lineJoin,"miter"),n.lineWidth=k,n.strokeStyle=C(w.strokeStyle,a),n.setLineDash(C(w.lineDash,[])),r.usePointStyle){let O={radius:g*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:k},P=l.xPlus(v,p/2),T=S+f;Ds(n,O,P,T,r.pointStyleWidth&&p)}else{let O=S+Math.max((d-g)/2,0),P=l.leftForLtr(v,p),T=Bt(w.borderRadius);n.beginPath(),Object.values(T).some(U=>U!==0)?_e(n,{x:P,y:O,w:p,h:g,radius:T}):n.rect(P,O,p,g),n.fill(),k!==0&&n.stroke()}n.restore()},x=function(v,S,w){zt(n,w.text,v,S+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},y=this.isHorizontal(),M=this._computeTitleHeight();y?u={x:G(o,this.left+h,this.right-s[0]),y:this.top+h+M,line:0}:u={x:this.left+h,y:G(o,this.top+M+h,this.bottom-t[0].height),line:0},Bs(this.ctx,i.textDirection);let _=m+h;this.legendItems.forEach((v,S)=>{n.strokeStyle=v.fontColor,n.fillStyle=v.fontColor;let w=n.measureText(v.text).width,k=l.textAlign(v.textAlign||(v.textAlign=r.textAlign)),O=p+f+w,P=u.x,T=u.y;l.setWidth(this.width),y?S>0&&P+O+h>this.right&&(T=u.y+=_,u.line++,P=u.x=G(o,this.left+h,this.right-s[u.line])):S>0&&T+_>this.bottom&&(P=u.x=P+t[u.line].width+h,u.line++,T=u.y=G(o,this.top+M+h,this.bottom-t[u.line].height));let U=l.x(P);if(b(U,T,v),P=Ur(k,P+p+f,y?P+O:this.right,i.rtl),x(l.x(P),T,v),y)u.x+=O+h;else if(typeof v.text!="string"){let it=c.lineHeight;u.y+=Pl(v,it)+h}else u.y+=_}),js(this.ctx,i.textDirection)}drawTitle(){let i=this.options,t=i.title,s=$(t.font),n=K(t.padding);if(!t.display)return;let o=Gt(i.rtl,this.left,this.width),r=this.ctx,a=t.position,l=s.size/2,c=n.top+l,h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=G(i.align,d,this.right-f);else{let p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);h=c+G(i.align,this.top,this.bottom-p-i.labels.padding-this._computeTitleHeight())}let u=G(a,d,d+f);r.textAlign=o.textAlign(Ai(a)),r.textBaseline="middle",r.strokeStyle=t.color,r.fillStyle=t.color,r.font=s.string,zt(r,t.text,u,h,s)}_computeTitleHeight(){let i=this.options.title,t=$(i.font),s=K(i.padding);return i.display?t.lineHeight+s.height:0}_getLegendItemAt(i,t){let s,n,o;if(gt(i,this.left,this.right)&&gt(t,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],gt(i,n.left,n.left+n.width)&&gt(t,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(i){let t=this.options;if(!Hg(i.type,t))return;let s=this._getLegendItemAt(i.x,i.y);if(i.type==="mousemove"||i.type==="mouseout"){let n=this._hoveredItem,o=Bg(n,s);n&&!o&&I(t.onLeave,[i,n,this],this),this._hoveredItem=s,s&&!o&&I(t.onHover,[i,s,this],this)}else s&&I(t.onClick,[i,s,this],this)}};function jg(e,i,t,s,n){let o=Ng(s,e,i,t),r=Vg(n,s,i.lineHeight);return{itemWidth:o,itemHeight:r}}function Ng(e,i,t,s){let n=e.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),i+t.size/2+s.measureText(n).width}function Vg(e,i,t){let s=e;return typeof i.text!="string"&&(s=Pl(i,t)),s}function Pl(e,i){let t=e.text?e.text.length:0;return i*t}function Hg(e,i){return!!((e==="mousemove"||e==="mouseout")&&(i.onHover||i.onLeave)||i.onClick&&(e==="click"||e==="mouseup"))}var Wg={id:"legend",_element:qi,start(e,i,t){let s=e.legend=new qi({ctx:e.ctx,options:t,chart:e});Z.configure(e,s,t),Z.addBox(e,s)},stop(e){Z.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,i,t){let s=e.legend;Z.configure(e,s,t),s.options=t},afterUpdate(e){let i=e.legend;i.buildLabels(),i.adjustHitBoxes()},afterEvent(e,i){i.replay||e.legend.handleEvent(i.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,i,t){let s=i.datasetIndex,n=t.chart;n.isDatasetVisible(s)?(n.hide(s),i.hidden=!0):(n.show(s),i.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){let i=e.data.datasets,{labels:{usePointStyle:t,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{let c=l.controller.getStyle(t?0:void 0),h=K(c.borderWidth);return{text:i[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}},Ze=class extends ct{constructor(i){super(),this.chart=i.chart,this.options=i.options,this.ctx=i.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(i,t){let s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=i,this.height=this.bottom=t;let n=F(s.text)?s.text.length:1;this._padding=K(s.padding);let o=n*$(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){let i=this.options.position;return i==="top"||i==="bottom"}_drawArgs(i){let{top:t,left:s,bottom:n,right:o,options:r}=this,a=r.align,l=0,c,h,d;return this.isHorizontal()?(h=G(a,s,o),d=t+i,c=o-s):(r.position==="left"?(h=s+i,d=G(a,n,t),l=L*-.5):(h=o-i,d=G(a,t,n),l=L*.5),c=n-t),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){let i=this.ctx,t=this.options;if(!t.display)return;let s=$(t.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);zt(i,t.text,0,0,s,{color:t.color,maxWidth:l,rotation:c,textAlign:Ai(t.align),textBaseline:"middle",translation:[r,a]})}};function $g(e,i){let t=new Ze({ctx:e.ctx,options:i,chart:e});Z.configure(e,t,i),Z.addBox(e,t),e.titleBlock=t}var Yg={id:"title",_element:Ze,start(e,i,t){$g(e,t)},stop(e){let i=e.titleBlock;Z.removeBox(e,i),delete e.titleBlock},beforeUpdate(e,i,t){let s=e.titleBlock;Z.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Ni=new WeakMap,Ug={id:"subtitle",start(e,i,t){let s=new Ze({ctx:e.ctx,options:t,chart:e});Z.configure(e,s,t),Z.addBox(e,s),Ni.set(e,s)},stop(e){Z.removeBox(e,Ni.get(e)),Ni.delete(e)},beforeUpdate(e,i,t){let s=Ni.get(e);Z.configure(e,s,t),s.options=t},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}},Ke={average(e){if(!e.length)return!1;let i,t,s=new Set,n=0,o=0;for(i=0,t=e.length;i<t;++i){let a=e[i].element;if(a&&a.hasValue()){let l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(e,i){if(!e.length)return!1;let t=i.x,s=i.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){let l=e[o].element;if(l&&l.hasValue()){let c=l.getCenterPoint(),h=Si(i,c);h<n&&(n=h,a=l)}}if(a){let l=a.tooltipPosition();t=l.x,s=l.y}return{x:t,y:s}}};function mt(e,i){return i&&(F(i)?Array.prototype.push.apply(e,i):e.push(i)),e}function kt(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function Xg(e,i){let{element:t,datasetIndex:s,index:n}=i,o=e.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:e,label:r,parsed:o.getParsed(n),raw:e.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:t}}function Xa(e,i){let t=e.chart.ctx,{body:s,footer:n,title:o}=e,{boxWidth:r,boxHeight:a}=i,l=$(i.bodyFont),c=$(i.titleFont),h=$(i.footerFont),d=o.length,f=n.length,u=s.length,p=K(i.padding),g=p.height,m=0,b=s.reduce((M,_)=>M+_.before.length+_.lines.length+_.after.length,0);if(b+=e.beforeBody.length+e.afterBody.length,d&&(g+=d*c.lineHeight+(d-1)*i.titleSpacing+i.titleMarginBottom),b){let M=i.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=u*M+(b-u)*l.lineHeight+(b-1)*i.bodySpacing}f&&(g+=i.footerMarginTop+f*h.lineHeight+(f-1)*i.footerSpacing);let x=0,y=function(M){m=Math.max(m,t.measureText(M).width+x)};return t.save(),t.font=c.string,E(e.title,y),t.font=l.string,E(e.beforeBody.concat(e.afterBody),y),x=i.displayColors?r+2+i.boxPadding:0,E(s,M=>{E(M.before,y),E(M.lines,y),E(M.after,y)}),x=0,t.font=h.string,E(e.footer,y),t.restore(),m+=p.width,{width:m,height:g}}function Gg(e,i){let{y:t,height:s}=i;return t<s/2?"top":t>e.height-s/2?"bottom":"center"}function Kg(e,i,t,s){let{x:n,width:o}=s,r=t.caretSize+t.caretPadding;if(e==="left"&&n+o+r>i.width||e==="right"&&n-o-r<0)return!0}function qg(e,i,t,s){let{x:n,width:o}=t,{width:r,chartArea:{left:a,right:l}}=e,c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Kg(c,e,i,t)&&(c="center"),c}function Ga(e,i,t){let s=t.yAlign||i.yAlign||Gg(e,t);return{xAlign:t.xAlign||i.xAlign||qg(e,i,t,s),yAlign:s}}function Zg(e,i){let{x:t,width:s}=e;return i==="right"?t-=s:i==="center"&&(t-=s/2),t}function Jg(e,i,t){let{y:s,height:n}=e;return i==="top"?s+=t:i==="bottom"?s-=n+t:s-=n/2,s}function Ka(e,i,t,s){let{caretSize:n,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=t,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Bt(r),p=Zg(i,a),g=Jg(i,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(h,f)+n:a==="right"&&(p+=Math.max(d,u)+n),{x:Y(p,0,s.width-i.width),y:Y(g,0,s.height-i.height)}}function Vi(e,i,t){let s=K(t.padding);return i==="center"?e.x+e.width/2:i==="right"?e.x+e.width-s.right:e.x+s.left}function qa(e){return mt([],kt(e))}function Qg(e,i,t){return wt(e,{tooltip:i,tooltipItems:t,type:"tooltip"})}function Za(e,i){let t=i&&i.dataset&&i.dataset.tooltip&&i.dataset.tooltip.callbacks;return t?e.override(t):e}var Ol={beforeTitle:pt,title(e){if(e.length>0){let i=e[0],t=i.chart.data.labels,s=t?t.length:0;if(this&&this.options&&this.options.mode==="dataset")return i.dataset.label||"";if(i.label)return i.label;if(s>0&&i.dataIndex<s)return t[i.dataIndex]}return""},afterTitle:pt,beforeBody:pt,beforeLabel:pt,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let i=e.dataset.label||"";i&&(i+=": ");let t=e.formattedValue;return A(t)||(i+=t),i},labelColor(e){let t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){let t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:pt,afterBody:pt,beforeFooter:pt,footer:pt,afterFooter:pt};function tt(e,i,t,s){let n=e[i].call(t,s);return typeof n>"u"?Ol[i].call(t,s):n}var Ja=(()=>{class e extends ct{static positioners=Ke;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){let t=this._cachedAnimations;if(t)return t;let s=this.chart,n=this.options.setContext(this.getContext()),o=n.enabled&&s.options.animation&&n.animations,r=new Yi(this.chart,o);return o._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=Qg(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,s){let{callbacks:n}=s,o=tt(n,"beforeTitle",this,t),r=tt(n,"title",this,t),a=tt(n,"afterTitle",this,t),l=[];return l=mt(l,kt(o)),l=mt(l,kt(r)),l=mt(l,kt(a)),l}getBeforeBody(t,s){return qa(tt(s.callbacks,"beforeBody",this,t))}getBody(t,s){let{callbacks:n}=s,o=[];return E(t,r=>{let a={before:[],lines:[],after:[]},l=Za(n,r);mt(a.before,kt(tt(l,"beforeLabel",this,r))),mt(a.lines,tt(l,"label",this,r)),mt(a.after,kt(tt(l,"afterLabel",this,r))),o.push(a)}),o}getAfterBody(t,s){return qa(tt(s.callbacks,"afterBody",this,t))}getFooter(t,s){let{callbacks:n}=s,o=tt(n,"beforeFooter",this,t),r=tt(n,"footer",this,t),a=tt(n,"afterFooter",this,t),l=[];return l=mt(l,kt(o)),l=mt(l,kt(r)),l=mt(l,kt(a)),l}_createItems(t){let s=this._active,n=this.chart.data,o=[],r=[],a=[],l=[],c,h;for(c=0,h=s.length;c<h;++c)l.push(Xg(this.chart,s[c]));return t.filter&&(l=l.filter((d,f,u)=>t.filter(d,f,u,n))),t.itemSort&&(l=l.sort((d,f)=>t.itemSort(d,f,n))),E(l,d=>{let f=Za(t.callbacks,d);o.push(tt(f,"labelColor",this,d)),r.push(tt(f,"labelPointStyle",this,d)),a.push(tt(f,"labelTextColor",this,d))}),this.labelColors=o,this.labelPointStyles=r,this.labelTextColors=a,this.dataPoints=l,l}update(t,s){let n=this.options.setContext(this.getContext()),o=this._active,r,a=[];if(!o.length)this.opacity!==0&&(r={opacity:0});else{let l=Ke[n.position].call(this,o,this._eventPosition);a=this._createItems(n),this.title=this.getTitle(a,n),this.beforeBody=this.getBeforeBody(a,n),this.body=this.getBody(a,n),this.afterBody=this.getAfterBody(a,n),this.footer=this.getFooter(a,n);let c=this._size=Xa(this,n),h=Object.assign({},l,c),d=Ga(this.chart,n,h),f=Ka(n,h,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,r={opacity:1,x:f.x,y:f.y,width:c.width,height:c.height,caretX:l.x,caretY:l.y}}this._tooltipItems=a,this.$context=void 0,r&&this._resolveAnimations().update(this,r),t&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:s})}drawCaret(t,s,n,o){let r=this.getCaretPosition(t,n,o);s.lineTo(r.x1,r.y1),s.lineTo(r.x2,r.y2),s.lineTo(r.x3,r.y3)}getCaretPosition(t,s,n){let{xAlign:o,yAlign:r}=this,{caretSize:a,cornerRadius:l}=n,{topLeft:c,topRight:h,bottomLeft:d,bottomRight:f}=Bt(l),{x:u,y:p}=t,{width:g,height:m}=s,b,x,y,M,_,v;return r==="center"?(_=p+m/2,o==="left"?(b=u,x=b-a,M=_+a,v=_-a):(b=u+g,x=b+a,M=_-a,v=_+a),y=b):(o==="left"?x=u+Math.max(c,d)+a:o==="right"?x=u+g-Math.max(h,f)-a:x=this.caretX,r==="top"?(M=p,_=M-a,b=x-a,y=x+a):(M=p+m,_=M+a,b=x+a,y=x-a),v=M),{x1:b,x2:x,x3:y,y1:M,y2:_,y3:v}}drawTitle(t,s,n){let o=this.title,r=o.length,a,l,c;if(r){let h=Gt(n.rtl,this.x,this.width);for(t.x=Vi(this,n.titleAlign,n),s.textAlign=h.textAlign(n.titleAlign),s.textBaseline="middle",a=$(n.titleFont),l=n.titleSpacing,s.fillStyle=n.titleColor,s.font=a.string,c=0;c<r;++c)s.fillText(o[c],h.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+l,c+1===r&&(t.y+=n.titleMarginBottom-l)}}_drawColorBox(t,s,n,o,r){let a=this.labelColors[n],l=this.labelPointStyles[n],{boxHeight:c,boxWidth:h}=r,d=$(r.bodyFont),f=Vi(this,"left",r),u=o.x(f),p=c<d.lineHeight?(d.lineHeight-c)/2:0,g=s.y+p;if(r.usePointStyle){let m={radius:Math.min(h,c)/2,pointStyle:l.pointStyle,rotation:l.rotation,borderWidth:1},b=o.leftForLtr(u,h)+h/2,x=g+c/2;t.strokeStyle=r.multiKeyBackground,t.fillStyle=r.multiKeyBackground,Ti(t,m,b,x),t.strokeStyle=a.borderColor,t.fillStyle=a.backgroundColor,Ti(t,m,b,x)}else{t.lineWidth=D(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,t.strokeStyle=a.borderColor,t.setLineDash(a.borderDash||[]),t.lineDashOffset=a.borderDashOffset||0;let m=o.leftForLtr(u,h),b=o.leftForLtr(o.xPlus(u,1),h-2),x=Bt(a.borderRadius);Object.values(x).some(y=>y!==0)?(t.beginPath(),t.fillStyle=r.multiKeyBackground,_e(t,{x:m,y:g,w:h,h:c,radius:x}),t.fill(),t.stroke(),t.fillStyle=a.backgroundColor,t.beginPath(),_e(t,{x:b,y:g+1,w:h-2,h:c-2,radius:x}),t.fill()):(t.fillStyle=r.multiKeyBackground,t.fillRect(m,g,h,c),t.strokeRect(m,g,h,c),t.fillStyle=a.backgroundColor,t.fillRect(b,g+1,h-2,c-2))}t.fillStyle=this.labelTextColors[n]}drawBody(t,s,n){let{body:o}=this,{bodySpacing:r,bodyAlign:a,displayColors:l,boxHeight:c,boxWidth:h,boxPadding:d}=n,f=$(n.bodyFont),u=f.lineHeight,p=0,g=Gt(n.rtl,this.x,this.width),m=function(k){s.fillText(k,g.x(t.x+p),t.y+u/2),t.y+=u+r},b=g.textAlign(a),x,y,M,_,v,S,w;for(s.textAlign=a,s.textBaseline="middle",s.font=f.string,t.x=Vi(this,b,n),s.fillStyle=n.bodyColor,E(this.beforeBody,m),p=l&&b!=="right"?a==="center"?h/2+d:h+2+d:0,_=0,S=o.length;_<S;++_){for(x=o[_],y=this.labelTextColors[_],s.fillStyle=y,E(x.before,m),M=x.lines,l&&M.length&&(this._drawColorBox(s,t,_,g,n),u=Math.max(f.lineHeight,c)),v=0,w=M.length;v<w;++v)m(M[v]),u=f.lineHeight;E(x.after,m)}p=0,u=f.lineHeight,E(this.afterBody,m),t.y-=r}drawFooter(t,s,n){let o=this.footer,r=o.length,a,l;if(r){let c=Gt(n.rtl,this.x,this.width);for(t.x=Vi(this,n.footerAlign,n),t.y+=n.footerMarginTop,s.textAlign=c.textAlign(n.footerAlign),s.textBaseline="middle",a=$(n.footerFont),s.fillStyle=n.footerColor,s.font=a.string,l=0;l<r;++l)s.fillText(o[l],c.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+n.footerSpacing}}drawBackground(t,s,n,o){let{xAlign:r,yAlign:a}=this,{x:l,y:c}=t,{width:h,height:d}=n,{topLeft:f,topRight:u,bottomLeft:p,bottomRight:g}=Bt(o.cornerRadius);s.fillStyle=o.backgroundColor,s.strokeStyle=o.borderColor,s.lineWidth=o.borderWidth,s.beginPath(),s.moveTo(l+f,c),a==="top"&&this.drawCaret(t,s,n,o),s.lineTo(l+h-u,c),s.quadraticCurveTo(l+h,c,l+h,c+u),a==="center"&&r==="right"&&this.drawCaret(t,s,n,o),s.lineTo(l+h,c+d-g),s.quadraticCurveTo(l+h,c+d,l+h-g,c+d),a==="bottom"&&this.drawCaret(t,s,n,o),s.lineTo(l+p,c+d),s.quadraticCurveTo(l,c+d,l,c+d-p),a==="center"&&r==="left"&&this.drawCaret(t,s,n,o),s.lineTo(l,c+f),s.quadraticCurveTo(l,c,l+f,c),s.closePath(),s.fill(),o.borderWidth>0&&s.stroke()}_updateAnimationTarget(t){let s=this.chart,n=this.$animations,o=n&&n.x,r=n&&n.y;if(o||r){let a=Ke[t.position].call(this,this._active,this._eventPosition);if(!a)return;let l=this._size=Xa(this,t),c=Object.assign({},a,this._size),h=Ga(s,t,c),d=Ka(t,c,h,s);(o._to!==d.x||r._to!==d.y)&&(this.xAlign=h.xAlign,this.yAlign=h.yAlign,this.width=l.width,this.height=l.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(t){let s=this.options.setContext(this.getContext()),n=this.opacity;if(!n)return;this._updateAnimationTarget(s);let o={width:this.width,height:this.height},r={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;let a=K(s.padding),l=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;s.enabled&&l&&(t.save(),t.globalAlpha=n,this.drawBackground(r,t,o,s),Bs(t,s.textDirection),r.y+=a.top,this.drawTitle(r,t,s),this.drawBody(r,t,s),this.drawFooter(r,t,s),js(t,s.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,s){let n=this._active,o=t.map(({datasetIndex:l,index:c})=>{let h=this.chart.getDatasetMeta(l);if(!h)throw new Error("Cannot find a dataset at index "+l);return{datasetIndex:l,element:h.data[c],index:c}}),r=!je(n,o),a=this._positionChanged(o,s);(r||a)&&(this._active=o,this._eventPosition=s,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,s,n=!0){if(s&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;let o=this.options,r=this._active||[],a=this._getActiveElements(t,r,s,n),l=this._positionChanged(a,t),c=s||!je(a,r)||l;return c&&(this._active=a,(o.enabled||o.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,s))),c}_getActiveElements(t,s,n,o){let r=this.options;if(t.type==="mouseout")return[];if(!o)return s.filter(l=>this.chart.data.datasets[l.datasetIndex]&&this.chart.getDatasetMeta(l.datasetIndex).controller.getParsed(l.index)!==void 0);let a=this.chart.getElementsAtEventForMode(t,r.mode,r,n);return r.reverse&&a.reverse(),a}_positionChanged(t,s){let{caretX:n,caretY:o,options:r}=this,a=Ke[r.position].call(this,t,s);return a!==!1&&(n!==a.x||o!==a.y)}}return e})(),tm={id:"tooltip",_element:Ja,positioners:Ke,afterInit(e,i,t){t&&(e.tooltip=new Ja({chart:e,options:t}))},beforeUpdate(e,i,t){e.tooltip&&e.tooltip.initialize(t)},reset(e,i,t){e.tooltip&&e.tooltip.initialize(t)},afterDraw(e){let i=e.tooltip;if(i&&i._willRender()){let t={tooltip:i};if(e.notifyPlugins("beforeTooltipDraw",Ct(ht({},t),{cancelable:!0}))===!1)return;i.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",t)}},afterEvent(e,i){if(e.tooltip){let t=i.replay;e.tooltip.handleEvent(i.event,t,i.inChartArea)&&(i.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,i)=>i.bodyFont.size,boxWidth:(e,i)=>i.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Ol},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},em=Object.freeze({__proto__:null,Colors:ug,Decimation:bg,Filler:zg,Legend:Wg,SubTitle:Ug,Title:Yg,Tooltip:tm}),im=(e,i,t,s)=>(typeof i=="string"?(t=e.push(i)-1,s.unshift({index:t,label:i})):isNaN(i)&&(t=null),t);function sm(e,i,t,s){let n=e.indexOf(i);if(n===-1)return im(e,i,t,s);let o=e.lastIndexOf(i);return n!==o?t:n}var nm=(e,i)=>e===null?null:Y(Math.round(e),0,i);function Qa(e){let i=this.getLabels();return e>=0&&e<i.length?i[e]:e}var om=(()=>{class e extends Zt{static id="category";static defaults={ticks:{callback:Qa}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){let s=this._addedLabels;if(s.length){let n=this.getLabels();for(let{index:o,label:r}of s)n[o]===r&&n.splice(o,1);this._addedLabels=[]}super.init(t)}parse(t,s){if(A(t))return null;let n=this.getLabels();return s=isFinite(s)&&n[s]===t?s:sm(n,t,C(s,t),this._addedLabels),nm(s,n.length-1)}determineDataLimits(){let{minDefined:t,maxDefined:s}=this.getUserBounds(),{min:n,max:o}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(n=0),s||(o=this.getLabels().length-1)),this.min=n,this.max=o}buildTicks(){let t=this.min,s=this.max,n=this.options.offset,o=[],r=this.getLabels();r=t===0&&s===r.length-1?r:r.slice(t,s+1),this._valueRange=Math.max(r.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let a=t;a<=s;a++)o.push({value:a});return o}getLabelForValue(t){return Qa.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){let s=this.ticks;return t<0||t>s.length-1?null:this.getPixelForValue(s[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}return e})();function rm(e,i){let t=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=e,u=o||1,p=h-1,{min:g,max:m}=i,b=!A(r),x=!A(a),y=!A(c),M=(m-g)/(d+1),_=bs((m-g)/p/u)*u,v,S,w,k;if(_<1e-14&&!b&&!x)return[{value:g},{value:m}];k=Math.ceil(m/_)-Math.floor(g/_),k>p&&(_=bs(k*_/p/u)*u),A(l)||(v=Math.pow(10,l),_=Math.ceil(_*v)/v),n==="ticks"?(S=Math.floor(g/_)*_,w=Math.ceil(m/_)*_):(S=g,w=m),b&&x&&o&&jr((a-r)/o,_/1e3)?(k=Math.round(Math.min((a-r)/_,h)),_=(a-r)/k,S=r,w=a):y?(S=b?r:S,w=x?a:w,k=c-1,_=(w-S)/k):(k=(w-S)/_,me(k,Math.round(k),_/1e3)?k=Math.round(k):k=Math.ceil(k));let O=Math.max(_s(_),_s(S));v=Math.pow(10,A(l)?O:l),S=Math.round(S*v)/v,w=Math.round(w*v)/v;let P=0;for(b&&(f&&S!==r?(t.push({value:r}),S<r&&P++,me(Math.round((S+P*_)*v)/v,r,tl(r,M,e))&&P++):S<r&&P++);P<k;++P){let T=Math.round((S+P*_)*v)/v;if(x&&T>a)break;t.push({value:T})}return x&&f&&w!==a?t.length&&me(t[t.length-1].value,a,tl(a,M,e))?t[t.length-1].value=a:t.push({value:a}):(!x||w===a)&&t.push({value:w}),t}function tl(e,i,{horizontal:t,minRotation:s}){let n=ot(s),o=(t?Math.sin(n):Math.cos(n))||.001,r=.75*i*(""+e).length;return Math.min(i/o,r)}var we=class extends Zt{constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(i,t){return A(i)||(typeof i=="number"||i instanceof Number)&&!isFinite(+i)?null:+i}handleTickRangeOptions(){let{beginAtZero:i}=this.options,{minDefined:t,maxDefined:s}=this.getUserBounds(),{min:n,max:o}=this,r=l=>n=t?n:l,a=l=>o=s?o:l;if(i){let l=lt(n),c=lt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),i||r(n-l)}this.min=n,this.max=o}getTickLimit(){let i=this.options.ticks,{maxTicksLimit:t,stepSize:s}=i,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),t=t||11),t&&(n=Math.min(t,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){let i=this.options,t=i.ticks,s=this.getTickLimit();s=Math.max(2,s);let n={maxTicks:s,bounds:i.bounds,min:i.min,max:i.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:t.includeBounds!==!1},o=this._range||this,r=rm(n,o);return i.bounds==="ticks"&&xs(r,this,"value"),i.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){let i=this.ticks,t=this.min,s=this.max;if(super.configure(),this.options.offset&&i.length){let n=(s-t)/Math.max(i.length-1,1)/2;t-=n,s+=n}this._startValue=t,this._endValue=s,this._valueRange=s-t}getLabelForValue(i){return xe(i,this.chart.options.locale,this.options.ticks.format)}},mn=class extends we{static id="linear";static defaults={ticks:{callback:Ne.formatters.numeric}};determineDataLimits(){let{min:i,max:t}=this.getMinMax(!0);this.min=V(i)?i:0,this.max=V(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){let i=this.isHorizontal(),t=i?this.width:this.height,s=ot(this.options.ticks.minRotation),n=(i?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,o.lineHeight/n))}getPixelForValue(i){return i===null?NaN:this.getPixelForDecimal((i-this._startValue)/this._valueRange)}getValueForPixel(i){return this._startValue+this.getDecimalForPixel(i)*this._valueRange}},Je=e=>Math.floor(vt(e)),qt=(e,i)=>Math.pow(10,Je(e)+i);function el(e){return e/Math.pow(10,Je(e))===1}function il(e,i,t){let s=Math.pow(10,t),n=Math.floor(e/s);return Math.ceil(i/s)-n}function am(e,i){let t=i-e,s=Je(t);for(;il(e,i,s)>10;)s++;for(;il(e,i,s)<10;)s--;return Math.min(s,Je(e))}function lm(e,{min:i,max:t}){i=Q(e.min,i);let s=[],n=Je(i),o=am(i,t),r=o<0?Math.pow(10,Math.abs(o)):1,a=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((i-l)*r)/r,h=Math.floor((i-l)/a/10)*a*10,d=Math.floor((c-h)/Math.pow(10,o)),f=Q(e.min,Math.round((l+h+d*Math.pow(10,o))*r)/r);for(;f<t;)s.push({value:f,major:el(f),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),f=Math.round((l+h+d*Math.pow(10,o))*r)/r;let u=Q(e.max,f);return s.push({value:u,major:el(u),significand:d}),s}var bn=class extends Zt{static id="logarithmic";static defaults={ticks:{callback:Ne.formatters.logarithmic,major:{enabled:!0}}};constructor(i){super(i),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(i,t){let s=we.prototype.parse.apply(this,[i,t]);if(s===0){this._zero=!0;return}return V(s)&&s>0?s:null}determineDataLimits(){let{min:i,max:t}=this.getMinMax(!0);this.min=V(i)?Math.max(0,i):null,this.max=V(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!V(this._userMin)&&(this.min=i===qt(this.min,0)?qt(this.min,-1):qt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){let{minDefined:i,maxDefined:t}=this.getUserBounds(),s=this.min,n=this.max,o=a=>s=i?s:a,r=a=>n=t?n:a;s===n&&(s<=0?(o(1),r(10)):(o(qt(s,-1)),r(qt(n,1)))),s<=0&&o(qt(n,-1)),n<=0&&r(qt(s,1)),this.min=s,this.max=n}buildTicks(){let i=this.options,t={min:this._userMin,max:this._userMax},s=lm(t,this);return i.bounds==="ticks"&&xs(s,this,"value"),i.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(i){return i===void 0?"0":xe(i,this.chart.options.locale,this.options.ticks.format)}configure(){let i=this.min;super.configure(),this._startValue=vt(i),this._valueRange=vt(this.max)-vt(i)}getPixelForValue(i){return(i===void 0||i===0)&&(i=this.min),i===null||isNaN(i)?NaN:this.getPixelForDecimal(i===this.min?0:(vt(i)-this._startValue)/this._valueRange)}getValueForPixel(i){let t=this.getDecimalForPixel(i);return Math.pow(10,this._startValue+t*this._valueRange)}};function xn(e){let i=e.ticks;if(i.display&&e.display){let t=K(i.backdropPadding);return C(i.font&&i.font.size,j.font.size)+t.height}return 0}function cm(e,i,t){return t=F(t)?t:[t],{w:Gr(e,i.string,t),h:t.length*i.lineHeight}}function sl(e,i,t,s,n){return e===s||e===n?{start:i-t/2,end:i+t/2}:e<s||e>n?{start:i-t,end:i}:{start:i,end:i+t}}function hm(e){let i={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},t=Object.assign({},i),s=[],n=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?L/o:0;for(let l=0;l<o;l++){let c=r.setContext(e.getPointLabelContext(l));n[l]=c.padding;let h=e.getPointPosition(l,e.drawingArea+n[l],a),d=$(c.font),f=cm(e.ctx,d,e._pointLabels[l]);s[l]=f;let u=X(e.getIndexAngle(l)+a),p=Math.round(Pi(u)),g=sl(p,h.x,f.w,0,180),m=sl(p,h.y,f.h,90,270);dm(t,i,u,g,m)}e.setCenterPoint(i.l-t.l,t.r-i.r,i.t-t.t,t.b-i.b),e._pointLabelItems=pm(e,s,n)}function dm(e,i,t,s,n){let o=Math.abs(Math.sin(t)),r=Math.abs(Math.cos(t)),a=0,l=0;s.start<i.l?(a=(i.l-s.start)/o,e.l=Math.min(e.l,i.l-a)):s.end>i.r&&(a=(s.end-i.r)/o,e.r=Math.max(e.r,i.r+a)),n.start<i.t?(l=(i.t-n.start)/r,e.t=Math.min(e.t,i.t-l)):n.end>i.b&&(l=(n.end-i.b)/r,e.b=Math.max(e.b,i.b+l))}function fm(e,i,t){let s=e.drawingArea,{extra:n,additionalAngle:o,padding:r,size:a}=t,l=e.getPointPosition(i,s+n+r,o),c=Math.round(Pi(X(l.angle+H))),h=bm(l.y,a.h,c),d=gm(c),f=mm(l.x,a.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:f,top:h,right:f+a.w,bottom:h+a.h}}function um(e,i){if(!i)return!0;let{left:t,top:s,right:n,bottom:o}=e;return!(ut({x:t,y:s},i)||ut({x:t,y:o},i)||ut({x:n,y:s},i)||ut({x:n,y:o},i))}function pm(e,i,t){let s=[],n=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:xn(o)/2,additionalAngle:r?L/n:0},c;for(let h=0;h<n;h++){l.padding=t[h],l.size=i[h];let d=fm(e,h,l);s.push(d),a==="auto"&&(d.visible=um(d,c),d.visible&&(c=d))}return s}function gm(e){return e===0||e===180?"center":e<180?"left":"right"}function mm(e,i,t){return t==="right"?e-=i:t==="center"&&(e-=i/2),e}function bm(e,i,t){return t===90||t===270?e-=i/2:(t>270||t<90)&&(e-=i),e}function xm(e,i,t){let{left:s,top:n,right:o,bottom:r}=t,{backdropColor:a}=i;if(!A(a)){let l=Bt(i.borderRadius),c=K(i.backdropPadding);e.fillStyle=a;let h=s-c.left,d=n-c.top,f=o-s+c.width,u=r-n+c.height;Object.values(l).some(p=>p!==0)?(e.beginPath(),_e(e,{x:h,y:d,w:f,h:u,radius:l}),e.fill()):e.fillRect(h,d,f,u)}}function _m(e,i){let{ctx:t,options:{pointLabels:s}}=e;for(let n=i-1;n>=0;n--){let o=e._pointLabelItems[n];if(!o.visible)continue;let r=s.setContext(e.getPointLabelContext(n));xm(t,r,o);let a=$(r.font),{x:l,y:c,textAlign:h}=o;zt(t,e._pointLabels[n],l,c+a.lineHeight/2,a,{color:r.color,textAlign:h,textBaseline:"middle"})}}function Al(e,i,t,s){let{ctx:n}=e;if(t)n.arc(e.xCenter,e.yCenter,i,0,z);else{let o=e.getPointPosition(0,i);n.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=e.getPointPosition(r,i),n.lineTo(o.x,o.y)}}function ym(e,i,t,s,n){let o=e.ctx,r=i.circular,{color:a,lineWidth:l}=i;!r&&!s||!a||!l||t<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(n.dash||[]),o.lineDashOffset=n.dashOffset,o.beginPath(),Al(e,t,r,s),o.closePath(),o.stroke(),o.restore())}function vm(e,i,t){return wt(e,{label:t,index:i,type:"pointLabel"})}var _n=class extends we{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ne.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(i){return i},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(i){super(i),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){let i=this._padding=K(xn(this.options)/2),t=this.width=this.maxWidth-i.width,s=this.height=this.maxHeight-i.height;this.xCenter=Math.floor(this.left+t/2+i.left),this.yCenter=Math.floor(this.top+s/2+i.top),this.drawingArea=Math.floor(Math.min(t,s)/2)}determineDataLimits(){let{min:i,max:t}=this.getMinMax(!1);this.min=V(i)&&!isNaN(i)?i:0,this.max=V(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/xn(this.options))}generateTickLabels(i){we.prototype.generateTickLabels.call(this,i),this._pointLabels=this.getLabels().map((t,s)=>{let n=I(this.options.pointLabels.callback,[t,s],this);return n||n===0?n:""}).filter((t,s)=>this.chart.getDataVisibility(s))}fit(){let i=this.options;i.display&&i.pointLabels.display?hm(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(i,t,s,n){this.xCenter+=Math.floor((i-t)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(i,t,s,n))}getIndexAngle(i){let t=z/(this._pointLabels.length||1),s=this.options.startAngle||0;return X(i*t+ot(s))}getDistanceFromCenterForValue(i){if(A(i))return NaN;let t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-i)*t:(i-this.min)*t}getValueForDistanceFromCenter(i){if(A(i))return NaN;let t=i/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(i){let t=this._pointLabels||[];if(i>=0&&i<t.length){let s=t[i];return vm(this.getContext(),i,s)}}getPointPosition(i,t,s=0){let n=this.getIndexAngle(i)-H+s;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter,angle:n}}getPointPositionForValue(i,t){return this.getPointPosition(i,this.getDistanceFromCenterForValue(t))}getBasePosition(i){return this.getPointPositionForValue(i||0,this.getBaseValue())}getPointLabelPosition(i){let{left:t,top:s,right:n,bottom:o}=this._pointLabelItems[i];return{left:t,top:s,right:n,bottom:o}}drawBackground(){let{backgroundColor:i,grid:{circular:t}}=this.options;if(i){let s=this.ctx;s.save(),s.beginPath(),Al(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),s.closePath(),s.fillStyle=i,s.fill(),s.restore()}}drawGrid(){let i=this.ctx,t=this.options,{angleLines:s,grid:n,border:o}=t,r=this._pointLabels.length,a,l,c;if(t.pointLabels.display&&_m(this,r),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);let f=this.getContext(d),u=n.setContext(f),p=o.setContext(f);ym(this,u,l,r,p)}}),s.display){for(i.save(),a=r-1;a>=0;a--){let h=s.setContext(this.getPointLabelContext(a)),{color:d,lineWidth:f}=h;!f||!d||(i.lineWidth=f,i.strokeStyle=d,i.setLineDash(h.borderDash),i.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),c=this.getPointPosition(a,l),i.beginPath(),i.moveTo(this.xCenter,this.yCenter),i.lineTo(c.x,c.y),i.stroke())}i.restore()}}drawBorder(){}drawLabels(){let i=this.ctx,t=this.options,s=t.ticks;if(!s.display)return;let n=this.getIndexAngle(0),o,r;i.save(),i.translate(this.xCenter,this.yCenter),i.rotate(n),i.textAlign="center",i.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!t.reverse)return;let c=s.setContext(this.getContext(l)),h=$(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){i.font=h.string,r=i.measureText(a.label).width,i.fillStyle=c.backdropColor;let d=K(c.backdropPadding);i.fillRect(-r/2-d.left,-o-h.size/2-d.top,r+d.width,h.size+d.height)}zt(i,a.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),i.restore()}drawTitle(){}},ts={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},et=Object.keys(ts);function nl(e,i){return e-i}function ol(e,i){if(A(i))return null;let t=e._adapter,{parser:s,round:n,isoWeekday:o}=e._parseOpts,r=i;return typeof s=="function"&&(r=s(r)),V(r)||(r=typeof s=="string"?t.parse(r,s):t.parse(r)),r===null?null:(n&&(r=n==="week"&&(Xt(o)||o===!0)?t.startOf(r,"isoWeek",o):t.startOf(r,n)),+r)}function rl(e,i,t,s){let n=et.length;for(let o=et.indexOf(e);o<n-1;++o){let r=ts[et[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((t-i)/(a*r.size))<=s)return et[o]}return et[n-1]}function Mm(e,i,t,s,n){for(let o=et.length-1;o>=et.indexOf(t);o--){let r=et[o];if(ts[r].common&&e._adapter.diff(n,s,r)>=i-1)return r}return et[t?et.indexOf(t):0]}function wm(e){for(let i=et.indexOf(e)+1,t=et.length;i<t;++i)if(ts[et[i]].common)return et[i]}function al(e,i,t){if(!t)e[i]=!0;else if(t.length){let{lo:s,hi:n}=Oi(t,i),o=t[s]>=i?t[s]:t[n];e[o]=!0}}function Sm(e,i,t,s){let n=e._adapter,o=+n.startOf(i[0].value,s),r=i[i.length-1].value,a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=t[a],l>=0&&(i[l].major=!0);return i}function ll(e,i,t){let s=[],n={},o=i.length,r,a;for(r=0;r<o;++r)a=i[r],n[a]=r,s.push({value:a,major:!1});return o===0||!t?s:Sm(e,s,n,t)}var yn=(()=>{class e extends Zt{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,s={}){let n=t.time||(t.time={}),o=this._adapter=new Iu._date(t.adapters.date);o.init(s),pe(n.displayFormats,o.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(t),this._normalized=s.normalized}parse(t,s){return t===void 0?null:ol(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){let t=this.options,s=this._adapter,n=t.time.unit||"day",{min:o,max:r,minDefined:a,maxDefined:l}=this.getUserBounds();function c(h){!a&&!isNaN(h.min)&&(o=Math.min(o,h.min)),!l&&!isNaN(h.max)&&(r=Math.max(r,h.max))}(!a||!l)&&(c(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&c(this.getMinMax(!1))),o=V(o)&&!isNaN(o)?o:+s.startOf(Date.now(),n),r=V(r)&&!isNaN(r)?r:+s.endOf(Date.now(),n)+1,this.min=Math.min(o,r-1),this.max=Math.max(o+1,r)}_getLabelBounds(){let t=this.getLabelTimestamps(),s=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return t.length&&(s=t[0],n=t[t.length-1]),{min:s,max:n}}buildTicks(){let t=this.options,s=t.time,n=t.ticks,o=n.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&o.length&&(this.min=this._userMin||o[0],this.max=this._userMax||o[o.length-1]);let r=this.min,a=this.max,l=Hr(o,r,a);return this._unit=s.unit||(n.autoSkip?rl(s.minUnit,this.min,this.max,this._getLabelCapacity(r)):Mm(this,l.length,s.minUnit,this.min,this.max)),this._majorUnit=!n.major.enabled||this._unit==="year"?void 0:wm(this._unit),this.initOffsets(o),t.reverse&&l.reverse(),ll(this,l,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let s=0,n=0,o,r;this.options.offset&&t.length&&(o=this.getDecimalForValue(t[0]),t.length===1?s=1-o:s=(this.getDecimalForValue(t[1])-o)/2,r=this.getDecimalForValue(t[t.length-1]),t.length===1?n=r:n=(r-this.getDecimalForValue(t[t.length-2]))/2);let a=t.length<3?.5:.25;s=Y(s,0,a),n=Y(n,0,a),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){let t=this._adapter,s=this.min,n=this.max,o=this.options,r=o.time,a=r.unit||rl(r.minUnit,s,n,this._getLabelCapacity(s)),l=C(o.ticks.stepSize,1),c=a==="week"?r.isoWeekday:!1,h=Xt(c)||c===!0,d={},f=s,u,p;if(h&&(f=+t.startOf(f,"isoWeek",c)),f=+t.startOf(f,h?"day":a),t.diff(n,s,a)>1e5*l)throw new Error(s+" and "+n+" are too far apart with stepSize of "+l+" "+a);let g=o.ticks.source==="data"&&this.getDataTimestamps();for(u=f,p=0;u<n;u=+t.add(u,l,a),p++)al(d,u,g);return(u===n||o.bounds==="ticks"||p===1)&&al(d,u,g),Object.keys(d).sort(nl).map(m=>+m)}getLabelForValue(t){let s=this._adapter,n=this.options.time;return n.tooltipFormat?s.format(t,n.tooltipFormat):s.format(t,n.displayFormats.datetime)}format(t,s){let o=this.options.time.displayFormats,r=this._unit,a=s||o[r];return this._adapter.format(t,a)}_tickFormatFunction(t,s,n,o){let r=this.options,a=r.ticks.callback;if(a)return I(a,[t,s,n],this);let l=r.time.displayFormats,c=this._unit,h=this._majorUnit,d=c&&l[c],f=h&&l[h],u=n[s],p=h&&f&&u&&u.major;return this._adapter.format(t,o||(p?f:d))}generateTickLabels(t){let s,n,o;for(s=0,n=t.length;s<n;++s)o=t[s],o.label=this._tickFormatFunction(o.value,s,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){let s=this._offsets,n=this.getDecimalForValue(t);return this.getPixelForDecimal((s.start+n)*s.factor)}getValueForPixel(t){let s=this._offsets,n=this.getDecimalForPixel(t)/s.factor-s.end;return this.min+n*(this.max-this.min)}_getLabelSize(t){let s=this.options.ticks,n=this.ctx.measureText(t).width,o=ot(this.isHorizontal()?s.maxRotation:s.minRotation),r=Math.cos(o),a=Math.sin(o),l=this._resolveTickFontOptions(0).size;return{w:n*r+l*a,h:n*a+l*r}}_getLabelCapacity(t){let s=this.options.time,n=s.displayFormats,o=n[s.unit]||n.millisecond,r=this._tickFormatFunction(t,0,ll(this,[t],this._majorUnit),o),a=this._getLabelSize(r),l=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return l>0?l:1}getDataTimestamps(){let t=this._cache.data||[],s,n;if(t.length)return t;let o=this.getMatchingVisibleMetas();if(this._normalized&&o.length)return this._cache.data=o[0].controller.getAllParsedValues(this);for(s=0,n=o.length;s<n;++s)t=t.concat(o[s].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){let t=this._cache.labels||[],s,n;if(t.length)return t;let o=this.getLabels();for(s=0,n=o.length;s<n;++s)t.push(ol(this,o[s]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Ms(t.sort(nl))}}return e})();function Hi(e,i,t){let s=0,n=e.length-1,o,r,a,l;t?(i>=e[s].pos&&i<=e[n].pos&&({lo:s,hi:n}=ft(e,"pos",i)),{pos:o,time:a}=e[s],{pos:r,time:l}=e[n]):(i>=e[s].time&&i<=e[n].time&&({lo:s,hi:n}=ft(e,"time",i)),{time:o,pos:a}=e[s],{time:r,pos:l}=e[n]);let c=r-o;return c?a+(l-a)*(i-o)/c:a}var vn=class extends yn{static id="timeseries";static defaults=yn.defaults;constructor(i){super(i),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){let i=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(i);this._minPos=Hi(t,this.min),this._tableRange=Hi(t,this.max)-this._minPos,super.initOffsets(i)}buildLookupTable(i){let{min:t,max:s}=this,n=[],o=[],r,a,l,c,h;for(r=0,a=i.length;r<a;++r)c=i[r],c>=t&&c<=s&&n.push(c);if(n.length<2)return[{time:t,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){let i=this.min,t=this.max,s=super.getDataTimestamps();return(!s.includes(i)||!s.length)&&s.splice(0,0,i),(!s.includes(t)||s.length===1)&&s.push(t),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let i=this._cache.all||[];if(i.length)return i;let t=this.getDataTimestamps(),s=this.getLabelTimestamps();return t.length&&s.length?i=this.normalize(t.concat(s)):i=t.length?t:s,i=this._cache.all=i,i}getDecimalForValue(i){return(Hi(this._table,i)-this._minPos)/this._tableRange}getValueForPixel(i){let t=this._offsets,s=this.getDecimalForPixel(i)/t.factor-t.end;return Hi(this._table,s*this._tableRange+this._minPos,!0)}},km=Object.freeze({__proto__:null,CategoryScale:om,LinearScale:mn,LogarithmicScale:bn,RadialLinearScale:_n,TimeScale:yn,TimeSeriesScale:vn}),Dl=[Ru,rg,em,km];var ti={TOOLTIP:"chartjs-tooltip",TOOLTIP_BODY:"chartjs-tooltip-body",TOOLTIP_BODY_ITEM:"chartjs-tooltip-body-item",TOOLTIP_HEADER:"chartjs-tooltip-header",TOOLTIP_HEADER_ITEM:"chartjs-tooltip-header-item"},Cm=e=>{let i=e.canvas.parentNode.querySelector("div");if(!i){i=document.createElement("div"),i.classList.add(ti.TOOLTIP);let t=document.createElement("table");t.style.margin="0px",i.append(t),e.canvas.parentNode.append(i)}return i},Tl=e=>{let{chart:i,tooltip:t}=e,s=Cm(i);if(t.opacity===0){s.style.opacity=0;return}if(t.body){let r=t.title||[],a=t.body.map(d=>d.lines),l=document.createElement("thead");l.classList.add(ti.TOOLTIP_HEADER);for(let d of r){let f=document.createElement("tr");f.style.borderWidth=0,f.classList.add(ti.TOOLTIP_HEADER_ITEM);let u=document.createElement("th");u.style.borderWidth=0;let p=document.createTextNode(d);u.append(p),f.append(u),l.append(f)}let c=document.createElement("tbody");c.classList.add(ti.TOOLTIP_BODY);for(let[d,f]of a.entries()){let u=t.labelColors[d],p=document.createElement("span");p.style.background=u.backgroundColor,p.style.borderColor=u.borderColor,p.style.borderWidth="2px",p.style.marginRight="10px",p.style.height="10px",p.style.width="10px",p.style.display="inline-block";let g=document.createElement("tr");g.classList.add(ti.TOOLTIP_BODY_ITEM);let m=document.createElement("td");m.style.borderWidth=0;let b=document.createTextNode(f);m.append(p),m.append(b),g.append(m),c.append(g)}let h=s.querySelector("table");for(;h.firstChild;)h.firstChild.remove();h.append(l),h.append(c)}let{offsetLeft:n,offsetTop:o}=i.canvas;s.style.opacity=1,s.style.left=`${n+t.caretX}px`,s.style.top=`${o+t.caretY}px`,s.style.font=t.options.bodyFont.string,s.style.padding=`${t.padding}px ${t.padding}px`};var Pm=["canvasElement"],Om=["*"];Zi.register(...Dl);var Am=0,k0=(()=>{class e{static ngAcceptInputType_redraw;ngZone=ii(On);renderer=ii(Cn);changeDetectorRef=ii(Wn);customTooltips=at(!0,{transform:si});data=at();height=at(null,{transform:t=>es(t,void 0)});idInput=at(`c-chartjs-${Am++}`,{alias:"id"});get id(){return this.idInput()}optionsInput=at({},{alias:"options"});options=Vn(this.optionsInput);plugins=at([]);redraw=at(!1,{transform:si});type=at("bar");width=at(null,{transform:t=>es(t,void 0)});wrapper=at(!0,{transform:si});getDatasetAtEvent=Pe();getElementAtEvent=Pe();getElementsAtEvent=Pe();chartRef=Pe();canvasElement=Hn.required("canvasElement");chart;ctx;hostClasses=Ce(()=>({"chart-wrapper":this.wrapper()}));constructor(){$n({read:()=>{let t=this.canvasElement();this.ctx=t?.nativeElement?.getContext("2d"),this.chartRender()}})}ngOnChanges(t){t.data&&!t.data.firstChange&&this.chartUpdate()}ngOnDestroy(){this.chartDestroy()}handleClick(t){if(!this.chart)return;let s=this.chart.getElementsAtEventForMode(t,"dataset",{intersect:!0},!1);this.getDatasetAtEvent.emit(s);let n=this.chart.getElementsAtEventForMode(t,"nearest",{intersect:!0},!1);this.getElementAtEvent.emit(n);let o=this.chart.getElementsAtEventForMode(t,"index",{intersect:!0},!1);this.getElementsAtEvent.emit(o)}chartDestroy(){this.chart?.destroy(),this.chartRef.emit(void 0)}chartRender(){let t=this.canvasElement();!t?.nativeElement||!this.ctx||this.chart||this.ngZone.runOutsideAngular(()=>{let s=this.chartConfig();s&&(this.chart=new Zi(this.ctx,s),this.ngZone.run(()=>{this.renderer.setStyle(t.nativeElement,"display","block"),this.changeDetectorRef.markForCheck(),this.chartRef.emit(this.chart)}))})}chartUpdate(){if(!this.chart)return;if(this.redraw()){this.chartDestroy(),this.chartRender();return}let t=this.chartConfig();this.options()&&Object.assign(this.chart.options??{},t.options??{}),this.chart.config.data||(this.chart.config.data=ht({},t.data),this.chartUpdateOutsideAngular()),this.chart&&(Object.assign(this.chart.config.options??{},t.options??{}),Object.assign(this.chart.config.plugins??[],t.plugins??[]),Object.assign(this.chart.config.data,t.data)),this.chartUpdateOutsideAngular()}chartUpdateOutsideAngular(){setTimeout(()=>{this.ngZone.runOutsideAngular(()=>{this.chart?.update(),this.ngZone.run(()=>{this.changeDetectorRef.markForCheck()})})})}chartToBase64Image(){return this.chart?.toBase64Image()}chartDataConfig=Ce(()=>{let{labels:t,datasets:s}=ht({},this.data());return{labels:t??[],datasets:s??[]}});chartOptions=Ce(()=>this.options()??{});chartConfig=Ce(()=>(this.chartCustomTooltips(),{data:this.chartDataConfig(),options:this.chartOptions(),plugins:this.plugins(),type:this.type()}));chartCustomTooltips(){if(this.customTooltips()){let t=this.options(),s=t?.plugins,n=t?.plugins?.tooltip;Nn(()=>{this.options.set(mr(Ct(ht({},t),{plugins:Ct(ht({},s),{tooltip:Ct(ht({},n),{enabled:!1,mode:"index",position:"nearest",external:Tl})})})))})}}static \u0275fac=function(s){return new(s||e)};static \u0275cmp=Pn({type:e,selectors:[["c-chart"]],viewQuery:function(s,n){s&1&&Fn(n.canvasElement,Pm,5),s&2&&zn()},hostVars:6,hostBindings:function(s,n){s&2&&(jn(n.hostClasses()),Bn("height",n.height(),"px")("width",n.width(),"px"))},inputs:{customTooltips:[1,"customTooltips"],data:[1,"data"],height:[1,"height"],idInput:[1,"id","idInput"],optionsInput:[1,"options","optionsInput"],plugins:[1,"plugins"],redraw:[1,"redraw"],type:[1,"type"],width:[1,"width"],wrapper:[1,"wrapper"]},outputs:{getDatasetAtEvent:"getDatasetAtEvent",getElementAtEvent:"getElementAtEvent",getElementsAtEvent:"getElementsAtEvent",chartRef:"chartRef"},exportAs:["cChart"],features:[kn],ngContentSelectors:Om,decls:3,vars:3,consts:[["canvasElement",""],["role","img",2,"display","none",3,"click","height","id","width"]],template:function(s,n){if(s&1){let o=Ln();Rn(),Dn(0,"canvas",1,0),En("click",function(a){return wn(o),Sn(n.handleClick(a))}),In(2),Tn()}s&2&&An("height",n.height()||null)("id",n.id)("width",n.width()||null)},styles:[".chart-wrapper[_nghost-%COMP%]{display:block}"],changeDetection:0})}return e})();export{k0 as a};
