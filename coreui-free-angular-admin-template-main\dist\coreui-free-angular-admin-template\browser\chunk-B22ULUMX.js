import{b as B}from"./chunk-3MARWV4R.js";import{e as I,h as G,l as R,r as U,s as O,v as N,w as M}from"./chunk-LKW33VZJ.js";import{E as S,F as x,I as y,ba as E,ca as h,da as k,ea as C,fa as g,ga as F,ia as L,ja as w,ka as V,la as T,n as b,oa as q,qa as D,va as _,wa as P}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as l,Hb as t,Ib as i,Jb as n,Nb as f,Qb as m,Va as a,eb as v,fc as e,ha as r,ia as d}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var p=class p{constructor(){this.customStylesValidated=!1;this.browserDefaultsValidated=!1;this.tooltipValidated=!1}ngOnInit(){}onSubmit1(){this.customStylesValidated=!0,console.log("Submit... 1")}onReset1(){this.customStylesValidated=!1,console.log("Reset... 1")}onSubmit2(){this.browserDefaultsValidated=!0,console.log("Submit... 2")}onReset2(){this.browserDefaultsValidated=!1,console.log("Reset... 3")}onSubmit3(){this.tooltipValidated=!0,console.log("Submit... 3")}onReset3(){this.tooltipValidated=!1,console.log("Reset... 3")}};p.\u0275fac=function(u){return new(u||p)},p.\u0275cmp=v({type:p,selectors:[["app-validation"]],decls:381,vars:46,consts:[["customStylesForm","ngForm"],["browserDefaultsForm","ngForm"],["tooltipForm","ngForm"],["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","forms/validation"],["cForm","",1,"row","g-3","needs-validation",3,"ngSubmit","validated"],["md","4"],["cLabel","","for","validationCustom01"],["cFormControl","","id","validationCustom01","required","","type","text","value","Mark"],[3,"valid"],["cLabel","","for","validationCustom02"],["cFormControl","","id","validationCustom02","required","","type","text","value","Otto"],["cLabel","","for","validationCustomUsername"],[1,"has-validation"],["cInputGroupText","","id","inputGroupPrepend"],["aria-describedby","inputGroupPrepend","cFormControl","","id","validationCustomUsername","required","","type","text"],["md","6"],["cLabel","","for","validationCustom03"],["cFormControl","","id","validationCustom03","required","","type","text"],["md","3"],["cLabel","","for","validationCustom04"],["cSelect","","id","validationCustom04","required",""],["value",""],["cLabel","","for","validationCustom05"],["cFormControl","","id","validationCustom05","required","","type","text"],["cFormCheckInput","","id","invalidCheck","name","invalidCheck","required","","type","checkbox"],["cFormCheckLabel","","for","invalidCheck"],["cButton","","color","primary","type","submit",1,"me-1"],["cButton","","color","secondary","type","reset",3,"click"],["href","forms/validation#browser-defaults"],["cForm","","ngNativeValidate","",1,"row","g-3",3,"ngSubmit","validated"],["cLabel","","for","validationDefault01"],["cFormControl","","id","validationDefault01","required","","type","text","value","Mark"],["cLabel","","for","validationDefault02"],["cFormControl","","id","validationDefault02","required","","type","text","value","Otto"],["cLabel","","for","validationDefaultUsername"],["cInputGroupText","","id","inputGroupPrepend1"],["aria-describedby","inputGroupPrepend1","cFormControl","","id","validationDefaultUsername","required","","type","text"],["cLabel","","for","validationDefault03"],["cFormControl","","id","validationDefault03","required","","type","text"],["cLabel","","for","validationDefault04"],["cSelect","","id","validationDefault04","required",""],["cLabel","","for","validationDefault05"],["cFormControl","","id","validationDefault05","required","","type","text"],["cFormCheckInput","","id","invalidCheck1","name","invalidCheck","required","","type","checkbox"],["cFormCheckLabel","","for","invalidCheck1"],["href","forms/validation#server-side"],["cForm","","ngNativeValidate","",1,"row","g-3","needs-validation"],["cLabel","","for","validationServer01"],["cFormControl","","id","validationServer01","required","","type","text","value","Mark",3,"valid"],["cLabel","","for","validationServer02"],["cFormControl","","id","validationServer02","required","","type","text","value","Otto",3,"valid"],["cLabel","","for","validationServerUsername"],["cInputGroupText","","id","inputGroupPrepend03"],["aria-describedby","inputGroupPrepend03","cFormControl","","id","validationServerUsername","required","","type","text",3,"valid"],["cLabel","","for","validationServer03"],["cFormControl","","id","validationServer03","required","","type","text",3,"valid"],["cLabel","","for","validationServer04"],["cSelect","","id","validationServer04",3,"valid"],["disabled",""],["cLabel","","for","validationServer05"],["cFormControl","","id","validationServer05","required","","type","text",3,"valid"],[1,"mb-3"],["cFormCheckInput","","id","invalidCheckServer","required","","type","checkbox",3,"valid"],["cFormCheckLabel","","for","invalidCheckServer"],["cButton","","color","primary","type","submit"],["cListGroup","",1,"mb-3",3,"flush"],["cListGroupItem",""],["href","forms/validation#supported-elements"],["cForm","",3,"validated"],["cLabel","","for","validationTextarea",1,"form-label"],["cFormControl","","id","validationTextarea","placeholder","Required example textarea","required","",3,"valid"],["cFormCheckInput","","id","validationFormCheck1","name","validationFormCheck1","required","","type","checkbox"],["cFormCheckLabel","","for","validationFormCheck1"],["cFormCheckInput","","id","validationFormCheck2","name","radio-stacked","required","","type","radio"],["cFormCheckLabel","","for","validationFormCheck2"],["cFormCheckInput","","id","validationFormCheck3","name","radio-stacked","required","","type","radio"],["cFormCheckLabel","","for","validationFormCheck3"],["aria-label","select example","cSelect","","required",""],["value","1"],["value","2"],["value","3"],["aria-label","file example","cFormControl","","id","validationText1","required","","type","file"],["cButton","","color","primary","disabled","","type","submit"],["href","forms/validation#tooltips"],["md","4",1,"position-relative"],["cLabel","","for","validationTooltip01"],["cFormControl","","id","validationTooltip01","required","","type","text","value","Mark"],["tooltip","",3,"valid"],["cLabel","","for","validationTooltip02"],["cFormControl","","id","validationTooltip02","required","","type","text","value","Otto"],["cLabel","","for","validationTooltipUsername"],["cInputGroupText","","id","inputGroupPrependTooltip"],["aria-describedby","inputGroupPrependTooltip","cFormControl","","id","validationTooltipUsername","required","","type","text"],["md","6",1,"position-relative"],["cLabel","","for","validationTooltip03"],["cFormControl","","id","validationTooltip03","required","","type","text"],["md","3",1,"position-relative"],["cLabel","","for","validationTooltip04"],["cSelect","","id","validationTooltip04","required",""],["cLabel","","for","validationTooltip05"],["cFormControl","","id","validationTooltip05","required","","type","text"],["xs","12",1,"position-relative"],["cFormCheckInput","","id","invalidCheckTooltip","name","invalidCheckTooltip","required","","type","checkbox"],["cFormCheckLabel","","for","invalidCheckTooltip"]],template:function(u,o){if(u&1){let c=f();t(0,"c-row")(1,"c-col",3)(2,"c-card",4)(3,"c-card-header"),e(4,`
        `),t(5,"strong"),e(6,"Validation"),i(),e(7," "),t(8,"small"),e(9,"Custom styles"),i(),e(10,`
      `),i(),t(11,"c-card-body")(12,"p",5),e(13," For custom CoreUI form validation messages, you'll need to add the "),t(14,"code"),e(15,"noValidate"),i(),e(16," boolean property to your "),t(17,"code"),e(18,"<CForm>"),i(),e(19,". This disables the browser default feedback tooltips, but still provides access to the form validation APIs in JavaScript. Try to submit the form below; our JavaScript will intercept the submit button and relay feedback to you. When attempting to submit, you'll see the "),t(20,"code"),e(21,":invalid"),i(),e(22," and "),t(23,"code"),e(24,":valid"),i(),e(25," styles applied to your form controls. "),i(),t(26,"p",5),e(27," Custom feedback styles apply custom colors, borders, focus styles, and background icons to better communicate feedback. "),i(),t(28,"app-docs-example",6)(29,"form",7,0),m("ngSubmit",function(){return r(c),d(o.onSubmit1())}),t(31,"c-col",8)(32,"label",9),e(33,"First name"),i(),n(34,"input",10),t(35,"c-form-feedback",11),e(36,"Looks good!"),i()(),t(37,"c-col",8)(38,"label",12),e(39,"Last name"),i(),n(40,"input",13),t(41,"c-form-feedback",11),e(42,"Looks good!"),i()(),t(43,"c-col",8)(44,"label",14),e(45,"Username"),i(),t(46,"c-input-group",15)(47,"span",16),e(48,"@"),i(),n(49,"input",17),t(50,"c-form-feedback",11),e(51,"Please choose a username."),i()()(),t(52,"c-col",18)(53,"label",19),e(54,"City"),i(),n(55,"input",20),t(56,"c-form-feedback",11),e(57,"Please provide a valid city."),i()(),t(58,"c-col",21)(59,"label",22),e(60,"State"),i(),t(61,"select",23)(62,"option",24),e(63,"Choose..."),i(),t(64,"option"),e(65,"..."),i()(),t(66,"c-form-feedback",11),e(67,"Please provide a valid State."),i()(),t(68,"c-col",21)(69,"label",25),e(70,"Zip code"),i(),n(71,"input",26),t(72,"c-form-feedback",11),e(73,"Please provide a valid zip."),i()(),t(74,"c-col",3)(75,"c-form-check"),n(76,"input",27),t(77,"label",28),e(78,"Agree to terms and conditions"),i()(),t(79,"c-form-feedback",11),e(80,"You must agree before submitting."),i()(),t(81,"c-col",3)(82,"button",29),e(83," Submit form "),i(),t(84,"button",30),m("click",function(){return r(c),d(o.onReset1())}),e(85," Reset "),i()()()()()()(),t(86,"c-col",3)(87,"c-card",4)(88,"c-card-header"),e(89,`
        `),t(90,"strong"),e(91,"Validation"),i(),e(92," "),t(93,"small"),e(94,"Browser defaults"),i(),e(95,`
      `),i(),t(96,"c-card-body")(97,"p",5),e(98," Not interested in custom validation feedback messages or writing JavaScript to change form behaviors? All good, you can use the browser defaults. Try submitting the form below. Depending on your browser and OS, you'll see a slightly different style of feedback. "),i(),t(99,"p",5),e(100," While these feedback styles cannot be styled with CSS, you can still customize the feedback text through JavaScript. "),i(),t(101,"app-docs-example",31)(102,"form",32,1),m("ngSubmit",function(){return r(c),d(o.onSubmit2())}),t(104,"c-col",8)(105,"label",33),e(106,"Email"),i(),n(107,"input",34),t(108,"c-form-feedback",11),e(109,"Looks good!"),i()(),t(110,"c-col",8)(111,"label",35),e(112,"Email"),i(),n(113,"input",36),t(114,"c-form-feedback",11),e(115,"Looks good!"),i()(),t(116,"c-col",8)(117,"label",37),e(118,"Username"),i(),t(119,"c-input-group",15)(120,"span",38),e(121,"@"),i(),n(122,"input",39),t(123,"c-form-feedback",11),e(124,"Please choose a username."),i()()(),t(125,"c-col",18)(126,"label",40),e(127,"City"),i(),n(128,"input",41),t(129,"c-form-feedback",11),e(130,"Please provide a valid city."),i()(),t(131,"c-col",21)(132,"label",42),e(133,"State"),i(),t(134,"select",43)(135,"option",24),e(136,"Choose..."),i(),t(137,"option"),e(138,"..."),i()(),t(139,"c-form-feedback",11),e(140,"Please provide a valid State."),i()(),t(141,"c-col",21)(142,"label",44),e(143,"Zip code"),i(),n(144,"input",45),t(145,"c-form-feedback",11),e(146,"Please provide a valid zip."),i()(),t(147,"c-col",3)(148,"c-form-check"),n(149,"input",46),t(150,"label",47),e(151,"Agree to terms and conditions"),i()(),t(152,"c-form-feedback",11),e(153,"You must agree before submitting."),i()(),t(154,"c-col",3)(155,"button",29),e(156," Submit form "),i(),t(157,"button",30),m("click",function(){return r(c),d(o.onReset2())}),e(158," Reset "),i()()()()()()(),t(159,"c-col",3)(160,"c-card",4)(161,"c-card-header"),e(162,`
        `),t(163,"strong"),e(164,"Validation"),i(),e(165," "),t(166,"small"),e(167,"Server side"),i(),e(168,`
      `),i(),t(169,"c-card-body")(170,"p",5),e(171," We recommend using client-side validation, but in case you require server-side validation, you can indicate invalid and valid form fields with "),t(172,"code"),e(173,"invalid"),i(),e(174," and "),t(175,"code"),e(176,"valid"),i(),e(177," boolean properties. "),i(),t(178,"p",5),e(179," For invalid fields, ensure that the invalid feedback/error message is associated with the relevant form field using "),t(180,"code"),e(181,"aria-describedby"),i(),e(182," (noting that this attribute allows more than one "),t(183,"code"),e(184,"id"),i(),e(185," to be referenced, in case the field already points to additional form text). "),i(),t(186,"app-docs-example",48)(187,"form",49)(188,"c-col",8)(189,"label",50),e(190,"First name"),i(),n(191,"input",51),t(192,"c-form-feedback",11),e(193,"Looks good!"),i()(),t(194,"c-col",8)(195,"label",52),e(196,"Last name"),i(),n(197,"input",53),t(198,"c-form-feedback",11),e(199,"Looks good!"),i()(),t(200,"c-col",8)(201,"label",54),e(202,"Username"),i(),t(203,"c-input-group",15)(204,"span",55),e(205,"@"),i(),n(206,"input",56),t(207,"c-form-feedback",11),e(208,"Please choose a username."),i()()(),t(209,"c-col",18)(210,"label",57),e(211,"City"),i(),n(212,"input",58),t(213,"c-form-feedback",11),e(214,"Please provide a valid city."),i()(),t(215,"c-col",21)(216,"label",59),e(217,"State"),i(),t(218,"select",60)(219,"option",61),e(220,"Choose..."),i(),t(221,"option"),e(222,"..."),i()(),t(223,"c-form-feedback",11),e(224,"Please provide a valid state."),i()(),t(225,"c-col",21)(226,"label",62),e(227,"Zip code"),i(),n(228,"input",63),t(229,"c-form-feedback",11),e(230,"Please provide a valid zip."),i()(),t(231,"c-col",3)(232,"c-form-check",64),n(233,"input",65),t(234,"label",66),e(235,"Agree to terms and conditions"),i()(),t(236,"c-form-feedback",11),e(237,"You must agree before submitting."),i()(),t(238,"c-col",3)(239,"button",67),e(240," Submit form "),i()()()()()()(),t(241,"c-col",3)(242,"c-card",4)(243,"c-card-header"),e(244,`
        `),t(245,"strong"),e(246,"Validation"),i(),e(247," "),t(248,"small"),e(249,"Supported elements"),i(),e(250,`
      `),i(),t(251,"c-card-body")(252,"p",5),e(253," Validation styles are available for the following form controls and components: "),i(),t(254,"ul",68)(255,"li",69)(256,"code"),e(257,"<input cFormControl>"),i()(),t(258,"li",69)(259,"code"),e(260,"<select cSelect>"),i()(),t(261,"li",69)(262,"code"),e(263,"<c-form-check>"),i()()(),t(264,"app-docs-example",70)(265,"form",71)(266,"div",64)(267,"label",72),e(268," Textarea "),i(),n(269,"textarea",73),t(270,"c-form-feedback",11),e(271,"Please enter a message in the textarea."),i()(),t(272,"c-form-check",64),n(273,"input",74),t(274,"label",75),e(275,"Check this checkbox"),i()(),t(276,"c-form-feedback",11),e(277,"Example invalid feedback text"),i(),t(278,"c-form-check"),n(279,"input",76),t(280,"label",77),e(281,"Check this radio"),i()(),t(282,"c-form-check",64),n(283,"input",78),t(284,"label",79),e(285,"Check this radio"),i()(),t(286,"c-form-feedback",11),e(287,"More example invalid feedback text"),i(),t(288,"div",64)(289,"select",80)(290,"option",24),e(291,"Open this select menu"),i(),t(292,"option",81),e(293,"One"),i(),t(294,"option",82),e(295,"Two"),i(),t(296,"option",83),e(297,"Three"),i()(),t(298,"c-form-feedback",11),e(299,"Example invalid select feedback"),i()(),t(300,"div",64),n(301,"input",84),t(302,"c-form-feedback",11),e(303,"Example invalid form file feedback"),i()(),t(304,"div",64)(305,"button",85),e(306," Submit form "),i()()()()()()(),t(307,"c-col",3)(308,"c-card",4)(309,"c-card-header"),e(310,`
        `),t(311,"strong"),e(312,"Validation"),i(),e(313," "),t(314,"small"),e(315,"Tooltips"),i(),e(316,`
      `),i(),t(317,"c-card-body")(318,"p",5),e(319," If your form layout allows it, you can swap the text for the tooltip to display validation feedback in a styled tooltip. Be sure to have a parent with "),t(320,"code"),e(321,"position: relative"),i(),e(322," on it for tooltip positioning. In the example below, our column classes have this already, but your project may require an alternative setup. "),i(),t(323,"app-docs-example",86)(324,"form",7,2),m("ngSubmit",function(){return r(c),d(o.onSubmit3())}),t(326,"c-col",87)(327,"label",88),e(328,"First name"),i(),n(329,"input",89),t(330,"c-form-feedback",90),e(331,"Looks good!"),i()(),t(332,"c-col",87)(333,"label",91),e(334,"Last name"),i(),n(335,"input",92),t(336,"c-form-feedback",90),e(337,"Looks good!"),i()(),t(338,"c-col",87)(339,"label",93),e(340,"Username"),i(),t(341,"c-input-group",15)(342,"span",94),e(343,"@"),i(),n(344,"input",95),t(345,"c-form-feedback",90),e(346,"Please choose a username."),i()()(),t(347,"c-col",96)(348,"label",97),e(349,"City"),i(),n(350,"input",98),t(351,"c-form-feedback",90),e(352,"Please provide a valid city."),i()(),t(353,"c-col",99)(354,"label",100),e(355,"State"),i(),t(356,"select",101)(357,"option",24),e(358,"Choose..."),i(),t(359,"option",81),e(360,"..."),i()(),t(361,"c-form-feedback",90),e(362,"Please provide a valid State."),i()(),t(363,"c-col",99)(364,"label",102),e(365,"Zip code"),i(),n(366,"input",103),t(367,"c-form-feedback",90),e(368,"Please provide a valid zip."),i()(),t(369,"c-col",104)(370,"c-form-check"),n(371,"input",105),t(372,"label",106),e(373,"Agree to terms and conditions"),i()(),t(374,"c-form-feedback",90),e(375,"You must agree before submitting."),i()(),t(376,"c-col",3)(377,"button",29),e(378," Submit form "),i(),t(379,"button",30),m("click",function(){return r(c),d(o.onReset3())}),e(380," Reset "),i()()()()()()()()}u&2&&(a(29),l("validated",o.customStylesValidated),a(6),l("valid",!0),a(6),l("valid",!0),a(9),l("valid",!1),a(6),l("valid",!1),a(10),l("valid",!1),a(6),l("valid",!1),a(7),l("valid",!1),a(23),l("validated",o.browserDefaultsValidated),a(6),l("valid",!0),a(6),l("valid",!0),a(9),l("valid",!1),a(6),l("valid",!1),a(10),l("valid",!1),a(6),l("valid",!1),a(7),l("valid",!1),a(39),l("valid",!0),a(),l("valid",!0),a(5),l("valid",!0),a(),l("valid",!0),a(8),l("valid",!1),a(),l("valid",!1),a(5),l("valid",!1),a(),l("valid",!1),a(5),l("valid",!1),a(5),l("valid",!1),a(5),l("valid",!1),a(),l("valid",!1),a(4),l("valid",!1),a(3),l("valid",!1),a(18),l("flush",!0),a(11),l("validated",!0),a(4),l("valid",!1),a(),l("valid",!1),a(6),l("valid",!1),a(10),l("valid",!1),a(12),l("valid",!1),a(4),l("valid",!1),a(22),l("validated",o.tooltipValidated),a(6),l("valid",!0),a(6),l("valid",!0),a(9),l("valid",!1),a(6),l("valid",!1),a(10),l("valid",!1),a(6),l("valid",!1),a(7),l("valid",!1))},dependencies:[D,q,S,y,x,B,M,R,U,O,I,N,G,E,L,C,F,V,T,w,k,g,h,b,_,P],encapsulation:2});var A=p;export{A as ValidationComponent};
