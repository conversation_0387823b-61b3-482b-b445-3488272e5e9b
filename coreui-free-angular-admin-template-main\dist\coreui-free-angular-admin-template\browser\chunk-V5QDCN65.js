import{b as T}from"./chunk-3MARWV4R.js";import{b as P,d as A,e as z,h as N,k as I,l as R,v as _,w as O}from"./chunk-LKW33VZJ.js";import{E as b,F as C,I as g,ba as y,ea as F,ia as h,n as f,na as v,oa as w,pa as D,qa as L,ra as M}from"./chunk-Y7QKHPW3.js";import{d as E}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as r,Hb as t,Ib as n,Jb as i,Va as l,eb as x,fc as e,gc as c,kc as p,lc as s,mc as u,uc as S}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var G=B=>({backgroundColor:B}),o=class o{constructor(){this.favoriteColor="#26ab3c"}};o.\u0275fac=function(a){return new(a||o)},o.\u0275cmp=x({type:o,selectors:[["app-form-controls"]],decls:333,vars:10,consts:[["xs","12"],[1,"mb-4"],["href","forms/form-control"],["cForm",""],[1,"mb-3"],["cLabel","","for","exampleFormControlInput1"],["cFormControl","","id","exampleFormControlInput1","placeholder","<EMAIL>","type","email"],["cLabel","","for","exampleFormControlTextarea1"],["cFormControl","","id","exampleFormControlTextarea1","rows","3"],[1,"text-body-secondary","small"],["href","forms/form-control#sizing"],["aria-label","lg input example","cFormControl","","placeholder","Large input","sizing","lg","type","text"],["aria-label","default input example","cFormControl","","placeholder","Default input","type","text"],["aria-label","sm input example","cFormControl","","placeholder","Small input","sizing","sm","type","text"],["href","forms/form-control#disabled"],["aria-label","Disabled input example","cFormControl","","disabled","","placeholder","Disabled input","type","text"],["aria-label","Disabled input example","cFormControl","","disabled","","placeholder","Disabled readonly input","readOnly","","type","text"],["href","forms/form-control#readonly"],["aria-label","readonly input example","cFormControl","","placeholder","Readonly input here...","readOnly","","type","text"],["cCol","","cLabel","col","for","staticEmail",3,"sm"],["sm","10"],["cFormControl","","id","staticEmail","readonly","","type","text","value","<EMAIL>",3,"plaintext"],["cCol","","cLabel","col","for","inputPassword",3,"sm"],["cFormControl","","id","inputPassword","type","password"],["cForm","","cRow","",3,"gutter"],["xs","auto"],["cLabel","","for","staticEmail2",1,"visually-hidden"],["cFormControl","","id","staticEmail2","readonly","","type","text","value","<EMAIL>",3,"plaintext"],["cLabel","","for","inputPassword2",1,"visually-hidden"],["cFormControl","","id","inputPassword2","placeholder","Password","type","password"],["cButton","","type","submit",1,"m-0"],["href","forms/form-control#file-input"],["cLabel","","for","formFile"],["cFormControl","","id","formFile","type","file"],["cLabel","","for","formFileMultiple"],["cFormControl","","id","formFileMultiple","multiple","","type","file"],["cLabel","","for","formFileDisabled"],["cFormControl","","disabled","","id","formFileDisabled","type","file"],["cLabel","","for","formFileSm"],["cFormControl","","id","formFileSm","sizing","sm","type","file"],["cLabel","","for","formFileLg"],["cFormControl","","id","formFileLg","sizing","lg","type","file"],["href","forms/form-control#color"],[1,"align-items-center","g-2"],["cLabel","col","for","exampleColorInput"],["cFormControl","","id","exampleColorInput","title","Choose your color","type","color",3,"ngModelChange","ngModel"],[1,"color-box","p-1","m-1",3,"ngStyle"]],template:function(a,m){a&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",0),e(3,`
    `),t(4,"c-card",1),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Form Control"),n(),e(10,`
      `),n(),e(11,`
      `),t(12,"c-card-body"),e(13,`
        `),t(14,"app-docs-example",2),e(15,`
          `),t(16,"form",3),e(17,`
            `),t(18,"div",4),e(19,`
              `),t(20,"label",5),e(21,"Email address"),n(),e(22,`
              `),i(23,"input",6),e(24,`
            `),n(),e(25,`
            `),t(26,"div",4),e(27,`
              `),t(28,"label",7),e(29,"Example textarea"),n(),e(30,`
              `),i(31,"textarea",8),e(32,`
            `),n(),e(33,`
          `),n(),e(34,`
        `),n(),e(35,`
      `),n(),e(36,`
    `),n(),e(37,`
  `),n(),e(38,`
  `),t(39,"c-col",0),e(40,`
    `),t(41,"c-card",1),e(42,`
      `),t(43,"c-card-header"),e(44,`
        `),t(45,"strong"),e(46,"Angular Form Control"),n(),e(47," "),t(48,"small"),e(49,"Sizing"),n(),e(50,`
      `),n(),e(51,`
      `),t(52,"c-card-body"),e(53,`
        `),t(54,"p",9),e(55,`
          Set heights using `),t(56,"code"),e(57,"sizing"),n(),e(58," property like "),t(59,"code"),e(60,'sizing="lg"'),n(),e(61,` and
          `),t(62,"code"),e(63,'sizing="sm"'),n(),e(64,`.
        `),n(),e(65,`
        `),t(66,"app-docs-example",10),e(67,`
          `),i(68,"input",11),e(69,`
          `),i(70,"br"),e(71,`
          `),i(72,"input",12),e(73,`
          `),i(74,"br"),e(75,`
          `),i(76,"input",13),e(77,`
        `),n(),e(78,`
      `),n(),e(79,`
    `),n(),e(80,`
  `),n(),e(81,`
  `),t(82,"c-col",0),e(83,`
    `),t(84,"c-card",1),e(85,`
      `),t(86,"c-card-header"),e(87,`
        `),t(88,"strong"),e(89,"Angular Form Control"),n(),e(90," "),t(91,"small"),e(92,"Disabled"),n(),e(93,`
      `),n(),e(94,`
      `),t(95,"c-card-body"),e(96,`
        `),t(97,"p",9),e(98,`
          Add the `),t(99,"code"),e(100,"disabled"),n(),e(101,` boolean attribute on an input to give it a grayed out
          appearance and remove pointer events.
        `),n(),e(102,`
        `),t(103,"app-docs-example",14),e(104,`
          `),i(105,"input",15),e(106,`
          `),i(107,"br"),e(108,`
          `),i(109,"input",16),e(110,`
          `),i(111,"br"),e(112,`
        `),n(),e(113,`
      `),n(),e(114,`
    `),n(),e(115,`
  `),n(),e(116,`
  `),t(117,"c-col",0),e(118,`
    `),t(119,"c-card",1),e(120,`
      `),t(121,"c-card-header"),e(122,`
        `),t(123,"strong"),e(124,"Angular Form Control"),n(),e(125," "),t(126,"small"),e(127,"Readonly"),n(),e(128,`
      `),n(),e(129,`
      `),t(130,"c-card-body"),e(131,`
        `),t(132,"p",9),e(133,`
          Add the `),t(134,"code"),e(135,"readOnly"),n(),e(136,` boolean attribute on an input to prevent modification of
          the input's value. Read-only inputs appear lighter (just like disabled inputs),
          but retain the standard cursor.
        `),n(),e(137,`
        `),t(138,"app-docs-example",17),e(139,`
          `),i(140,"input",18),e(141,`
        `),n(),e(142,`
      `),n(),e(143,`
    `),n(),e(144,`
  `),n(),e(145,`
  `),t(146,"c-col",0),e(147,`
    `),t(148,"c-card",1),e(149,`
      `),t(150,"c-card-header"),e(151,`
        `),t(152,"strong"),e(153,"Angular Form Control"),n(),e(154," "),t(155,"small"),e(156,"Readonly plain text"),n(),e(157,`
      `),n(),e(158,`
      `),t(159,"c-card-body"),e(160,`
        `),t(161,"p",9),e(162,`
          If you want to have `),t(163,"code"),e(164,"<input readonly>"),n(),e(165,` elements in your form styled
          as plain text, use the `),t(166,"code"),e(167,"plainText"),n(),e(168,` boolean property to remove the default
          form field styling and preserve the correct margin and padding.
        `),n(),e(169,`
        `),t(170,"app-docs-example",17),e(171,`
          `),t(172,"c-row",4),e(173,`
            `),t(174,"label",19),e(175,`
              Email
            `),n(),e(176,`
            `),t(177,"c-col",20),e(178,`
              `),i(179,"input",21),e(180,`
            `),n(),e(181,`
          `),n(),e(182,`
          `),t(183,"c-row",4),e(184,`
            `),t(185,"label",22),e(186,`
              Password
            `),n(),e(187,`
            `),t(188,"c-col",20),e(189,`
              `),i(190,"input",23),e(191,`
            `),n(),e(192,`
          `),n(),e(193,`
        `),n(),e(194,`
        `),t(195,"app-docs-example",17),e(196,`
          `),t(197,"form",24),e(198,`
            `),t(199,"c-col",25),e(200,`
              `),t(201,"label",26),e(202,`
                Email
              `),n(),e(203,`
              `),i(204,"input",27),e(205,`
            `),n(),e(206,`
            `),t(207,"c-col",25),e(208,`
              `),t(209,"label",28),e(210,`
                Password
              `),n(),e(211,`
              `),i(212,"input",29),e(213,`
            `),n(),e(214,`
            `),t(215,"c-col",25),e(216,`
              `),t(217,"button",30),e(218,`
                Confirm identity
              `),n(),e(219,`
            `),n(),e(220,`
          `),n(),e(221,`
        `),n(),e(222,`
      `),n(),e(223,`
    `),n(),e(224,`
  `),n(),e(225,`
  `),t(226,"c-col",0),e(227,`
    `),t(228,"c-card",1),e(229,`
      `),t(230,"c-card-header"),e(231,`
        `),t(232,"strong"),e(233,"Angular Form Control"),n(),e(234," "),t(235,"small"),e(236,"File input"),n(),e(237,`
      `),n(),e(238,`
      `),t(239,"c-card-body"),e(240,`
        `),t(241,"app-docs-example",31),e(242,`
          `),t(243,"div",4),e(244,`
            `),t(245,"label",32),e(246,"Default file input example"),n(),e(247,`
            `),i(248,"input",33),e(249,`
          `),n(),e(250,`
          `),t(251,"div",4),e(252,`
            `),t(253,"label",34),e(254,"Multiple files input example"),n(),e(255,`
            `),i(256,"input",35),e(257,`
          `),n(),e(258,`
          `),t(259,"div",4),e(260,`
            `),t(261,"label",36),e(262,"Disabled file input example"),n(),e(263,`
            `),i(264,"input",37),e(265,`
          `),n(),e(266,`
          `),t(267,"div",4),e(268,`
            `),t(269,"label",38),e(270,"Small file input example"),n(),e(271,`
            `),i(272,"input",39),e(273,`
          `),n(),e(274,`
          `),t(275,"div"),e(276,`
            `),t(277,"label",40),e(278,"Large file input example"),n(),e(279,`
            `),i(280,"input",41),e(281,`
          `),n(),e(282,`
        `),n(),e(283,`
      `),n(),e(284,`
    `),n(),e(285,`
  `),n(),e(286,`
  `),t(287,"c-col",0),e(288,`
    `),t(289,"c-card",1),e(290,`
      `),t(291,"c-card-header"),e(292,`
        `),t(293,"strong"),e(294,"Angular Form Control"),n(),e(295," "),t(296,"small"),e(297,"Color"),n(),e(298,`
      `),n(),e(299,`
      `),t(300,"c-card-body"),e(301,`
        `),t(302,"app-docs-example",42),e(303,`
          `),t(304,"c-row",43),e(305,`
            `),t(306,"c-col",25),e(307,`
              `),t(308,"label",44),e(309,"Color picker"),n(),e(310,`
            `),n(),e(311,`
            `),t(312,"c-col",25),e(313,`
              `),t(314,"input",45),u("ngModelChange",function(d){return s(m.favoriteColor,d)||(m.favoriteColor=d),d}),n(),e(315,`
            `),n(),e(316,`
            `),t(317,"c-col",25),e(318,`
              `),i(319,"div",46),e(320,`
            `),n(),e(321,`
            `),t(322,"c-col",25),e(323,`
              `),t(324,"strong"),e(325),n(),e(326,`
            `),n(),e(327,`
          `),n(),e(328,`
        `),n(),e(329,`
      `),n(),e(330,`
    `),n(),e(331,`
  `),n(),e(332,`
`),n()),a&2&&(l(174),r("sm",2),l(5),r("plaintext",!0),l(6),r("sm",2),l(12),r("gutter",3),l(7),r("plaintext",!0),l(110),p("ngModel",m.favoriteColor),l(5),r("ngStyle",S(8,G,m.favoriteColor)),l(6),c(m.favoriteColor))},dependencies:[L,w,b,g,C,T,O,R,P,A,z,_,I,N,y,h,F,f,E,D,M,v],styles:["[_nghost-%COMP%]   #exampleColorInput[_ngcontent-%COMP%]{min-width:2.5rem}[_nghost-%COMP%]   .color-box[_ngcontent-%COMP%]{min-width:2rem;min-height:2rem}"]});var k=o;export{k as FormControlsComponent};
