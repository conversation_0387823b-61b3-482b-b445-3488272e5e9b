import{b as g}from"./chunk-3MARWV4R.js";import{E as r,F as o,I as d,ea as x,ia as c,oa as s,qa as p}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as t,Ib as n,Jb as i,eb as m,fc as e}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var a=class a{constructor(){}};a.\u0275fac=function(l){return new(l||a)},a.\u0275cmp=m({type:a,selectors:[["app-ranges"]],decls:144,vars:0,consts:[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","forms/range"],["cLabel","","for","customRange1"],["cFormControl","","id","customRange1","type","range"],["href","forms/range#disabled"],["cLabel","","for","disabledRange"],["cFormControl","","disabled","","id","disabledRange","type","range"],["href","forms/range#min-and-max"],["cLabel","","for","customRange2"],["cFormControl","","id","customRange2","max","5","min","0","type","range","value","3"],["href","forms/range#steps"],["cLabel","","for","customRange3"],["cFormControl","","id","customRange3","max","5","min","0","step","0.5","type","range","value","3"]],template:function(l,u){l&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",0),e(3,`
    `),t(4,"c-card",1),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Range"),n(),e(10," "),i(11,"small"),e(12,`
      `),n(),e(13,`
      `),t(14,"c-card-body"),e(15,`
        `),t(16,"p",2),e(17,`
          Create custom `),t(18,"code"),e(19,'<input type="range">'),n(),e(20,` controls
          with `),t(21,"code"),e(22,'<input cFormControl type="range">'),n(),e(23,`.
        `),n(),e(24,`
        `),t(25,"app-docs-example",3),e(26,`
          `),t(27,"label",4),e(28,"Example range"),n(),e(29,`
          `),i(30,"input",5),e(31,`
        `),n(),e(32,`
      `),n(),e(33,`
    `),n(),e(34,`
  `),n(),e(35,`
  `),t(36,"c-col",0),e(37,`
    `),t(38,"c-card",1),e(39,`
      `),t(40,"c-card-header"),e(41,`
        `),t(42,"strong"),e(43,"Angular Range"),n(),e(44," "),t(45,"small"),e(46,"Disabled"),n(),e(47,`
      `),n(),e(48,`
      `),t(49,"c-card-body"),e(50,`
        `),t(51,"p",2),e(52,`
          Add the `),t(53,"code"),e(54,"disabled"),n(),e(55,` boolean attribute on an input to give it
          a grayed out appearance and remove pointer events.
        `),n(),e(56,`
        `),t(57,"app-docs-example",6),e(58,`
          `),t(59,"label",7),e(60,"Disabled range"),n(),e(61,`
          `),i(62,"input",8),e(63,`
        `),n(),e(64,`
      `),n(),e(65,`
    `),n(),e(66,`
  `),n(),e(67,`
  `),t(68,"c-col",0),e(69,`
    `),t(70,"c-card",1),e(71,`
      `),t(72,"c-card-header"),e(73,`
        `),t(74,"strong"),e(75,"Angular Range"),n(),e(76," "),t(77,"small"),e(78,"Min and max"),n(),e(79,`
      `),n(),e(80,`
      `),t(81,"c-card-body"),e(82,`
        `),t(83,"p",2),e(84,`
          Range inputs have implicit values for `),t(85,"code"),e(86,"min-0"),n(),e(87,` and
          `),t(88,"code"),e(89,"max-100"),n(),e(90,`, respectively.
          You may specify new values for those using the `),t(91,"code"),e(92,"min"),n(),e(93,` and
          `),t(94,"code"),e(95,"max"),n(),e(96,` attributes.
        `),n(),e(97,`
        `),t(98,"app-docs-example",9),e(99,`
          `),t(100,"label",10),e(101,"Example range"),n(),e(102,`
          `),i(103,"input",11),e(104,`
        `),n(),e(105,`
      `),n(),e(106,`
    `),n(),e(107,`
  `),n(),e(108,`
  `),t(109,"c-col",0),e(110,`
    `),t(111,"c-card",1),e(112,`
      `),t(113,"c-card-header"),e(114,`
        `),t(115,"strong"),e(116,"Angular Range"),n(),e(117," "),t(118,"small"),e(119,"Steps"),n(),e(120,`
      `),n(),e(121,`
      `),t(122,"c-card-body"),e(123,`
        `),t(124,"p",2),e(125,`
          By default, range inputs "snap" to integer values. To change
          this, you can specify a `),t(126,"code"),e(127,"step"),n(),e(128,` value. In the example below,
          we double the number of steps by using
          `),t(129,"code"),e(130,'step="0.5"'),n(),e(131,`.
        `),n(),e(132,`
        `),t(133,"app-docs-example",12),e(134,`
          `),t(135,"label",13),e(136,"Example range"),n(),e(137,`
          `),i(138,"input",14),e(139,`
        `),n(),e(140,`
      `),n(),e(141,`
    `),n(),e(142,`
  `),n(),e(143,`
`),n())},dependencies:[p,s,r,d,o,g,c,x],encapsulation:2});var E=a;export{E as RangesComponent};
