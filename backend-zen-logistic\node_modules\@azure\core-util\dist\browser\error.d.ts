/**
 * Typeguard for an error object shape (has name and message)
 * @param e - Something caught by a catch clause.
 */
export declare function isError(e: unknown): e is Error;
/**
 * Given what is thought to be an error object, return the message if possible.
 * If the message is missing, returns a stringified version of the input.
 * @param e - Something thrown from a try block
 * @returns The error message or a string of the input
 */
export declare function getErrorMessage(e: unknown): string;
//# sourceMappingURL=error.d.ts.map