import{a as A,b as $}from"./chunk-7UYBLBNW.js";import{a as R}from"./chunk-5AY4JT2K.js";import{a as S}from"./chunk-C7XZ7IYY.js";import{b as k}from"./chunk-3MARWV4R.js";import{E as T,F as E,H as b,I as V,Sa as D,f as x,g as W,oa as y,qa as C,qb as I,rb as G,tb as B,ub as L}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as t,Hb as l,Ib as o,Jb as n,Nc as w,Va as a,ac as _,ba as f,eb as u,fc as d,ja as p,kb as m}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var g=class g{constructor(){this.changeDetectorRef=f(w);this.datasets=[];this.labels=[];this.data=[];this.barOptions={maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{x:{display:!1},y:{display:!1}}};this.lineOptions={maintainAspectRatio:!1,elements:{line:{tension:.4},point:{radius:0}},plugins:{legend:{display:!1}},scales:{x:{display:!1},y:{display:!1}}};this.prepareLabels(),this.prepareDatasets(),this.prepareData()}get random(){return Math.floor(Math.random()*61+40)}get randomData(){let i=[];for(let c=0;c<15;c++)i.push(this.random);return i}get baseDatasets(){return[{data:this.randomData,barThickness:"flex",borderColor:"transparent",backgroundColor:"transparent",pointBackgroundColor:"transparent",pointHoverBorderColor:"transparent",borderWidth:1}]}ngAfterContentInit(){this.changeDetectorRef.detectChanges()}prepareData(){for(let i=0;i<6;i++)this.data.push({labels:this.labels,datasets:this.datasets[i]})}prepareLabels(){for(let i=0;i<15;i++)this.labels.push(this.getDayName(i))}prepareDatasets(){let i=[{backgroundColor:"danger"},{backgroundColor:"primary"},{backgroundColor:"secondary"},{borderColor:"danger",borderWidth:2},{borderColor:"success",borderWidth:2},{borderColor:"info",borderWidth:2}];for(let c=0;c<6;c++)this.datasets.push(this.getDataset(i[c]))}getDataset({backgroundColor:i="transparent",borderColor:c="transparent",borderWidth:r=1}){let s=this.baseDatasets;return s[0].backgroundColor=i!=="transparent"?S(`--cui-${i}`):i,s[0].borderColor=c!=="transparent"?S(`--cui-${c}`):c,s[0].pointBackgroundColor=S(`--cui-${c}`),s[0].borderWidth=r,s}getDayName(i=0){let c=navigator.language??navigator.userLanguage??navigator.systemLanguage??navigator.browserLanguage??"en-US",r=new Date(Date.UTC(2e3,1,0));return r.setDate(r.getDate()+i),r.toLocaleDateString(c,{weekday:"short"})}};g.\u0275fac=function(c){return new(c||g)},g.\u0275cmp=u({type:g,selectors:[["app-widgets-e"]],decls:19,vars:24,consts:[[1,"g-4"],["xl","2","lg","4","sm","6"],[3,"title","value"],["height","40","width","80",1,"mx-auto",3,"data","options"],["height","40","type","line","width","80",1,"mx-auto",3,"data","options"]],template:function(c,r){c&1&&(l(0,"c-row",0)(1,"c-col",1)(2,"c-widget-stat-e",2),n(3,"c-chart",3),o()(),l(4,"c-col",1)(5,"c-widget-stat-e",2),n(6,"c-chart",3),o()(),l(7,"c-col",1)(8,"c-widget-stat-e",2),n(9,"c-chart",3),o()(),l(10,"c-col",1)(11,"c-widget-stat-e",2),n(12,"c-chart",4),o()(),l(13,"c-col",1)(14,"c-widget-stat-e",2),n(15,"c-chart",4),o()(),l(16,"c-col",1)(17,"c-widget-stat-e",2),n(18,"c-chart",4),o()()()),c&2&&(a(2),t("title","title")("value","1,123"),a(),t("data",r.data[0])("options",r.barOptions),a(2),t("title","title")("value","1,123"),a(),t("data",r.data[1])("options",r.barOptions),a(2),t("title","title")("value","1,123"),a(),t("data",r.data[2])("options",r.barOptions),a(2),t("title","title")("value","1,123"),a(),t("data",r.data[3])("options",r.lineOptions),a(2),t("title","title")("value","1,123"),a(),t("data",r.data[4])("options",r.lineOptions),a(2),t("title","title")("value","1,123"),a(),t("data",r.data[5])("options",r.lineOptions))},dependencies:[C,y,B,R],encapsulation:2});var h=g;function U(e,i){e&1&&(p(),n(0,"svg",51))}function j(e,i){e&1&&(p(),n(0,"svg",52))}function H(e,i){e&1&&(p(),n(0,"svg",53))}function q(e,i){e&1&&(p(),n(0,"svg",54))}function J(e,i){e&1&&(p(),n(0,"svg",51))}function K(e,i){e&1&&(l(0,"a",55),d(1," View more "),p(),n(2,"svg",56),o())}function Q(e,i){e&1&&(p(),n(0,"svg",52))}function X(e,i){e&1&&(l(0,"a",55),d(1," View more "),p(),n(2,"svg",56),o())}function Y(e,i){e&1&&(p(),n(0,"svg",53))}function Z(e,i){e&1&&(l(0,"a",55),d(1," View more "),p(),n(2,"svg",56),o())}function ee(e,i){e&1&&(p(),n(0,"svg",54))}function te(e,i){e&1&&(l(0,"a",55),d(1," View more "),p(),n(2,"svg",56),o())}function ie(e,i){e&1&&(p(),n(0,"svg",51))}function ne(e,i){e&1&&(p(),n(0,"svg",52))}function ae(e,i){e&1&&(p(),n(0,"svg",53))}function le(e,i){e&1&&(p(),n(0,"svg",57))}function oe(e,i){e&1&&(p(),n(0,"svg",58))}function pe(e,i){e&1&&n(0,"c-progress",59),e&2&&t("value",75)}function me(e,i){e&1&&(p(),n(0,"svg",60))}function ce(e,i){e&1&&n(0,"c-progress",61),e&2&&t("value",75)}function re(e,i){e&1&&(p(),n(0,"svg",62))}function se(e,i){e&1&&n(0,"c-progress",63),e&2&&t("value",75)}function de(e,i){e&1&&(p(),n(0,"svg",64))}function ge(e,i){e&1&&n(0,"c-progress",65),e&2&&t("value",75)}function ve(e,i){e&1&&(p(),n(0,"svg",66))}function _e(e,i){e&1&&n(0,"c-progress",67),e&2&&t("value",75)}function fe(e,i){e&1&&(p(),n(0,"svg",58))}function ue(e,i){e&1&&n(0,"c-progress",59),e&2&&t("value",75)}function we(e,i){e&1&&(p(),n(0,"svg",60))}function ye(e,i){e&1&&n(0,"c-progress",61),e&2&&t("value",75)}function Ce(e,i){e&1&&(p(),n(0,"svg",62))}function Se(e,i){e&1&&n(0,"c-progress",63),e&2&&t("value",75)}function he(e,i){e&1&&(p(),n(0,"svg",64))}function xe(e,i){e&1&&n(0,"c-progress",65),e&2&&t("value",75)}function We(e,i){e&1&&(p(),n(0,"svg",66))}function Te(e,i){e&1&&n(0,"c-progress",67),e&2&&t("value",75)}function Ee(e,i){e&1&&(p(),n(0,"svg",68))}function be(e,i){e&1&&n(0,"c-progress",69),e&2&&t("value",75)}function Ve(e,i){e&1&&(p(),n(0,"svg",58))}function De(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}function Ie(e,i){e&1&&(p(),n(0,"svg",60))}function Ge(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}function Be(e,i){e&1&&(p(),n(0,"svg",62))}function Le(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}function Re(e,i){e&1&&(p(),n(0,"svg",64))}function Ae(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}function $e(e,i){e&1&&(p(),n(0,"svg",66))}function ke(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}function Pe(e,i){e&1&&(p(),n(0,"svg",68))}function Ne(e,i){e&1&&n(0,"c-progress",70),e&2&&t("value",75)}var v=class v{constructor(){this.changeDetectorRef=f(w)}ngAfterContentInit(){this.changeDetectorRef.detectChanges()}};v.\u0275fac=function(c){return new(c||v)},v.\u0275cmp=u({type:v,selectors:[["app-widgets"]],decls:159,vars:50,consts:[["widgetStatB1inv","cWidgetStatB"],["widgetStatB2inv","cWidgetStatB"],["widgetStatB3inv","cWidgetStatB"],["widgetStatB4inv","cWidgetStatB"],[1,"mb-4"],["href","components/widgets/#cwidgetstatsa"],["href","components/widgets/#cwidgetstatsb"],[1,"g-4"],["xl","3","md","6","sm","6"],["text","Lorem ipsum dolor sit amet enim.","value","89.9%",3,"title"],["thin","","color","success",1,"my-2",3,"value"],["text","Lorem ipsum dolor sit amet enim.","value","12.124",3,"title"],["thin","","color","info",1,"my-2",3,"value"],["text","Lorem ipsum dolor sit amet enim.","value","$98,111.00",3,"title"],["thin","","color","warning",1,"my-2",3,"value"],["text","Lorem ipsum dolor sit amet enim.","value","2 TB",3,"title"],["thin","","color","primary",1,"my-2",3,"value"],["color","success","inverse","","text","Lorem ipsum dolor sit amet enim.","value","89.9%",3,"title"],["thin","",1,"my-2",3,"white","value"],["color","info","inverse","","text","Lorem ipsum dolor sit amet enim.","value","12.124",3,"title"],["color","warning","inverse","","text","Lorem ipsum dolor sit amet enim.","value","$98,111.00",3,"title"],["color","primary","inverse","","text","Lorem ipsum dolor sit amet enim.","value","2 TB",3,"title"],["href","components/widgets/#cwidgetstatse"],["href","components/widgets/#cwidgetstatsf"],["color","primary","padding","","value","$1,999.50",3,"title"],["cTemplateId","widgetIconTemplate"],["color","info","padding","","value","$1,999.50",3,"title"],["color","warning","padding","","value","$1,999.50",3,"title"],["color","danger","padding","","value","$1,999.50",3,"title"],["cTemplateId","widgetFooterTemplate"],["color","primary","value","$1,999.50",3,"title"],["color","info","value","$1,999.50",3,"title"],["color","warning","value","$1,999.50",3,"title"],["color","danger","value","$1,999.50",3,"title"],["href","components/widgets/#cwidgetstatsd"],[3,"withCharts"],["href","components/widgets/#cwidgetstatsc"],["value","87,500",3,"title"],["cTemplateId","widgetProgressTemplate"],["value","385",3,"title"],["value","1238",3,"title"],["value","28%",3,"title"],["value","5:34:11",3,"title"],["xl","2","lg","4","sm","6"],["value","972",3,"title"],["color","info","inverse","","value","87,500",3,"title"],["color","success","inverse","","value","385",3,"title"],["color","warning","inverse","","value","1238",3,"title"],["color","primary","inverse","","value","28%",3,"title"],["color","danger","inverse","","value","5:34:11",3,"title"],["color","dark","inverse","","value","972",3,"title"],["cIcon","","name","cilSettings","size","xl","width","24"],["cIcon","","name","cilUser","size","xl","width","24"],["cIcon","","name","cilMoon","size","xl","width","24"],["cIcon","","name","cilBell","size","xl","width","24"],["href","https://coreui.io/","rel","noopener norefferer","target","_blank",1,"font-weight-bold","font-xs","text-body-secondary"],["cIcon","","name","cilArrowRight","width","16",1,"float-end"],["cIcon","","name","cilBell","size","xl","width","24",1,"rounded-5"],["cIcon","","height","36","name","cilPeople"],["thin","","color","info",1,"mt-3","mb-0",3,"value"],["cIcon","","height","36","name","cilUserFollow"],["thin","","color","success",1,"mt-3","mb-0",3,"value"],["cIcon","","height","36","name","cilBasket"],["thin","","color","warning",1,"mt-3","mb-0",3,"value"],["cIcon","","height","36","name","cilChartPie"],["thin","","color","primary",1,"mt-3","mb-0",3,"value"],["cIcon","","height","36","name","cilSpeedometer"],["thin","","color","danger",1,"mt-3","mb-0",3,"value"],["cIcon","","height","36","name","cilSpeech"],["thin","","color","dark",1,"mt-3","mb-0",3,"value"],["thin","","white","",1,"mt-3","mb-0",3,"value"]],template:function(c,r){if(c&1&&(l(0,"c-card",4)(1,"c-card-header"),d(2,"Widgets"),o(),l(3,"c-card-body")(4,"app-docs-example",5),n(5,"app-widgets-dropdown"),o(),l(6,"app-docs-example",6)(7,"c-row",7)(8,"c-col",8)(9,"c-widget-stat-b",9),n(10,"c-progress",10),o()(),l(11,"c-col",8)(12,"c-widget-stat-b",11),n(13,"c-progress",12),o()(),l(14,"c-col",8)(15,"c-widget-stat-b",13),n(16,"c-progress",14),o()(),l(17,"c-col",8)(18,"c-widget-stat-b",15),n(19,"c-progress",16),o()()()(),l(20,"app-docs-example",6)(21,"c-row",7)(22,"c-col",8)(23,"c-widget-stat-b",17,0),n(25,"c-progress",18),o()(),l(26,"c-col",8)(27,"c-widget-stat-b",19,1),n(29,"c-progress",18),o()(),l(30,"c-col",8)(31,"c-widget-stat-b",20,2),n(33,"c-progress",18),o()(),l(34,"c-col",8)(35,"c-widget-stat-b",21,3),n(37,"c-progress",18),o()()()(),l(38,"app-docs-example",22),n(39,"app-widgets-e"),o(),l(40,"app-docs-example",23)(41,"c-row",7)(42,"c-col",8)(43,"c-widget-stat-f",24),m(44,U,1,0,"ng-template",25),o()(),l(45,"c-col",8)(46,"c-widget-stat-f",26),m(47,j,1,0,"ng-template",25),o()(),l(48,"c-col",8)(49,"c-widget-stat-f",27),m(50,H,1,0,"ng-template",25),o()(),l(51,"c-col",8)(52,"c-widget-stat-f",28),m(53,q,1,0,"ng-template",25),o()()()(),l(54,"app-docs-example",23)(55,"c-row",7)(56,"c-col",8)(57,"c-widget-stat-f",24),m(58,J,1,0,"ng-template",25)(59,K,3,0,"ng-template",29),o()(),l(60,"c-col",8)(61,"c-widget-stat-f",26),m(62,Q,1,0,"ng-template",25)(63,X,3,0,"ng-template",29),o()(),l(64,"c-col",8)(65,"c-widget-stat-f",27),m(66,Y,1,0,"ng-template",25)(67,Z,3,0,"ng-template",29),o()(),l(68,"c-col",8)(69,"c-widget-stat-f",28),m(70,ee,1,0,"ng-template",25)(71,te,3,0,"ng-template",29),o()()()(),l(72,"app-docs-example",23)(73,"c-row",7)(74,"c-col",8)(75,"c-widget-stat-f",30),m(76,ie,1,0,"ng-template",25),o()(),l(77,"c-col",8)(78,"c-widget-stat-f",31),m(79,ne,1,0,"ng-template",25),o()(),l(80,"c-col",8)(81,"c-widget-stat-f",32),m(82,ae,1,0,"ng-template",25),o()(),l(83,"c-col",8)(84,"c-widget-stat-f",33),m(85,le,1,0,"ng-template",25),o()()()(),l(86,"app-docs-example",34),n(87,"app-widgets-brand"),o(),l(88,"app-docs-example",34),n(89,"app-widgets-brand",35),o(),l(90,"app-docs-example",36)(91,"c-card-group",4)(92,"c-widget-stat-c",37),m(93,oe,1,0,"ng-template",25)(94,pe,1,1,"ng-template",38),o(),l(95,"c-widget-stat-c",39),m(96,me,1,0,"ng-template",25)(97,ce,1,1,"ng-template",38),o(),l(98,"c-widget-stat-c",40),m(99,re,1,0,"ng-template",25)(100,se,1,1,"ng-template",38),o(),l(101,"c-widget-stat-c",41),m(102,de,1,0,"ng-template",25)(103,ge,1,1,"ng-template",38),o(),l(104,"c-widget-stat-c",42),m(105,ve,1,0,"ng-template",25)(106,_e,1,1,"ng-template",38),o()()(),l(107,"app-docs-example",36)(108,"c-row",7)(109,"c-col",43)(110,"c-widget-stat-c",37),m(111,fe,1,0,"ng-template",25)(112,ue,1,1,"ng-template",38),o()(),l(113,"c-col",43)(114,"c-widget-stat-c",39),m(115,we,1,0,"ng-template",25)(116,ye,1,1,"ng-template",38),o()(),l(117,"c-col",43)(118,"c-widget-stat-c",40),m(119,Ce,1,0,"ng-template",25)(120,Se,1,1,"ng-template",38),o()(),l(121,"c-col",43)(122,"c-widget-stat-c",41),m(123,he,1,0,"ng-template",25)(124,xe,1,1,"ng-template",38),o()(),l(125,"c-col",43)(126,"c-widget-stat-c",42),m(127,We,1,0,"ng-template",25)(128,Te,1,1,"ng-template",38),o()(),l(129,"c-col",43)(130,"c-widget-stat-c",44),m(131,Ee,1,0,"ng-template",25)(132,be,1,1,"ng-template",38),o()()()(),l(133,"app-docs-example",36)(134,"c-row",7)(135,"c-col",43)(136,"c-widget-stat-c",45),m(137,Ve,1,0,"ng-template",25)(138,De,1,1,"ng-template",38),o()(),l(139,"c-col",43)(140,"c-widget-stat-c",46),m(141,Ie,1,0,"ng-template",25)(142,Ge,1,1,"ng-template",38),o()(),l(143,"c-col",43)(144,"c-widget-stat-c",47),m(145,Be,1,0,"ng-template",25)(146,Le,1,1,"ng-template",38),o()(),l(147,"c-col",43)(148,"c-widget-stat-c",48),m(149,Re,1,0,"ng-template",25)(150,Ae,1,1,"ng-template",38),o()(),l(151,"c-col",43)(152,"c-widget-stat-c",49),m(153,$e,1,0,"ng-template",25)(154,ke,1,1,"ng-template",38),o()(),l(155,"c-col",43)(156,"c-widget-stat-c",50),m(157,Pe,1,0,"ng-template",25)(158,Ne,1,1,"ng-template",38),o()()()()()()),c&2){let s=_(24),N=_(28),O=_(32),M=_(36);a(9),t("title","Widget title"),a(),t("value",89.9),a(2),t("title","Widget title"),a(),t("value",89.9),a(2),t("title","Widget title"),a(),t("value",89.9),a(2),t("title","Widget title"),a(),t("value",89.9),a(4),t("title","Widget title"),a(2),t("white",s.inverse())("value",89.9),a(2),t("title","Widget title"),a(2),t("white",N.inverse())("value",89.9),a(2),t("title","Widget title"),a(2),t("white",O.inverse())("value",89.9),a(2),t("title","Widget title"),a(2),t("white",M.inverse())("value",89.9),a(6),t("title","Income"),a(3),t("title","Income"),a(3),t("title","Income"),a(3),t("title","Income"),a(5),t("title","Income"),a(4),t("title","Income"),a(4),t("title","Income"),a(4),t("title","Income"),a(6),t("title","Income"),a(3),t("title","Income"),a(3),t("title","Income"),a(3),t("title","Income"),a(5),t("withCharts",!0),a(3),t("title","Visitors"),a(3),t("title","New Clients"),a(3),t("title","Products sold"),a(3),t("title","Returning Visitors"),a(3),t("title","Avg. Time"),a(6),t("title","Visitors"),a(4),t("title","New Clients"),a(4),t("title","Products sold"),a(4),t("title","Returning Visitors"),a(4),t("title","Avg. Time"),a(4),t("title","Comments"),a(6),t("title","Visitors"),a(4),t("title","New Clients"),a(4),t("title","Products sold"),a(4),t("title","Returning Visitors"),a(4),t("title","Avg. Time"),a(4),t("title","Comments")}},dependencies:[T,V,E,k,$,C,y,I,D,h,L,W,x,A,b,G],encapsulation:2});var P=v;export{P as WidgetsComponent};
