{"version": 3, "file": "applicationTokenCertificateCredentials.js", "sourceRoot": "", "sources": ["../../../lib/credentials/applicationTokenCertificateCredentials.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,2BAAkC;AAClC,mCAAoC;AACpC,uFAAoF;AAEpF,yDAAqE;AAIrE,MAAa,sCAAuC,SAAQ,iEAA+B;IAIzF;;;;;;;;;;;;;OAaG;IACH,YACE,QAAgB,EAChB,MAAc,EACd,WAAmB,EACnB,UAAkB,EAClB,aAA6B,EAC7B,WAAyB,EACzB,UAAuB;QAEvB,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC7D,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,UAAU,IAAI,OAAO,UAAU,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC3D,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC3D;QACD,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAEhE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACU,QAAQ;;YACnB,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;aACvC;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,6BAAa,CAAC,kBAAkB,CAAC,EAAE;oBAC9D,MAAM,KAAK,CAAC;iBACb;gBAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;oBACrD,IAAI,CAAC,WAAW,CAAC,iCAAiC,CAChD,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,UAAU,EACf,CAAC,KAAU,EAAE,aAA4C,EAAE,EAAE;wBAC3D,IAAI,KAAK,EAAE;4BACT,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;yBACtB;wBACD,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,gBAAgB,EAAE;4BACzD,OAAO,MAAM,CAAC,aAAa,CAAC,CAAC;yBAC9B;wBACD,OAAO,OAAO,CAAC,aAA8B,CAAC,CAAC;oBACjD,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,MAAM,CAClB,QAAgB,EAChB,2BAAmC,EACnC,MAAc,EACd,OAAqC;QAErC,IAAI,CAAC,2BAA2B,IAAI,OAAO,2BAA2B,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC7F,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;SAC9E;QACD,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;YACzD,2BAA2B,GAAG,iBAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;SACjF;QACD,MAAM,kBAAkB,GAAG,iGAAiG,CAAC;QAC7H,MAAM,SAAS,GAAG,2BAA2B,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QACxE,MAAM,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE;YACnB,MAAM,IAAI,KAAK,CACb,qGAAqG,CACtG,CAAC;SACH;QACD,MAAM,UAAU,GAAG,mBAAU,CAAC,MAAM,CAAC;aAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;aAC7C,MAAM,CAAC,KAAK,CAAC,CAAC;QACjB,OAAO,IAAI,sCAAsC,CAC/C,QAAQ,EACR,MAAM,EACN,2BAA2B,EAC3B,UAAU,EACV,OAAO,CAAC,aAAa,EACrB,OAAO,CAAC,WAAW,EACnB,OAAO,CAAC,UAAU,CACnB,CAAC;IACJ,CAAC;CACF;AAvHD,wFAuHC"}