{"version": 3, "file": "msiTokenCredentials.js", "sourceRoot": "", "sources": ["../../../lib/credentials/msiTokenCredentials.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,kDAA0F;AAE1F,yDAAsD;AAkCtD;;;GAGG;AACH,MAAsB,mBAAmB;IAYvC;;;;;;;OAOG;IACH,YAAY,OAAmB;QAC7B,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAE,CAAC;QAE3B,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACrB,OAAO,CAAC,QAAQ,GAAG,6BAAa,CAAC,yBAAyB,CAAC;SAC5D;aAAM,IAAI,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACzD,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;SAC5C;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,8BAAiB,EAAE,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,kBAAkB,CAAC,IAAY;QAC7B,2KAA2K;QAC3K,iDAAiD;QACjD,qHAAqH;QACrH,yCAAyC;QACzC,mMAAmM;QACnM,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACpC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,cAAc,CAAC,CAAC;QAClC,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;QAChD,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC;QAChC,IAAI,UAAU,CAAC,eAAe,CAAC,EAAE;YAC/B,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC;YACtD,OAAO,UAAU,CAAC,eAAe,CAAC,CAAC;SACpC;QACD,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,OAAO,UAAU,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;gBAChD,yCAAyC;gBACzC,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;aAC/D;YACD,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,OAAO,UAAU,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;gBAChD,yCAAyC;gBACzC,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;aAC/D;YACD,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE;YAC5B,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;YAChD,IAAI,OAAO,UAAU,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;gBAChD,yDAAyD;gBACzD,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;oBACpF,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;iBACzE;qBAAM;oBACL,yCAAyC;oBACzC,UAAU,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;iBACzE;aACF;YACD,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC;SACjC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAWD;;;;;OAKG;IACU,WAAW,CAAC,WAAwB;;YAC/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5C,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,sBAAS,CAAC,eAAe,CAAC,aAAa,EACvC,GAAG,aAAa,CAAC,SAAS,IAAI,aAAa,CAAC,WAAW,EAAE,CAC1D,CAAC;YACF,OAAO,WAAW,CAAC;QACrB,CAAC;KAAA;CACF;AA7GD,kDA6GC"}