import{a as ye,b as fe}from"./chunk-7UYBLBNW.js";import{a as be}from"./chunk-5AY4JT2K.js";import{a as g}from"./chunk-C7XZ7IYY.js";import{b as le,d as se,e as ce,f as me,i as de,l as pe,m as ue,n as ge,p as he,w as ve}from"./chunk-LKW33VZJ.js";import{B as K,E as Q,F as Z,G as X,I as ee,Sa as re,ca as te,cb as oe,f as Y,n as q,oa as ae,qa as ie,ra as ne,w as $}from"./chunk-Y7QKHPW3.js";import{d as z}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Dc as W,Eb as V,Fb as U,Gb as m,Hb as e,Ib as t,Jb as i,Qb as x,Va as o,Y as _,Za as N,ba as C,eb as A,fc as a,gc as F,hc as w,ic as H,ja as s,ka as c,ma as B,na as G,qc as b,ra as J,rc as L,tc as j}from"./chunk-J5YWIVYY.js";import{a as p,e as u,f as v}from"./chunk-L3UST63Y.js";var h=class h{constructor(){this.mainChart={type:"line"};this.initMainChart()}random(r,n){return Math.floor(Math.random()*(n-r+1)+r)}initMainChart(r="Month"){let n=g("--cui-success")??"#4dbd74",l=g("--cui-info")??"#20a8d8",S=`rgba(${g("--cui-info-rgb")}, .1)`,E=g("--cui-danger")??"#f86c6b";this.mainChart.elements=r==="Month"?12:27,this.mainChart.Data1=[],this.mainChart.Data2=[],this.mainChart.Data3=[];for(let d=0;d<=this.mainChart.elements;d++)this.mainChart.Data1.push(this.random(50,240)),this.mainChart.Data2.push(this.random(20,160)),this.mainChart.Data3.push(65);let I=[];if(r==="Month")I=["January","February","March","April","May","June","July","August","September","October","November","December"];else{let d=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];I=d.concat(d,d,d)}let P=[{backgroundColor:S,borderColor:l,pointHoverBackgroundColor:l,borderWidth:2,fill:!0},{backgroundColor:"transparent",borderColor:n||"#4dbd74",pointHoverBackgroundColor:"#fff"},{backgroundColor:"transparent",borderColor:E||"#f86c6b",pointHoverBackgroundColor:E,borderWidth:1,borderDash:[8,5]}],Ce=[p({data:this.mainChart.Data1,label:"Current"},P[0]),p({data:this.mainChart.Data2,label:"Previous"},P[1]),p({data:this.mainChart.Data3,label:"BEP"},P[2])],xe={legend:{display:!1},tooltip:{callbacks:{labelColor:d=>({backgroundColor:d.dataset.borderColor})}}},Ee=this.getScales(),we={maintainAspectRatio:!1,plugins:xe,scales:Ee,elements:{line:{tension:.4},point:{radius:0,hitRadius:10,hoverRadius:4,hoverBorderWidth:3}}};this.mainChart.type="line",this.mainChart.options=we,this.mainChart.data={datasets:Ce,labels:I}}getScales(){let r=g("--cui-border-color-translucent"),n=g("--cui-body-color");return{x:{grid:{color:r,drawOnChartArea:!1},ticks:{color:n}},y:{border:{color:r},grid:{color:r},max:250,beginAtZero:!0,ticks:{color:n,maxTicksLimit:5,stepSize:Math.ceil(250/5)}}}}};h.\u0275fac=function(n){return new(n||h)},h.\u0275prov=_({token:h,factory:h.\u0275fac,providedIn:"any"});var T=h;var De=()=>({"marginTop.px":40}),Me=(R,r)=>r.name;function ke(R,r){if(R&1&&(e(0,"tr")(1,"td",83),i(2,"c-avatar",84),t(),e(3,"td")(4,"div"),a(5),t(),e(6,"div",85)(7,"span"),a(8),t()()(),e(9,"td",83),s(),i(10,"svg",86),t(),c(),e(11,"td")(12,"div",87)(13,"div",88)(14,"strong"),a(15),t()(),e(16,"div",89)(17,"small",17),a(18),t()()(),i(19,"c-progress",90),t(),e(20,"td",83),s(),i(21,"svg",91),t(),c(),e(22,"td")(23,"div",3),a(24,"Last login"),t(),e(25,"div",92),a(26),t()()()),R&2){let n=r.$implicit;o(2),m("src",b(n.avatar))("status",b(n.status))("size","md"),o(3),F(n.name),o(3),H(" ",n.state," | Registered: ",n.registered," "),o(2),m("id",b(n.country+n.name))("name",L("cif",n.country))("title",b(n.country)),o(5),w("",n.usage,"%"),o(3),w(" ",n.period," "),o(),m("color",b(n.color))("value",n.usage),o(2),m("name",L("cibCc",n.payment)),o(5),F(n.activity)}}var D,M,k,f,O,y=class y{constructor(){v(this,D,C(G));v(this,M,C(B));v(this,k,C(N));v(this,f,C(T));this.users=[{name:"Yiorgos Avraamu",state:"New",registered:"Jan 1, 2021",country:"Us",usage:50,period:"Jun 11, 2021 - Jul 10, 2021",payment:"Mastercard",activity:"10 sec ago",avatar:"./assets/images/avatars/1.jpg",status:"success",color:"success"},{name:"Avram Tarasios",state:"Recurring ",registered:"Jan 1, 2021",country:"Br",usage:10,period:"Jun 11, 2021 - Jul 10, 2021",payment:"Visa",activity:"5 minutes ago",avatar:"./assets/images/avatars/2.jpg",status:"danger",color:"info"},{name:"Quintin Ed",state:"New",registered:"Jan 1, 2021",country:"In",usage:74,period:"Jun 11, 2021 - Jul 10, 2021",payment:"Stripe",activity:"1 hour ago",avatar:"./assets/images/avatars/3.jpg",status:"warning",color:"warning"},{name:"En\xE9as Kwadwo",state:"Sleep",registered:"Jan 1, 2021",country:"Fr",usage:98,period:"Jun 11, 2021 - Jul 10, 2021",payment:"Paypal",activity:"Last month",avatar:"./assets/images/avatars/4.jpg",status:"secondary",color:"danger"},{name:"Agapetus Tade\xE1\u0161",state:"New",registered:"Jan 1, 2021",country:"Es",usage:22,period:"Jun 11, 2021 - Jul 10, 2021",payment:"ApplePay",activity:"Last week",avatar:"./assets/images/avatars/5.jpg",status:"success",color:"primary"},{name:"Friderik D\xE1vid",state:"New",registered:"Jan 1, 2021",country:"Pl",usage:43,period:"Jun 11, 2021 - Jul 10, 2021",payment:"Amex",activity:"Yesterday",avatar:"./assets/images/avatars/6.jpg",status:"info",color:"dark"}];this.mainChart={type:"line"};this.mainChartRef=J(void 0);v(this,O,W(()=>{this.mainChartRef()&&this.setChartStyles()}));this.chart=[];this.trafficRadioGroup=new me({trafficRadio:new de("Month")})}ngOnInit(){this.initCharts(),this.updateChartOnColorModeChange()}initCharts(){this.mainChartRef()?.stop(),this.mainChart=u(this,f).mainChart}setTrafficPeriod(r){this.trafficRadioGroup.setValue({trafficRadio:r}),u(this,f).initMainChart(r),this.initCharts()}handleChartRef(r){r&&this.mainChartRef.set(r)}updateChartOnColorModeChange(){let r=u(this,k).listen(u(this,M).documentElement,"ColorSchemeChange",()=>{this.setChartStyles()});u(this,D).onDestroy(()=>{r()})}setChartStyles(){this.mainChartRef()&&setTimeout(()=>{let r=p({},this.mainChart.options),n=u(this,f).getScales();this.mainChartRef().options.scales=p(p({},r.scales),n),this.mainChartRef().update()})}};D=new WeakMap,M=new WeakMap,k=new WeakMap,f=new WeakMap,O=new WeakMap,y.\u0275fac=function(n){return new(n||y)},y.\u0275cmp=A({type:y,selectors:[["ng-component"]],decls:254,vars:17,consts:[[1,"my-4"],["sm","5"],["id","traffic",1,"card-title","mb-0"],[1,"small","text-body-secondary"],["sm","7",1,"d-none","d-md-block"],["cButton","","color","primary","aria-label","Download",1,"float-end"],["cIcon","","name","cilCloudDownload"],[3,"formGroup"],["role","group",1,"float-end","me-3"],["formControlName","trafficRadio","type","radio","value","Day","id","dayRadio",1,"btn-check"],["cButton","","cFormCheckLabel","","color","secondary","variant","outline","for","dayRadio",3,"click"],["formControlName","trafficRadio","type","radio","value","Month","id","radioMonth",1,"btn-check"],["cButton","","cFormCheckLabel","","color","secondary","variant","outline","for","radioMonth",3,"click"],["formControlName","trafficRadio","type","radio","value","Year","id","radioYear",1,"btn-check"],["cButton","","cFormCheckLabel","","color","secondary","variant","outline","for","radioYear",3,"click"],[3,"chartRef","data","height","ngStyle","options","type"],[1,"text-center","mb-2",3,"xl","lg","sm","xs","gutter"],[1,"text-body-secondary"],["thin","","color","success","value","40","aria-label","User visits",1,"mt-2"],[1,"fw-semibold","text-truncate"],["thin","","color","info","value","20","aria-label","Unique users",1,"mt-2"],["thin","","color","warning","value","60","aria-label","Page views",1,"mt-2"],["thin","","color","danger","value","80","aria-label","New users",1,"mt-2"],[1,"d-none","d-xl-block"],["thin","","value","40","aria-label","Bounce rate",1,"mt-2"],[3,"withCharts"],[1,"mt-4"],["xs",""],[1,"mb-4"],["md","6","xl","6","xs","12"],["xs","6"],[1,"border-start","border-start-4","border-start-info","py-1","px-3","mb-3"],[1,"text-body-secondary","text-truncate","small"],[1,"fs-5","fw-semibold"],[1,"border-start","border-start-4","border-start-danger","py-1","px-3","mb-3"],[1,"mt-0"],[1,"progress-group","mb-4"],[1,"progress-group-prepend"],[1,"text-body-secondary","small"],[1,"progress-group-bars"],["thin","","color","info","value","34","aria-label","Monday new clients"],["thin","","color","danger","value","78","aria-label","Monday recurring clients"],["thin","","color","info","value","56","aria-label","Tuesday new clients"],["thin","","color","danger","value","94","aria-label","Tuesday recurring clients"],["thin","","color","info","value","12","aria-label","Wednesday new clients"],["thin","","color","danger","value","67","aria-label","Wednesday recurring clients"],["thin","","color","info","value","43","aria-label","Thursday new clients"],["thin","","color","danger","value","91","aria-label","Thursday recurring clients"],["thin","","color","info","value","22","aria-label","Friday new clients"],["thin","","color","danger","value","73","aria-label","Friday recurring clients"],["thin","","color","info","value","53","aria-label","Saturday new clients"],["thin","","color","danger","value","82","aria-label","Saturday recurring clients"],["thin","","color","info","value","9","aria-label","Sunday new clients"],["thin","","color","danger","value","69","aria-label","Sunday recurring clients"],[1,"legend","text-center","d-none","d-md-block"],[1,"badge","badge-pill","badge-sm","bg-info"],[1,"badge","badge-pill","badge-sm","bg-danger"],[1,"border-start","border-start-4","border-start-warning","py-1","px-3","mb-3"],[1,"border-start","border-start-4","border-start-success","py-1","px-3","mb-3"],[1,"progress-group-header"],["cIcon","","name","cilUser",1,"icon","icon-lg","me-2"],[1,"ms-auto","font-semibold"],["thin","","color","warning","value","43","aria-label","Male users"],[1,"progress-group","mb-5"],["cIcon","","name","cilUserFemale",1,"icon","icon-lg","me-2"],["thin","","color","warning","value","37","aria-label","Feale users"],[1,"progress-group"],["cIcon","","name","cibGoogle",1,"icon","icon-lg","me-2"],["thin","","color","success","value","56","aria-label","Organic search"],["cIcon","","name","cibFacebook",1,"icon","icon-lg","me-2"],["thin","","color","success","value","15","aria-label","Facebook"],["cIcon","","name","cibTwitter",1,"icon","icon-lg","me-2"],["thin","","color","success","value","11","aria-label","Twitter"],["cIcon","","name","cibLinkedin",1,"icon","icon-lg","me-2"],["thin","","color","success","value","8","aria-label","LinkedIn"],[1,"divider","d-flex","justify-content-center"],["cButton","","color","transparent","size","sm","type","button","aria-label","Options",1,"text-muted","btn-link"],["cIcon","","name","cil-options"],["align","middle","cTable","",1,"mb-0","border",3,"hover","responsive","striped"],[1,"text-nowrap","text-truncate"],[1,"bg-body-tertiary","text-center"],["cIcon","","name","cilPeople"],[1,"bg-body-tertiary"],[1,"text-center"],[3,"size","src","status"],[1,"small","text-body-secondary","text-nowrap"],["cIcon","","size","xl",3,"id","name","title"],[1,"d-flex","justify-content-between"],[1,"float-start"],[1,"float-end","ms-1","text-nowrap"],["thin","","aria-label","Usage",3,"value","color"],["cIcon","","size","xl",3,"name"],[1,"fw-semibold","text-nowrap"]],template:function(n,l){n&1&&(i(0,"app-widgets-dropdown"),e(1,"c-card",0)(2,"c-card-body")(3,"c-row")(4,"c-col",1)(5,"h4",2),a(6,"Traffic"),t(),e(7,"div",3),a(8,"January - December 2023"),t()(),e(9,"c-col",4)(10,"button",5),s(),i(11,"svg",6),t(),c(),e(12,"form",7)(13,"c-button-group",8),i(14,"input",9),e(15,"label",10),x("click",function(){return l.setTrafficPeriod("Day")}),a(16," Day "),t(),i(17,"input",11),e(18,"label",12),x("click",function(){return l.setTrafficPeriod("Month")}),a(19," Month "),t(),i(20,"input",13),e(21,"label",14),x("click",function(){return l.setTrafficPeriod("Year")}),a(22," Year "),t()()()()(),e(23,"c-chart",15),x("chartRef",function(E){return l.handleChartRef(E)}),a(24," Main chart "),t()(),e(25,"c-card-footer")(26,"c-row",16)(27,"c-col")(28,"div",17),a(29,"Visits"),t(),e(30,"strong"),a(31,"29.703 Users (40%)"),t(),i(32,"c-progress",18),t(),e(33,"c-col")(34,"div",17),a(35,"Unique"),t(),e(36,"div",19),a(37,"24.093 Users (20%)"),t(),i(38,"c-progress",20),t(),e(39,"c-col")(40,"div",17),a(41,"Page views"),t(),e(42,"div",19),a(43,"78.706 Views (60%)"),t(),i(44,"c-progress",21),t(),e(45,"c-col")(46,"div",17),a(47,"New Users"),t(),e(48,"div",19),a(49,"22.123 Users (80%)"),t(),i(50,"c-progress",22),t(),e(51,"c-col",23)(52,"div",17),a(53,"Bounce Rate"),t(),e(54,"div",19),a(55,"Average Rate (40.15%)"),t(),i(56,"c-progress",24),t()()()(),i(57,"app-widgets-brand",25),e(58,"c-row",26)(59,"c-col",27)(60,"c-card",28)(61,"c-card-header"),a(62),t(),e(63,"c-card-body")(64,"c-row")(65,"c-col",29)(66,"c-row")(67,"c-col",30)(68,"div",31)(69,"div",32),a(70,"New Clients"),t(),e(71,"div",33),a(72,"9,123"),t()()(),e(73,"c-col",30)(74,"div",34)(75,"div",32),a(76," Recurring Clients "),t(),e(77,"div",33),a(78,"22,643"),t()()()(),i(79,"hr",35),e(80,"div",36)(81,"div",37)(82,"span",38),a(83,"Monday"),t()(),e(84,"div",39),i(85,"c-progress",40)(86,"c-progress",41),t()(),e(87,"div",36)(88,"div",37)(89,"span",38),a(90,"Tuesday"),t()(),e(91,"div",39),i(92,"c-progress",42)(93,"c-progress",43),t()(),e(94,"div",36)(95,"div",37)(96,"span",38),a(97,"Wednesday"),t()(),e(98,"div",39),i(99,"c-progress",44)(100,"c-progress",45),t()(),e(101,"div",36)(102,"div",37)(103,"span",38),a(104,"Thursday"),t()(),e(105,"div",39),i(106,"c-progress",46)(107,"c-progress",47),t()(),e(108,"div",36)(109,"div",37)(110,"span",38),a(111,"Friday"),t()(),e(112,"div",39),i(113,"c-progress",48)(114,"c-progress",49),t()(),e(115,"div",36)(116,"div",37)(117,"span",38),a(118,"Saturday"),t()(),e(119,"div",39),i(120,"c-progress",50)(121,"c-progress",51),t()(),e(122,"div",36)(123,"div",37)(124,"span",38),a(125,"Sunday"),t()(),e(126,"div",39),i(127,"c-progress",52)(128,"c-progress",53),t()(),e(129,"div",54),a(130,`
              `),e(131,"small"),a(132,`
                `),e(133,"sup"),a(134,`
                  `),e(135,"span",55),a(136,"\xA0"),t(),a(137,`
                `),t(),a(138,`
                `),e(139,"span"),a(140,"New clients"),t(),a(141,`
                \xA0\xA0
                `),e(142,"sup"),a(143,`
                  `),e(144,"span",56),a(145,"\xA0"),t(),a(146,`
                `),t(),a(147,`
                `),e(148,"span"),a(149,"Recurring clients"),t(),a(150,`
              `),t(),a(151,`
            `),t()(),e(152,"c-col",29)(153,"c-row")(154,"c-col",30)(155,"div",57)(156,"div",32),a(157,"Page views"),t(),e(158,"div",33),a(159,"78,623"),t()()(),e(160,"c-col",30)(161,"div",58)(162,"div",32),a(163,"Organic"),t(),e(164,"div",33),a(165,"49,123"),t()()()(),i(166,"hr",35),e(167,"div",36)(168,"div",59),s(),i(169,"svg",60),c(),e(170,"span"),a(171,"Male"),t(),e(172,"span",61),a(173,"43%"),t()(),e(174,"div",39),i(175,"c-progress",62),t()(),e(176,"div",63)(177,"div",59),s(),i(178,"svg",64),c(),e(179,"span"),a(180,"Female"),t(),e(181,"span",61),a(182,"37%"),t()(),e(183,"div",39),i(184,"c-progress",65),t()(),e(185,"div",66)(186,"div",59),s(),i(187,"svg",67),c(),e(188,"span"),a(189,"Organic Search"),t(),e(190,"span",61),a(191," 191,235 "),e(192,"span",38),a(193,"(56%)"),t()()(),e(194,"div",39),i(195,"c-progress",68),t()(),e(196,"div",66)(197,"div",59),s(),i(198,"svg",69),c(),e(199,"span"),a(200,"Facebook"),t(),e(201,"span",61),a(202," 51,223 "),e(203,"span",38),a(204,"(15%)"),t()()(),e(205,"div",39),i(206,"c-progress",70),t()(),e(207,"div",66)(208,"div",59),s(),i(209,"svg",71),c(),e(210,"span"),a(211,"Twitter"),t(),e(212,"span",61),a(213," 37,564 "),e(214,"span",38),a(215,"(11%)"),t()()(),e(216,"div",39),i(217,"c-progress",72),t()(),e(218,"div",66)(219,"div",59),s(),i(220,"svg",73),c(),e(221,"span"),a(222,"LinkedIn"),t(),e(223,"span",61),a(224," 27,319 "),e(225,"span",38),a(226,"(8%)"),t()()(),e(227,"div",39),i(228,"c-progress",74),t()(),e(229,"div",75)(230,"button",76),s(),i(231,"svg",77),t()()()()()()()(),c(),e(232,"c-row")(233,"c-col",27)(234,"c-card",28)(235,"c-card-body")(236,"table",78)(237,"thead",79)(238,"tr")(239,"th",80),s(),i(240,"svg",81),t(),c(),e(241,"th",82),a(242,"User"),t(),e(243,"th",80),a(244,"Country"),t(),e(245,"th",82),a(246,"Usage"),t(),e(247,"th",80),a(248,"Payment Method"),t(),e(249,"th",82),a(250,"Activity"),t()()(),e(251,"tbody"),V(252,ke,27,22,"tr",null,Me),t()()()()()()),n&2&&(o(12),m("formGroup",l.trafficRadioGroup),o(11),m("data",l.mainChart.data)("height",300)("ngStyle",j(16,De))("options",l.mainChart.options)("type",l.mainChart.type),o(3),m("xl",5)("lg",4)("sm",2)("xs",1)("gutter",4),o(31),m("withCharts",!0),o(5),w("Traffic ","&"," Sales"),o(174),m("hover",!0)("responsive",!0)("striped",!0),o(16),U(l.users))},dependencies:[fe,Q,Z,ie,ae,q,Y,ve,pe,le,ue,se,ce,ge,he,K,te,be,z,X,ne,re,ye,ee,oe,$],styles:["[_nghost-%COMP%]   .legend[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:x-small}"]});var Se=y;export{Se as DashboardComponent};
