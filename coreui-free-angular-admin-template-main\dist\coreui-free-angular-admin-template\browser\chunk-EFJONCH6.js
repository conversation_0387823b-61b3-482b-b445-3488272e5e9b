import{b as T}from"./chunk-3MARWV4R.js";import{A as f,E as h,F as $,I as g,oa as B,qa as I,y as E,z as y}from"./chunk-Y7QKHPW3.js";import{b as C}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Db as d,Eb as u,Fb as s,Gb as c,Hb as t,Ib as n,Jb as b,Va as a,eb as S,fc as e,gc as v,hc as x,uc as _}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var D=m=>({mark:m});function F(m,i){if(m&1&&(e(0,`
              `),t(1,"c-breadcrumb-item",12),e(2),n(),e(3,`
            `)),m&2){let r=i.$implicit,l=i.$index,o=i.$count;a(),c("active",l===o-1)("url",r.url),a(),x(`
                `,r.label,`
              `)}}function w(m,i){if(m&1&&(e(0,`
              `),t(1,"c-breadcrumb-item",12),e(2),n(),e(3,`
            `)),m&2){let r=i.$implicit,l=i.$index,o=i.$count;a(),c("active",l===o-1)("url",r.url),a(),x(`
                `,r.label,`
              `)}}function H(m,i){if(m&1&&(e(0,`
              `),t(1,"c-breadcrumb-item",12),e(2),n(),e(3,`
            `)),m&2){let r=i.$implicit,l=i.$index,o=i.$count;a(),c("active",l===o-1)("url",r.url),a(),x(`
                `,r.label,`
              `)}}function U(m,i){if(m&1&&(e(0,`
              `),t(1,"c-breadcrumb-item",12),e(2),n(),e(3,`
            `)),m&2){let r=i.$implicit,l=i.$index,o=i.$count;a(),c("active",l===o-1)("url",r.url),a(),x(`
                `,r.label,`
              `)}}function L(m,i){if(m&1&&(e(0,`
              `),t(1,"c-breadcrumb-item",12),e(2,`
                `),t(3,"span",13),e(4),n(),e(5,`
              `),n(),e(6,`
            `)),m&2){let r=i.$implicit,l=i.$index,o=i.$count;a(),c("active",l===o-1)("url",r.url),a(2),c("ngClass",_(4,D,l===o-1)),a(),v(r.label)}}var p=class p{constructor(){this.items=[]}ngOnInit(){this.items=[{label:"Home",url:"/",attributes:{title:"Home"}},{label:"Library",url:"/"},{label:"Data",url:"/dashboard/"},{label:"CoreUI",url:"/"}],setTimeout(()=>{this.items=[{label:"CoreUI",url:"/"},{label:"Data",url:"/dashboard/"},{label:"Library",url:"/"},{label:"Home",url:"/",attributes:{title:"Home"}}]},5e3)}};p.\u0275fac=function(r){return new(r||p)},p.\u0275cmp=S({type:p,selectors:[["ng-component"]],decls:104,vars:2,consts:[["xs","12",1,"mb-4"],[1,"text-body-secondary","small"],["href","https://developer.mozilla.org/en-US/docs/Web/CSS/::before"],["href","https://developer.mozilla.org/en-US/docs/Web/CSS/content"],["href","components/breadcrumb"],[1,"mb-2"],[1,"mb-0"],["url","/"],[3,"active"],["xs","12"],["href","components/breadcrumb#router"],[3,"items"],[3,"active","url"],[3,"ngClass"]],template:function(r,l){r&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",0),e(3,`
    `),t(4,"c-card"),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Breadcrumbs"),n(),e(10,`
      `),n(),e(11,`
      `),t(12,"c-card-body"),e(13,`
        `),t(14,"p",1),e(15,`
          The breadcrumb navigation provides links back to each previous page the user navigated
          through and shows the current location in a website or an application. You don\u2019t have
          to add separators, because they automatically added in CSS through
          `),t(16,"a",2),e(17,`
            `),t(18,"code"),e(19,"::before"),n(),e(20,`
          `),n(),e(21,`
          and
          `),t(22,"a",3),e(23,`
            `),t(24,"code"),e(25,"content"),n(),e(26,`
          `),n(),e(27,`
          .
        `),n(),e(28,`
        `),t(29,"app-docs-example",4),e(30,`
          `),t(31,"c-breadcrumb",5),e(32,`
            `),u(33,F,4,3,null,null,d),n(),e(35,`
          `),b(36,"hr"),e(37,`
          `),t(38,"c-breadcrumb",5),e(39,`
            `),u(40,w,4,3,null,null,d),n(),e(42,`
          `),t(43,"c-breadcrumb",5),e(44,`
            `),u(45,H,4,3,null,null,d),n(),e(47,`
          `),t(48,"c-breadcrumb",5),e(49,`
            `),u(50,U,4,3,null,null,d),n(),e(52,`
          `),t(53,"c-breadcrumb",5),e(54,`
            `),u(55,L,7,6,null,null,d),n(),e(57,`
          `),b(58,"hr"),e(59,`
          `),t(60,"c-breadcrumb",6),e(61,`
            `),t(62,"c-breadcrumb-item",7),e(63,`
              Home
            `),n(),e(64,`
            `),t(65,"c-breadcrumb-item",7),e(66,`
              Library
            `),n(),e(67,`
            `),t(68,"c-breadcrumb-item",7),e(69,`
              Data
            `),n(),e(70,`
            `),t(71,"c-breadcrumb-item",8),e(72,`
              Bootstrap
            `),n(),e(73,`
          `),n(),e(74,`
        `),n(),e(75,`
      `),n(),e(76,`
    `),n(),e(77,`
  `),n(),e(78,`
  `),t(79,"c-col",9),e(80,`
    `),t(81,"c-card"),e(82,`
      `),t(83,"c-card-header"),e(84,`
        `),t(85,"strong"),e(86,"Angular Breadcrumbs"),n(),e(87," "),t(88,"small"),e(89,"router"),n(),e(90,`
      `),n(),e(91,`
      `),t(92,"c-card-body"),e(93,`
        `),t(94,"app-docs-example",10),e(95,`
          `),b(96,"c-breadcrumb-router"),e(97,`
          `),b(98,"c-breadcrumb-router",11),e(99,`
        `),n(),e(100,`
      `),n(),e(101,`
    `),n(),e(102,`
  `),n(),e(103,`
`),n()),r&2&&(a(33),s(l.items),a(7),s(l.items.slice(0,1)),a(5),s(l.items.slice(0,2)),a(5),s(l.items.slice(0,3)),a(5),s(l.items.slice(0,4)),a(16),c("active",!0),a(27),c("items",l.items))},dependencies:[I,B,h,g,$,T,y,E,C,f],encapsulation:2});var k=p;export{k as BreadcrumbsComponent};
