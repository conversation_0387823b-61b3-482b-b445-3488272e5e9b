{"_args": [["xmlbuilder@11.0.1", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "xmlbuilder@11.0.1", "_id": "xmlbuilder@11.0.1", "_inBundle": false, "_integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==", "_location": "/@azure/ms-rest-js/xmlbuilder", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "xmlbuilder@11.0.1", "name": "xmlbuilder", "escapedName": "xmlbuilder", "rawSpec": "11.0.1", "saveSpec": null, "fetchSpec": "11.0.1"}, "_requiredBy": ["/@azure/ms-rest-js/xml2js"], "_resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "_spec": "11.0.1", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "contributors": [], "dependencies": {}, "description": "An XML builder for node.js", "devDependencies": {"coffee-coverage": "2.*", "coffeescript": "1.*", "coveralls": "*", "istanbul": "*", "mocha": "*", "xpath": "*"}, "engines": {"node": ">=4.0"}, "homepage": "http://github.com/oozcitak/xmlbuilder-js", "keywords": ["xml", "xmlbuilder"], "license": "MIT", "main": "./lib/index", "name": "xmlbuilder", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "scripts": {"postpublish": "rm -rf lib", "prepublishOnly": "coffee -co lib src", "test": "mocha \"test/**/*.coffee\" && istanbul report text lcov"}, "typings": "./typings/index.d.ts", "version": "11.0.1"}