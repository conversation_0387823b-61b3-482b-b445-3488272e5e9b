import{G as an,b as we,d as ds,e as Le,f as ps,j as sn,k as us,t as fs,u as rn,v as ms,x as Si,y as Ai,z as Nn}from"./chunk-VLR5A2CC.js";import{c as It,d as q,e as ii,f as hs,g as H,h as Me,i as Ie,j as ot,k as Rn,l as Nt,m as qe}from"./chunk-4PPJG5BB.js";import{$ as Zi,$b as ee,Ab as A,Bb as E,Bc as Fe,C as Bt,Cc as d,Da as xi,Db as Ii,Dc as T,E as <PERSON>,<PERSON>a as Go,Eb as <PERSON>,Ec as me,Fa as Qo,Fb as Ti,G as Ho,Ga as qo,Gb as m,Gc as Re,Hb as I,Hc as r,I as Yi,Ib as w,Ic as ti,Jb as K,Kb as it,Kc as wt,Lb as nt,Lc as je,Mb as W,Mc as on,Nb as Ct,Nc as <PERSON>,O as Xi,Ob as Xt,P as $o,Pb as Zt,Qb as oe,Qc as v,R as Qt,Ra as Yo,Rb as tn,Rc as Ae,S as Vo,Sa as Ji,Sb as y,T as ht,Tb as x,Tc as ls,U as zo,Ua as Xo,Ub as C,V as Wo,Va as f,W as An,Wb as ns,X as En,Xa as en,Xb as os,Y as k,Ya as qt,Yb as ss,Z as vt,Za as X,Zb as pe,_b as Jt,a as Fo,aa as kn,ab as tt,ac as Q,ba as c,bc as Bn,c as Gt,cc as _t,dc as ei,eb as h,ec as g,f as ke,fa as Uo,fb as yt,fc as z,g as Ke,ga as Ko,gb as _,gc as se,h as Bo,ha as Je,hb as wi,hc as xt,ia as et,ib as Qe,ja as Ci,jb as Pe,kb as R,l as Po,la as gt,ma as ce,na as be,nb as st,nc as rs,ob as Zo,oc as as,p as yi,pb as Yt,pc as Pn,q as No,qa as On,qc as nn,ra as B,sc as Ne,t as Ro,tc as Oe,uc as Pt,va as _i,vb as Jo,vc as cs,w as Ft,wa as bt,wb as es,wc as ue,x as jo,xb as ts,xc as fe,y as Ge,ya as O,yb as is,za as Fn,zb as M,zc as re}from"./chunk-J5YWIVYY.js";import{a as S,b as mt}from"./chunk-L3UST63Y.js";function ye(t){t||(t=c(be));let o=new Gt(e=>{if(t.destroyed){e.next();return}return t.onDestroy(e.next.bind(e))});return e=>e.pipe(Qt(o))}function jn(t,o){let e=o?.injector??c(gt),i=new Bo(1),n=T(()=>{let s;try{s=t()}catch(a){Fe(()=>i.error(a));return}Fe(()=>i.next(s))},{injector:e,manualCleanup:!0});return e.get(be).onDestroy(()=>{n.destroy(),i.complete()}),i.asObservable()}var Sr=(()=>{class t{get iconNames(){return this.#e}#e={};get icons(){return this.#t}set icons(e){for(let i in e)this.#e[i]=i;this.#t=e}#t={};getIcon(e){return this.icons[e]||console.warn(`CoreUI WARN: Icon ${e} is not registered in IconService`),this.icons[e]}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Ar(t){return t.replace(/([-_][a-z0-9])/ig,o=>o.toUpperCase().replace("-",""))}function Er(t){return t&&t.includes("-")?Ar(t):t}var Ln=(()=>{class t{#e=c(fs);#t=c(Sr);content=r(void 0,{alias:"cIcon"});customClasses=r();size=r("");title=r();height=r();width=r();name=r("",{transform:Er});viewBoxInput=r(void 0,{alias:"viewBox"});xmlns=r("http://www.w3.org/2000/svg");pointerEvents=r("none",{alias:"pointer-events"});role=r("img");hostClasses=d(()=>{let e=this.computedSize(),i={icon:!0,[`icon-${e}`]:!!e};return this.customClasses()??i});viewBox=d(()=>this.viewBoxInput()??this.scale());innerHtml=d(()=>{let e=this.code(),i=Array.isArray(e)?e?.[1]??e?.[0]??"":e||"";return this.#e.bypassSecurityTrustHtml(this.#i()+i||"")});#i=d(()=>this.title()?`<title>${this.title()}</title>`:"");code=d(()=>{let e=this.content();if(e)return e;let i=this.name();return this.#t&&i?this.#t.getIcon(i):(i&&!this.#t?.icons[i]&&console.warn(`cIcon directive: The '${i}' icon not found. Add it to the IconSet service for use with the 'name' property. 
`,i),"")});scale=d(()=>Array.isArray(this.code())&&(this.code()?.length??0)>1?`0 0 ${this.code()?.[0]}`:"0 0 64 64");computedSize=d(()=>{let e=!this.size()&&(this.width()||this.height());return this.size()==="custom"||e?"custom-size":this.size()});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["svg","cIcon",""]],hostVars:8,hostBindings:function(i,n){i&2&&(Xt("innerHtml",n.innerHtml(),Yo),M("viewBox",n.viewBox())("xmlns",n.xmlns())("pointer-events",n.pointerEvents())("role",n.role())("aria-hidden",!0),g(n.hostClasses()))},inputs:{content:[1,"cIcon","content"],customClasses:[1,"customClasses"],size:[1,"size"],title:[1,"title"],height:[1,"height"],width:[1,"width"],name:[1,"name"],viewBoxInput:[1,"viewBox","viewBoxInput"],xmlns:[1,"xmlns"],pointerEvents:[1,"pointer-events","pointerEvents"],role:[1,"role"]},exportAs:["cIcon"]})}return t})();var zn=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:()=>c(kr),providedIn:"root"})}return t})(),Hn=class{},kr=(()=>{class t extends zn{animationModuleType=c(Go,{optional:!0});_nextAnimationId=0;_renderer;constructor(e,i){super();let n={id:"0",encapsulation:qo.None,styles:[],data:{animation:[]}};if(this._renderer=e.createRenderer(i.body,n),this.animationModuleType===null&&!Fr(this._renderer))throw new An(3600,!1)}build(e){let i=this._nextAnimationId;this._nextAnimationId++;let n=Array.isArray(e)?hs(e):e;return vs(this._renderer,null,i,"register",[n]),new $n(i,this._renderer)}static \u0275fac=function(i){return new(i||t)(kn(qt),kn(ce))};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),$n=class extends Hn{_id;_renderer;constructor(o,e){super(),this._id=o,this._renderer=e}create(o,e){return new Vn(this._id,o,e||{},this._renderer)}},Vn=class{id;element;_renderer;parentPlayer=null;_started=!1;constructor(o,e,i,n){this.id=o,this.element=e,this._renderer=n,this._command("create",i)}_listen(o,e){return this._renderer.listen(this.element,`@@${this.id}:${o}`,e)}_command(o,...e){vs(this._renderer,this.element,this.id,o,e)}onDone(o){this._listen("done",o)}onStart(o){this._listen("start",o)}onDestroy(o){this._listen("destroy",o)}init(){this._command("init")}hasStarted(){return this._started}play(){this._command("play"),this._started=!0}pause(){this._command("pause")}restart(){this._command("restart")}finish(){this._command("finish")}destroy(){this._command("destroy")}reset(){this._command("reset"),this._started=!1}setPosition(o){this._command("setPosition",o)}getPosition(){return Or(this._renderer)?.engine?.players[this.id]?.getPosition()??0}totalTime=0};function vs(t,o,e,i,n){t.setProperty(o,`@@${e}:${i}`,n)}function Or(t){let o=t.\u0275type;return o===0?t:o===1?t.animationRenderer:null}function Fr(t){let o=t.\u0275type;return o===0||o===1}var te="top",he="bottom",le="right",ae="left",cn="auto",Dt=[te,he,le,ae],at="start",Rt="end",gs="clippingParents",ln="viewport",ni="popper",bs="reference",Wn=Dt.reduce(function(t,o){return t.concat([o+"-"+at,o+"-"+Rt])},[]),dn=[].concat(Dt,[cn]).reduce(function(t,o){return t.concat([o,o+"-"+at,o+"-"+Rt])},[]),Br="beforeRead",Pr="read",Nr="afterRead",Rr="beforeMain",jr="main",Lr="afterMain",Hr="beforeWrite",$r="write",Vr="afterWrite",ys=[Br,Pr,Nr,Rr,jr,Lr,Hr,$r,Vr];function Ce(t){return t?(t.nodeName||"").toLowerCase():null}function Z(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var o=t.ownerDocument;return o&&o.defaultView||window}return t}function He(t){var o=Z(t).Element;return t instanceof o||t instanceof Element}function ve(t){var o=Z(t).HTMLElement;return t instanceof o||t instanceof HTMLElement}function oi(t){if(typeof ShadowRoot>"u")return!1;var o=Z(t).ShadowRoot;return t instanceof o||t instanceof ShadowRoot}function zr(t){var o=t.state;Object.keys(o.elements).forEach(function(e){var i=o.styles[e]||{},n=o.attributes[e]||{},s=o.elements[e];!ve(s)||!Ce(s)||(Object.assign(s.style,i),Object.keys(n).forEach(function(a){var l=n[a];l===!1?s.removeAttribute(a):s.setAttribute(a,l===!0?"":l)}))})}function Wr(t){var o=t.state,e={popper:{position:o.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(o.elements.popper.style,e.popper),o.styles=e,o.elements.arrow&&Object.assign(o.elements.arrow.style,e.arrow),function(){Object.keys(o.elements).forEach(function(i){var n=o.elements[i],s=o.attributes[i]||{},a=Object.keys(o.styles.hasOwnProperty(i)?o.styles[i]:e[i]),l=a.reduce(function(p,b){return p[b]="",p},{});!ve(n)||!Ce(n)||(Object.assign(n.style,l),Object.keys(s).forEach(function(p){n.removeAttribute(p)}))})}}var Cs={name:"applyStyles",enabled:!0,phase:"write",fn:zr,effect:Wr,requires:["computeStyles"]};function _e(t){return t.split("-")[0]}var Ye=Math.max,jt=Math.min,ct=Math.round;function si(){var t=navigator.userAgentData;return t!=null&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(o){return o.brand+"/"+o.version}).join(" "):navigator.userAgent}function Ei(){return!/^((?!chrome|android).)*safari/i.test(si())}function $e(t,o,e){o===void 0&&(o=!1),e===void 0&&(e=!1);var i=t.getBoundingClientRect(),n=1,s=1;o&&ve(t)&&(n=t.offsetWidth>0&&ct(i.width)/t.offsetWidth||1,s=t.offsetHeight>0&&ct(i.height)/t.offsetHeight||1);var a=He(t)?Z(t):window,l=a.visualViewport,p=!Ei()&&e,b=(i.left+(p&&l?l.offsetLeft:0))/n,u=(i.top+(p&&l?l.offsetTop:0))/s,N=i.width/n,U=i.height/s;return{width:N,height:U,top:u,right:b+N,bottom:u+U,left:b,x:b,y:u}}function Lt(t){var o=$e(t),e=t.offsetWidth,i=t.offsetHeight;return Math.abs(o.width-e)<=1&&(e=o.width),Math.abs(o.height-i)<=1&&(i=o.height),{x:t.offsetLeft,y:t.offsetTop,width:e,height:i}}function ki(t,o){var e=o.getRootNode&&o.getRootNode();if(t.contains(o))return!0;if(e&&oi(e)){var i=o;do{if(i&&t.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function Ee(t){return Z(t).getComputedStyle(t)}function Un(t){return["table","td","th"].indexOf(Ce(t))>=0}function De(t){return((He(t)?t.ownerDocument:t.document)||window.document).documentElement}function lt(t){return Ce(t)==="html"?t:t.assignedSlot||t.parentNode||(oi(t)?t.host:null)||De(t)}function _s(t){return!ve(t)||Ee(t).position==="fixed"?null:t.offsetParent}function Ur(t){var o=/firefox/i.test(si()),e=/Trident/i.test(si());if(e&&ve(t)){var i=Ee(t);if(i.position==="fixed")return null}var n=lt(t);for(oi(n)&&(n=n.host);ve(n)&&["html","body"].indexOf(Ce(n))<0;){var s=Ee(n);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||o&&s.willChange==="filter"||o&&s.filter&&s.filter!=="none")return n;n=n.parentNode}return null}function Xe(t){for(var o=Z(t),e=_s(t);e&&Un(e)&&Ee(e).position==="static";)e=_s(e);return e&&(Ce(e)==="html"||Ce(e)==="body"&&Ee(e).position==="static")?o:e||Ur(t)||o}function Ht(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function $t(t,o,e){return Ye(t,jt(o,e))}function xs(t,o,e){var i=$t(t,o,e);return i>e?e:i}function Oi(){return{top:0,right:0,bottom:0,left:0}}function Fi(t){return Object.assign({},Oi(),t)}function Bi(t,o){return o.reduce(function(e,i){return e[i]=t,e},{})}var Kr=function(o,e){return o=typeof o=="function"?o(Object.assign({},e.rects,{placement:e.placement})):o,Fi(typeof o!="number"?o:Bi(o,Dt))};function Gr(t){var o,e=t.state,i=t.name,n=t.options,s=e.elements.arrow,a=e.modifiersData.popperOffsets,l=_e(e.placement),p=Ht(l),b=[ae,le].indexOf(l)>=0,u=b?"height":"width";if(!(!s||!a)){var N=Kr(n.padding,e),U=Lt(s),F=p==="y"?te:ae,Y=p==="y"?he:le,L=e.rects.reference[u]+e.rects.reference[p]-a[p]-e.rects.popper[u],j=a[p]-e.rects.reference[p],G=Xe(s),ie=G?p==="y"?G.clientHeight||0:G.clientWidth||0:0,ne=L/2-j/2,P=N[F],$=ie-U[u]-N[Y],V=ie/2-U[u]/2+ne,J=$t(P,V,$),xe=p;e.modifiersData[i]=(o={},o[xe]=J,o.centerOffset=J-V,o)}}function Qr(t){var o=t.state,e=t.options,i=e.element,n=i===void 0?"[data-popper-arrow]":i;n!=null&&(typeof n=="string"&&(n=o.elements.popper.querySelector(n),!n)||ki(o.elements.popper,n)&&(o.elements.arrow=n))}var ws={name:"arrow",enabled:!0,phase:"main",fn:Gr,effect:Qr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ve(t){return t.split("-")[1]}var qr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Yr(t,o){var e=t.x,i=t.y,n=o.devicePixelRatio||1;return{x:ct(e*n)/n||0,y:ct(i*n)/n||0}}function Is(t){var o,e=t.popper,i=t.popperRect,n=t.placement,s=t.variation,a=t.offsets,l=t.position,p=t.gpuAcceleration,b=t.adaptive,u=t.roundOffsets,N=t.isFixed,U=a.x,F=U===void 0?0:U,Y=a.y,L=Y===void 0?0:Y,j=typeof u=="function"?u({x:F,y:L}):{x:F,y:L};F=j.x,L=j.y;var G=a.hasOwnProperty("x"),ie=a.hasOwnProperty("y"),ne=ae,P=te,$=window;if(b){var V=Xe(e),J="clientHeight",xe="clientWidth";if(V===Z(e)&&(V=De(e),Ee(V).position!=="static"&&l==="absolute"&&(J="scrollHeight",xe="scrollWidth")),V=V,n===te||(n===ae||n===le)&&s===Rt){P=he;var ge=N&&V===$&&$.visualViewport?$.visualViewport.height:V[J];L-=ge-i.height,L*=p?1:-1}if(n===ae||(n===te||n===he)&&s===Rt){ne=le;var de=N&&V===$&&$.visualViewport?$.visualViewport.width:V[xe];F-=de-i.width,F*=p?1:-1}}var Te=Object.assign({position:l},b&&qr),We=u===!0?Yr({x:F,y:L},Z(e)):{x:F,y:L};if(F=We.x,L=We.y,p){var Se;return Object.assign({},Te,(Se={},Se[P]=ie?"0":"",Se[ne]=G?"0":"",Se.transform=($.devicePixelRatio||1)<=1?"translate("+F+"px, "+L+"px)":"translate3d("+F+"px, "+L+"px, 0)",Se))}return Object.assign({},Te,(o={},o[P]=ie?L+"px":"",o[ne]=G?F+"px":"",o.transform="",o))}function Xr(t){var o=t.state,e=t.options,i=e.gpuAcceleration,n=i===void 0?!0:i,s=e.adaptive,a=s===void 0?!0:s,l=e.roundOffsets,p=l===void 0?!0:l,b={placement:_e(o.placement),variation:Ve(o.placement),popper:o.elements.popper,popperRect:o.rects.popper,gpuAcceleration:n,isFixed:o.options.strategy==="fixed"};o.modifiersData.popperOffsets!=null&&(o.styles.popper=Object.assign({},o.styles.popper,Is(Object.assign({},b,{offsets:o.modifiersData.popperOffsets,position:o.options.strategy,adaptive:a,roundOffsets:p})))),o.modifiersData.arrow!=null&&(o.styles.arrow=Object.assign({},o.styles.arrow,Is(Object.assign({},b,{offsets:o.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),o.attributes.popper=Object.assign({},o.attributes.popper,{"data-popper-placement":o.placement})}var Ds={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Xr,data:{}};var pn={passive:!0};function Zr(t){var o=t.state,e=t.instance,i=t.options,n=i.scroll,s=n===void 0?!0:n,a=i.resize,l=a===void 0?!0:a,p=Z(o.elements.popper),b=[].concat(o.scrollParents.reference,o.scrollParents.popper);return s&&b.forEach(function(u){u.addEventListener("scroll",e.update,pn)}),l&&p.addEventListener("resize",e.update,pn),function(){s&&b.forEach(function(u){u.removeEventListener("scroll",e.update,pn)}),l&&p.removeEventListener("resize",e.update,pn)}}var Ts={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Zr,data:{}};var Jr={left:"right",right:"left",bottom:"top",top:"bottom"};function ri(t){return t.replace(/left|right|bottom|top/g,function(o){return Jr[o]})}var ea={start:"end",end:"start"};function un(t){return t.replace(/start|end/g,function(o){return ea[o]})}function Vt(t){var o=Z(t),e=o.pageXOffset,i=o.pageYOffset;return{scrollLeft:e,scrollTop:i}}function zt(t){return $e(De(t)).left+Vt(t).scrollLeft}function Kn(t,o){var e=Z(t),i=De(t),n=e.visualViewport,s=i.clientWidth,a=i.clientHeight,l=0,p=0;if(n){s=n.width,a=n.height;var b=Ei();(b||!b&&o==="fixed")&&(l=n.offsetLeft,p=n.offsetTop)}return{width:s,height:a,x:l+zt(t),y:p}}function Gn(t){var o,e=De(t),i=Vt(t),n=(o=t.ownerDocument)==null?void 0:o.body,s=Ye(e.scrollWidth,e.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),a=Ye(e.scrollHeight,e.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),l=-i.scrollLeft+zt(t),p=-i.scrollTop;return Ee(n||e).direction==="rtl"&&(l+=Ye(e.clientWidth,n?n.clientWidth:0)-s),{width:s,height:a,x:l,y:p}}function Wt(t){var o=Ee(t),e=o.overflow,i=o.overflowX,n=o.overflowY;return/auto|scroll|overlay|hidden/.test(e+n+i)}function fn(t){return["html","body","#document"].indexOf(Ce(t))>=0?t.ownerDocument.body:ve(t)&&Wt(t)?t:fn(lt(t))}function Tt(t,o){var e;o===void 0&&(o=[]);var i=fn(t),n=i===((e=t.ownerDocument)==null?void 0:e.body),s=Z(i),a=n?[s].concat(s.visualViewport||[],Wt(i)?i:[]):i,l=o.concat(a);return n?l:l.concat(Tt(lt(a)))}function ai(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function ta(t,o){var e=$e(t,!1,o==="fixed");return e.top=e.top+t.clientTop,e.left=e.left+t.clientLeft,e.bottom=e.top+t.clientHeight,e.right=e.left+t.clientWidth,e.width=t.clientWidth,e.height=t.clientHeight,e.x=e.left,e.y=e.top,e}function Ms(t,o,e){return o===ln?ai(Kn(t,e)):He(o)?ta(o,e):ai(Gn(De(t)))}function ia(t){var o=Tt(lt(t)),e=["absolute","fixed"].indexOf(Ee(t).position)>=0,i=e&&ve(t)?Xe(t):t;return He(i)?o.filter(function(n){return He(n)&&ki(n,i)&&Ce(n)!=="body"}):[]}function Qn(t,o,e,i){var n=o==="clippingParents"?ia(t):[].concat(o),s=[].concat(n,[e]),a=s[0],l=s.reduce(function(p,b){var u=Ms(t,b,i);return p.top=Ye(u.top,p.top),p.right=jt(u.right,p.right),p.bottom=jt(u.bottom,p.bottom),p.left=Ye(u.left,p.left),p},Ms(t,a,i));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function Pi(t){var o=t.reference,e=t.element,i=t.placement,n=i?_e(i):null,s=i?Ve(i):null,a=o.x+o.width/2-e.width/2,l=o.y+o.height/2-e.height/2,p;switch(n){case te:p={x:a,y:o.y-e.height};break;case he:p={x:a,y:o.y+o.height};break;case le:p={x:o.x+o.width,y:l};break;case ae:p={x:o.x-e.width,y:l};break;default:p={x:o.x,y:o.y}}var b=n?Ht(n):null;if(b!=null){var u=b==="y"?"height":"width";switch(s){case at:p[b]=p[b]-(o[u]/2-e[u]/2);break;case Rt:p[b]=p[b]+(o[u]/2-e[u]/2);break;default:}}return p}function Ze(t,o){o===void 0&&(o={});var e=o,i=e.placement,n=i===void 0?t.placement:i,s=e.strategy,a=s===void 0?t.strategy:s,l=e.boundary,p=l===void 0?gs:l,b=e.rootBoundary,u=b===void 0?ln:b,N=e.elementContext,U=N===void 0?ni:N,F=e.altBoundary,Y=F===void 0?!1:F,L=e.padding,j=L===void 0?0:L,G=Fi(typeof j!="number"?j:Bi(j,Dt)),ie=U===ni?bs:ni,ne=t.rects.popper,P=t.elements[Y?ie:U],$=Qn(He(P)?P:P.contextElement||De(t.elements.popper),p,u,a),V=$e(t.elements.reference),J=Pi({reference:V,element:ne,strategy:"absolute",placement:n}),xe=ai(Object.assign({},ne,J)),ge=U===ni?xe:V,de={top:$.top-ge.top+G.top,bottom:ge.bottom-$.bottom+G.bottom,left:$.left-ge.left+G.left,right:ge.right-$.right+G.right},Te=t.modifiersData.offset;if(U===ni&&Te){var We=Te[n];Object.keys(de).forEach(function(Se){var St=[le,he].indexOf(Se)>=0?1:-1,At=[te,he].indexOf(Se)>=0?"y":"x";de[Se]+=We[At]*St})}return de}function qn(t,o){o===void 0&&(o={});var e=o,i=e.placement,n=e.boundary,s=e.rootBoundary,a=e.padding,l=e.flipVariations,p=e.allowedAutoPlacements,b=p===void 0?dn:p,u=Ve(i),N=u?l?Wn:Wn.filter(function(Y){return Ve(Y)===u}):Dt,U=N.filter(function(Y){return b.indexOf(Y)>=0});U.length===0&&(U=N);var F=U.reduce(function(Y,L){return Y[L]=Ze(t,{placement:L,boundary:n,rootBoundary:s,padding:a})[_e(L)],Y},{});return Object.keys(F).sort(function(Y,L){return F[Y]-F[L]})}function na(t){if(_e(t)===cn)return[];var o=ri(t);return[un(t),o,un(o)]}function oa(t){var o=t.state,e=t.options,i=t.name;if(!o.modifiersData[i]._skip){for(var n=e.mainAxis,s=n===void 0?!0:n,a=e.altAxis,l=a===void 0?!0:a,p=e.fallbackPlacements,b=e.padding,u=e.boundary,N=e.rootBoundary,U=e.altBoundary,F=e.flipVariations,Y=F===void 0?!0:F,L=e.allowedAutoPlacements,j=o.options.placement,G=_e(j),ie=G===j,ne=p||(ie||!Y?[ri(j)]:na(j)),P=[j].concat(ne).reduce(function(Kt,ft){return Kt.concat(_e(ft)===cn?qn(o,{placement:ft,boundary:u,rootBoundary:N,padding:b,flipVariations:Y,allowedAutoPlacements:L}):ft)},[]),$=o.rects.reference,V=o.rects.popper,J=new Map,xe=!0,ge=P[0],de=0;de<P.length;de++){var Te=P[de],We=_e(Te),Se=Ve(Te)===at,St=[te,he].indexOf(We)>=0,At=St?"width":"height",Be=Ze(o,{placement:Te,boundary:u,rootBoundary:N,altBoundary:U,padding:b}),Ue=St?Se?le:ae:Se?he:te;$[At]>V[At]&&(Ue=ri(Ue));var Ui=ri(Ue),Et=[];if(s&&Et.push(Be[We]<=0),l&&Et.push(Be[Ue]<=0,Be[Ui]<=0),Et.every(function(Kt){return Kt})){ge=Te,xe=!1;break}J.set(Te,Et)}if(xe)for(var Ki=Y?3:1,Dn=function(ft){var bi=P.find(function(Qi){var kt=J.get(Qi);if(kt)return kt.slice(0,ft).every(function(Tn){return Tn})});if(bi)return ge=bi,"break"},gi=Ki;gi>0;gi--){var Gi=Dn(gi);if(Gi==="break")break}o.placement!==ge&&(o.modifiersData[i]._skip=!0,o.placement=ge,o.reset=!0)}}var Ss={name:"flip",enabled:!0,phase:"main",fn:oa,requiresIfExists:["offset"],data:{_skip:!1}};function As(t,o,e){return e===void 0&&(e={x:0,y:0}),{top:t.top-o.height-e.y,right:t.right-o.width+e.x,bottom:t.bottom-o.height+e.y,left:t.left-o.width-e.x}}function Es(t){return[te,le,he,ae].some(function(o){return t[o]>=0})}function sa(t){var o=t.state,e=t.name,i=o.rects.reference,n=o.rects.popper,s=o.modifiersData.preventOverflow,a=Ze(o,{elementContext:"reference"}),l=Ze(o,{altBoundary:!0}),p=As(a,i),b=As(l,n,s),u=Es(p),N=Es(b);o.modifiersData[e]={referenceClippingOffsets:p,popperEscapeOffsets:b,isReferenceHidden:u,hasPopperEscaped:N},o.attributes.popper=Object.assign({},o.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":N})}var ks={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:sa};function ra(t,o,e){var i=_e(t),n=[ae,te].indexOf(i)>=0?-1:1,s=typeof e=="function"?e(Object.assign({},o,{placement:t})):e,a=s[0],l=s[1];return a=a||0,l=(l||0)*n,[ae,le].indexOf(i)>=0?{x:l,y:a}:{x:a,y:l}}function aa(t){var o=t.state,e=t.options,i=t.name,n=e.offset,s=n===void 0?[0,0]:n,a=dn.reduce(function(u,N){return u[N]=ra(N,o.rects,s),u},{}),l=a[o.placement],p=l.x,b=l.y;o.modifiersData.popperOffsets!=null&&(o.modifiersData.popperOffsets.x+=p,o.modifiersData.popperOffsets.y+=b),o.modifiersData[i]=a}var Os={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:aa};function ca(t){var o=t.state,e=t.name;o.modifiersData[e]=Pi({reference:o.rects.reference,element:o.rects.popper,strategy:"absolute",placement:o.placement})}var Fs={name:"popperOffsets",enabled:!0,phase:"read",fn:ca,data:{}};function Yn(t){return t==="x"?"y":"x"}function la(t){var o=t.state,e=t.options,i=t.name,n=e.mainAxis,s=n===void 0?!0:n,a=e.altAxis,l=a===void 0?!1:a,p=e.boundary,b=e.rootBoundary,u=e.altBoundary,N=e.padding,U=e.tether,F=U===void 0?!0:U,Y=e.tetherOffset,L=Y===void 0?0:Y,j=Ze(o,{boundary:p,rootBoundary:b,padding:N,altBoundary:u}),G=_e(o.placement),ie=Ve(o.placement),ne=!ie,P=Ht(G),$=Yn(P),V=o.modifiersData.popperOffsets,J=o.rects.reference,xe=o.rects.popper,ge=typeof L=="function"?L(Object.assign({},o.rects,{placement:o.placement})):L,de=typeof ge=="number"?{mainAxis:ge,altAxis:ge}:Object.assign({mainAxis:0,altAxis:0},ge),Te=o.modifiersData.offset?o.modifiersData.offset[o.placement]:null,We={x:0,y:0};if(V){if(s){var Se,St=P==="y"?te:ae,At=P==="y"?he:le,Be=P==="y"?"height":"width",Ue=V[P],Ui=Ue+j[St],Et=Ue-j[At],Ki=F?-xe[Be]/2:0,Dn=ie===at?J[Be]:xe[Be],gi=ie===at?-xe[Be]:-J[Be],Gi=o.elements.arrow,Kt=F&&Gi?Lt(Gi):{width:0,height:0},ft=o.modifiersData["arrow#persistent"]?o.modifiersData["arrow#persistent"].padding:Oi(),bi=ft[St],Qi=ft[At],kt=$t(0,J[Be],Kt[Be]),Tn=ne?J[Be]/2-Ki-kt-bi-de.mainAxis:Dn-kt-bi-de.mainAxis,xr=ne?-J[Be]/2+Ki+kt+Qi+de.mainAxis:gi+kt+Qi+de.mainAxis,Mn=o.elements.arrow&&Xe(o.elements.arrow),wr=Mn?P==="y"?Mn.clientTop||0:Mn.clientLeft||0:0,Io=(Se=Te?.[P])!=null?Se:0,Ir=Ue+Tn-Io-wr,Dr=Ue+xr-Io,Do=$t(F?jt(Ui,Ir):Ui,Ue,F?Ye(Et,Dr):Et);V[P]=Do,We[P]=Do-Ue}if(l){var To,Tr=P==="x"?te:ae,Mr=P==="x"?he:le,Ot=V[$],qi=$==="y"?"height":"width",Mo=Ot+j[Tr],So=Ot-j[Mr],Sn=[te,ae].indexOf(G)!==-1,Ao=(To=Te?.[$])!=null?To:0,Eo=Sn?Mo:Ot-J[qi]-xe[qi]-Ao+de.altAxis,ko=Sn?Ot+J[qi]+xe[qi]-Ao-de.altAxis:So,Oo=F&&Sn?xs(Eo,Ot,ko):$t(F?Eo:Mo,Ot,F?ko:So);V[$]=Oo,We[$]=Oo-Ot}o.modifiersData[i]=We}}var Bs={name:"preventOverflow",enabled:!0,phase:"main",fn:la,requiresIfExists:["offset"]};function Xn(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function Zn(t){return t===Z(t)||!ve(t)?Vt(t):Xn(t)}function da(t){var o=t.getBoundingClientRect(),e=ct(o.width)/t.offsetWidth||1,i=ct(o.height)/t.offsetHeight||1;return e!==1||i!==1}function Jn(t,o,e){e===void 0&&(e=!1);var i=ve(o),n=ve(o)&&da(o),s=De(o),a=$e(t,n,e),l={scrollLeft:0,scrollTop:0},p={x:0,y:0};return(i||!i&&!e)&&((Ce(o)!=="body"||Wt(s))&&(l=Zn(o)),ve(o)?(p=$e(o,!0),p.x+=o.clientLeft,p.y+=o.clientTop):s&&(p.x=zt(s))),{x:a.left+l.scrollLeft-p.x,y:a.top+l.scrollTop-p.y,width:a.width,height:a.height}}function pa(t){var o=new Map,e=new Set,i=[];t.forEach(function(s){o.set(s.name,s)});function n(s){e.add(s.name);var a=[].concat(s.requires||[],s.requiresIfExists||[]);a.forEach(function(l){if(!e.has(l)){var p=o.get(l);p&&n(p)}}),i.push(s)}return t.forEach(function(s){e.has(s.name)||n(s)}),i}function eo(t){var o=pa(t);return ys.reduce(function(e,i){return e.concat(o.filter(function(n){return n.phase===i}))},[])}function to(t){var o;return function(){return o||(o=new Promise(function(e){Promise.resolve().then(function(){o=void 0,e(t())})})),o}}function io(t){var o=t.reduce(function(e,i){var n=e[i.name];return e[i.name]=n?Object.assign({},n,i,{options:Object.assign({},n.options,i.options),data:Object.assign({},n.data,i.data)}):i,e},{});return Object.keys(o).map(function(e){return o[e]})}var Ps={placement:"bottom",modifiers:[],strategy:"absolute"};function Ns(){for(var t=arguments.length,o=new Array(t),e=0;e<t;e++)o[e]=arguments[e];return!o.some(function(i){return!(i&&typeof i.getBoundingClientRect=="function")})}function Rs(t){t===void 0&&(t={});var o=t,e=o.defaultModifiers,i=e===void 0?[]:e,n=o.defaultOptions,s=n===void 0?Ps:n;return function(l,p,b){b===void 0&&(b=s);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ps,s),modifiersData:{},elements:{reference:l,popper:p},attributes:{},styles:{}},N=[],U=!1,F={state:u,setOptions:function(G){var ie=typeof G=="function"?G(u.options):G;L(),u.options=Object.assign({},s,u.options,ie),u.scrollParents={reference:He(l)?Tt(l):l.contextElement?Tt(l.contextElement):[],popper:Tt(p)};var ne=eo(io([].concat(i,u.options.modifiers)));return u.orderedModifiers=ne.filter(function(P){return P.enabled}),Y(),F.update()},forceUpdate:function(){if(!U){var G=u.elements,ie=G.reference,ne=G.popper;if(Ns(ie,ne)){u.rects={reference:Jn(ie,Xe(ne),u.options.strategy==="fixed"),popper:Lt(ne)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(de){return u.modifiersData[de.name]=Object.assign({},de.data)});for(var P=0;P<u.orderedModifiers.length;P++){if(u.reset===!0){u.reset=!1,P=-1;continue}var $=u.orderedModifiers[P],V=$.fn,J=$.options,xe=J===void 0?{}:J,ge=$.name;typeof V=="function"&&(u=V({state:u,options:xe,name:ge,instance:F})||u)}}}},update:to(function(){return new Promise(function(j){F.forceUpdate(),j(u)})}),destroy:function(){L(),U=!0}};if(!Ns(l,p))return F;F.setOptions(b).then(function(j){!U&&b.onFirstUpdate&&b.onFirstUpdate(j)});function Y(){u.orderedModifiers.forEach(function(j){var G=j.name,ie=j.options,ne=ie===void 0?{}:ie,P=j.effect;if(typeof P=="function"){var $=P({state:u,name:G,instance:F,options:ne}),V=function(){};N.push($||V)}})}function L(){N.forEach(function(j){return j()}),N=[]}return F}}var ua=[Ts,Fs,Ds,Cs,Os,Ss,Bs,ws,ks],Ni=Rs({defaultModifiers:ua});function js(t){return t.buttons===0||t.detail===0}function Ls(t){let o=t.touches&&t.touches[0]||t.changedTouches&&t.changedTouches[0];return!!o&&o.identifier===-1&&(o.radiusX==null||o.radiusX===1)&&(o.radiusY==null||o.radiusY===1)}var no;function fa(){if(no==null){let t=typeof document<"u"?document.head:null;no=!!(t&&(t.createShadowRoot||t.attachShadow))}return no}function Hs(t){if(fa()){let o=t.getRootNode?t.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&o instanceof ShadowRoot)return o}return null}function $s(){let t=typeof document<"u"&&document?document.activeElement:null;for(;t&&t.shadowRoot;){let o=t.shadowRoot.activeElement;if(o===t)break;t=o}return t}function ci(t){return t.composedPath?t.composedPath()[0]:t.target}var oo;try{oo=typeof Intl<"u"&&Intl.v8BreakIterator}catch{oo=!1}var dt=(()=>{class t{_platformId=c(xi);isBrowser=this._platformId?sn(this._platformId):typeof document=="object"&&!!document;EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent);TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent);BLINK=this.isBrowser&&!!(window.chrome||oo)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT;WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT;IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window);FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent);ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT;SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT;constructor(){}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Ri;function ma(){if(Ri==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>Ri=!0}))}finally{Ri=Ri||!1}return Ri}function Vs(t){return ma()?t:!!t.capture}function fh(t,o=0){return ha(t)?Number(t):arguments.length===2?o:0}function ha(t){return!isNaN(parseFloat(t))&&!isNaN(Number(t))}function mn(t){return t instanceof O?t.nativeElement:t}var zs=new Zi("cdk-input-modality-detector-options"),Ws={ignoreKeys:[18,17,224,91,16]},Us=650,so={passive:!0,capture:!0},Ks=(()=>{class t{_platform=c(dt);_listenerCleanups;modalityDetected;modalityChanged;get mostRecentModality(){return this._modality.value}_mostRecentTarget=null;_modality=new Ke(null);_options;_lastTouchMs=0;_onKeydown=e=>{this._options?.ignoreKeys?.some(i=>i===e.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=ci(e))};_onMousedown=e=>{Date.now()-this._lastTouchMs<Us||(this._modality.next(js(e)?"keyboard":"mouse"),this._mostRecentTarget=ci(e))};_onTouchstart=e=>{if(Ls(e)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=ci(e)};constructor(){let e=c(st),i=c(ce),n=c(zs,{optional:!0});if(this._options=S(S({},Ws),n),this.modalityDetected=this._modality.pipe(Xi(1)),this.modalityChanged=this.modalityDetected.pipe(Ho()),this._platform.isBrowser){let s=c(qt).createRenderer(null,null);this._listenerCleanups=e.runOutsideAngular(()=>[s.listen(i,"keydown",this._onKeydown,so),s.listen(i,"mousedown",this._onMousedown,so),s.listen(i,"touchstart",this._onTouchstart,so)])}}ngOnDestroy(){this._modality.complete(),this._listenerCleanups?.forEach(e=>e())}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ji=function(t){return t[t.IMMEDIATE=0]="IMMEDIATE",t[t.EVENTUAL=1]="EVENTUAL",t}(ji||{}),Gs=new Zi("cdk-focus-monitor-default-options"),hn=Vs({passive:!0,capture:!0}),ro=(()=>{class t{_ngZone=c(st);_platform=c(dt);_inputModalityDetector=c(Ks);_origin=null;_lastFocusOrigin;_windowFocused=!1;_windowFocusTimeoutId;_originTimeoutId;_originFromTouchInteraction=!1;_elementInfo=new Map;_monitoredElementCount=0;_rootNodeFocusListenerCount=new Map;_detectionMode;_windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=setTimeout(()=>this._windowFocused=!1)};_document=c(ce,{optional:!0});_stopInputModalityDetector=new ke;constructor(){let e=c(Gs,{optional:!0});this._detectionMode=e?.detectionMode||ji.IMMEDIATE}_rootNodeFocusAndBlurListener=e=>{let i=ci(e);for(let n=i;n;n=n.parentElement)e.type==="focus"?this._onFocus(e,n):this._onBlur(e,n)};monitor(e,i=!1){let n=mn(e);if(!this._platform.isBrowser||n.nodeType!==1)return Po();let s=Hs(n)||this._getDocument(),a=this._elementInfo.get(n);if(a)return i&&(a.checkChildren=!0),a.subject;let l={checkChildren:i,subject:new ke,rootNode:s};return this._elementInfo.set(n,l),this._registerGlobalListeners(l),l.subject}stopMonitoring(e){let i=mn(e),n=this._elementInfo.get(i);n&&(n.subject.complete(),this._setClasses(i),this._elementInfo.delete(i),this._removeGlobalListeners(n))}focusVia(e,i,n){let s=mn(e),a=this._getDocument().activeElement;s===a?this._getClosestElementsInfo(s).forEach(([l,p])=>this._originChanged(l,i,p)):(this._setOrigin(i),typeof s.focus=="function"&&s.focus(n))}ngOnDestroy(){this._elementInfo.forEach((e,i)=>this.stopMonitoring(i))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===ji.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,i){e.classList.toggle("cdk-focused",!!i),e.classList.toggle("cdk-touch-focused",i==="touch"),e.classList.toggle("cdk-keyboard-focused",i==="keyboard"),e.classList.toggle("cdk-mouse-focused",i==="mouse"),e.classList.toggle("cdk-program-focused",i==="program")}_setOrigin(e,i=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=e,this._originFromTouchInteraction=e==="touch"&&i,this._detectionMode===ji.IMMEDIATE){clearTimeout(this._originTimeoutId);let n=this._originFromTouchInteraction?Us:1;this._originTimeoutId=setTimeout(()=>this._origin=null,n)}})}_onFocus(e,i){let n=this._elementInfo.get(i),s=ci(e);!n||!n.checkChildren&&i!==s||this._originChanged(i,this._getFocusOrigin(s),n)}_onBlur(e,i){let n=this._elementInfo.get(i);!n||n.checkChildren&&e.relatedTarget instanceof Node&&i.contains(e.relatedTarget)||(this._setClasses(i),this._emitOrigin(n,null))}_emitOrigin(e,i){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(i))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;let i=e.rootNode,n=this._rootNodeFocusListenerCount.get(i)||0;n||this._ngZone.runOutsideAngular(()=>{i.addEventListener("focus",this._rootNodeFocusAndBlurListener,hn),i.addEventListener("blur",this._rootNodeFocusAndBlurListener,hn)}),this._rootNodeFocusListenerCount.set(i,n+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Qt(this._stopInputModalityDetector)).subscribe(s=>{this._setOrigin(s,!0)}))}_removeGlobalListeners(e){let i=e.rootNode;if(this._rootNodeFocusListenerCount.has(i)){let n=this._rootNodeFocusListenerCount.get(i);n>1?this._rootNodeFocusListenerCount.set(i,n-1):(i.removeEventListener("focus",this._rootNodeFocusAndBlurListener,hn),i.removeEventListener("blur",this._rootNodeFocusAndBlurListener,hn),this._rootNodeFocusListenerCount.delete(i))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,i,n){this._setClasses(e,i),this._emitOrigin(n,i),this._lastFocusOrigin=i}_getClosestElementsInfo(e){let i=[];return this._elementInfo.forEach((n,s)=>{(s===e||n.checkChildren&&s.contains(e))&&i.push([s,n])}),i}_isLastInteractionFromInputLabel(e){let{_mostRecentTarget:i,mostRecentModality:n}=this._inputModalityDetector;if(n!=="mouse"||!i||i===e||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA"||e.disabled)return!1;let s=e.labels;if(s){for(let a=0;a<s.length;a++)if(s[a].contains(i))return!0}return!1}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var vn=new WeakMap,Qs=(()=>{class t{_appRef;_injector=c(gt);_environmentInjector=c(Uo);load(e){let i=this._appRef=this._appRef||this._injector.get(Jo),n=vn.get(i);n||(n={loaders:new Set,refs:[]},vn.set(i,n),i.onDestroy(()=>{vn.get(i)?.refs.forEach(s=>s.destroy()),vn.delete(i)})),n.loaders.has(e)||(n.loaders.add(e),n.refs.push(ls(e,{environmentInjector:this._environmentInjector})))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var qs=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["ng-component"]],exportAs:["cdkVisuallyHidden"],decls:0,vars:0,template:function(i,n){},styles:[`.cdk-visually-hidden{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;white-space:nowrap;outline:0;-webkit-appearance:none;-moz-appearance:none;left:0}[dir=rtl] .cdk-visually-hidden{left:auto;right:0}
`],encapsulation:2,changeDetection:0})}return t})();function ao(t){return Array.isArray(t)?t:[t]}var Ys=new Set,Ut,Zs=(()=>{class t{_platform=c(dt);_nonce=c(Qo,{optional:!0});_matchMedia;constructor(){this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):ga}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&va(e,this._nonce),this._matchMedia(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function va(t,o){if(!Ys.has(t))try{Ut||(Ut=document.createElement("style"),o&&Ut.setAttribute("nonce",o),Ut.setAttribute("type","text/css"),document.head.appendChild(Ut)),Ut.sheet&&(Ut.sheet.insertRule(`@media ${t} {body{ }}`,0),Ys.add(t))}catch(e){console.error(e)}}function ga(t){return{matches:t==="all"||t==="",media:t,addListener:()=>{},removeListener:()=>{}}}var li=(()=>{class t{_mediaMatcher=c(Zs);_zone=c(st);_queries=new Map;_destroySubject=new ke;constructor(){}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return Xs(ao(e)).some(n=>this._registerQuery(n).mql.matches)}observe(e){let n=Xs(ao(e)).map(a=>this._registerQuery(a).observable),s=No(n);return s=Ro(s.pipe(Lo(1)),s.pipe(Xi(1),Bt(0))),s.pipe(yi(a=>{let l={matches:!1,breakpoints:{}};return a.forEach(({matches:p,query:b})=>{l.matches=l.matches||p,l.breakpoints[b]=p}),l}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);let i=this._mediaMatcher.matchMedia(e),s={observable:new Gt(a=>{let l=p=>this._zone.run(()=>a.next(p));return i.addListener(l),()=>{i.removeListener(l)}}).pipe($o(i),yi(({matches:a})=>({query:e,matches:a})),Qt(this._destroySubject)),mql:i};return this._queries.set(e,s),s}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Xs(t){return t.map(o=>o.split(",")).reduce((o,e)=>o.concat(e)).map(o=>o.trim())}var ba=(()=>{class t{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Js=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=yt({type:t});static \u0275inj=vt({providers:[ba]})}return t})();var ya=(()=>{class t{_platform=c(dt);constructor(){}isDisabled(e){return e.hasAttribute("disabled")}isVisible(e){return _a(e)&&getComputedStyle(e).visibility==="visible"}isTabbable(e){if(!this._platform.isBrowser)return!1;let i=Ca(Aa(e));if(i&&(er(i)===-1||!this.isVisible(i)))return!1;let n=e.nodeName.toLowerCase(),s=er(e);return e.hasAttribute("contenteditable")?s!==-1:n==="iframe"||n==="object"||this._platform.WEBKIT&&this._platform.IOS&&!Ma(e)?!1:n==="audio"?e.hasAttribute("controls")?s!==-1:!1:n==="video"?s===-1?!1:s!==null?!0:this._platform.FIREFOX||e.hasAttribute("controls"):e.tabIndex>=0}isFocusable(e,i){return Sa(e)&&!this.isDisabled(e)&&(i?.ignoreVisibility||this.isVisible(e))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Ca(t){try{return t.frameElement}catch{return null}}function _a(t){return!!(t.offsetWidth||t.offsetHeight||typeof t.getClientRects=="function"&&t.getClientRects().length)}function xa(t){let o=t.nodeName.toLowerCase();return o==="input"||o==="select"||o==="button"||o==="textarea"}function wa(t){return Da(t)&&t.type=="hidden"}function Ia(t){return Ta(t)&&t.hasAttribute("href")}function Da(t){return t.nodeName.toLowerCase()=="input"}function Ta(t){return t.nodeName.toLowerCase()=="a"}function nr(t){if(!t.hasAttribute("tabindex")||t.tabIndex===void 0)return!1;let o=t.getAttribute("tabindex");return!!(o&&!isNaN(parseInt(o,10)))}function er(t){if(!nr(t))return null;let o=parseInt(t.getAttribute("tabindex")||"",10);return isNaN(o)?-1:o}function Ma(t){let o=t.nodeName.toLowerCase(),e=o==="input"&&t.type;return e==="text"||e==="password"||o==="select"||o==="textarea"}function Sa(t){return wa(t)?!1:xa(t)||Ia(t)||t.hasAttribute("contenteditable")||nr(t)}function Aa(t){return t.ownerDocument&&t.ownerDocument.defaultView||window}var lo=class{_element;_checker;_ngZone;_document;_injector;_startAnchor;_endAnchor;_hasAttached=!1;startAnchorListener=()=>this.focusLastTabbableElement();endAnchorListener=()=>this.focusFirstTabbableElement();get enabled(){return this._enabled}set enabled(o){this._enabled=o,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(o,this._startAnchor),this._toggleAnchorTabIndex(o,this._endAnchor))}_enabled=!0;constructor(o,e,i,n,s=!1,a){this._element=o,this._checker=e,this._ngZone=i,this._document=n,this._injector=a,s||this.attachAnchors()}destroy(){let o=this._startAnchor,e=this._endAnchor;o&&(o.removeEventListener("focus",this.startAnchorListener),o.remove()),e&&(e.removeEventListener("focus",this.endAnchorListener),e.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(o){return new Promise(e=>{this._executeOnStable(()=>e(this.focusInitialElement(o)))})}focusFirstTabbableElementWhenReady(o){return new Promise(e=>{this._executeOnStable(()=>e(this.focusFirstTabbableElement(o)))})}focusLastTabbableElementWhenReady(o){return new Promise(e=>{this._executeOnStable(()=>e(this.focusLastTabbableElement(o)))})}_getRegionBoundary(o){let e=this._element.querySelectorAll(`[cdk-focus-region-${o}], [cdkFocusRegion${o}], [cdk-focus-${o}]`);return o=="start"?e.length?e[0]:this._getFirstTabbableElement(this._element):e.length?e[e.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(o){let e=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(e){if(!this._checker.isFocusable(e)){let i=this._getFirstTabbableElement(e);return i?.focus(o),!!i}return e.focus(o),!0}return this.focusFirstTabbableElement(o)}focusFirstTabbableElement(o){let e=this._getRegionBoundary("start");return e&&e.focus(o),!!e}focusLastTabbableElement(o){let e=this._getRegionBoundary("end");return e&&e.focus(o),!!e}hasAttached(){return this._hasAttached}_getFirstTabbableElement(o){if(this._checker.isFocusable(o)&&this._checker.isTabbable(o))return o;let e=o.children;for(let i=0;i<e.length;i++){let n=e[i].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(e[i]):null;if(n)return n}return null}_getLastTabbableElement(o){if(this._checker.isFocusable(o)&&this._checker.isTabbable(o))return o;let e=o.children;for(let i=e.length-1;i>=0;i--){let n=e[i].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(e[i]):null;if(n)return n}return null}_createAnchor(){let o=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,o),o.classList.add("cdk-visually-hidden"),o.classList.add("cdk-focus-trap-anchor"),o.setAttribute("aria-hidden","true"),o}_toggleAnchorTabIndex(o,e){o?e.setAttribute("tabindex","0"):e.removeAttribute("tabindex")}toggleAnchors(o){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(o,this._startAnchor),this._toggleAnchorTabIndex(o,this._endAnchor))}_executeOnStable(o){this._injector?Yt(o,{injector:this._injector}):setTimeout(o)}},or=(()=>{class t{_checker=c(ya);_ngZone=c(st);_document=c(ce);_injector=c(gt);constructor(){c(Qs).load(qs)}create(e,i=!1){return new lo(e,this._checker,this._ngZone,this._document,i,this._injector)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),po=(()=>{class t{_elementRef=c(O);_focusTrapFactory=c(or);focusTrap;_previouslyFocusedElement=null;get enabled(){return this.focusTrap?.enabled||!1}set enabled(e){this.focusTrap&&(this.focusTrap.enabled=e)}autoCapture;constructor(){c(dt).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(e){let i=e.autoCapture;i&&!i.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=$s(),this.focusTrap?.focusInitialElementWhenReady()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",v],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",v]},exportAs:["cdkTrapFocus"],features:[_i]})}return t})();var Mt=function(t){return t[t.NONE=0]="NONE",t[t.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",t[t.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",t}(Mt||{}),tr="cdk-high-contrast-black-on-white",ir="cdk-high-contrast-white-on-black",co="cdk-high-contrast-active",sr=(()=>{class t{_platform=c(dt);_hasCheckedHighContrastMode;_document=c(ce);_breakpointSubscription;constructor(){this._breakpointSubscription=c(li).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return Mt.NONE;let e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);let i=this._document.defaultView||window,n=i&&i.getComputedStyle?i.getComputedStyle(e):null,s=(n&&n.backgroundColor||"").replace(/ /g,"");switch(e.remove(),s){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return Mt.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return Mt.BLACK_ON_WHITE}return Mt.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let e=this._document.body.classList;e.remove(co,tr,ir),this._hasCheckedHighContrastMode=!0;let i=this.getHighContrastMode();i===Mt.BLACK_ON_WHITE?e.add(co,tr):i===Mt.WHITE_ON_BLACK&&e.add(co,ir)}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),uo=(()=>{class t{constructor(){c(sr)._applyBodyHighContrastModeCssClasses()}static \u0275fac=function(i){return new(i||t)};static \u0275mod=yt({type:t});static \u0275inj=vt({imports:[Js]})}return t})();var Ea=200,gn=class{_letterKeyStream=new ke;_items=[];_selectedItemIndex=-1;_pressedLetters=[];_skipPredicateFn;_selectedItem=new ke;selectedItem=this._selectedItem;constructor(o,e){let i=typeof e?.debounceInterval=="number"?e.debounceInterval:Ea;e?.skipPredicate&&(this._skipPredicateFn=e.skipPredicate),this.setItems(o),this._setupKeyHandler(i)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(o){this._selectedItemIndex=o}setItems(o){this._items=o}handleKey(o){let e=o.keyCode;o.key&&o.key.length===1?this._letterKeyStream.next(o.key.toLocaleUpperCase()):(e>=65&&e<=90||e>=48&&e<=57)&&this._letterKeyStream.next(String.fromCharCode(e))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(o){this._letterKeyStream.pipe(ht(e=>this._pressedLetters.push(e)),Bt(o),Ge(()=>this._pressedLetters.length>0),yi(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(e=>{for(let i=1;i<this._items.length+1;i++){let n=(this._selectedItemIndex+i)%this._items.length,s=this._items[n];if(!this._skipPredicateFn?.(s)&&s.getLabel?.().toLocaleUpperCase().trim().indexOf(e)===0){this._selectedItem.next(s);break}}this._pressedLetters=[]})}};function rr(t,...o){return o.length?o.some(e=>t[e]):t.altKey||t.shiftKey||t.ctrlKey||t.metaKey}var bn=class{_items;_activeItemIndex=B(-1);_activeItem=B(null);_wrap=!1;_typeaheadSubscription=Fo.EMPTY;_itemChangesSubscription;_vertical=!0;_horizontal;_allowedModifierKeys=[];_homeAndEnd=!1;_pageUpAndDown={enabled:!1,delta:10};_effectRef;_typeahead;_skipPredicateFn=o=>o.disabled;constructor(o,e){this._items=o,o instanceof Fn?this._itemChangesSubscription=o.changes.subscribe(i=>this._itemsChanged(i.toArray())):On(o)&&(this._effectRef=T(()=>this._itemsChanged(o()),{injector:e}))}tabOut=new ke;change=new ke;skipPredicate(o){return this._skipPredicateFn=o,this}withWrap(o=!0){return this._wrap=o,this}withVerticalOrientation(o=!0){return this._vertical=o,this}withHorizontalOrientation(o){return this._horizontal=o,this}withAllowedModifierKeys(o){return this._allowedModifierKeys=o,this}withTypeAhead(o=200){this._typeaheadSubscription.unsubscribe();let e=this._getItemsArray();return this._typeahead=new gn(e,{debounceInterval:typeof o=="number"?o:void 0,skipPredicate:i=>this._skipPredicateFn(i)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(i=>{this.setActiveItem(i)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(o=!0){return this._homeAndEnd=o,this}withPageUpDown(o=!0,e=10){return this._pageUpAndDown={enabled:o,delta:e},this}setActiveItem(o){let e=this._activeItem();this.updateActiveItem(o),this._activeItem()!==e&&this.change.next(this._activeItemIndex())}onKeydown(o){let e=o.keyCode,n=["altKey","ctrlKey","metaKey","shiftKey"].every(s=>!o[s]||this._allowedModifierKeys.indexOf(s)>-1);switch(e){case 9:this.tabOut.next();return;case 40:if(this._vertical&&n){this.setNextItemActive();break}else return;case 38:if(this._vertical&&n){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&n){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&n){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&n){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&n){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&n){let s=this._activeItemIndex()-this._pageUpAndDown.delta;this._setActiveItemByIndex(s>0?s:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&n){let s=this._activeItemIndex()+this._pageUpAndDown.delta,a=this._getItemsArray().length;this._setActiveItemByIndex(s<a?s:a-1,-1);break}else return;default:(n||rr(o,"shiftKey"))&&this._typeahead?.handleKey(o);return}this._typeahead?.reset(),o.preventDefault()}get activeItemIndex(){return this._activeItemIndex()}get activeItem(){return this._activeItem()}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex()<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex()<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(o){let e=this._getItemsArray(),i=typeof o=="number"?o:e.indexOf(o),n=e[i];this._activeItem.set(n??null),this._activeItemIndex.set(i),this._typeahead?.setCurrentSelectedItemIndex(i)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(o){this._wrap?this._setActiveInWrapMode(o):this._setActiveInDefaultMode(o)}_setActiveInWrapMode(o){let e=this._getItemsArray();for(let i=1;i<=e.length;i++){let n=(this._activeItemIndex()+o*i+e.length)%e.length,s=e[n];if(!this._skipPredicateFn(s)){this.setActiveItem(n);return}}}_setActiveInDefaultMode(o){this._setActiveItemByIndex(this._activeItemIndex()+o,o)}_setActiveItemByIndex(o,e){let i=this._getItemsArray();if(i[o]){for(;this._skipPredicateFn(i[o]);)if(o+=e,!i[o])return;this.setActiveItem(o)}}_getItemsArray(){return On(this._items)?this._items():this._items instanceof Fn?this._items.toArray():this._items}_itemsChanged(o){this._typeahead?.setItems(o);let e=this._activeItem();if(e){let i=o.indexOf(e);i>-1&&i!==this._activeItemIndex()&&(this._activeItemIndex.set(i),this._typeahead?.setCurrentSelectedItemIndex(i))}}};var di=class extends bn{_origin="program";setFocusOrigin(o){return this._origin=o,this}setActiveItem(o){super.setActiveItem(o),this.activeItem&&this.activeItem.focus(this._origin)}};var D=["*"],Oa=()=>({display:"none"}),pi=["*","*"];function Fa(t,o){t&1&&W(0)}function Ba(t,o){t&1&&W(0)}function Pa(t,o){t&1&&W(0)}function Na(t,o){if(t&1){let e=Ct();I(0,"button",7),oe("click",function(){Je(e);let n=y();return et(n.toggleItem())}),R(1,Pa,1,0,"ng-container",5),w()}if(t&2){let e=y(),i=Q(9),n=Pn(0);m("collapsed",!e.itemVisible()),M("aria-controls",e.contentId),f(),m("ngTemplateOutlet",n.accordionHeader||i)("ngTemplateOutletContext",e.itemContext)}}function Ra(t,o){t&1&&C(0)}function ja(t,o){t&1&&W(0)}function La(t,o){if(t&1&&(I(0,"div",8),R(1,ja,1,0,"ng-container",5),w()),t&2){let e=y(),i=Q(13),n=Pn(0);f(),m("ngTemplateOutlet",n.accordionBody||i)("ngTemplateOutletContext",e.itemContext)}}function Ha(t,o){t&1&&C(0,1)}function $a(t,o){t&1&&W(0)}function Va(t,o){if(t&1&&R(0,$a,1,0,"ng-container",1),t&2){let e,i=y(2),n=Q(2);m("ngTemplateOutlet",((e=i.templates())==null?null:e.alertButtonCloseTemplate)||n)}}function za(t,o){if(t&1&&(A(0,Va,1,1,"ng-container"),C(1)),t&2){let e=y();E(e.dismissible?0:-1)}}function Wa(t,o){if(t&1){let e=Ct();I(0,"button",2),oe("click",function(){Je(e);let n=y();return et(n.visible=!1)}),w()}}function Ua(t,o){if(t&1&&K(0,"img",1),t&2){let e=y(3);m("src",nn(e.src()??""),Ji)("alt",nn(e.alt()))}}function Ka(t,o){t&1&&(Ci(),I(0,"svg",2),K(1,"rect",3),w())}function Ga(t,o){t&1&&(R(0,Ua,1,4)(1,Ka,2,0),es(2,0,null,null,1),is(),ts())}function Qa(t,o){if(t&1&&A(0,Ga,4,0),t&2){let e=y();E(e.src()?0:-1)}}function qa(t,o){if(t&1&&K(0,"span",0),t&2){let e=y();m("ngClass",e.statusClass())}}var pt=()=>({});function Ya(t,o){t&1&&W(0)}function Xa(t,o){if(t&1&&(I(0,"a",1),R(1,Ya,1,0,"ng-container",3),w()),t&2){let e,i,n,s,a,l,p,b,u=y(),N=Q(3);m("routerLink",u.url())("cHtmlAttr",u.attributes()??Oe(11,pt))("target",(e=u.attributes())==null?null:e.target)("queryParams",((i=u.linkProps())==null?null:i.queryParams)??null)("fragment",(n=u.linkProps())==null?null:n.fragment)("queryParamsHandling",((s=u.linkProps())==null?null:s.queryParamsHandling)??null)("preserveFragment",((a=u.linkProps())==null?null:a.preserveFragment)??!1)("skipLocationChange",((l=u.linkProps())==null?null:l.skipLocationChange)??!1)("replaceUrl",((p=u.linkProps())==null?null:p.replaceUrl)??!1)("state",((b=u.linkProps())==null?null:b.state)??Oe(12,pt)),f(),m("ngTemplateOutlet",N)}}function Za(t,o){t&1&&W(0)}function Ja(t,o){if(t&1&&(I(0,"span",2),R(1,Za,1,0,"ng-container",3),w()),t&2){let e=y(),i=Q(3);m("cHtmlAttr",e.attributes()??Oe(2,pt)),f(),m("ngTemplateOutlet",i)}}function ec(t,o){t&1&&C(0)}function tc(t,o){if(t&1&&(I(0,"c-breadcrumb-item",1),z(1),w()),t&2){let e=y(),i=e.$implicit,n=e.$index,s=e.$count;m("active",n===s-1)("url",i==null?null:i.url)("attributes",i==null?null:i.attributes)("linkProps",i==null?null:i.linkProps),f(),xt(" ",i==null?null:i.label," ")}}function ic(t,o){if(t&1&&A(0,tc,2,5,"c-breadcrumb-item",1),t&2){let e=o.$implicit,i=o.$index,n=o.$count;E(e!=null&&e.label&&((e==null||e.url==null?null:e.url.slice(-1))==="/"||i===n-1)?0:-1)}}function nc(t,o){if(t&1&&(K(0,"span"),I(1,"span",0),z(2),w()),t&2){let e=y();g(e.carouselControlIconClass()),M("aria-hidden",!0),f(2),se(e.caption())}}var oc=(t,o)=>({$implicit:t,active:o}),sc=t=>({active:t});function rc(t,o){t&1&&W(0)}function ac(t,o){if(t&1){let e=Ct();I(0,"button",3),oe("click",function(){let n=Je(e).$index,s=y(2);return et(s.onClick(n))}),w()}if(t&2){let e=o.$index,i=y(2);g(Pt(4,sc,i.active===e)),M("data-coreui-target",e)("aria-current",i.active===e)}}function cc(t,o){if(t&1&&Di(0,ac,1,6,"button",2,Ii),t&2){let e=y();Ti(e.items)}}function lc(t,o){t&1&&C(0)}function dc(t,o){if(t&1&&(I(0,"div",0),C(1),w()),t&2){let e=y();m("ngClass",e.headerClasses())}}function pc(t,o){t&1&&C(0,1)}function uc(t,o){t&1&&W(0)}function fc(t,o){if(t&1&&(I(0,"div",3),C(1),w()),t&2){let e=y();m("ngClass",e.containerClass())}}function mc(t,o){t&1&&C(0,1)}var hc=["modalContentRef"],vc="[_nghost-%COMP%]{display:list-item;text-align:-webkit-match-parent;text-align:match-parent}",gc=["popoverTemplate"],bc=t=>({"popover-arrow":t});function yc(t,o){t&1&&W(0)}function Cc(t,o){if(t&1&&R(0,yc,1,0,"ng-container",2),t&2){y();let e=Q(3);m("ngTemplateOutlet",e)}}function _c(t,o){t&1&&W(0)}function xc(t,o){if(t&1&&(I(0,"c-progress-bar",1),R(1,_c,1,0,"ng-container",2),w()),t&2){let e=y(),i=Q(3),n=e.progressBarDirective;m("animated",n==null?null:n.animated())("variant",n==null?null:n.variant())("color",n==null?null:n.color())("value",e.barValue()),f(),m("ngTemplateOutlet",i)}}function wc(t,o){t&1&&C(0)}function Ic(t,o){if(t&1&&K(0,"img",1),t&2){let e=y(2);m("cHtmlAttr",e.brandFull())("ngClass","sidebar-brand-full")}}function Dc(t,o){if(t&1&&K(0,"img",1),t&2){let e=y(2);m("cHtmlAttr",e.brandNarrow())("ngClass","sidebar-brand-narrow")}}function Tc(t,o){if(t&1&&(I(0,"a",0),A(1,Ic,1,2,"img",1),A(2,Dc,1,2,"img",1),w()),t&2){let e=y();m("routerLink",e.routerLink()),f(),E(e.brandFull()?1:-1),f(),E(e.brandNarrow()?2:-1)}}function Mc(t,o){t&1&&C(0)}function Sc(t,o){if(t&1&&(it(0),z(1),nt()),t&2){let e=y();f(),se((e.item==null?null:e.item.name)??"")}}var wn=t=>({$implicit:t}),Ac=()=>({exact:!1});function Ec(t,o){t&1&&W(0)}function kc(t,o){if(t&1&&(I(0,"span",6),ue(1,"cSidebarNavBadge"),z(2),w()),t&2){let e=y(2);m("ngClass",fe(1,2,e.item)),f(2),se(e.item.badge==null?null:e.item.badge.text)}}function Oc(t,o){if(t&1&&(I(0,"a",1),ue(1,"cSidebarNavLink"),R(2,Ec,1,0,"ng-container",4),K(3,"c-sidebar-nav-link-content",5),A(4,kc,3,4,"span",6),w()),t&2){let e=y(),i=Q(4);m("cHtmlAttr",e.item.attributes??Oe(8,pt))("ngClass",fe(1,6,e.item)),f(2),m("ngTemplateOutlet",i)("ngTemplateOutletContext",Pt(9,wn,e.item)),f(),m("item",e.item),f(),E(e.item.badge?4:-1)}}function Fc(t,o){t&1&&W(0)}function Bc(t,o){if(t&1&&(I(0,"span",6),ue(1,"cSidebarNavBadge"),z(2),w()),t&2){let e=y(2);m("ngClass",fe(1,2,e.item)),f(2),se(e.item.badge==null?null:e.item.badge.text)}}function Pc(t,o){if(t&1){let e=Ct();I(0,"a",7),ue(1,"cSidebarNavLink"),oe("click",function(){Je(e);let n=y();return et(n.linkClicked())}),R(2,Fc,1,0,"ng-container",4),K(3,"c-sidebar-nav-link-content",5),A(4,Bc,3,4,"span",6),w()}if(t&2){let e=y(),i=Q(4);m("cHtmlAttr",e.item.attributes??Oe(9,pt))("href",e.href,Ji)("ngClass",fe(1,7,e.item)),f(2),m("ngTemplateOutlet",i)("ngTemplateOutletContext",Pt(10,wn,e.item)),f(),m("item",e.item),f(),E(e.item.badge?4:-1)}}function Nc(t,o){t&1&&W(0)}function Rc(t,o){if(t&1&&(I(0,"span",6),ue(1,"cSidebarNavBadge"),z(2),w()),t&2){let e=y(2);m("ngClass",fe(1,2,e.item)),f(2),se(e.item.badge==null?null:e.item.badge.text)}}function jc(t,o){if(t&1){let e=Ct();I(0,"a",8),ue(1,"cSidebarNavLink"),oe("click",function(){Je(e);let n=y();return et(n.linkClicked())}),R(2,Nc,1,0,"ng-container",4),K(3,"c-sidebar-nav-link-content",5),A(4,Rc,3,4,"span",6),w()}if(t&2){let e=y(),i=Q(4);m("cHtmlAttr",e.item.attributes??Oe(18,pt))("fragment",e.item.linkProps==null?null:e.item.linkProps.fragment)("ngClass",fe(1,16,e.item))("preserveFragment",(e.item.linkProps==null?null:e.item.linkProps.preserveFragment)??!1)("queryParamsHandling",e.item.linkProps==null?null:e.item.linkProps.queryParamsHandling)("queryParams",(e.item.linkProps==null?null:e.item.linkProps.queryParams)??null)("replaceUrl",(e.item.linkProps==null?null:e.item.linkProps.replaceUrl)??!1)("routerLinkActiveOptions",(e.item.linkProps==null?null:e.item.linkProps.routerLinkActiveOptions)??Oe(19,Ac))("routerLink",e.item.url)("skipLocationChange",(e.item.linkProps==null?null:e.item.linkProps.skipLocationChange)??!1)("state",(e.item.linkProps==null?null:e.item.linkProps.state)??Oe(20,pt))("target",e.item.attributes==null?null:e.item.attributes.target),f(2),m("ngTemplateOutlet",i)("ngTemplateOutletContext",Pt(21,wn,e.item)),f(),m("item",e.item),f(),E(e.item.badge?4:-1)}}function Lc(t,o){if(t&1&&(I(0,"span",9),K(1,"span",6),w()),t&2){let e=y().$implicit;f(),m("ngClass",e.icon??"")}}function Hc(t,o){if(t&1&&(Ci(),K(0,"svg",10),ue(1,"cSidebarNavIcon")),t&2){let e=y().$implicit;m("cIcon",e.iconComponent==null?null:e.iconComponent.content)("customClasses",fe(1,3,e))("name",e.iconComponent==null?null:e.iconComponent.name)}}function $c(t,o){if(t&1&&(K(0,"span",6),ue(1,"cSidebarNavIcon")),t&2){let e=y().$implicit;m("ngClass",fe(1,1,e))}}function Vc(t,o){if(t&1&&(A(0,Lc,2,1,"span",9),A(1,Hc,2,5,":svg:svg",10),A(2,$c,2,3,"span",6)),t&2){let e=o.$implicit;E(e!=null&&e.icon?0:-1),f(),E(e!=null&&e.iconComponent?1:-1),f(),E(!(e!=null&&e.icon)&&!(e!=null&&e.iconComponent)?2:-1)}}function zc(t,o){if(t&1&&K(0,"i",1),t&2){let e=y();m("ngClass",e.getLabelIconClass())}}function Wc(t,o){if(t&1&&(I(0,"span",1),ue(1,"cSidebarNavBadge"),z(2),w()),t&2){let e=y();m("ngClass",fe(1,2,e.item)),f(2),se(e.item.badge.text)}}function Uc(t,o){t&1&&W(0)}function Kc(t,o){if(t&1&&(I(0,"span",3),ue(1,"cSidebarNavBadge"),z(2),w()),t&2){let e=y();m("ngClass",fe(1,2,e.item)),f(2),se(e.item.badge.text)}}function Gc(t,o){if(t&1&&(I(0,"span",5),K(1,"span",3),w()),t&2){let e=y().$implicit;f(),m("ngClass",e.icon??"")}}function Qc(t,o){if(t&1&&(Ci(),K(0,"svg",6),ue(1,"cSidebarNavIcon")),t&2){let e=y().$implicit;m("cIcon",e.iconComponent==null?null:e.iconComponent.content)("customClasses",fe(1,3,e))("name",e.iconComponent==null?null:e.iconComponent.name)}}function qc(t,o){if(t&1&&(K(0,"span",3),ue(1,"cSidebarNavIcon")),t&2){let e=y().$implicit;m("ngClass",fe(1,1,e))}}function Yc(t,o){if(t&1&&(A(0,Gc,2,1,"span",5),A(1,Qc,2,5,":svg:svg",6),A(2,qc,2,3,"span",3)),t&2){let e=o.$implicit;E(e!=null&&e.icon?0:-1),f(),E(e!=null&&e.iconComponent?1:-1),f(),E(!(e!=null&&e.icon)&&!(e!=null&&e.iconComponent)?2:-1)}}var Xc=()=>({exact:!0});function Zc(t,o){if(t&1&&(K(0,"c-sidebar-nav-group",1,0),ue(2,"cSidebarNavItemClass")),t&2){let e=y().$implicit,i=y();m("dropdownMode",i.dropdownMode)("item",e)("ngClass",fe(2,5,e))("routerLinkActiveOptions",Oe(7,Xc))("compact",i.compact)}}function Jc(t,o){if(t&1&&(K(0,"c-sidebar-nav-divider",2),ue(1,"cSidebarNavItemClass")),t&2){let e=y().$implicit;m("cHtmlAttr",e.attributes??Oe(5,pt))("item",e)("ngClass",fe(1,3,e))}}function el(t,o){if(t&1&&(K(0,"c-sidebar-nav-title",2),ue(1,"cSidebarNavItemClass")),t&2){let e=y().$implicit;m("cHtmlAttr",e.attributes??Oe(5,pt))("item",e)("ngClass",fe(1,3,e))}}function tl(t,o){if(t&1&&(K(0,"c-sidebar-nav-label",3),ue(1,"cSidebarNavItemClass")),t&2){let e=y().$implicit;m("item",e)("ngClass",fe(1,2,e))}}function il(t,o){t&1&&W(0)}function nl(t,o){if(t&1){let e=Ct();I(0,"c-sidebar-nav-link",4),ue(1,"cSidebarNavItemClass"),oe("linkClick",function(){Je(e);let n=y(2);return et(n.hideMobile())}),w()}if(t&2){let e=y().$implicit;m("item",e)("ngClass",fe(1,2,e))}}function ol(t,o){if(t&1&&A(0,Zc,3,8,"c-sidebar-nav-group",1)(1,Jc,2,6,"c-sidebar-nav-divider",2)(2,el,2,6,"c-sidebar-nav-title",2)(3,tl,2,4,"c-sidebar-nav-label",3)(4,il,1,0,"ng-container")(5,nl,2,4,"c-sidebar-nav-link",3),t&2){let e,i=o.$implicit,n=y();E((e=n.helper.itemType(i))==="group"?0:e==="divider"?1:e==="title"?2:e==="label"?3:e==="empty"?4:5)}}function sl(t,o){if(t&1&&(I(0,"span",0),z(1),w()),t&2){let e=y();f(),se(e.label())}}var rl=()=>({outline:0});function al(t,o){if(t&1&&K(0,"button",1),t&2){let e=y();ei(Oe(3,rl)),m("cToastClose",e.toast())}}function cl(t,o){}var ll=["tooltipTemplate"],dl=[[["",8,"chart-wrapper"]],"*"],pl=[".chart-wrapper","*"];function ul(t,o){t&1&&W(0)}function fl(t,o){if(t&1&&(I(0,"div",5),R(1,ul,1,0,"ng-container",6),w()),t&2){let e=y(),i=Q(10);f(),m("ngTemplateOutlet",(e.templates==null?null:e.templates.widgetValueTemplate)||i)}}function ml(t,o){t&1&&W(0)}function hl(t,o){if(t&1&&(I(0,"div"),R(1,ml,1,0,"ng-container",6),w()),t&2){let e=y(),i=Q(8);f(),m("ngTemplateOutlet",(e.templates==null?null:e.templates.widgetTitleTemplate)||i)}}function vl(t,o){t&1&&W(0)}function gl(t,o){t&1&&W(0)}function bl(t,o){if(t&1&&z(0),t&2){let e=y();xt(" ",e.title(),`
`)}}function yl(t,o){if(t&1&&z(0),t&2){let e=y();xt(" ",e.value(),`
`)}}function Cl(t,o){t&1&&C(0)}function _l(t,o){t&1&&C(0,1)}function xl(t,o){if(t&1&&(I(0,"div",0),z(1),w()),t&2){let e=y();f(),se(e.value())}}function wl(t,o){if(t&1&&(I(0,"div"),z(1),w()),t&2){let e=y();f(),se(e.title())}}function Il(t,o){if(t&1&&(I(0,"small",1),z(1),w()),t&2){let e=y();m("ngClass",e.inverse()?"text-white text-opacity-75":"text-body-secondary"),f(),xt(" ",e.text()," ")}}function Dl(t,o){t&1&&W(0)}function Tl(t,o){if(t&1&&(I(0,"div",2),R(1,Dl,1,0,"ng-container",3),w()),t&2){let e=y(),i=Q(6);m("ngClass",e.iconClasses()),f(),m("ngTemplateOutlet",(e.templates==null?null:e.templates.widgetIconTemplate)||i)}}function Ml(t,o){if(t&1&&(I(0,"div",2),z(1),w()),t&2){let e=y();m("ngClass",e.valueClasses()),f(),xt(" ",e.value()," ")}}function Sl(t,o){if(t&1&&(I(0,"div",2),z(1),w()),t&2){let e=y();m("ngClass",e.titleClasses()),f(),xt(" ",e.title()," ")}}function Al(t,o){t&1&&W(0)}function El(t,o){if(t&1&&R(0,Al,1,0,"ng-container",3),t&2){let e=y(),i=Q(8);m("ngTemplateOutlet",(e.templates==null?null:e.templates.widgetProgressTemplate)||i)}}function kl(t,o){if(t&1&&z(0),t&2){let e=y();xt(" ",e.icon(),`
`)}}function Ol(t,o){t&1&&C(0)}function Fl(t,o){t&1&&K(0,"div",2)}function Bl(t,o){if(t&1&&(A(0,Fl,1,0,"div",2),I(1,"c-col")(2,"div",3),z(3),w(),I(4,"div",4),z(5),w()()),t&2){let e=o.$implicit,i=o.$index;E(i%2!==0?0:-1),f(3),se(e.value),f(2),se(e.title)}}function Pl(t,o){if(t&1&&(I(0,"div",1),z(1),w()),t&2){let e=y();m("ngClass",e.titleClasses()),f(),se(e.title())}}function Nl(t,o){if(t&1&&(I(0,"div",2),z(1),w()),t&2){let e=y();f(),se(e.value())}}function Rl(t,o){t&1&&W(0)}function jl(t,o){t&1&&W(0)}function Ll(t,o){if(t&1&&(I(0,"c-card-footer"),R(1,jl,1,0,"ng-container",3),w()),t&2){let e=y(),i=Q(13);f(),m("ngTemplateOutlet",(e.templates==null?null:e.templates.widgetFooterTemplate)||i)}}function Hl(t,o){if(t&1&&(I(0,"span"),z(1),w()),t&2){let e=y();f(),se(e.icon())}}function $l(t,o){if(t&1&&(I(0,"span"),z(1),w()),t&2){let e=y();f(),se(e.footer())}}var In=function(t){return t.xs="xs",t.sm="sm",t.md="md",t.lg="lg",t.xl="xl",t.xxl="xxl",t}(In||{});var mi=(()=>{class t{cHtmlAttr=r();#e=c(X);#t=c(O);#i=T(()=>{let e=this.cHtmlAttr();for(let i in e)i==="style"&&typeof e[i]=="object"?this.setStyle(e[i]):i==="class"?this.addClass(e[i]):this.setAttrib(i,e[i])});setStyle(e){for(let i in e)i&&this.#e.setStyle(this.#t.nativeElement,i,e[i])}addClass(e){(Array.isArray(e)?e:e.split(" ")).filter(n=>n.length>0).forEach(n=>{this.#e.addClass(this.#t.nativeElement,n)})}setAttrib(e,i){i!==null?this.#e.setAttribute(this.#t.nativeElement,e,i):this.#e.removeAttribute(this.#t.nativeElement,e)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cHtmlAttr",""]],inputs:{cHtmlAttr:[1,"cHtmlAttr"]},exportAs:["cHtmlAttr"]})}return t})(),ze=(()=>{class t{templateRef=c(en);cTemplateId=r.required();get id(){return this.cTemplateId()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTemplateId",""]],inputs:{cTemplateId:[1,"cTemplateId"]}})}return t})(),zi=(()=>{class t{#e=c(O);#t=c(X);colorScheme=r();#i=T(()=>{let e=this.colorScheme();e?this.setTheme(e):this.unsetTheme()});dark=r(!1,{transform:v});#n=T(()=>{this.dark()||Fe(this.colorScheme)==="dark"?this.setTheme("dark"):this.unsetTheme()});setTheme(e){e&&this.#t.setAttribute(this.#e.nativeElement,"data-coreui-theme",e)}unsetTheme(){this.#t.removeAttribute(this.#e.nativeElement,"data-coreui-theme")}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTheme",""]],inputs:{colorScheme:[1,"colorScheme"],dark:[1,"dark"]},exportAs:["cTheme"]})}return t})();var Vl=(()=>{class t{collapsed=r(void 0);type=r("button");hostClasses=d(()=>({"accordion-button":!0,collapsed:this.collapsed()}));ariaExpanded=d(()=>!this.collapsed());static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cAccordionButton",""]],hostVars:4,hostBindings:function(i,n){i&2&&(M("type",n.type())("aria-expanded",n.ariaExpanded()),g(n.hostClasses()))},inputs:{collapsed:[1,"collapsed"],type:[1,"type"]}})}return t})(),ho=(()=>{class t{items=[];alwaysOpen=!1;addItem(e){this.items.push(e)}removeItem(e){let i=this.items.indexOf(e);i!==-1&&this.items.splice(i,1)}toggleItem(e){e.itemVisible.update(i=>!i),this.closeOtherItems(e)}closeOtherItems(e){this.alwaysOpen||this.items.forEach(i=>{i!==e&&i.itemVisible.set(!1)})}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),ug=(()=>{class t{#e=c(ho);flush=r(!1,{transform:v});alwaysOpen=r(!1,{transform:v});#t=T(()=>{this.#e.alwaysOpen=this.alwaysOpen()});hostClasses=d(()=>({accordion:!0,"accordion-flush":this.flush()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-accordion"]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{flush:[1,"flush"],alwaysOpen:[1,"alwaysOpen"]},exportAs:["cAccordionItem"],features:[Ne([ho])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),zl=ot([q("{{ time }} {{ easing }}")]),Wl=ot([H({height:"*",minHeight:"*"}),q("{{ time }} {{ easing }}",H({height:0,minHeight:0}))]),Ul=ot([q("{{ time }} {{ easing }}")]),Kl=ot([q("{{ time }} {{ easing }}")]),vo=(()=>{class t{#e=c(zn);#t=c(O);#i=c(X);#n=void 0;constructor(){Yt({read:()=>{this.#o.set(!0)}})}animateInput=r(!0,{transform:v,alias:"animate"});animate=me({source:this.animateInput,computation:e=>e});horizontal=r(!1,{transform:v});visibleInput=r(!1,{transform:v,alias:"visible"});visibleChange=Re();visible=me({source:this.visibleInput,computation:e=>e});#o=B(!1);#s=T(()=>{let e=this.visible();this.#o()&&this.createPlayer(e)});navbar=r(!1,{transform:v});duration=r("350ms");transition=r("ease");collapseChange=Re();hostClasses=d(()=>({"navbar-collapse":this.navbar(),"collapse-horizontal":this.horizontal()}));ngOnDestroy(){this.destroyPlayer()}toggle(e=!this.visible()){this.visible.set(e)}destroyPlayer(){this.#n?.destroy(),this.#n=void 0}createPlayer(e=this.visible()){this.#n?.hasStarted()&&this.destroyPlayer();let i=this.#t.nativeElement;e&&this.#i.removeStyle(i,"display");let n=this.animate()?this.duration():"0ms",s=this.horizontal()?Ul:zl,a=this.horizontal()?Kl:Wl,l=this.horizontal()?"width":"height",b=`scroll${l[0].toUpperCase()+l.slice(1)}`,u=this.#e?.build(Nt(e?s:a,{params:{time:n,easing:this.transition()}}));this.#n=u.create(i),!e&&i.offsetHeight&&i.style[l]&&i.scrollHeight,this.#i.setStyle(i,l,e?0:`${i.getBoundingClientRect()[l]}px`),this.#n.onStart(()=>{this.setMaxSize(),this.#i.removeClass(i,"collapse"),this.#i.addClass(i,"collapsing"),this.#i.removeClass(i,"show"),this.#i.setStyle(i,l,e?`${i[b]}px`:""),this.#n&&this.collapseChange?.emit(e?"opening":"collapsing")}),this.#n.onDone(()=>{this.#i.removeClass(i,"collapsing"),this.#i.addClass(i,"collapse"),e?(this.#i.addClass(i,"show"),this.#i.setStyle(i,l,"")):this.#i.removeClass(i,"show"),this.#n&&(this.collapseChange?.emit(e?"open":"collapsed"),this.visibleChange?.emit(e)),this.destroyPlayer()}),this.#n?.play()}setMaxSize(){let e=this.#t.nativeElement;this.horizontal()&&e.scrollWidth>0&&this.#i.setStyle(e,"maxWidth",`${e.scrollWidth}px`)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCollapse",""]],hostVars:5,hostBindings:function(i,n){i&2&&(ei(Oe(4,Oa)),g(n.hostClasses()))},inputs:{animateInput:[1,"animate","animateInput"],horizontal:[1,"horizontal"],visibleInput:[1,"visible","visibleInput"],navbar:[1,"navbar"],duration:[1,"duration"],transition:[1,"transition"]},outputs:{visibleChange:"visibleChange",collapseChange:"collapseChange"},exportAs:["cCollapse"]})}return t})();var Gl=0,fg=(()=>{class t{#e=c(ho);visibleInput=r(!1,{transform:v,alias:"visible"});itemVisible=B(!1);#t=T(()=>{this.visible=this.visibleInput()});set visible(e){this.itemVisible.set(e)}get visible(){return this.itemVisible()}contentId=`accordion-item-${Gl++}`;get itemContext(){return{$implicit:this.itemVisible()}}contentTemplates=je(ze,{descendants:!0});templates=d(()=>this.contentTemplates().reduce((e,i)=>(e[i.id]=i.templateRef,e),{}));ngOnInit(){this.#e.addItem(this)}ngOnDestroy(){this.#e.removeItem(this)}toggleItem(){this.#e.toggleItem(this)}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-accordion-item"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostAttrs:[1,"accordion-item"],inputs:{visibleInput:[1,"visible","visibleInput"]},exportAs:["cAccordionItem"],ngContentSelectors:pi,decls:14,vars:8,consts:[["defaultAccordionHeaderTemplate",""],["defaultAccordionHeaderContentTemplate",""],["defaultAccordionBodyTemplate",""],["defaultAccordionBodyContentTemplate",""],[1,"accordion-header"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["cCollapse","",1,"accordion-collapse",3,"visible","id"],["cAccordionButton","",3,"click","collapsed"],[1,"accordion-body"]],template:function(i,n){if(i&1&&(x(pi),rs(0),it(1),I(2,"div",4),R(3,Fa,1,0,"ng-container",5),w(),I(4,"div",6),R(5,Ba,1,0,"ng-container",5),w(),nt(),R(6,Na,2,4,"ng-template",null,0,re)(8,Ra,1,0,"ng-template",null,1,re)(10,La,2,2,"ng-template",null,2,re)(12,Ha,1,0,"ng-template",null,3,re)),i&2){let s=Q(7),a=Q(11),l=as(n.templates());f(3),m("ngTemplateOutlet",l.accordionHeaderTemplate||s)("ngTemplateOutletContext",n.itemContext),f(),m("visible",n.itemVisible())("id",n.contentId),M("aria-expanded",n.itemVisible()),f(),m("ngTemplateOutlet",l.accordionBodyTemplate||a)("ngTemplateOutletContext",n.itemContext)}},dependencies:[Vl,Le,vo],styles:["[_nghost-%COMP%]{display:block;overflow:hidden}"]})}return t})();var mg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cAlertHeading",""]],hostAttrs:[1,"alert-heading"]})}return t})(),hg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cAlertLink",""]],hostAttrs:[1,"alert-link"]})}return t})(),Ql=(()=>{class t{static ngAcceptInputType_active;static ngAcceptInputType_disabled;active=r(!1,{transform:v});color=r("primary");disabled=r(!1,{transform:v});shape=r();size=r("");tabindex=r(void 0,{transform:Ae});type=r("button");variant=r();hostClasses=d(()=>({btn:!0,[`btn-${this.color()}`]:!!this.color()&&!this.variant(),[`btn-${this.variant()}`]:!!this.variant()&&!this.color(),[`btn-${this.variant()}-${this.color()}`]:!!this.variant()&&!!this.color(),[`btn-${this.size()}`]:!!this.size(),[`${this.shape()}`]:!!this.shape(),active:this.active(),disabled:this._disabled()}));_disabled=d(()=>this.disabled());ariaDisabled=d(()=>this._disabled()?!0:null);attrDisabled=d(()=>this._disabled()?"":null);tabIndex=d(()=>this._disabled()?"-1":this.tabindex()??null);isActive=d(()=>this.active()||null);static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cButton",""]],hostAttrs:[1,"btn"],hostVars:7,hostBindings:function(i,n){i&2&&(M("aria-disabled",n.ariaDisabled())("aria-pressed",n.isActive())("disabled",n.attrDisabled())("tabindex",n.tabIndex())("type",n.type()),g(n.hostClasses()))},inputs:{active:[1,"active"],color:[1,"color"],disabled:[1,"disabled"],shape:[1,"shape"],size:[1,"size"],tabindex:[1,"tabindex"],type:[1,"type"],variant:[1,"variant"]},exportAs:["cButton"]})}return t})(),mr=(()=>{class t extends Ql{white=r(!1,{transform:v});hostClasses=d(()=>({btn:!0,"btn-close":!0,"btn-close-white":this.white(),[`btn-${this.size()}`]:!!this.size(),active:this.active(),disabled:this._disabled()}));static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275dir=_({type:t,selectors:[["","cButtonClose",""]],hostAttrs:[1,"btn","btn-close"],hostVars:7,hostBindings:function(i,n){i&2&&(M("aria-disabled",n.ariaDisabled())("aria-pressed",n.isActive())("disabled",n.attrDisabled())("tabindex",n.tabIndex())("type",n.type()),g(n.hostClasses()))},inputs:{white:[1,"white"]},features:[Pe([{directive:zi,inputs:["dark","dark"]}]),Qe]})}return t})();var vg=(()=>{class t{color=r("primary");role=r("alert");variant=r();dismissibleInput=r(!1,{transform:v,alias:"dismissible"});#e=me({source:this.dismissibleInput,computation:e=>e});set dismissible(e){this.#e.set(e)}get dismissible(){return this.#e()}fade=r(!1,{transform:v});visibleInput=r(!0,{transform:v,alias:"visible"});#t=me({source:this.visibleInput,computation:e=>e});set visible(e){this.#t()!==e&&(this.#t.set(e),this.visibleChange?.emit(e))}get visible(){return this.#t()}hide=B(!1);visibleChange=Re();contentTemplates=je(ze,{descendants:!0});templates=d(()=>this.contentTemplates().reduce((e,i)=>(e[i.id]=i.templateRef,e),{}));get animateType(){return this.visible?"show":"hide"}hostClasses=d(()=>{let e=this.color(),i=this.variant();return{alert:!0,"alert-dismissible":this.dismissible,fade:this.fade(),show:!this.hide(),[`alert-${e}`]:!!e&&i!=="solid",[`bg-${e}`]:!!e&&i==="solid","text-white":!!e&&i==="solid"}});onAnimationStart(e){this.onAnimationEvent(e)}onAnimationDone(e){this.onAnimationEvent(e)}onAnimationEvent(e){this.hide.set(e.phaseName==="start"&&e.toState==="show"),e.phaseName==="done"&&(this.hide.set(e.toState==="hide"||e.toState==="void"),e.toState==="show"&&this.hide.set(!1))}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-alert"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostVars:5,hostBindings:function(i,n){i&1&&tn("@fadeInOut.start",function(a){return n.onAnimationStart(a)})("@fadeInOut.done",function(a){return n.onAnimationDone(a)}),i&2&&(Zt("@.disabled",!n.fade())("@fadeInOut",n.animateType),M("role",n.role()),g(n.hostClasses()))},inputs:{color:[1,"color"],role:[1,"role"],variant:[1,"variant"],dismissibleInput:[1,"dismissible","dismissibleInput"],fade:[1,"fade"],visibleInput:[1,"visible","visibleInput"]},outputs:{visibleChange:"visibleChange"},exportAs:["cAlert"],ngContentSelectors:D,decls:3,vars:1,consts:[["defaultAlertButtonCloseTemplate",""],[4,"ngTemplateOutlet"],["aria-label","Close","cButtonClose","",3,"click"]],template:function(i,n){i&1&&(x(),A(0,za,2,1),R(1,Wa,1,0,"ng-template",null,0,re)),i&2&&E(n.visible||!n.hide()?0:-1)},dependencies:[Le,mr],styles:["[_nghost-%COMP%]{display:block;overflow:hidden}"],data:{animation:[It("fadeInOut",[Me("show",H({opacity:1,height:"*",padding:"*",border:"*",margin:"*"})),Me("hide",H({opacity:0,height:0,padding:0,border:0,margin:0})),Me("void",H({opacity:0,height:0,padding:0,border:0,margin:0})),Ie("show => hide",[q(".3s ease-out")]),Ie("hide => show",[q(".3s ease-in")]),Ie("show => void",[q(".3s ease-out")]),Ie("void => show",[q(".3s ease-in")])])]}})}return t})();var gg=(()=>{class t{align=r(void 0,{alias:"cAlign"});hostClasses=d(()=>{let e=this.align();return{[`align-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cAlign",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{align:[1,"cAlign","align"]},exportAs:["cAlign"]})}return t})(),bg=(()=>{class t{cBgColor=r("");gradient=r();hostClasses=d(()=>{let e=this.cBgColor();return{[`bg-${e}`]:!!e,"bg-gradient":this.gradient()}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cBgColor",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{cBgColor:[1,"cBgColor"],gradient:[1,"gradient"]},exportAs:["cBgColor"]})}return t})(),yg=(()=>{class t{cBorder=r(!0);hostClasses=d(()=>{let e=this.cBorder();if(typeof e=="boolean")return{border:e};if(typeof e=="number"||typeof e=="string")return{border:!0,[`border-${e}`]:!0};if(typeof e=="object"){let i=S({top:void 0,end:void 0,bottom:void 0,start:void 0,color:void 0},e),n=Object.keys(i).filter(a=>i[a]!==void 0),s={};return n.forEach(a=>{let l=i[a];typeof l=="boolean"?s[`border-${a}`]=!0:typeof l=="number"||typeof l=="string"?s[`border-${a}-${l}`]=!0:typeof l=="object"&&("color"in l&&(s[`border-${a}-${l.color}`]=!0),"width"in l&&(s[`border-${a}-${l.width}`]=!0))}),Object.entries(s).length===0?{border:!1}:s}return{border:!1}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cBorder",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{cBorder:[1,"cBorder"]},exportAs:["cBorder"]})}return t})(),Cg=(()=>{class t{cRounded=r(!0);hostClasses=d(()=>{let e=this.cRounded();if(typeof e=="boolean")return{rounded:e};if(typeof e=="number"||typeof e=="string")return{[`rounded-${e}`]:!0};if(typeof e=="object"){let i=S({top:void 0,end:void 0,bottom:void 0,start:void 0,circle:void 0,pill:void 0,size:void 0},e),n=Object.keys(i).filter(a=>i[a]!==void 0),s={};return n.forEach(a=>{let l=i[a];typeof l=="boolean"?s[`rounded-${a}`]=l:s[`rounded-${l}`]=!0}),Object.entries(s).length===0?{rounded:!1}:s}return{rounded:!1}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cRounded",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{cRounded:[1,"cRounded"]},exportAs:["cRounded"]})}return t})(),_g=(()=>{class t{#e=c(be);#t=c(ce);#i=c(O);#n=B(!1);#o=T(()=>{this.#i.nativeElement.classList.toggle(this.#a,this.#n())});#s;#a="shadow-sm";constructor(){this.#e.onDestroy(()=>{this.#o?.destroy()})}cShadowOnScroll=r(!0);#r=T(()=>{let e=this.cShadowOnScroll();Fe(()=>{this.#n.set(!1),e?(this.#a=e===!0?"shadow":`shadow-${e}`,this.#s=Ft(this.#t,"scroll").pipe(ye(this.#e)).subscribe(i=>{this.#n.set(this.#t.documentElement.scrollTop>0)})):this.#s?.unsubscribe()})});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cShadowOnScroll",""]],inputs:{cShadowOnScroll:[1,"cShadowOnScroll"]},exportAs:["cShadowOnScroll"]})}return t})(),_o=(()=>{class t{color=r("",{alias:"cTextColor"});hostClasses=d(()=>{let e=this.color();return{[`text-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTextColor",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"cTextColor","color"]}})}return t})(),hr=(()=>{class t{textBgColor=r("",{alias:"cTextBgColor"});hostClasses=d(()=>{let e=this.textBgColor();return{[`text-bg-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTextBgColor",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{textBgColor:[1,"cTextBgColor","textBgColor"]}})}return t})();var xg=(()=>{class t{color=r();shape=r();size=r("");alt=r("");src=r();status=r();textColor=r();statusClass=d(()=>({"avatar-status":!0,[`bg-${this.status()}`]:!!this.status()}));hostClasses=d(()=>({avatar:!0,[`avatar-${this.size()}`]:!!this.size(),[`bg-${this.color()}`]:!!this.color(),[`${this.shape()}`]:!!this.shape()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-avatar"]],hostAttrs:[1,"avatar"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"color"],shape:[1,"shape"],size:[1,"size"],alt:[1,"alt"],src:[1,"src"],status:[1,"status"],textColor:[1,"textColor"]},features:[Pe([{directive:_o,inputs:["cTextColor","textColor"]}])],ngContentSelectors:D,decls:3,vars:1,consts:[[3,"ngClass"],[1,"avatar-img",3,"src","alt"],["aria-label","Avatar placeholder","focusable","false","preserveAspectRatio","xMidYMid slice","role","img","xmlns","http://www.w3.org/2000/svg",1,"avatar-img",2,"position","absolute","width","100%","height","100%","inset","0"],["fill","#868e96","height","100%","width","100%"]],template:function(i,n){i&1&&(x(),C(0,0,null,Qa,1,1),A(2,qa,1,1,"span",0)),i&2&&(f(2),E(n.status()?2:-1))},dependencies:[we],styles:["[_nghost-%COMP%]   .avatar-img[_ngcontent-%COMP%]{object-fit:cover}"]})}return t})();var wg=(()=>{class t{color=r();position=r();shape=r();size=r();textColor=r();textBgColor=r();hostClasses=d(()=>{let e=this.position(),i={"position-absolute":!!e,"translate-middle":!!e,"top-0":e?.includes("top"),"top-100":e?.includes("bottom"),"start-100":e?.includes("end"),"start-0":e?.includes("start")};return Object.assign({badge:!0,[`bg-${this.color()}`]:!!this.color(),[`badge-${this.size()}`]:!!this.size(),[`${this.shape()}`]:!!this.shape()},e?i:{})});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-badge"]],hostAttrs:[1,"badge"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"color"],position:[1,"position"],shape:[1,"shape"],size:[1,"size"],textColor:[1,"textColor"],textBgColor:[1,"textBgColor"]},features:[Pe([{directive:_o,inputs:["cTextColor","textColor"]},{directive:hr,inputs:["cTextBgColor","textBgColor"]}])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var ql=(()=>{class t{#e=new ke;backdropClick$=this.#e.asObservable();#t=c(ce);#i=c(qt);#n=this.#i.createRenderer(null,null);#o;activeBackdrop;get#s(){let e=this.#t.documentElement.clientWidth;return`${Math.abs((this.#t.defaultView?.innerWidth??e)-e)}px`}scrollbarWidth=this.#s;setBackdrop(e="modal"){let i=this.#n.createElement("div");return this.#n.addClass(i,`${e}-backdrop`),this.#n.addClass(i,"fade"),this.#n.appendChild(this.#t.body,i),this.#o=this.#n.listen(i,"click",n=>{this.onClickHandler()}),this.scrollbarWidth=this.#s,setTimeout(()=>{this.#n.addClass(i,"show")}),this.activeBackdrop=i,i}clearBackdrop(e){e&&(this.#o(),this.#n.removeClass(e,"show"),setTimeout(()=>{this.activeBackdrop===e&&this.resetScrollbar(),this.#n.removeChild(this.#t.body,e),e=void 0},300))}get#a(){return[this.#t.documentElement.dir,this.#t.body.dir].includes("rtl")}#r=!0;hideScrollbar(){this.#r&&(this.#n.setStyle(this.#t.body,"overflow","hidden"),this.#n.setStyle(this.#t.body,`padding-${this.#a?"left":"right"}`,this.scrollbarWidth),this.#r=!1)}resetScrollbar(){this.#n.removeStyle(this.#t.body,"overflow"),this.#n.removeStyle(this.#t.body,`padding-${this.#a?"left":"right"}`),this.#r=!0}onClickHandler(){this.#e.next(!0)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Yl=(()=>{class t{active=r(void 0,{transform:v});url=r();attributes=r();linkProps=r();ariaCurrent=d(()=>this.active()?"page":null);hostClasses=d(()=>({"breadcrumb-item":!0,active:this.active()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-breadcrumb-item"]],hostVars:3,hostBindings:function(i,n){i&2&&(M("aria-current",n.ariaCurrent()),g(n.hostClasses()))},inputs:{active:[1,"active"],url:[1,"url"],attributes:[1,"attributes"],linkProps:[1,"linkProps"]},exportAs:["breadcrumbItem"],ngContentSelectors:D,decls:4,vars:1,consts:[["defaultBreadcrumbItemContentTemplate",""],[3,"routerLink","cHtmlAttr","target","queryParams","fragment","queryParamsHandling","preserveFragment","skipLocationChange","replaceUrl","state"],[3,"cHtmlAttr"],[4,"ngTemplateOutlet"]],template:function(i,n){i&1&&(x(),A(0,Xa,2,13,"a",1)(1,Ja,2,3,"span",2),R(2,ec,1,0,"ng-template",null,0,re)),i&2&&E(n.active()?1:0)},dependencies:[an,Ai,Le,mi],styles:["[_nghost-%COMP%]{display:list-item;text-align:-webkit-match-parent;text-align:match-parent}"]})}return t})(),Xl=(()=>{class t{ariaLabel=r("breadcrumb");role=r("navigation");static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-breadcrumb"]],hostAttrs:[1,"breadcrumb"],hostVars:2,hostBindings:function(i,n){i&2&&M("aria-label",n.ariaLabel())("role",n.role())},inputs:{ariaLabel:[1,"ariaLabel"],role:[1,"role"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Zl=(()=>{class t{#e=c(Si);#t=c(ms);outlet="primary";#i=new Ke(new Array);breadcrumbs$=this.#i.asObservable();constructor(){this.#e.events.pipe(ye(),Ge(e=>e instanceof rn)).subscribe(e=>{let i=[],n=this.#t.root,s="";do{let a=n.children;n=null,a.forEach(l=>{if(l.outlet===this.outlet){let p=l.snapshot;s+="/"+p.url.map(b=>b.path).join("/"),i.push({label:p.data.title??p.title??"",url:s,queryParams:p.queryParams}),n=l}})}while(n);return this.#i.next(Object.assign([],i)),i})}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Ig=(()=>{class t{service=c(Zl);items=r();breadcrumbs;ngOnInit(){this.breadcrumbs=this.service.breadcrumbs$}setup=T(()=>{let e=this.items();e&&e.length>0&&(this.breadcrumbs=new Gt(i=>{let n=this.items();n&&i.next(n)}))});ngOnDestroy(){this.breadcrumbs=void 0}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-breadcrumb-router"],["","cBreadcrumbRouter",""]],inputs:{items:[1,"items"]},decls:4,vars:2,consts:[[1,"m-0"],[3,"active","url","attributes","linkProps"]],template:function(i,n){i&1&&(I(0,"c-breadcrumb",0),Di(1,ic,1,1,null,null,Ii),ue(3,"async"),w()),i&2&&(f(),Ti(fe(3,0,n.breadcrumbs)))},dependencies:[Xl,Yl,ps],encapsulation:2})}return t})();var Dg=(()=>{class t{size=r();vertical=r(!1,{transform:v});role=r("group");hostClasses=d(()=>({"btn-group":!this.vertical(),"btn-group-vertical":this.vertical(),[`btn-group-${this.size()}`]:!!this.size()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-button-group"]],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{size:[1,"size"],vertical:[1,"vertical"],role:[1,"role"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Tg=(()=>{class t{role=r("toolbar");static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-button-toolbar"]],hostAttrs:[1,"btn-toolbar"],hostVars:1,hostBindings:function(i,n){i&2&&M("role",n.role())},inputs:{role:[1,"role"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var Mg=(()=>{class t{color=r();hostClasses=d(()=>{let e=this.color();return{callout:!0,[`callout-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-callout"],["","cCallout",""]],hostAttrs:[1,"callout"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"color"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})();var hi=(()=>{class t{color=r();textColor=r();textBgColor=r();hostClasses=d(()=>{let e=this.color();return{card:!0,[`bg-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-card"],["","c-card",""]],hostAttrs:[1,"card"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"color"],textColor:[1,"textColor"],textBgColor:[1,"textBgColor"]},features:[Pe([{directive:_o,inputs:["cTextColor","textColor"]},{directive:hr,inputs:["cTextBgColor","textBgColor"]}])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),vi=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-card-body"],["","c-card-body",""]],hostAttrs:[1,"card-body"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Jl=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-card-footer"],["","c-card-footer",""]],hostAttrs:[1,"card-footer"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Sg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-card-group"],["","c-card-group",""]],hostAttrs:[1,"card-group"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),ed=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-card-header"],["","c-card-header",""]],hostAttrs:[1,"card-header"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var Ag=(()=>{class t{orientation=r(void 0,{alias:"cCardImg"});hostClasses=d(()=>{let e=this.orientation(),i=e?`-${e}`:"",n=["start","end"].includes(e??"-")?e:void 0;return{[`card-img${i}`]:!n,"img-fluid":!!n,[`rounded-${n}`]:!!n}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCardImg",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{orientation:[1,"cCardImg","orientation"]}})}return t})();var Eg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCardLink",""]],hostAttrs:[1,"card-link"]})}return t})(),kg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCardSubtitle",""]],hostAttrs:[1,"card-subtitle"]})}return t})(),Og=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCardText",""]],hostAttrs:[1,"card-text"]})}return t})(),Fg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCardTitle",""]],hostAttrs:[1,"card-title"]})}return t})();var Li=(()=>{class t{platformId=c(xi);#e=new Ke({isIntersecting:!1});intersecting$=this.#e.asObservable();defaultObserverOptions={root:null,rootMargin:"0px",threshold:.2};hostElementRefs=new Map;createIntersectionObserver(e,i=this.defaultObserverOptions){if(us(this.platformId)){this.#e.next({isIntersecting:!0,hostElement:e});return}let n=S(S({},this.defaultObserverOptions),i),s=(a,l)=>{a.forEach(p=>{this.#e.next({isIntersecting:p.isIntersecting,hostElement:e})})};this.hostElementRefs.set(e,new IntersectionObserver(s,n)),this.hostElementRefs.get(e)?.observe(e.nativeElement)}unobserve(e){this.hostElementRefs.get(e)?.unobserve(e.nativeElement),this.hostElementRefs.set(e,null),this.hostElementRefs.delete(e)}ngOnDestroy(){this.hostElementRefs.forEach((e,i)=>{e?.unobserve(i.nativeElement)})}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ui=(()=>{class t{renderer=c(X);listeners=new Map;setListeners({hostElement:e,trigger:i,callbackOn:n,callbackOff:s,callbackToggle:a}){let l=e.nativeElement,p=Array.isArray(i)?i:i?.split(" ")??[];p?.includes("click")&&typeof a=="function"&&this.listeners.set("click",this.renderer.listen(l,"click",a)),p?.includes("focus")&&typeof n=="function"&&this.listeners.set("focus",this.renderer.listen(l,"focus",n)),p?.includes("focusin")&&(typeof s=="function"&&this.listeners.set("focusout",this.renderer.listen(l,"focusout",s)),typeof n=="function"&&this.listeners.set("focusin",this.renderer.listen(l,"focusin",n))),(p?.includes("click")||p?.includes("focus"))&&typeof s=="function"&&this.listeners.set("blur",this.renderer.listen(l,"blur",s)),p?.includes("hover")&&(typeof n=="function"&&this.listeners.set("mouseenter",this.renderer.listen(l,"mouseenter",n)),typeof s=="function"&&this.listeners.set("mouseleave",this.renderer.listen(l,"mouseleave",s)))}clearListeners(){this.listeners.forEach((e,i)=>{e()}),this.listeners.forEach((e,i)=>{this.listeners.set(i,null)}),this.listeners.clear()}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})();var td=(()=>{class t{#e=new Map;setItem(e,i){this.#e.set(e,JSON.stringify(i))}getItem(e){return this.#e.has(e)?JSON.parse(this.#e.get(e)??"null"):void 0}removeItem(e){this.#e.delete(e)}clear(){this.#e.clear()}get length(){return this.#e.size}key(e){return Array.from(this.#e.keys())[e]}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),id=(()=>{class t{platformId=c(xi);document=c(ce);constructor(){this.#e=sn(this.platformId)&&this.document.defaultView?this.document.defaultView?.localStorage:new td}#e;#t=new Ke(null);data$=this.#t.asObservable();setItem(e,i){this.#e.setItem(e,JSON.stringify(i)),this.#t.next({key:e,data:i})}getItem(e){let i=JSON.parse(this.#e.getItem(e)||"null");return this.#t.next({key:e,data:i}),i}removeItem(e){this.#e.removeItem(e),this.#t.next({key:e,data:null})}clear(){this.#e.clear(),this.#t.next(null)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Bg=(()=>{class t{#e=c(be);#t=c(ce);#i=c(id);eventName=B("ColorSchemeChange");localStorageItemName=B(void 0);localStorageItemName$=jn(this.localStorageItemName);colorMode=B(void 0);#n=T(()=>{let e=this.colorMode();if(e){let i=this.localStorageItemName();i&&this.setStoredTheme(i,e),this.#o(e)}});constructor(){Yt({read:()=>{this.localStorageItemName$.pipe(ht(e=>{this.colorMode.set(this.getDefaultScheme(e))}),ye(this.#e)).subscribe()}})}getStoredTheme(e){return this.#i.getItem(e)}setStoredTheme(e,i){return this.#i.setItem(e,i)}removeStoredTheme(e){this.#i.removeItem(e)}getDefaultScheme(e){return this.#t.defaultView===void 0?this.getDatasetTheme():(e&&this.getStoredTheme(e))??this.getDatasetTheme()}getPrefersColorScheme(){return this.#t.defaultView?.matchMedia("(prefers-color-scheme: dark)").matches?"dark":this.#t.defaultView?.matchMedia("(prefers-color-scheme: light)").matches?"light":void 0}getDatasetTheme(){return this.#t.documentElement.dataset.coreuiTheme}#o(e){this.#t.documentElement.dataset.coreuiTheme=e==="auto"?this.getPrefersColorScheme():e;let i=new Event(this.eventName());this.#t.documentElement.dispatchEvent(i)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Hi=(()=>{class t{#e=new Ke({});carouselIndex$=this.#e.asObservable();setIndex(e){this.#e.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),$i=(()=>{class t{#e=c(Hi);#t={activeItemIndex:-1,animate:!0,items:[],direction:"next",transition:"slide",interval:0};get state(){return this.#t}set state(e){let i=S({},this.#t),n=S(S({},this.#t),e);if(this.#t=n,i.activeItemIndex!==n.activeItemIndex){let s=this.state.activeItemIndex||0,a=this.state.items&&this.state.items[s]?.interval()||-1;this.#e.setIndex({active:n.activeItemIndex,interval:a,lastItemIndex:(n.items?.length??0)-1})}}setItems(e){if(e.length){let i=e;i.forEach((n,s)=>{n.index=s}),this.state={items:[...i]}}else this.reset()}setNextIndex(e){this.#e.setIndex(e)}direction(e="next"){this.state={direction:e};let{activeItemIndex:i=-1,items:n}=this.state,s=n?.length??0;return s>0?e==="next"?i===s-1?0:i+1:i===0?s-1:i-1:0}reset(){this.state={activeItemIndex:-1,animate:!0,items:[],direction:"next",transition:"slide",interval:0}}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),nd=(()=>{class t{activeIndex=0;animate=!0;direction="next";interval;static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Pg=(()=>{class t{config=c(nd);#e=c(O);#t=c(Hi);#i=c($i);#n=c(Li);#o=c(ui);constructor(){this.loadConfig()}loadConfig(){this.activeIndex.update(e=>this.config?.activeIndex??e),this.animate.update(e=>this.config?.animate??e),this.direction.update(e=>this.config?.direction??e),this.interval.update(e=>this.config?.interval??e)}activeIndexInput=r(0,{alias:"activeIndex",transform:Ae});activeIndex=me({source:this.activeIndexInput,computation:e=>e});animateInput=r(!0,{alias:"animate"});animate=me({source:this.animateInput,computation:e=>e});directionInput=r("next",{alias:"direction"});direction=me({source:this.directionInput,computation:e=>e});intervalInput=r(-1,{alias:"interval",transform:Ae});interval=me({source:this.intervalInput,computation:e=>e});#s=T(()=>{let e=this.interval();this.#i.state={interval:e},e?this.setTimer():this.resetTimer()});pause=r("hover");touch=r(!0);transition=r("slide");wrap=r(!0);itemChange=Re();timerId;activeItemInterval=0;swipeSubscription;#a=c(be);ngOnInit(){this.carouselStateSubscribe()}ngOnDestroy(){this.resetTimer(),this.clearListeners(),this.swipeSubscribe(!1)}ngAfterContentInit(){this.intersectionServiceSubscribe(),this.#i.state={activeItemIndex:this.activeIndex(),animate:this.animate(),interval:this.interval(),transition:this.transition()},this.setListeners(),this.swipeSubscribe()}setListeners(){let e={hostElement:this.#e,trigger:this.pause()||[],callbackOff:()=>{this.setTimer()},callbackOn:()=>{this.resetTimer()}};this.#o.setListeners(e)}clearListeners(){this.#o.clearListeners()}set visible(e){this.#r=e}get visible(){return this.#r}#r=!0;setTimer(){let e=this.activeItemInterval||this.interval(),i=this.direction();this.resetTimer(),e>0&&(this.timerId=setTimeout(()=>{let n=this.#i.direction(i);this.#i.state={activeItemIndex:n}},e))}resetTimer(){clearTimeout(this.timerId),this.timerId=void 0}carouselStateSubscribe(){this.#t.carouselIndex$.pipe(ye(this.#a)).subscribe(e=>{"active"in e&&typeof e.active=="number"&&this.itemChange?.emit(e.active),this.activeItemInterval=typeof e.interval=="number"&&e.interval>-1?e.interval:this.interval();let i=this.direction(),n=e.active===e.lastItemIndex&&i==="next"||e.active===0&&i==="prev";!this.wrap()&&n?this.resetTimer():this.setTimer()})}intersectionServiceSubscribe(){this.#n.createIntersectionObserver(this.#e),this.#n.intersecting$.pipe(Ge(e=>e.hostElement===this.#e),Yi(()=>{this.#n.unobserve(this.#e)}),ye(this.#a)).subscribe(e=>{this.visible=e.isIntersecting,e.isIntersecting?this.setTimer():this.resetTimer()})}swipeSubscribe(e=!0){if(this.touch()&&e){let i=this.#e.nativeElement,n=Ft(i,"touchstart"),s=Ft(i,"touchend"),a=Ft(i,"touchmove");this.swipeSubscription=n.pipe(Wo(s.pipe(zo(a))),ye(this.#a)).subscribe(([l,[p,b]])=>{l.stopPropagation(),b.stopPropagation();let u=l.touches[0]?.clientX-b.touches[0]?.clientX||0;if(Math.abs(u)>.3*i.clientWidth&&l.timeStamp<=b.timeStamp){let N=this.#i.direction(u>0?"next":"prev");this.#i.state={activeItemIndex:N}}})}else this.swipeSubscription?.unsubscribe()}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel"]],hostAttrs:[1,"carousel","slide"],hostVars:2,hostBindings:function(i,n){i&2&&_t("carousel-fade",n.transition()==="crossfade"&&n.animate())},inputs:{activeIndexInput:[1,"activeIndex","activeIndexInput"],animateInput:[1,"animate","animateInput"],directionInput:[1,"direction","directionInput"],intervalInput:[1,"interval","intervalInput"],pause:[1,"pause"],touch:[1,"touch"],transition:[1,"transition"],wrap:[1,"wrap"]},outputs:{itemChange:"itemChange"},exportAs:["cCarousel"],features:[Ne([Hi,$i,ui]),Pe([{directive:zi,inputs:["dark","dark"]}])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),Ng=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel-caption"]],hostVars:2,hostBindings:function(i,n){i&2&&_t("carousel-caption",!0)},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),Rg=(()=>{class t{#e=c($i);captionInput=r(void 0,{alias:"caption"});caption=me({source:this.captionInput,computation:e=>e||(this.direction()==="prev"?"Previous":"Next")});direction=r("next");role=r("button");hostClasses=d(()=>`carousel-control-${this.direction()}`);carouselControlIconClass=d(()=>`carousel-control-${this.direction()}-icon`);onKeyUp(e){e.key==="Enter"&&this.#t(),e.key==="ArrowLeft"&&this.#t("prev"),e.key==="ArrowRight"&&this.#t("next")}onClick(e){this.#t()}#t(e=this.direction()){let i=this.#e.direction(e);this.#e.state={activeItemIndex:i}}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel-control"]],hostVars:3,hostBindings:function(i,n){i&1&&oe("keyup",function(a){return n.onKeyUp(a)})("click",function(a){return n.onClick(a)}),i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{captionInput:[1,"caption","captionInput"],direction:[1,"direction"],role:[1,"role"]},exportAs:["cCarouselControl"],ngContentSelectors:D,decls:2,vars:0,consts:[[1,"visually-hidden"]],template:function(i,n){i&1&&(x(),C(0,0,null,nc,3,4))},encapsulation:2})}return t})(),jg=(()=>{class t{#e=c(be);#t=c(Hi);#i=c($i);items=[];active=0;contentTemplates=je(ze,{descendants:!0});templates=d(()=>this.contentTemplates().reduce((e,i)=>(e[i.id]=i.templateRef,e),{}));ngOnInit(){this.#t.carouselIndex$.pipe(ye(this.#e)).subscribe(e=>{this.items=this.#i?.state?.items?.map(i=>i.index)??[],"active"in e&&(this.active=e.active??0)})}onClick(e){if(e!==this.active){let i=e<this.active?"prev":"next";this.#i.state={direction:i,activeItemIndex:e}}}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel-indicators"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostAttrs:[1,"carousel-indicators"],exportAs:["cCarouselIndicators"],decls:3,vars:5,consts:[["defaultCarouselIndicatorsTemplate",""],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["type","button",3,"class"],["type","button",3,"click"]],template:function(i,n){if(i&1&&R(0,rc,1,0,"ng-container",1)(1,cc,2,0,"ng-template",null,0,re),i&2){let s=Q(2),a=n.templates();m("ngTemplateOutlet",a.carouselIndicatorsTemplate||s)("ngTemplateOutletContext",cs(2,oc,n.items,n.active))}},dependencies:[Le],encapsulation:2})}return t})(),ar=(()=>{class t{#e=c(be);#t=c(Hi);index;activeInput=r(!1,{transform:v,alias:"active"});active=me({source:this.activeInput,computation:e=>e});interval=r(-1);role=r("group");constructor(){this.#t.carouselIndex$.pipe(ye(this.#e)).subscribe(e=>{"active"in e&&this.active.set(e.active===this.index)})}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel-item"]],hostAttrs:[1,"carousel-item"],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),_t("active",n.active()))},inputs:{activeInput:[1,"active","activeInput"],interval:[1,"interval"],role:[1,"role"]},exportAs:["cCarouselItem"],ngContentSelectors:D,decls:1,vars:1,template:function(i,n){i&1&&(x(),A(0,lc,1,0)),i&2&&E(n.active()?0:-1)},styles:["[_nghost-%COMP%]{display:block}"]})}return t})();function od(t,o){return o.left===!0&&o.type==="slide"}function sd(t,o){return o.left===!1&&o.type==="slide"}function rd(t,o){return o.left===!0&&o.type!=="slide"}function ad(t,o){return o.left===!1&&o.type!=="slide"}var cd=ot(ii([qe(":leave",[q("0.6s ease-in-out",H({transform:"translateX(-100%)"}))],{optional:!0}),qe(":enter",[H({transform:"translateX(100%)"}),q("0.6s ease-in-out",H("*"))],{optional:!0})])),ld=ot(ii([qe(":enter",[H({transform:"translateX(-100%)"}),q("0.6s ease-in-out",H("*"))],{optional:!0}),qe(":leave",[q("0.6s ease-in-out",H({transform:"translateX(100%)"}))],{optional:!0})])),dd=ot(ii([qe(":leave",[q("0.9s ease-in-out",H({zIndex:0,opacity:0}))],{optional:!0}),qe(":enter",[H({zIndex:1,opacity:1}),q("0.6s ease-in-out",H("*"))],{optional:!0})])),pd=ot(ii([qe(":enter",[H({zIndex:1,opacity:1}),q("0.6s ease-in-out",H("*"))],{optional:!0}),qe(":leave",[q("0.9s ease-in-out",H({zIndex:0,opacity:0}))],{optional:!0})])),ud=It("carouselPlay",[Me("*",H({transform:"translateX(0)",display:"block",opacity:1})),Ie(rd,Nt(dd)),Ie(ad,Nt(pd)),Ie(od,Nt(cd)),Ie(sd,Nt(ld))]),Lg=(()=>{class t{#e=c($i);activeIndex=B(void 0);animate=B(!0);interval=B(0);slide=B({left:!0});transition=B("crossfade");slideType=d(()=>({left:this.slide().left,type:this.transition()}));ariaLive=d(()=>this.interval()?"off":"polite");contentItems=je(ar);#t=B([]);ngAfterContentInit(){this.setItems()}ngAfterContentChecked(){this.setItems();let e=this.#e?.state,i=e?.activeItemIndex,n=e?.direction;this.activeIndex()!==i&&(this.animate.set(e?.animate??!1),this.activeIndex.set(e?.activeItemIndex),this.interval.set(e?.interval??0),this.slide.set({left:n==="next"}),this.transition.set(e?.transition??"slide"))}setItems(){let e=this.contentItems();this.#t()!==e&&(this.#t.set([...e]),this.#e.setItems(e))}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-carousel-inner"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentItems,ar,4),i&2&&ee()},hostAttrs:[1,"carousel-inner"],hostVars:3,hostBindings:function(i,n){i&2&&(Zt("@carouselPlay",n.slideType())("@.disabled",!n.animate()),M("aria-live",n.ariaLive()))},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"],data:{animation:[ud]}})}return t})();var Hg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cDropdownDivider",""]],hostAttrs:[1,"dropdown-divider"]})}return t})(),$g=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cDropdownHeader",""]],hostAttrs:[1,"dropdown-header"]})}return t})(),fi=(()=>{class t{dropdownState=new Ke({});dropdownState$=this.dropdownState.asObservable();toggle(e){this.dropdownState.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),yn=(()=>{class t{#e=c(be);elementRef=c(O);#t=c(fi);#i;alignment=r();visibleInput=r(!1,{transform:v,alias:"visible"});visible=me({source:this.visibleInput,computation:e=>e});hostClasses=d(()=>{let e=this.alignment(),i=this.visible();return{"dropdown-menu":!0,[`dropdown-menu-${e}`]:!!e,show:i}});hostStyles=d(()=>{let e=this.visible();return{visibility:e?null:"",display:e?null:""}});onKeyDown(e){this.visible()&&(["Space","ArrowDown"].includes(e.code)&&e.preventDefault(),this.#i.onKeydown(e))}onKeyUp(e){this.visible()&&["Tab"].includes(e.key)&&(this.#i.activeItem?e.shiftKey?this.#i.setPreviousItemActive():this.#i.setNextItemActive():this.#i.setFirstItemActive())}dropdownItemsContent=je(En(()=>lr),{descendants:!0});items$=jn(this.dropdownItemsContent);ngAfterContentInit(){this.focusKeyManagerInit(),this.items$.pipe(ht(e=>{this.focusKeyManagerInit()}),ye(this.#e)).subscribe()}ngOnInit(){this.#t.dropdownState$.pipe(ht(e=>{"visible"in e&&(this.visible.update(i=>e.visible==="toggle"?!i:e.visible),this.visible()||this.#i?.setActiveItem(-1))}),ye(this.#e)).subscribe()}focusKeyManagerInit(){this.#i=new di(this.dropdownItemsContent()).withHomeAndEnd().withPageUpDown().withWrap().skipPredicate(e=>e.disabled===!0)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cDropdownMenu",""]],contentQueries:function(i,n,s){i&1&&pe(s,n.dropdownItemsContent,lr,5),i&2&&ee()},hostAttrs:[1,"dropdown-menu"],hostVars:4,hostBindings:function(i,n){i&1&&oe("keydown",function(a){return n.onKeyDown(a)})("keyup",function(a){return n.onKeyUp(a)}),i&2&&(ei(n.hostStyles()),g(n.hostClasses()))},inputs:{alignment:[1,"alignment"],visibleInput:[1,"visible","visibleInput"]},exportAs:["cDropdownMenu"],features:[Pe([{directive:zi,inputs:["dark","dark"]}])]})}return t})(),Cn=class{},cr=(()=>{class t{#e=c(be);elementRef=c(O);#t=c(fi);dropdown=c(Cn,{optional:!0});dropdownComponent=r();disabled=r(!1,{transform:v});caret=r(!0);split=r(!1,{transform:v});hostClasses=d(()=>({"dropdown-toggle":this.caret(),"dropdown-toggle-split":this.split(),disabled:this.disabled()}));#i=B(!1);get ariaExpanded(){return this.#i()}onClick(e){e.preventDefault(),!this.disabled()&&this.#t.toggle({visible:"toggle",dropdown:this.dropdown})}ngAfterViewInit(){let e=this.dropdownComponent();e&&(this.dropdown=e,this.#t=e?.dropdownService),this.dropdown&&this.dropdown?.visibleChange?.subscribe(n=>{this.#i.set(n)})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cDropdownToggle",""]],hostVars:3,hostBindings:function(i,n){i&1&&oe("click",function(a){return n.onClick(a)}),i&2&&(M("aria-expanded",n.ariaExpanded),g(n.hostClasses()))},inputs:{dropdownComponent:[1,"dropdownComponent"],disabled:[1,"disabled"],caret:[1,"caret"],split:[1,"split"]},exportAs:["cDropdownToggle"],features:[Ne([{provide:Cn,useExisting:En(()=>vr)}])]})}return t})(),vr=(()=>{class t{#e=c(be);#t=c(ce);#i=c(O);#n=c(X);#o=c(st);#s=c(Mi);dropdownService=c(fi);constructor(){this.dropdownStateSubscribe()}alignment=r();autoClose=r(!0);direction=r();placement=r("bottom-start");popper=r(!0,{transform:v});popperOptionsInput=r({},{alias:"popperOptions"});#a=T(()=>{this.popperOptions=S(S({},Fe(this.#r)),this.popperOptionsInput())});set popperOptions(e){this.#r.update(i=>S(S({},i),e))}get popperOptions(){let e=this.placement();switch(this.direction()){case"dropup":{e="top-start";break}case"dropend":{e="right-start";break}case"dropstart":{e="left-start";break}case"center":{e="bottom";break}case"dropup-center":{e="top";break}}return this.alignment()==="end"&&(e="bottom-end"),this.#r.update(i=>mt(S({},i),{placement:e})),this.#r()}#r=B({placement:this.placement(),modifiers:[],strategy:"absolute"});variant=r("dropdown");visibleInput=r(!1,{transform:v,alias:"visible"});visible=me({source:this.visibleInput,computation:e=>e});#c=T(()=>{let e=this.visible();this.activeTrap=e,e?this.createPopperInstance():this.destroyPopperInstance(),this.setVisibleState(e),this.visibleChange?.emit(e)});visibleChange=Re();dropdownContext={$implicit:this.visible()};_toggler=wt(cr);_menu=wt(yn);_menuElementRef=wt(yn,{read:O});activeTrap=!1;popperInstance;listeners=[];hostClasses=d(()=>{let e=this.direction(),i=this.variant();return{dropdown:(i==="dropdown"||i==="nav-item")&&!e,[`${e}`]:!!e,[`${i}`]:!!i,dropup:e==="dropup"||e==="dropup-center",show:this.visible()}});hostStyle=d(()=>this.variant()==="input-group"?{display:"contents"}:{});clickedTarget;onHostClick(e){this.clickedTarget=e.target}dropdownStateSubscribe(){this.dropdownService.dropdownState$.pipe(Ge(e=>this===e.dropdown),ye(this.#e)).subscribe(e=>{"visible"in e&&(e?.visible==="toggle"?this.toggleDropdown():this.visible.set(e.visible))})}toggleDropdown(){this.visible.update(e=>!e)}onClick(e){this._toggler()?.elementRef.nativeElement.contains(e.target?.closest("[cDropdownToggle]"))||this.toggleDropdown()}#l=T(()=>{let e=this.variant(),i=this._toggler();e==="nav-item"&&i&&this.#n.addClass(i.elementRef.nativeElement,"nav-link")});ngOnInit(){this.setVisibleState(this.visible())}ngOnDestroy(){this.clearListeners(),this.destroyPopperInstance()}setVisibleState(e){this.dropdownService.toggle({visible:e,dropdown:this})}createPopperInstance(){let e=this._toggler(),i=this._menu();e&&i&&this.#o.runOutsideAngular(()=>{i.elementRef.nativeElement.style.visibility="hidden",i.elementRef.nativeElement.style.display="block",this.popper()&&(this.popperInstance=Ni(e.elementRef.nativeElement,i.elementRef.nativeElement,S({},this.popperOptions))),this.#o.run(()=>{this.setListeners(),this.#s.markForCheck(),this.#s.detectChanges()})})}destroyPopperInstance(){this.clearListeners(),this.popperInstance?.destroy(),this.popperInstance=void 0,this.#s.markForCheck()}setListeners(){this.listeners.push(this.#n.listen(this.#t,"click",e=>{let i=e.target;if(this._menuElementRef()?.nativeElement.contains(e.target)&&(this.clickedTarget=i),this._toggler()?.elementRef.nativeElement.contains(e.target))return;let n=this.autoClose();if(n===!0){this.setVisibleState(!1);return}if(this.clickedTarget===i&&n==="inside"){this.setVisibleState(!1);return}if(this.clickedTarget!==i&&n==="outside"){this.setVisibleState(!1);return}})),this.listeners.push(this.#n.listen(this.#i.nativeElement,"keyup",e=>{if(e.key==="Escape"&&this.autoClose()!==!1){e.stopPropagation(),this.setVisibleState(!1);return}})),this.listeners.push(this.#n.listen(this.#t,"keyup",e=>{if(e.key==="Tab"&&this.autoClose()!==!1&&!this.#i.nativeElement.contains(e.target)){this.setVisibleState(!1);return}}))}clearListeners(){this.listeners.forEach(e=>{e()}),this.listeners.fill(void 0),this.listeners=[]}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-dropdown"]],contentQueries:function(i,n,s){i&1&&(pe(s,n._toggler,cr,5),pe(s,n._menu,yn,5),pe(s,n._menuElementRef,yn,5,O)),i&2&&ee(3)},hostVars:4,hostBindings:function(i,n){i&1&&oe("click",function(a){return n.onHostClick(a)}),i&2&&(ei(n.hostStyle()),g(n.hostClasses()))},inputs:{alignment:[1,"alignment"],autoClose:[1,"autoClose"],direction:[1,"direction"],placement:[1,"placement"],popper:[1,"popper"],popperOptionsInput:[1,"popperOptions","popperOptionsInput"],variant:[1,"variant"],visibleInput:[1,"visible","visibleInput"]},outputs:{visibleChange:"visibleChange"},exportAs:["cDropdown"],features:[Ne([fi]),Pe([{directive:zi,inputs:["dark","dark"]}])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:[".dropdown[_nghost-%COMP%]:not(.btn-group), .dropdown   [_nghost-%COMP%]:not(.btn-group), .dropup[_nghost-%COMP%]:not(.btn-group), .dropup   [_nghost-%COMP%]:not(.btn-group){display:block}.dropstart[_nghost-%COMP%]:not(.btn-group), .dropstart   [_nghost-%COMP%]:not(.btn-group), .dropend[_nghost-%COMP%]:not(.btn-group), .dropend   [_nghost-%COMP%]:not(.btn-group){display:inline-flex}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:first-child  :first-child, html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:first-child  :first-child, .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:first-child  :first-child{border-top-right-radius:0;border-bottom-right-radius:0}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu), html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu), .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu){margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu):not(:only-of-type), html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu):not(:only-of-type), .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu):not(:only-of-type){border-top-right-radius:0;border-bottom-right-radius:0}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:last-child  :first-child, html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:last-child  :first-child, .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:last-child  :first-child{border-top-left-radius:0;border-bottom-left-radius:0}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:last-child  :first-child:not(:only-of-type), html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:last-child  :first-child:not(:only-of-type), .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:last-child  :first-child:not(:only-of-type){border-top-right-radius:0;border-bottom-right-radius:0}html:not([dir=rtl]).input-group   [_nghost-%COMP%]:last-child  :not(:first-child):not(.dropdown-menu), html:not([dir=rtl])   .input-group   [_nghost-%COMP%]:last-child  :not(:first-child):not(.dropdown-menu), .input-group   html:not([dir=rtl])   [_nghost-%COMP%]:last-child  :not(:first-child):not(.dropdown-menu){margin-left:-1px;border-top-left-radius:0;border-bottom-left-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]{direction:rtl}[dir=rtl]   .input-group   [_nghost-%COMP%]:first-child  :first-child{border-top-left-radius:0;border-bottom-left-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu){margin-right:-1px;border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]:first-child  :not(:first-child):not(.dropdown-menu):not(:only-of-type){border-top-left-radius:0;border-bottom-left-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]:last-child  :first-child{border-top-right-radius:0;border-bottom-right-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]:last-child  :first-child:not(:only-of-type){border-top-left-radius:0;border-bottom-left-radius:0}[dir=rtl]   .input-group   [_nghost-%COMP%]:last-child  :not(:first-child):not(.dropdown-menu){margin-right:-1px;border-top-right-radius:0;border-bottom-right-radius:0}"]})}return t})(),lr=(()=>{class t{#e=c(O);#t=c(fi);dropdown=c(vr,{optional:!0});active=r();autoClose=r(!0);disabledInput=r(!1,{transform:v,alias:"disabled"});#i=me({source:this.disabledInput,computation:e=>e});set disabled(e){this.#i.set(e)}get disabled(){return this.#i()}role=r("list-item");tabIndexInput=r("0",{alias:"tabIndex"});tabIndex=me({source:this.tabIndexInput,computation:e=>this.disabled?"-1":e});focus(e){this.#e?.nativeElement?.focus()}getLabel(){return this.#e?.nativeElement?.textContent.trim()}ariaCurrent=d(()=>this.active()?"true":null);hostClasses=d(()=>({"dropdown-item":!0,active:this.active(),disabled:this.disabled}));onClick(e){this.handleInteraction()}onKeyUp(e){e.key==="Enter"&&this.handleInteraction()}handleInteraction(){this.autoClose()&&this.#t.toggle({visible:"toggle",dropdown:this.dropdown})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cDropdownItem",""]],hostAttrs:[1,"dropdown-item"],hostVars:6,hostBindings:function(i,n){i&1&&oe("click",function(a){return n.onClick(a)})("keyup",function(a){return n.onKeyUp(a)}),i&2&&(M("tabindex",n.tabIndex())("aria-current",n.ariaCurrent())("aria-disabled",n.disabled||null)("role",n.role()),g(n.hostClasses()))},inputs:{active:[1,"active"],autoClose:[1,"autoClose"],disabledInput:[1,"disabled","disabledInput"],role:[1,"role"],tabIndexInput:[1,"tabIndex","tabIndexInput"]},exportAs:["cDropdownItem"]})}return t})();var Vg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=yt({type:t});static \u0275inj=vt({providers:[fi]})}return t})(),zg=(()=>{class t{position=r();role=r("contentinfo");hostClasses=d(()=>({footer:!0,[`footer-${this.position()}`]:!!this.position()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-footer"],["","cFooter",""]],hostAttrs:[1,"footer"],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{position:[1,"position"],role:[1,"role"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var Wg=(()=>{class t{validated=r(!1,{transform:v});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["form","cForm",""]],hostVars:2,hostBindings:function(i,n){i&2&&_t("was-validated",n.validated())},inputs:{validated:[1,"validated"]}})}return t})(),dr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["label","cFormCheckLabel",""]],hostAttrs:[1,"form-check-label"]})}return t})(),Ug=(()=>{class t{static ngAcceptInputType_inline;static ngAcceptInputType_reverse;static ngAcceptInputType_switch;inline=r(!1,{transform:v});reverse=r(!1,{transform:v});sizing=r();switch=r(!1,{transform:v});formCheckLabel=wt(dr);hostClasses=d(()=>{let e=this.sizing(),i=this.switch();return{"form-check":!!this.formCheckLabel(),"form-switch":i,[`form-switch-${e}`]:i&&!!e,"form-check-inline":this.inline(),"form-check-reverse":this.reverse()}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-form-check"]],contentQueries:function(i,n,s){i&1&&pe(s,n.formCheckLabel,dr,5),i&2&&ee()},hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{inline:[1,"inline"],reverse:[1,"reverse"],sizing:[1,"sizing"],switch:[1,"switch"]},exportAs:["cFormCheck"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Kg=(()=>{class t{#e=c(O);sizing=r();valid=r();type=r("text");plaintext=r(!1,{transform:v});hostClasses=d(()=>{let e=this.type(),i=e==="range",n=this.plaintext(),s=this.sizing(),a=this.valid();return{"form-control":!i&&!n,"form-control-plaintext":!i&&n,"form-control-color":e==="color","form-range":i,[`form-control-${s}`]:!!s&&!i,"is-valid":a===!0,"is-invalid":a===!1}});get hostTag(){return this.#e.nativeElement.tagName}ngOnInit(){let e=this.hostTag.toLowerCase();e!=="input"&&e!=="textarea"&&console.warn(`CoreUI [cFormControl] works with '<input>' and '<textarea>' - not with '<${e}>'`)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["input","cFormControl",""],["textarea","cFormControl",""]],hostVars:3,hostBindings:function(i,n){i&2&&(M("type",n.type()),g(n.hostClasses()))},inputs:{sizing:[1,"sizing"],valid:[1,"valid"],type:[1,"type"],plaintext:[1,"plaintext"]}})}return t})(),Gg=(()=>{class t{static ngAcceptInputType_indeterminate;#e=c(X);#t=c(O);type=r("checkbox");indeterminateInput=r(!1,{transform:v,alias:"indeterminate"});#i=me(this.indeterminateInput);#n=T(()=>{if(this.type()==="checkbox"){let e=this.#i(),i=this.#t.nativeElement;e&&this.#e.setProperty(i,"checked",!1),this.#e.setProperty(i,"indeterminate",e)}});get indeterminate(){return this.#i()}valid=r();hostClasses=d(()=>{let e=this.valid();return{"form-check-input":!0,"is-valid":e===!0,"is-invalid":e===!1}});get checked(){return this.#t?.nativeElement?.checked}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["input","cFormCheckInput",""]],hostAttrs:[1,"form-check-input"],hostVars:3,hostBindings:function(i,n){i&2&&(M("type",n.type()),g(n.hostClasses()))},inputs:{type:[1,"type"],indeterminateInput:[1,"indeterminate","indeterminateInput"],valid:[1,"valid"]}})}return t})(),Qg=(()=>{class t{tooltip=r(!1,{transform:v});valid=r();hostClasses=d(()=>{let e=this.valid()===!0?"valid":"invalid",i=this.tooltip()?"tooltip":"feedback";return{[`${e}-${i}`]:!0}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-form-feedback"]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{tooltip:[1,"tooltip"],valid:[1,"valid"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),qg=(()=>{class t{floating=r(!0,{transform:v,alias:"cFormFloating"});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cFormFloating",""]],hostVars:2,hostBindings:function(i,n){i&2&&_t("form-floating",n.floating())},inputs:{floating:[1,"cFormFloating","floating"]}})}return t})(),Yg=(()=>{class t{col=r("",{alias:"cLabel"});sizing=r();hostClasses=d(()=>{let e=this.col(),i=this.sizing();return{"form-label":!0,"col-form-label":e==="col",[`col-form-label-${i}`]:!!i&&e==="col"}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cLabel",""]],hostAttrs:[1,"form-label"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{col:[1,"cLabel","col"],sizing:[1,"sizing"]}})}return t})(),Xg=(()=>{class t{sizing=r();valid=r();hostClasses=d(()=>{let e=this.sizing(),i=this.valid();return{"form-select":!0,[`form-select-${e}`]:!!e,"is-valid":i===!0,"is-invalid":i===!1}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["select","cSelect",""]],hostAttrs:[1,"form-select"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{sizing:[1,"sizing"],valid:[1,"valid"]}})}return t})();var Zg=(()=>{class t{sizing=r();hostClasses=d(()=>{let e=this.sizing();return{"input-group":!0,[`input-group-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-input-group"]],hostAttrs:[1,"input-group"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{sizing:[1,"sizing"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),Jg=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cInputGroupText",""]],hostAttrs:[1,"input-group-text"]})}return t})();var e0=(()=>{class t{breakpoint=r("");fluid=r(!1,{transform:v});hostClasses=d(()=>{let e=this.breakpoint(),i=this.fluid();return{container:!i&&!e,"container-fluid":!!i,[`container-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-container"],["","cContainer",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{breakpoint:[1,"breakpoint"],fluid:[1,"fluid"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),fd=(()=>{class t{static ngAcceptInputType_cCol;static ngAcceptInputType_xs;static ngAcceptInputType_sm;static ngAcceptInputType_md;static ngAcceptInputType_lg;static ngAcceptInputType_xl;static ngAcceptInputType_xxl;cCol=r(!1,{transform:this.coerceInput});xs=r(!1,{transform:this.coerceInput});sm=r(!1,{transform:this.coerceInput});md=r(!1,{transform:this.coerceInput});lg=r(!1,{transform:this.coerceInput});xl=r(!1,{transform:this.coerceInput});xxl=r(!1,{transform:this.coerceInput});breakpoints=d(()=>({xs:this.xs()||this.cCol(),sm:this.sm(),md:this.md(),lg:this.lg(),xl:this.xl(),xxl:this.xxl()}));offset=r();order=r();hostClasses=d(()=>{let e={col:!0},i=this.breakpoints(),n=this.offset(),s=this.order();if(Object.keys(In).forEach(a=>{let l=i[a],p=a==="xs"?"":`-${a}`;e[`col${p}`]=l===!0,e[`col${p}-${l}`]=typeof l=="number"||typeof l=="string"}),typeof n=="object"){let a=S({},n);Object.entries(a).forEach(l=>{let[p,b]=[...l],u=p==="xs"?"":`-${p}`;e[`offset${u}-${b}`]=b>=0&&b<=11})}else{let a=Ae(n);e[`offset-${a}`]=typeof a=="number"&&a>0&&a<=11}if(typeof s=="object"){let a=S({},s);Object.entries(a).forEach(l=>{let[p,b]=[...l],u=p==="xs"?"":`-${p}`;e[`order${u}-${b}`]=!!b})}else{let a=s;e[`order-${a}`]=!!a}return e.col=!Object.entries(e).filter(a=>a[0].startsWith("col-")&&a[1]).length||i.xs===!0,e});coerceInput(e){return e==="auto"?e:e===""||e===void 0||e===null?v(e):typeof e=="boolean"?e:Ae(e)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cCol",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{cCol:[1,"cCol"],xs:[1,"xs"],sm:[1,"sm"],md:[1,"md"],lg:[1,"lg"],xl:[1,"xl"],xxl:[1,"xxl"],offset:[1,"offset"],order:[1,"order"]}})}return t})(),md=(()=>{class t extends fd{static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-col"]],features:[Qe],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),gr=(()=>{class t{xs=r();sm=r();md=r();lg=r();xl=r();xxl=r();hostClasses=d(()=>{let e=this.xs(),i={row:!0,[`row-cols-${e}`]:!!e};return Object.keys(In).forEach(n=>{let s=this[n]();if(typeof s=="number"||typeof s=="string"){let a=n==="xs"?"":`-${n}`;i[`row-cols${a}-${s}`]=!!s}}),i});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cRow",""]],hostAttrs:[1,"row"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{xs:[1,"xs"],sm:[1,"sm"],md:[1,"md"],lg:[1,"lg"],xl:[1,"xl"],xxl:[1,"xxl"]}})}return t})(),t0=(()=>{class t extends gr{static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-row"]],features:[Qe],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),i0=(()=>{class t{gutter=r({});hostClasses=d(()=>{let e,i=this.gutter();if(typeof i=="number")return e=t.getGutterClasses({g:i}),e;{let{g:n,gx:s,gy:a}=S({},i);e=t.getGutterClasses({g:n,gx:s,gy:a})}return Object.keys(In).forEach(n=>{let s=i[n]?S({},i[n]):void 0;if(s){let a=t.getGutterClasses(s,n);e=S(S({},e),a)}}),e});static getGutterClasses(e,i){let{g:n,gx:s,gy:a}=S({},e),l=i?`-${i}`:"";return{[`g${l}-${n}`]:typeof n=="number",[`gx${l}-${s}`]:typeof s=="number",[`gy${l}-${a}`]:typeof a=="number"}}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","gutter",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{gutter:[1,"gutter"]},exportAs:["gutter"]})}return t})();var n0=(()=>{class t{container=r();position=r();role=r("banner");hostClasses=d(()=>this.container()?this.containerClasses():this.headerClasses());headerClasses=d(()=>{let e=this.position();return{header:!0,[`header-${e}`]:!!e}});containerClasses=d(()=>{let e=this.container();return{container:e===!0,[`container-${e}`]:typeof e=="string"}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-header"],["","c-header",""]],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{container:[1,"container"],position:[1,"position"],role:[1,"role"]},exportAs:["cHeader"],ngContentSelectors:pi,decls:2,vars:1,consts:[[3,"ngClass"]],template:function(i,n){i&1&&(x(pi),A(0,dc,2,1,"div",0)(1,pc,1,0)),i&2&&E(n.container()?0:1)},dependencies:[we],encapsulation:2})}return t})();var o0=(()=>{class t{role=r("navigation");static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-header-nav"]],hostAttrs:[1,"header-nav"],hostVars:1,hostBindings:function(i,n){i&2&&M("role",n.role())},inputs:{role:[1,"role"]},exportAs:["cHeaderNav"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var s0=(()=>{class t{#e=c(X);#t=c(O);type=r("button");ariaLabel=r("Toggle navigation");addDefaultIcon(){let e=this.#e.createElement("span");this.#e.addClass(e,"header-toggler-icon"),this.#e.appendChild(this.#t.nativeElement,e)}ngAfterContentInit(){this.#t.nativeElement.childNodes.length>0||this.addDefaultIcon()}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cHeaderToggler",""]],hostAttrs:[1,"header-toggler"],hostVars:2,hostBindings:function(i,n){i&2&&M("type",n.type())("aria-label",n.ariaLabel())},inputs:{type:[1,"type"],ariaLabel:[1,"ariaLabel"]},exportAs:["cHeaderToggler"]})}return t})();var r0=(()=>{class t{static ngAcceptInputType_flush;flush=r(!1,{transform:v});horizontal=r();hostClasses=d(()=>{let e=this.horizontal();return{"list-group":!0,"list-group-horizontal":e===!0||e==="",[`list-group-horizontal-${e}`]:!!e&&typeof e!="boolean","list-group-flush":this.flush()}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cListGroup",""]],hostAttrs:[1,"list-group"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{flush:[1,"flush"],horizontal:[1,"horizontal"]}})}return t})(),a0=(()=>{class t{static ngAcceptInputType_active;static ngAcceptInputType_disabled;hostElement=c(O);active=r(!1,{transform:v});color=r();disabled=r(!1,{transform:v});tabindex=r(void 0,{transform:Ae});hostClasses=d(()=>{let e=this.hostElement.nativeElement;return{"list-group-item":!0,"list-group-item-action":e.nodeName==="A"||e.nodeName==="BUTTON",active:this.active(),disabled:this._disabled(),[`list-group-item-${this.color()}`]:!!this.color()}});_disabled=d(()=>this.disabled());ariaDisabled=d(()=>this._disabled()?!0:null);attrDisabled=d(()=>this._disabled()?"":null);tabIndex=d(()=>this._disabled()?"-1":this.tabindex()??null);ariaCurrent=d(()=>this.active()||null);static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cListGroupItem",""],["c-list-group-item"]],hostVars:6,hostBindings:function(i,n){i&2&&(M("aria-disabled",n.ariaDisabled())("aria-current",n.ariaCurrent())("disabled",n.attrDisabled())("tabindex",n.tabIndex()),g(n.hostClasses()))},inputs:{active:[1,"active"],color:[1,"color"],disabled:[1,"disabled"],tabindex:[1,"tabindex"]},exportAs:["cListGroupItem"]})}return t})();var c0=(()=>{class t{static ngAcceptInputType_disabled;cNavLink=r(!0,{transform:v});active=r();disabled=r(!1,{transform:v});tabindex=r(void 0,{transform:Ae});ariaCurrent=d(()=>this.active()?"page":null);ariaDisabled=null;attrDisabled=null;attrTabindex=null;styleCursor=null;#e=T(()=>{let e=this.disabled();this.ariaDisabled=e||null,this.attrDisabled=e?"":null,this.attrTabindex=e?-1:this.tabindex()??null,this.styleCursor=e?null:"pointer"});hostClasses=d(()=>({"nav-link":this.cNavLink(),disabled:this.disabled(),active:this.active()}));static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cNavLink",""]],hostVars:8,hostBindings:function(i,n){i&2&&(M("aria-current",n.ariaCurrent())("aria-disabled",n.ariaDisabled)("disabled",n.attrDisabled)("tabindex",n.attrTabindex),g(n.hostClasses()),Bn("cursor",n.styleCursor))},inputs:{cNavLink:[1,"cNavLink"],active:[1,"active"],disabled:[1,"disabled"],tabindex:[1,"tabindex"]}})}return t})(),l0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-nav-item"]],hostAttrs:[1,"nav-item"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:list-item;text-align:match-parent;text-align:-webkit-match-parent}"]})}return t})(),d0=(()=>{class t{layout=r();variant=r();hostClasses=d(()=>{let e=this.layout(),i=this.variant();return{nav:!0,[`nav-${e}`]:!!e,[`nav-${i}`]:!!i}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-nav"]],hostAttrs:[1,"nav"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{layout:[1,"layout"],variant:[1,"variant"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]   .nav-link[_ngcontent-%COMP%]:focus{outline:0}.nav-underline-border[_nghost-%COMP%]{column-gap:0}"]})}return t})();var p0=(()=>{class t{#e=c(li);#t=c(ce);#i=c(O);color=r();container=r();expand=r();placement=r();role=r("navigation");collapse=wt(vo);hostClasses=d(()=>{let e=this.color(),i=this.expand(),n=i===!0?"":`-${i}`,s=this.placement();return{navbar:!0,[`navbar-expand${n}`]:!!i,[`bg-${e}`]:!!e,[`${s}`]:!!s}});containerClass=d(()=>{let e=this.container();return`container${e!==!0?"-"+e:""}`});computedStyle=B("");#n=Zo({read:()=>{let e=this.expand();if(typeof e=="string"){let i=this.#t.defaultView?.getComputedStyle(this.#i.nativeElement)?.getPropertyValue(`--cui-breakpoint-${e}`)??!1;i&&this.computedStyle.set(i)}}});breakpoint=d(()=>typeof this.expand()=="string"?this.computedStyle():!1);#o;ngAfterContentInit(){let e=this.breakpoint();if(e){let i=`(min-width: ${e})`;this.#o=this.#e.observe([i]).pipe().subscribe(n=>{let s=this.collapse();if(s){let a=s.animate();s.animate.set(!1),s.toggle(!1),setTimeout(()=>{s.toggle(n.matches),setTimeout(()=>{s.animate.set(a)})})}})}}ngOnDestroy(){this.#o?.unsubscribe()}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-navbar"]],contentQueries:function(i,n,s){i&1&&pe(s,n.collapse,vo,5),i&2&&ee()},hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{color:[1,"color"],container:[1,"container"],expand:[1,"expand"],placement:[1,"placement"],role:[1,"role"]},features:[Pe([{directive:zi,inputs:["colorScheme","colorScheme"]}])],ngContentSelectors:pi,decls:5,vars:1,consts:[["withContainerTemplate",""],["noContainerTemplate",""],[4,"ngTemplateOutlet"],[3,"ngClass"]],template:function(i,n){if(i&1&&(x(pi),R(0,uc,1,0,"ng-container",2)(1,fc,2,1,"ng-template",null,0,re)(3,mc,1,0,"ng-template",null,1,re)),i&2){let s=Q(2),a=Q(4);m("ngTemplateOutlet",n.container()?s:a)}},dependencies:[we,Le],encapsulation:2})}return t})(),u0=(()=>{class t{role=r("button");static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cNavbarBrand",""]],hostAttrs:[1,"navbar-brand"],hostVars:1,hostBindings:function(i,n){i&2&&M("role",n.role())},inputs:{role:[1,"role"]}})}return t})(),f0=(()=>{class t{scroll=r(!1,{transform:v});hostClasses=d(()=>({"navbar-nav":!0,"navbar-nav-scroll":this.scroll()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-navbar-nav"]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{scroll:[1,"scroll"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var m0=(()=>{class t{#e=c(X);#t=c(O);constructor(){Yt({read:()=>{this.#t.nativeElement.childNodes.length||this.addDefaultIcon()}})}collapseRef=r(void 0,{alias:"cNavbarToggler"});type=r("button");ariaLabel=r("Toggle navigation");handleClick(e){let i=this.collapseRef();i?.toggle(!i?.visible())}addDefaultIcon(){let e=this.#e.createElement("span");this.#e.addClass(e,"navbar-toggler-icon"),this.#e.appendChild(this.#t.nativeElement,e)}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cNavbarToggler",""]],hostAttrs:[1,"navbar-toggler"],hostVars:2,hostBindings:function(i,n){i&1&&oe("click",function(a){return n.handleClick(a)}),i&2&&M("aria-label",n.ariaLabel())("type",n.type())},inputs:{collapseRef:[1,"cNavbarToggler","collapseRef"],type:[1,"type"],ariaLabel:[1,"ariaLabel"]}})}return t})();var h0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal-body"]],hostAttrs:[1,"modal-body"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),hd=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal-content"]],hostAttrs:[1,"modal-content"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),vd=(()=>{class t{alignment=r();fullscreen=r();scrollable=r(!1,{transform:v});size=r();hostClasses=d(()=>{let e=this.fullscreen(),i=this.size();return{"modal-dialog":!0,"modal-dialog-centered":this.alignment()==="center","modal-fullscreen":e===!0,[`modal-fullscreen-${e}-down`]:typeof e=="string","modal-dialog-scrollable":this.scrollable(),[`modal-${i}`]:!!i}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal-dialog"]],hostAttrs:[1,"modal-dialog"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{alignment:[1,"alignment"],fullscreen:[1,"fullscreen"],scrollable:[1,"scrollable"],size:[1,"size"]},ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}.modal-dialog-centered[_nghost-%COMP%]{display:flex}"]})}return t})(),br=(()=>{class t{#e=new ke;modalState$=this.#e.asObservable();toggle(e){this.#e.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),v0=(()=>{class t{#e=c(br);toggle=r(void 0,{alias:"cModalToggle"});dismiss(e){e.preventDefault(),this.#e.toggle({show:"toggle",id:this.toggle()})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cModalToggle",""]],hostBindings:function(i,n){i&1&&oe("click",function(a){return n.dismiss(a)})},inputs:{toggle:[1,"cModalToggle","toggle"]}})}return t})(),g0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal-footer"]],hostAttrs:[1,"modal-footer"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),b0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal-header"]],hostAttrs:[1,"modal-header"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),y0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cModalTitle",""]],hostAttrs:[1,"modal-title"]})}return t})(),C0=(()=>{class t{#e=c(ce);#t=c(X);#i=c(O);#n=c(br);#o=c(ql);#s=c(be);#a=c(ro);alignment=r("top");backdrop=r(!0);fullscreen=r();keyboard=r(!0,{transform:v});attrId=r(void 0,{alias:"id"});get id(){return this.attrId()}size=r();transition=r(!0,{transform:v});role=r("dialog");ariaModalInput=r(!1,{transform:v,alias:"ariaModal"});ariaModal=d(()=>this.visible||this.ariaModalInput()?!0:null);scrollable=r(!1,{transform:v});visibleInput=r(!1,{transform:v,alias:"visible"});#r=T(()=>{let e=this.visibleInput();Fe(()=>{this.visible=e})});set visible(e){this.#c()!==e&&(this.#c.set(e),this.setBodyStyles(e),this.setBackdrop(this.backdrop()!==!1&&e),this.visibleChange?.emit(e))}get visible(){return this.#c()}#c=B(!1);#l=B(null);#d=T(()=>{let e=this.#c(),i=this.#u();Fe(()=>{if(e&&i)this.#l.set(this.#e.activeElement),setTimeout(()=>{let n=this.modalContentRef()?.nativeElement.querySelectorAll('[tabindex]:not([tabindex="-1"]), button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled])');n?.length&&this.#a.focusVia(n[0],"keyboard")});else{let n=this.#l();n&&this.#e.contains(n)&&(this.#a.focusVia(n,"keyboard"),setTimeout(()=>{this.#l.set(null)}))}})});visibleChange=Re();modalContentRef=ti("modalContentRef",{read:O});#p;hostClasses=d(()=>({modal:!0,fade:this.transition(),show:this.show}));get ariaHidden(){return this.visible?null:!0}animateTrigger=d(()=>this.visible?"visible":"hidden");get show(){return this.visible&&this.#f()}set show(e){this.#f.set(e)}#f=B(!0);animateStart(e){e.toState==="visible"?(this.#o.hideScrollbar(),this.#t.setStyle(this.#i.nativeElement,"display","block")):this.transition()||this.#t.setStyle(this.#i.nativeElement,"display","none")}animateDone(e){setTimeout(()=>{e.toState==="hidden"&&(this.#t.setStyle(this.#i.nativeElement,"display","none"),this.#o.resetScrollbar())}),this.show=this.visible}onKeyUpHandler(e){e.key==="Escape"&&this.keyboard()&&this.visible&&(this.backdrop()==="static"?this.setStaticBackdrop():this.#n.toggle({show:!1,modal:this}))}mouseDownTarget=null;onMouseDownHandler(e){this.mouseDownTarget=e.target}onClickHandler(e){if(this.mouseDownTarget!==e.target){this.mouseDownTarget=null;return}if(e.target===this.#i.nativeElement){if(this.backdrop()==="static"){this.setStaticBackdrop();return}this.#n.toggle({show:!1,modal:this})}}ngOnInit(){this.stateToggleSubscribe()}#u=B(!1);ngAfterViewInit(){this.#u.set(!0)}ngOnDestroy(){this.#n.toggle({show:!1,modal:this}),this.#u.set(!1)}stateToggleSubscribe(){this.#n.modalState$.pipe(ye(this.#s)).subscribe(e=>{this===e.modal||this.id===e.id?"show"in e&&(this.visible=e?.show==="toggle"?!this.visible:e.show):this.visible&&(this.visible=!1)})}setBackdrop(e){this.#p=e?this.#o.setBackdrop("modal"):this.#o.clearBackdrop(this.#p)}setBodyStyles(e){e?this.backdrop()===!0&&this.#t.addClass(this.#e.body,"modal-open"):this.#t.removeClass(this.#e.body,"modal-open")}setStaticBackdrop(){this.transition()&&(this.#t.addClass(this.#i.nativeElement,"modal-static"),this.#t.setStyle(this.#i.nativeElement,"overflow-y","hidden"),setTimeout(()=>{this.#t.removeClass(this.#i.nativeElement,"modal-static"),this.#t.removeStyle(this.#i.nativeElement,"overflow-y")},300))}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-modal"]],viewQuery:function(i,n){i&1&&Jt(n.modalContentRef,hc,5,O),i&2&&ee()},hostAttrs:[1,"modal"],hostVars:8,hostBindings:function(i,n){i&1&&(tn("@showHide.start",function(a){return n.animateStart(a)})("@showHide.done",function(a){return n.animateDone(a)}),oe("mousedown",function(a){return n.onMouseDownHandler(a)})("click",function(a){return n.onClickHandler(a)})("keyup",function(a){return n.onKeyUpHandler(a)},Xo)),i&2&&(Zt("@showHide",n.animateTrigger()),M("role",n.role())("inert",n.ariaHidden)("id",n.id)("aria-modal",n.ariaModal())("tabindex",-1),g(n.hostClasses()))},inputs:{alignment:[1,"alignment"],backdrop:[1,"backdrop"],fullscreen:[1,"fullscreen"],keyboard:[1,"keyboard"],attrId:[1,"id","attrId"],size:[1,"size"],transition:[1,"transition"],role:[1,"role"],ariaModalInput:[1,"ariaModal","ariaModalInput"],scrollable:[1,"scrollable"],visibleInput:[1,"visible","visibleInput"]},outputs:{visibleChange:"visibleChange"},exportAs:["cModal"],ngContentSelectors:D,decls:5,vars:6,consts:[["modalContentRef",""],[3,"alignment","fullscreen","scrollable","size"],[2,"display","contents",3,"cdkTrapFocus","cdkTrapFocusAutoCapture"]],template:function(i,n){i&1&&(x(),I(0,"c-modal-dialog",1)(1,"c-modal-content")(2,"div",2,0),C(4),w()()()),i&2&&(m("alignment",n.alignment())("fullscreen",n.fullscreen())("scrollable",n.scrollable())("size",n.size()),f(2),m("cdkTrapFocus",n.visible)("cdkTrapFocusAutoCapture",n.visible))},dependencies:[vd,hd,uo,po],encapsulation:2,data:{animation:[It("showHide",[Me("visible",H({})),Me("hidden",H({})),Ie("visible <=> *",[q("150ms")])])]}})}return t})();var pr=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cPageLink",""]],hostAttrs:[1,"page-link"]})}return t})(),gd=(()=>{class t{#e=c(X);active=r();disabled=r();ariaCurrent=d(()=>this.active()?"page":null);hostClasses=d(()=>({"page-item":!0,disabled:this.disabled(),active:this.active()}));pageLinkElementRef=wt(pr,{read:O});pageLinkElementRefEffect=T(()=>{let e=this.pageLinkElementRef(),i=this.disabled();if(!e)return;let n=e.nativeElement;i?(this.#e.setAttribute(n,"aria-disabled","true"),this.#e.setAttribute(n,"tabindex","-1")):(this.#e.removeAttribute(n,"aria-disabled"),this.#e.removeAttribute(n,"tabindex"))});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cPageItem",""]],contentQueries:function(i,n,s){i&1&&pe(s,n.pageLinkElementRef,pr,5,O),i&2&&ee()},hostAttrs:[1,"page-item"],hostVars:3,hostBindings:function(i,n){i&2&&(M("aria-current",n.ariaCurrent()),g(n.hostClasses()))},inputs:{active:[1,"active"],disabled:[1,"disabled"]}})}return t})(),_0=(()=>{class t extends gd{static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-page-item"]],features:[Qe],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:[vc]})}return t})(),x0=(()=>{class t{align=r("");size=r();role=r("navigation");paginationClass=d(()=>{let e=this.size(),i=this.align();return{pagination:!0,[`pagination-${e}`]:!!e,[`justify-content-${i}`]:!!i}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-pagination"]],hostVars:1,hostBindings:function(i,n){i&2&&M("role",n.role())},inputs:{align:[1,"align"],size:[1,"size"],role:[1,"role"]},ngContentSelectors:D,decls:2,vars:1,consts:[[3,"ngClass"]],template:function(i,n){i&1&&(x(),I(0,"ul",0),C(1),w()),i&2&&m("ngClass",n.paginationClass())},dependencies:[we],encapsulation:2})}return t})();var ur=(()=>{class t{visible=r(!1,{transform:v,alias:"cPlaceholder"});size=r(void 0,{alias:"cPlaceholderSize"});ariaHidden=d(()=>this.visible()?null:!0);hostClasses=d(()=>({placeholder:this.visible(),[`placeholder-${this.size()}`]:!!this.size()}));static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cPlaceholder",""]],hostVars:3,hostBindings:function(i,n){i&2&&(M("aria-hidden",n.ariaHidden()),g(n.hostClasses()))},inputs:{visible:[1,"cPlaceholder","visible"],size:[1,"cPlaceholderSize","size"]},exportAs:["cPlaceholder"]})}return t})(),w0=(()=>{class t{animation=r(void 0,{alias:"cPlaceholderAnimation"});placeholder=wt(ur);hostClasses=d(()=>({[`placeholder-${this.animation()}`]:this.placeholder()?.visible()&&!!this.animation()}));static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cPlaceholderAnimation",""]],contentQueries:function(i,n,s){i&1&&pe(s,n.placeholder,ur,5),i&2&&ee()},hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{animation:[1,"cPlaceholderAnimation","animation"]}})}return t})();var bd=(()=>{class t{renderer=c(X);content=r("");#e=T(()=>{this.updateView(this.content())});visible=r(!1,{transform:v});id=r();role=r("tooltip");viewContainerRef=ti("popoverTemplate",{read:tt});textNode;hostClasses=d(()=>({popover:!0,fade:!0,show:this.visible(),"bs-popover-auto":!0}));ngOnDestroy(){this.clear()}clear(){this.viewContainerRef()?.clear(),this.textNode&&this.renderer.removeChild(this.textNode.parentNode,this.textNode)}updateView(e){if(this.clear(),!!e)if(e instanceof en)this.viewContainerRef()?.createEmbeddedView(e);else{let i=this.renderer.createText(e);this.textNode=this.renderer.createElement("div"),this.renderer.addClass(this.textNode,"popover-body"),this.renderer.appendChild(this.textNode,i);let n=this.viewContainerRef()?.element.nativeElement;this.renderer.appendChild(n.parentNode,this.textNode)}}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-popover"]],viewQuery:function(i,n){i&1&&Jt(n.viewContainerRef,gc,5,tt),i&2&&ee()},hostAttrs:[1,"popover","fade","bs-popover-auto"],hostVars:4,hostBindings:function(i,n){i&2&&(M("role",n.role())("id",n.id()),g(n.hostClasses()))},inputs:{content:[1,"content"],visible:[1,"visible"],id:[1,"id"],role:[1,"role"]},decls:4,vars:3,consts:[["popoverTemplate",""],["data-popper-arrow","",3,"ngClass"]],template:function(i,n){i&1&&(it(0),K(1,"div",1),W(2,null,0),nt()),i&2&&(f(),m("ngClass",Pt(1,bc,!!n.content())))},dependencies:[we],encapsulation:2})}return t})(),I0=(()=>{class t{#e=c(X);#t=c(O);#i=c(tt);#n=c(ui);#o=c(Mi);#s=c(Li);#a=c(be);#r=c(ce);content=r(void 0,{alias:"cPopover"});#c=T(()=>{this.content()&&this.destroyTooltipElement()});popperOptions=r({},{alias:"cPopoverOptions"});#l=T(()=>{this._popperOptions=S(mt(S({},this._popperOptions),{placement:this.placement()}),this.popperOptions())});popperOptionsComputed=d(()=>S({placement:this.placement()},this._popperOptions));placement=r("top",{alias:"cPopoverPlacement"});reference=r(void 0,{alias:"cTooltipRef"});referenceRef=d(()=>this.reference()?.elementRef??this.#t);trigger=r("hover",{alias:"cPopoverTrigger"});visible=on(!1,{alias:"cPopoverVisible"});#d=T(()=>{this.visible()?this.addTooltipElement():this.removeTooltipElement()});get ariaDescribedBy(){return this.tooltipId?this.tooltipId:null}tooltip;tooltipId;tooltipRef;popperInstance;_popperOptions={modifiers:[{name:"offset",options:{offset:[0,9]}}]};ngAfterViewInit(){this.intersectionServiceSubscribe()}ngOnDestroy(){this.clearListeners(),this.destroyTooltipElement()}ngOnInit(){this.setListeners()}setListeners(){let e={hostElement:this.#t,trigger:this.trigger(),callbackToggle:()=>{this.visible.update(i=>!i)},callbackOff:()=>{this.visible.set(!1)},callbackOn:()=>{this.visible.set(!0)}};this.#n.setListeners(e)}clearListeners(){this.#n.clearListeners()}intersectionServiceSubscribe(){this.#s.createIntersectionObserver(this.referenceRef()),this.#s.intersecting$.pipe(Ge(e=>e.hostElement===this.referenceRef()),Bt(100),Yi(()=>{this.#s.unobserve(this.referenceRef())}),ye(this.#a)).subscribe(e=>{this.visible.set(e.isIntersecting?this.visible():!1)})}getUID(e){let i=e??"random-id";do i=`${e}-${Math.floor(Math.random()*1e6).toString(10)}`;while(this.#r.getElementById(i));return i}createTooltipElement(){this.tooltipRef||(this.tooltipRef=this.#i.createComponent(bd))}destroyTooltipElement(){this.tooltip?.remove(),this.tooltipRef?.destroy(),this.tooltipRef=void 0,this.popperInstance?.destroy(),this.#i?.detach(),this.#i?.clear()}addTooltipElement(){if(!this.content()){this.destroyTooltipElement();return}if(this.tooltipRef||this.createTooltipElement(),this.tooltipRef?.setInput("content",this.content()??""),this.tooltip=this.tooltipRef?.location.nativeElement,this.#e.addClass(this.tooltip,"d-none"),this.#e.addClass(this.tooltip,"fade"),this.popperInstance?.destroy(),this.#i.insert(this.tooltipRef.hostView),this.#e.appendChild(this.#r.body,this.tooltip),this.popperInstance=Ni(this.referenceRef().nativeElement,this.tooltip,S({},this.popperOptionsComputed())),!this.visible()){this.removeTooltipElement();return}setTimeout(()=>{this.tooltipId=this.getUID("popover"),this.tooltipRef?.setInput("id",this.tooltipId),this.#e.removeClass(this.tooltip,"d-none"),this.tooltipRef?.setInput("visible",this.visible()),this.popperInstance?.forceUpdate(),this.#o?.markForCheck()},100)}removeTooltipElement(){this.tooltipId="",this.tooltipRef&&(this.tooltipRef.setInput("visible",!1),this.tooltipRef.setInput("id",void 0),this.#o.markForCheck(),setTimeout(()=>{this.#i?.detach()},300))}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cPopover",""]],hostVars:1,hostBindings:function(i,n){i&2&&M("aria-describedby",n.ariaDescribedBy)},inputs:{content:[1,"cPopover","content"],popperOptions:[1,"cPopoverOptions","popperOptions"],placement:[1,"cPopoverPlacement","placement"],reference:[1,"cTooltipRef","reference"],trigger:[1,"cPopoverTrigger","trigger"],visible:[1,"cPopoverVisible","visible"]},outputs:{visible:"cPopoverVisibleChange"},exportAs:["cPopover"],features:[Ne([ui,Li])]})}return t})();var go=(()=>{class t{stacked=B(!1);value=B(void 0);precision=B(0);min=B(0);max=B(100);percent=d(()=>+(((this.value()??0)-this.min())/(this.max()-this.min())*100).toFixed(this.precision()));static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),_n=(()=>{class t{#e=c(X);#t=c(O);#i=c(go);#n=T(()=>{let e=this.#t.nativeElement,i=this.#i.value(),n=this.#i.percent(),s=this.#i.stacked();if(i===void 0)for(let l of["aria-valuenow","aria-valuemax","aria-valuemin","role"])this.#e.removeAttribute(e,l);else{let{min:l,max:p}=this.#i;this.#e.setAttribute(e,"aria-valuenow",String(i)),this.#e.setAttribute(e,"aria-valuemin",String(l())),this.#e.setAttribute(e,"aria-valuemax",String(p())),this.#e.setAttribute(e,"role",this.role())}let a=e.tagName;n>=0&&(s&&a==="C-PROGRESS"||!s&&a!=="C-PROGRESS")?this.#e.setStyle(e,"width",`${n}%`):this.#e.removeStyle(e,"width")});animated=r(void 0,{transform:v});color=r();precision=r(0,{transform:Ae});value=r(0,{transform:Ae});variant=r();max=r(100,{transform:Ae});role=r("progressbar");#o=T(()=>{this.#i.precision.set(this.precision());let e=this.max();this.#i.max.set(isNaN(e)||e<=0?100:e);let i=this.value();this.#i.value.set(i&&!isNaN(i)?i:void 0)});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cProgressBar",""]],inputs:{animated:[1,"animated"],color:[1,"color"],precision:[1,"precision"],value:[1,"value"],variant:[1,"variant"],max:[1,"max"],role:[1,"role"]},exportAs:["cProgressBar"]})}return t})(),fo=(()=>{class t{#e=c(_n,{optional:!0});hostClasses=d(()=>{let e=this.#e?.animated(),i=this.#e?.color(),n=this.#e?.variant();return{"progress-bar":!0,"progress-bar-animated":!!e,[`progress-bar-${n}`]:!!n,[`bg-${i}`]:!!i}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-progress-bar"]],hostAttrs:[1,"progress-bar"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},features:[Pe([{directive:_n,inputs:["animated","animated","color","color","max","max","role","role","value","value","variant","variant"]}])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),yd=(()=>{class t{stacked=r(!0);static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-progress-stacked"]],hostVars:2,hostBindings:function(i,n){i&2&&_t("progress-stacked",n.stacked())},inputs:{stacked:[1,"stacked"]},exportAs:["cProgressStacked"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:flex}"]})}return t})(),D0=(()=>{class t{#e=c(O);progressBarDirective=c(_n,{optional:!0});#t=c(yd,{optional:!0})?.stacked()??!1;#i=c(go);constructor(){this.#i.stacked.set(this.#t)}stacked=this.#i.stacked;percent=this.#i.percent;barValue=this.#i.value;contentProgressBars=je(fo);height=r(0,{transform:Ae});thin=r(!1,{transform:v});white=r(!1,{transform:v});hostClasses=d(()=>({progress:!0,"progress-thin":this.thin(),"progress-white":this.white()}));hostStyle=d(()=>{let e=this.height();return e?`${e}px`:this.#e?.nativeElement?.style?.height??void 0});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-progress"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentProgressBars,fo,4),i&2&&ee()},hostAttrs:[1,"progress"],hostVars:4,hostBindings:function(i,n){i&2&&(g(n.hostClasses()),Bn("height",n.hostStyle()))},inputs:{height:[1,"height"],thin:[1,"thin"],white:[1,"white"]},exportAs:["cProgress"],features:[Ne([go]),Pe([{directive:_n,inputs:["animated","animated","color","color","max","max","role","role","value","value","variant","variant"]}])],ngContentSelectors:D,decls:4,vars:1,consts:[["defaultContent",""],[3,"animated","variant","color","value"],[4,"ngTemplateOutlet"]],template:function(i,n){if(i&1&&(x(),A(0,Cc,1,1,"ng-container")(1,xc,2,5,"c-progress-bar",1),R(2,wc,1,0,"ng-template",null,0,re)),i&2){let s;E((s=n.contentProgressBars())!=null&&s.length?0:1)}},dependencies:[fo,Le],styles:[".progress-stacked.progress[_nghost-%COMP%], .progress-stacked   .progress[_nghost-%COMP%]{transition:var(--cui-progress-bar-transition)}"]})}return t})();var Wi=(()=>{class t{sidebarState=new Ke({});sidebarState$=this.sidebarState.asObservable();toggle(e){this.sidebarState.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Cd=(()=>{class t{#e=c(ce);#t=c(Wi);#i;renderer;#n=()=>{};setBackdrop(e){this.#e.getElementsByClassName("sidebar-backdrop").length===0&&(this.#i=this.renderer.createElement("div"),this.renderer.addClass(this.#i,"sidebar-backdrop"),this.renderer.appendChild(this.#e.body,this.#i),this.#n=this.renderer.listen(this.#i,"click",n=>{this.#t.toggle({toggle:"visible",sidebar:e})})),this.#i&&e.sidebarState.mobile&&e.sidebarState.visible?(this.renderer.addClass(this.#i,"fade"),this.renderer.addClass(this.#i,"show")):(this.renderer.removeClass(this.#i,"show"),this.renderer.removeClass(this.#i,"fade"))}clearBackdrop(){this.#i&&(this.#n(),this.renderer.removeChild(this.#e.body,this.#i),this.#i=void 0)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),_d=(()=>{class t{#e=c(ce);#t=c(X);#i=c(li);#n=c(Wi);#o=c(Cd);#s=!1;#a;#r;state=B({sidebar:this});#c={narrow:!1,visible:!1,unfoldable:!1};colorScheme=r();id=r();narrowInput=r(!1,{transform:v,alias:"narrow"});#l=me(this.narrowInput);set narrow(e){this.#l.set(e)}get narrow(){return this.#l()}overlaid=r(!1,{transform:v});placement=r();position=r("fixed");size=r();unfoldableInput=r(!1,{transform:v,alias:"unfoldable"});unfoldable=me({source:this.unfoldableInput,computation:e=>e});visibleInput=r(!1,{transform:v,alias:"visible"});#d=me(this.visibleInput);#p=T(()=>{this.visibleChange?.emit(this.#d())});set visible(e){this.#d.set(e)}get visible(){return this.#d()}visibleChange=Re();set sidebarState(e){let i=e;"toggle"in i?i.toggle==="visible"?(i.visible=!this.state().visible,this.#d.set(i.visible)):i.toggle==="unfoldable"&&(i.unfoldable=!this.state().unfoldable,this.unfoldable.set(i.unfoldable)):this.#d.update(n=>(i.visible??n)&&!this.overlaid()),this.state.update(n=>S(S({},n),i)),this.state().mobile&&this.state().visible?this.#o.setBackdrop(this):this.#o.clearBackdrop()}get sidebarState(){return S({},this.state())}get getMobileBreakpoint(){let e=this.#e.documentElement,i=this.#e.defaultView?.getComputedStyle(e)?.getPropertyValue("--cui-mobile-breakpoint")??"md",n=this.#e.defaultView?.getComputedStyle(e)?.getPropertyValue(`--cui-breakpoint-${i.trim()}`)??"768px";return`${parseFloat(n.trim())-.02}px`}constructor(){this.#o.renderer=this.#t}hostClasses=d(()=>{let{mobile:e,visible:i}=S({},this.sidebarState),n=this.unfoldable(),s=this.placement(),a=this.colorScheme(),l=this.size();return{sidebar:!0,"sidebar-fixed":this.position()==="fixed"&&!e,"sidebar-narrow":this.#l()&&!n,"sidebar-narrow-unfoldable":n,"sidebar-overlaid":this.overlaid(),[`sidebar-${s}`]:!!s,[`sidebar-${a}`]:!!a,[`sidebar-${l}`]:!!l,show:i,hide:!i}});ngOnInit(){this.setInitialState(),this.layoutChangeSubscribe(),this.stateToggleSubscribe()}ngOnDestroy(){this.stateToggleSubscribe(!1),this.layoutChangeSubscribe(!1)}ngOnChanges(e){let i=new Map(Object.entries(this.state())),n=new Map;n.set("sidebar",this);let s=["visible","unfoldable","narrow"];for(let a in e)if(s.includes(a)&&e[a]&&!e[a].firstChange){let l=v(e[a].currentValue);i.get(a)!==l&&n.set(a,l)}if(n.size>1){let a=Object.fromEntries(n.entries());this.#n.toggle(a)}}setInitialState(){this.#c={narrow:this.#l(),visible:this.#d(),unfoldable:this.unfoldable()},this.#n.toggle(mt(S({},this.#c),{sidebar:this}))}stateToggleSubscribe(e=!0){e?this.#r=this.#n.sidebarState$.subscribe(i=>{(this===i.sidebar||this.id()===i.id)&&(this.sidebarState=S({},i))}):this.#r?.unsubscribe()}layoutChangeSubscribe(e=!0){let i=`(max-width: ${this.getMobileBreakpoint})`;if(e){let n=this.#i.observe([i]);this.#a=n.subscribe(s=>{let a=s.breakpoints[i],l=a?!1:this.unfoldable();this.#s!==a&&(this.#s=a,this.#n.toggle({mobile:a,unfoldable:l,visible:a?!a:this.#c.visible,sidebar:this}))})}else this.#a?.unsubscribe()}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar"]],hostAttrs:[1,"sidebar"],hostVars:3,hostBindings:function(i,n){i&2&&(M("inert",!n.sidebarState.visible||null),g(n.hostClasses()))},inputs:{colorScheme:[1,"colorScheme"],id:[1,"id"],narrowInput:[1,"narrow","narrowInput"],overlaid:[1,"overlaid"],placement:[1,"placement"],position:[1,"position"],size:[1,"size"],unfoldableInput:[1,"unfoldable","unfoldableInput"],visibleInput:[1,"visible","visibleInput"]},outputs:{visibleChange:"visibleChange"},exportAs:["cSidebar"],features:[_i],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),T0=(()=>{class t{brandFull=r();brandNarrow=r();routerLink=r();brandImg=d(()=>!!(this.brandFull()||this.brandNarrow()));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-brand"]],hostAttrs:[1,"sidebar-brand"],inputs:{brandFull:[1,"brandFull"],brandNarrow:[1,"brandNarrow"],routerLink:[1,"routerLink"]},ngContentSelectors:D,decls:2,vars:1,consts:[[3,"routerLink"],[3,"cHtmlAttr","ngClass"]],template:function(i,n){i&1&&(x(),A(0,Tc,3,3,"a",0)(1,Mc,1,0)),i&2&&E(n.brandImg()?0:1)},dependencies:[Ai,mi,we],encapsulation:2})}return t})(),xd=(()=>{class t{#e=c(Wi);id=r(void 0,{alias:"cSidebarToggle"});toggle=r("visible");toggleOpen(e){e.preventDefault(),this.#e.toggle({toggle:this.toggle(),id:this.id()})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cSidebarToggle",""]],hostBindings:function(i,n){i&1&&oe("click",function(a){return n.toggleOpen(a)})},inputs:{id:[1,"cSidebarToggle","id"],toggle:[1,"toggle"]},exportAs:["cSidebarToggle"]})}return t})(),M0=(()=>{class t{role=r("button");get getStyles(){return{appearance:"button","align-items":"flex-start",cursor:"pointer"}}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cSidebarToggler",""]],hostAttrs:[1,"sidebar-toggler"],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),ei(n.getStyles))},inputs:{role:[1,"role"]},features:[Pe([{directive:xd,inputs:["cSidebarToggle","cSidebarToggler","toggle","toggle"]}])]})}return t})(),S0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-header"]],hostAttrs:[1,"sidebar-header"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),A0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-footer"]],hostAttrs:[1,"sidebar-footer"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})();var ut=(()=>{class t{itemType(e){return e.divider?"divider":e.title?"title":e.children&&e.children.length>0?"group":e.label?"label":Object.keys(e).length?"link":"empty"}isActive(e,i){return e.isActive(i.url,!1)}hasBadge=e=>!!e.badge;hasIcon=e=>!!e.icon||e.icon==="";hasIconComponent=e=>!!e.iconComponent;getIconClass(e){let i={"nav-icon":!0},n=e.icon;return i[n]=this.hasIcon(e),i}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),bo=(()=>{class t{sidebarNavGroupState=new Ke({});sidebarNavGroupState$=this.sidebarNavGroupState.asObservable();toggle(e){this.sidebarNavGroupState.next(e)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),yr=(()=>{class t{transform(e,i){let n=e.icon;return{"nav-icon":!0,[`${n}`]:!!n}}static \u0275fac=function(i){return new(i||t)};static \u0275pipe=wi({name:"cSidebarNavIcon",type:t,pure:!0})}return t})(),xo=(()=>{class t{transform(e,i){let n=e.badge;return{badge:!0,"ms-auto":!0,"badge-sm":!n.size,[`badge-${n.size}`]:!!n.size,[`bg-${n.color}`]:!!n.color,[`${n.class}`]:!!n.class}}static \u0275fac=function(i){return new(i||t)};static \u0275pipe=wi({name:"cSidebarNavBadge",type:t,pure:!0})}return t})(),wd=(()=>{class t{transform(e){let i=e?.attributes?.disabled;return{"nav-link":!0,disabled:i,"btn-link":i,[`nav-link-${e.variant}`]:!!e.variant}}static \u0275fac=function(i){return new(i||t)};static \u0275pipe=wi({name:"cSidebarNavLink",type:t,pure:!0})}return t})(),Id=(()=>{class t{helper=c(ut);item;static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-link-content"]],inputs:{item:"item"},features:[Ne([ut])],decls:1,vars:1,template:function(i,n){i&1&&A(0,Sc,2,1,"ng-container"),i&2&&E(0)},encapsulation:2})}return t})(),Cr=(()=>{class t{router=c(Si);_item={};set item(e){this._item=JSON.parse(JSON.stringify(e))}get item(){return this._item}linkClick=Re();linkType;href;linkActive;url;navigationEndObservable;navSubscription;constructor(){let e=this.router;this.navigationEndObservable=e.events.pipe(Ge(i=>i instanceof rn))}ngOnInit(){this.url=typeof this.item.url=="string"?this.item.url:this.router.serializeUrl(this.router.createUrlTree(this.item.url??[""])),this.linkType=this.getLinkType(),this.href=this.isDisabled()?"":this.item.href||this.url,this.linkActive=this.router.url.split(/[?#(;]/)[0]===this.href.split(/[?#(;]/)[0],this.navSubscription=this.navigationEndObservable.subscribe(e=>{let i=this.href.split(/[?#(;]/)[0].split("/"),n=e.urlAfterRedirects.split(/[?#(;]/)[0].split("/");this.linkActive=i.every((s,a)=>s===n[a])})}ngOnDestroy(){this.navSubscription?.unsubscribe()}getLinkType(){return this.isDisabled()?"disabled":this.isExternalLink()?"external":"link"}isDisabled(){return this.item?.attributes?.disabled}isExternalLink(){let e=Array.isArray(this.item.url)?this.item.url[0]:this.item.url;return!!this.item.href||e?.substring(0,4)==="http"}linkClicked(){this.linkClick?.emit()}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-link"]],inputs:{item:"item"},outputs:{linkClick:"linkClick"},features:[Ne([ut])],decls:5,vars:1,consts:[["iconTemplate",""],[3,"cHtmlAttr","ngClass"],[3,"cHtmlAttr","href","ngClass"],["routerLinkActive","active",3,"cHtmlAttr","fragment","ngClass","preserveFragment","queryParamsHandling","queryParams","replaceUrl","routerLinkActiveOptions","routerLink","skipLocationChange","state","target"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"item"],[3,"ngClass"],[3,"click","cHtmlAttr","href","ngClass"],["routerLinkActive","active",3,"click","cHtmlAttr","fragment","ngClass","preserveFragment","queryParamsHandling","queryParams","replaceUrl","routerLinkActiveOptions","routerLink","skipLocationChange","state","target"],[1,"nav-icon"],[3,"cIcon","customClasses","name"]],template:function(i,n){if(i&1&&(A(0,Oc,5,11,"a",1)(1,Pc,5,12,"a",2)(2,jc,5,23,"a",3),R(3,Vc,3,3,"ng-template",null,0,re)),i&2){let s;E((s=n.linkType)==="disabled"?0:s==="external"?1:2)}},dependencies:[an,Ai,Nn,mi,Ln,Id,wd,xo,yr,Le,we],encapsulation:2})}return t})(),Dd=(()=>{class t{helper=c(ut);item;classes={"c-nav-label":!0,"c-active":!0};iconClasses={};ngOnInit(){this.iconClasses=this.helper.getIconClass(this.item)}getItemClass(){let e=this.item.class;return this.classes[e]=!!e,this.classes}getLabelIconClass(){let e=`text-${this.item.label.variant}`;this.iconClasses[e]=!!this.item.label.variant;let i=this.item.label.class;return this.iconClasses[i]=!!i,this.iconClasses}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-label"]],inputs:{item:"item"},decls:5,vars:7,consts:[[3,"cHtmlAttr","ngClass","href"],[3,"ngClass"]],template:function(i,n){i&1&&(I(0,"a",0),A(1,zc,1,1,"i",1),it(2),z(3),nt(),A(4,Wc,3,4,"span",1),w()),i&2&&(m("href",nn(n.item.url),Ji)("cHtmlAttr",n.item.attributes)("ngClass",n.getItemClass()),f(),E(n.helper.hasIcon(n.item)?1:-1),f(2),se(n.item.name),f(),E(n.helper.hasBadge(n.item)?4:-1))},dependencies:[mi,xo,we],encapsulation:2})}return t})(),Td=(()=>{class t{#e=c(O);#t=c(X);item;ngOnInit(){let e=this.#e.nativeElement,i=this.#t.createText(this.item.name);if(this.item.class){let n=this.item.class;this.#t.addClass(e,n)}if(this.item.wrapper){let n=this.#t.createElement(this.item.wrapper.element);this.addAttribs(this.item.wrapper.attributes,n),this.#t.appendChild(n,i),this.#t.appendChild(e,n)}else this.#t.appendChild(e,i)}addAttribs(e,i){if(e)for(let n in e)n==="style"&&typeof e[n]=="object"?this.setStyle(e[n],i):n==="class"?this.addClass(e[n],i):this.setAttrib(n,e[n],i)}setStyle(e,i){for(let n in e)n&&this.#t.setStyle(i,n,e[n])}addClass(e,i){(Array.isArray(e)?e:e.split(" ")).filter(s=>s.length>0).forEach(s=>{this.#t.addClass(i,s)})}setAttrib(e,i,n){this.#t.setAttribute(n,e,i)}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-title"]],inputs:{item:"item"},decls:0,vars:0,template:function(i,n){},encapsulation:2})}return t})(),Md=(()=>{class t{item;static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-divider"]],inputs:{item:"item"},decls:0,vars:0,template:function(i,n){},encapsulation:2})}return t})(),Sd=(()=>{class t{helper=c(ut);transform(e,i){let n=this.helper.itemType(e),s;return["divider","title"].includes(n)?s=`nav-${n}`:n==="group"?s="":s="nav-item",e.class?`${s} ${e.class}`:s}static \u0275fac=function(i){return new(i||t)};static \u0275pipe=wi({name:"cSidebarNavItemClass",type:t,pure:!0})}return t})(),_r=(()=>{class t{#e=c(Si);#t=c(X);#i=c(O);#n=c(bo);helper=c(ut);constructor(){let e=this.#e;this.navigationEndObservable=e.events.pipe(Ge(i=>i instanceof rn))}item;dropdownMode="path";show;compact;get hostClasses(){return{"nav-group":!0,show:this.open}}sidebarNav;navigationEndObservable;navSubscription;navGroupSubscription;open;navItems=[];display={display:"block"};ngOnInit(){this.navItems=[...this.item.children],this.navSubscription=this.navigationEndObservable.subscribe(e=>{if(this.dropdownMode!=="none"){let i=this.samePath(e.url);this.openGroup(i)}}),this.samePath(this.#e.routerState.snapshot.url)&&this.openGroup(!0),this.navGroupSubscription=this.#n.sidebarNavGroupState$.subscribe(e=>{if(this.dropdownMode==="close"&&e.sidebarNavGroup&&e.sidebarNavGroup!==this){if(e.sidebarNavGroup.item.url.startsWith(this.item.url))return;if(this.samePath(this.#e.routerState.snapshot.url)){this.openGroup(!0);return}this.openGroup(!1)}})}samePath(e){let i=this.item.url?.split("/"),n=e.split("/");return i?.every((s,a)=>s===n[a])}openGroup(e){this.open=e}toggleGroup(e){e.preventDefault(),this.openGroup(!this.open),this.open&&this.#n.toggle({open:this.open,sidebarNavGroup:this})}ngOnDestroy(){this.navSubscription?.unsubscribe()}onAnimationStart(e){this.display={display:"block"},setTimeout(()=>{let i=this.sidebarNav?.nativeElement;e.toState==="open"&&i&&this.#t.setStyle(i,"height",`${i.scrollHeight}px`)})}onAnimationDone(e){setTimeout(()=>{let i=this.sidebarNav?.nativeElement;e.toState==="open"&&i&&this.#t.setStyle(i,"height","auto"),e.toState==="closed"&&setTimeout(()=>{this.display=null})})}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav-group"]],viewQuery:function(i,n){if(i&1&&ns(yo,5,O),i&2){let s;os(s=ss())&&(n.sidebarNav=s.first)}},hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses)},inputs:{item:"item",dropdownMode:"dropdownMode",show:"show",compact:[2,"compact","compact",v]},features:[Ne([ut,bo])],decls:8,vars:13,consts:[["iconTemplate",""],["href","",1,"nav-link","nav-group-toggle",3,"click","cHtmlAttr"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"ngClass"],[3,"compact","dropdownMode","groupItems","navItems","ngStyle"],[1,"nav-icon"],[3,"cIcon","customClasses","name"]],template:function(i,n){if(i&1){let s=Ct();I(0,"a",1),oe("click",function(l){return Je(s),et(n.toggleGroup(l))}),R(1,Uc,1,0,"ng-container",2),it(2),z(3),nt(),A(4,Kc,3,4,"span",3),w(),I(5,"c-sidebar-nav",4),oe("@openClose.done",function(l){return Je(s),et(n.onAnimationDone(l))})("@openClose.start",function(l){return Je(s),et(n.onAnimationStart(l))}),w(),R(6,Yc,3,3,"ng-template",null,0,re)}if(i&2){let s=Q(7);m("cHtmlAttr",n.item.attributes),f(),m("ngTemplateOutlet",s)("ngTemplateOutletContext",Pt(11,wn,n.item)),f(2),se(n.item.name),f(),E(n.helper.hasBadge(n.item)?4:-1),f(),m("@openClose",n.open?"open":"closed")("compact",n.compact)("dropdownMode",n.dropdownMode)("groupItems",!0)("navItems",n.navItems)("ngStyle",n.display)}},dependencies:()=>[mi,Ln,Le,we,yr,xo,yo,ds],styles:[".nav-group-toggle[_ngcontent-%COMP%]{cursor:pointer}.nav-group-items[_ngcontent-%COMP%]{display:block}"],data:{animation:[It("openClose",[Me("open",H({height:"*"})),Me("closed",H({height:"0px"})),Ie("open <=> closed",[q(".15s ease")])])]}})}return t})(),yo=(()=>{class t{sidebar=c(_d,{optional:!0});helper=c(ut);router=c(Si);#e=c(X);#t=c(O);#i=c(Wi);navItems=[];dropdownMode="path";groupItems;compact;get hostClasses(){return{"sidebar-nav":!this.groupItems,"nav-group-items":this.groupItems,compact:this.groupItems&&this.compact}}role="navigation";navItemsArray=[];ngOnChanges(e){this.navItemsArray=Array.isArray(this.navItems)?this.navItems.slice():[]}hideMobile(){this.sidebar&&this.sidebar.sidebarState.mobile&&this.#i.toggle({toggle:"visible",sidebar:this.sidebar})}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-sidebar-nav"]],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role),g(n.hostClasses))},inputs:{navItems:"navItems",dropdownMode:"dropdownMode",groupItems:[2,"groupItems","groupItems",v],compact:[2,"compact","compact",v],role:"role"},features:[_i],ngContentSelectors:D,decls:3,vars:0,consts:[["rla","routerLinkActive"],["routerLinkActive","show",3,"dropdownMode","item","ngClass","routerLinkActiveOptions","compact"],[3,"cHtmlAttr","item","ngClass"],[3,"item","ngClass"],[3,"linkClick","item","ngClass"]],template:function(i,n){i&1&&(x(),Di(0,ol,6,1,null,null,Ii),C(2)),i&2&&Ti(n.navItemsArray)},dependencies:()=>[we,mi,Cr,Dd,Td,Md,_r,Sd,an,Nn],encapsulation:2})}return t})(),E0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275mod=yt({type:t});static \u0275inj=vt({providers:[Wi,ut,bo],imports:[yo,_r,Cr]})}return t})(),k0=(()=>{class t{color=r();label=r("Loading...");size=r();variant=r("border");role=r("status");hostClasses=d(()=>({[`spinner-${this.variant()}`]:!0,[`text-${this.color()}`]:!!this.color(),[`spinner-${this.variant()}-${this.size()}`]:!!this.size()}));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-spinner"]],hostVars:3,hostBindings:function(i,n){i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{color:[1,"color"],label:[1,"label"],size:[1,"size"],variant:[1,"variant"],role:[1,"role"]},ngContentSelectors:D,decls:2,vars:0,consts:[[1,"visually-hidden"]],template:function(i,n){i&1&&(x(),C(0,0,null,sl,2,1))},encapsulation:2})}return t})();var O0=(()=>{class t{color=r(void 0,{alias:"cTableColor"});hostClasses=d(()=>{let e=this.color();return{[`table-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTableColor",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{color:[1,"cTableColor","color"]},exportAs:["cTableColor"]})}return t})(),F0=(()=>{class t{active=r(!1,{alias:"cTableActive",transform:v});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTableActive",""]],hostVars:2,hostBindings:function(i,n){i&2&&_t("table-active",n.active())},inputs:{active:[1,"cTableActive","active"]},exportAs:["cTableActive"]})}return t})(),B0=(()=>{class t{#e=c(X);#t=c(O);align=r();borderColor=r();bordered=r(!1,{transform:v});borderless=r(!1,{transform:v});caption=r();color=r();hover=r(!1,{transform:v});responsive=r();small=r(!1,{transform:v});striped=r(!1,{transform:v});stripedColumns=r(!1,{transform:v});hostClasses=d(()=>{let e=this.align(),i=this.caption(),n=this.borderColor(),s=this.bordered(),a=this.borderless(),l=this.color(),p=this.hover(),b=this.small(),u=this.striped(),N=this.stripedColumns();return{table:!0,[`align-${e}`]:!!e,[`caption-${i}`]:!!i,[`border-${n}`]:!!n,"table-bordered":s,"table-borderless":a,[`table-${l}`]:!!l,"table-hover":p,"table-sm":b,"table-striped":u,"table-striped-columns":N}});#i=T(()=>{let e=this.responsive();if(e){let i=this.#t.nativeElement,n=this.#e.createElement("div"),s=e===!0?"table-responsive":`table-responsive-${e}`;this.#e.addClass(n,s);let a=this.#e.parentNode(i);this.#e.appendChild(a,n),this.#e.insertBefore(a,n,i),this.#e.appendChild(n,i)}});static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["table","cTable",""]],hostAttrs:[1,"table"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{align:[1,"align"],borderColor:[1,"borderColor"],bordered:[1,"bordered"],borderless:[1,"borderless"],caption:[1,"caption"],color:[1,"color"],hover:[1,"hover"],responsive:[1,"responsive"],small:[1,"small"],striped:[1,"striped"],stripedColumns:[1,"stripedColumns"]},exportAs:["cTable"]})}return t})();var Vi=(()=>{class t{activeItem=B(void 0);activeItemKey=B(void 0);id=B(void 0);static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac})}return t})(),Ad=0,P0=(()=>{class t{tabsService=c(Vi);activeItemKey=on();tabsId=`tabs-${Ad++}`;id=r(this.tabsId);#e=T(()=>{this.tabsService.id.set(this.id()),this.tabsService.activeItemKey.set(this.activeItemKey())});#t=T(()=>{this.activeItemKey.set(this.tabsService.activeItemKey())});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-tabs"]],hostAttrs:[1,"tabs"],hostVars:1,hostBindings:function(i,n){i&2&&Xt("id",n.id())},inputs:{activeItemKey:[1,"activeItemKey"],id:[1,"id"]},outputs:{activeItemKey:"activeItemKeyChange"},exportAs:["cTabs"],features:[Ne([Vi])],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),fr=(()=>{class t{#e=c(gt);#t=c(be);#i=c(O);#n=c(Vi);disabledInput=r(!1,{transform:v,alias:"disabled"});#o=B(!1);attrDisabled=d(()=>this.#o()||null);#s=T(()=>{let e=this.disabledInput();Fe(()=>{this.disabled=e})});set disabled(e){this.#o.set(e)}get disabled(){return this.#o()}itemKey=r.required();id=r();ariaControls=r(void 0,{alias:"aria-controls"});isActive=B(!1);hostClasses=d(()=>({"nav-link":!0,active:this.isActive(),disabled:this.#o()}));propId=d(()=>this.id()??`${this.#n.id()}-tab-${this.itemKey()}`);attrAriaControls=d(()=>this.ariaControls()??`${this.#n.id()}-panel-${this.itemKey()}`);#a=T(()=>{let e=this.#o();if(!e){let i=Ft(this.#i.nativeElement,"click"),n=Ft(this.#i.nativeElement,"focusin");jo(n,i).pipe(Ge(s=>!e),ht(s=>{this.#n.activeItemKey.set(Fe(this.itemKey))}),Vo(()=>!e),ye(this.#t)).subscribe()}});focus(e){this.#i.nativeElement.focus()}ngOnInit(){Ko(this.#e,()=>{T(()=>{let e=!this.#o()&&this.#n.activeItemKey()===this.itemKey();this.isActive.set(e)})})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["button","cTab",""]],hostAttrs:["type","button","role","tab"],hostVars:7,hostBindings:function(i,n){i&2&&(Xt("id",n.propId())("tabindex",n.isActive()?0:-1),M("aria-selected",n.isActive())("aria-controls",n.attrAriaControls())("disabled",n.attrDisabled()||null),g(n.hostClasses()))},inputs:{disabledInput:[1,"disabled","disabledInput"],itemKey:[1,"itemKey"],id:[1,"id"],ariaControls:[1,"aria-controls","ariaControls"]},exportAs:["cTab"]})}return t})(),N0=(()=>{class t{#e=c(be);tabsService=c(Vi);layout=r();variant=r();role=r("tablist");hostClasses=d(()=>({nav:!0,[`nav-${this.layout()}`]:this.layout(),[`nav-${this.variant()}`]:this.variant()}));tabs=je(fr);#t;#i=T(()=>{let e=this.tabs();e.length!==0&&(this.#t=new di(e).skipPredicate(i=>i.disabled===!0).withHorizontalOrientation("ltr").withHomeAndEnd().withWrap(),this.#t.change.pipe(ht(i=>{this.tabsService.activeItemKey.set(this.#t.activeItem?.itemKey()),this.tabsService.activeItem.set(this.#t.activeItem)}),ye(this.#e)).subscribe(),Fe(()=>{setTimeout(()=>{let i=e.find(s=>s.isActive())??e.find(s=>!s.disabled),n=e.findIndex(s=>s===i);this.#t?.updateActiveItem(n<0?0:n),this.tabsService.activeItemKey.set(this.#t.activeItem?.itemKey()),this.tabsService.activeItem.set(this.#t.activeItem)})}))});#n=T(()=>{let e=this.tabs().findIndex(i=>Fe(i.isActive)&&Fe(i.itemKey)===this.tabsService.activeItemKey());this.#t?.updateActiveItem(e<0?0:e)});onKeyDown(e){if(["ArrowLeft","ArrowRight"].includes(e.key)){this.#t.onKeydown(e);return}["Tab"].includes(e.key)&&this.#t?.tabOut.next()}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-tabs-list"]],contentQueries:function(i,n,s){i&1&&pe(s,n.tabs,fr,4),i&2&&ee()},hostVars:3,hostBindings:function(i,n){i&1&&oe("keydown",function(a){return n.onKeyDown(a)}),i&2&&(M("role",n.role()),g(n.hostClasses()))},inputs:{layout:[1,"layout"],variant:[1,"variant"],role:[1,"role"]},exportAs:["cTabsList"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),R0=(()=>{class t{static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-tabs-content"]],hostAttrs:[1,"tab-content"],exportAs:["cTabsContent"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2})}return t})(),j0=(()=>{class t{tabsService=c(Vi);ariaLabelledBy=r(void 0,{alias:"aria-labelledby"});id=r();itemKey=r.required();role=r("tabpanel");tabindex=r(0,{transform:Ae});transition=r(!0);visibleChange=Re();show=B(!1);visible=d(()=>{let e=this.tabsService.activeItemKey()===this.itemKey()&&!this.tabsService.activeItem()?.disabled;return this.visibleChange?.emit({itemKey:this.itemKey(),visible:e}),e});propId=d(()=>this.id()??`${this.tabsService.id()}-panel-${this.itemKey()}`);attrAriaLabelledBy=d(()=>this.ariaLabelledBy()??`${this.tabsService.id()}-tab-${this.itemKey()}`);hostClasses=d(()=>({"tab-pane":!0,active:this.show(),fade:this.transition(),show:this.show(),invisible:this.tabsService.activeItem()?.disabled}));onAnimationDone(e){this.show.set(this.visible())}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-tab-panel"]],hostVars:8,hostBindings:function(i,n){i&1&&tn("@fadeInOut.done",function(a){return n.onAnimationDone(a)}),i&2&&(Xt("tabindex",n.visible()?n.tabindex():-1)("id",n.propId()),Zt("@.disabled",!n.transition())("@fadeInOut",n.visible()?"show":"hide"),M("aria-labelledby",n.attrAriaLabelledBy())("role",n.role()),g(n.hostClasses()))},inputs:{ariaLabelledBy:[1,"aria-labelledby","ariaLabelledBy"],id:[1,"id"],itemKey:[1,"itemKey"],role:[1,"role"],tabindex:[1,"tabindex"],transition:[1,"transition"]},outputs:{visibleChange:"visibleChange"},exportAs:["cTabPanel"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},encapsulation:2,data:{animation:[It("fadeInOut",[Me("show",H({opacity:1})),Me("hide",H({opacity:0})),Me("void",H({opacity:1})),Ie("* => *",[qe("@*",[Rn()],{optional:!0}),q("150ms linear")])])]}})}return t})();var wo=(()=>{class t{#e=new Ke({});toasterState$=this.#e.asObservable();setState(e){this.#e.next(S({},e))}static \u0275fac=function(i){return new(i||t)};static \u0275prov=k({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),xn=(()=>{class t{changeDetectorRef=c(Mi);hostElement=c(O);renderer=c(X);toasterService=c(wo);dynamic=r();placementInput=r(void 0,{alias:"placement"});get placement(){return this.placementInput()}autohide=r(!0);color=r("");delay=r(5e3,{transform:Ae});fade=r(!0);visibleInput=r(!1,{transform:v,alias:"visible"});#e=T(()=>{this.visible=this.visibleInput()});set visible(e){let i=e;this.#t!==i&&(this.#t=i,i?this.setTimer():this.clearTimer(),this.visibleChange?.emit(i),this.changeDetectorRef.markForCheck())}get visible(){return this.#t}#t=!1;index=r(0,{transform:Ae});visibleChange=Re();timer=Re();timerId;clockId;clockTimerId;_clock;get clock(){return this._clock}set clock(e){this._clock=e,this.timer?.emit(this._clock),this.changeDetectorRef.markForCheck()}animationDisabled=d(()=>!this.fade());get animateType(){return this.visible?"show":"hide"}hostClasses=d(()=>{let e=this.color();return{toast:!0,show:!0,[`bg-${e}`]:!!e,"border-0":!!e}});ngOnInit(){this.visible&&(this.toasterService.setState({toast:this,show:this.visible,placement:this.placement}),this.clearTimer(),this.setTimer())}ngOnDestroy(){this.clearTimer()}setTimer(){this.clearTimer(),this.autohide()&&this.visible&&(this.timerId=this.delay()>0?setTimeout(()=>this.onClose(),this.delay()):void 0,this.setClock())}clearTimer(){this.clearClock(),clearTimeout(this.timerId),this.timerId=void 0}onClose(){this.clearTimer(),this.toasterService.setState({toast:this,show:!1,placement:this.placement})}setClock(){this.clearClock(),this.clock=0,this.clockId=setInterval(()=>{this.clock+=1,this.changeDetectorRef.markForCheck()},1e3),this.clockTimerId=setTimeout(()=>{this.clearClock()},this.delay())}clearClock(){clearTimeout(this.clockTimerId),clearInterval(this.clockId),this.clockId=void 0}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-toast"]],hostAttrs:[1,"toast","show"],hostVars:4,hostBindings:function(i,n){i&1&&oe("mouseover",function(){return n.clearTimer()})("mouseout",function(){return n.setTimer()}),i&2&&(Zt("@fadeInOut",n.animateType)("@.disabled",n.animationDisabled()),g(n.hostClasses()))},inputs:{dynamic:[1,"dynamic"],placementInput:[1,"placement","placementInput"],autohide:[1,"autohide"],color:[1,"color"],delay:[1,"delay"],fade:[1,"fade"],visibleInput:[1,"visible","visibleInput"],index:[1,"index"]},outputs:{visibleChange:"visibleChange",timer:"timer"},exportAs:["cToast"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block;overflow:hidden}"],data:{animation:[It("fadeInOut",[Me("show",H({opacity:1,height:"*",padding:"*",border:"*",margin:"*"})),Me("hide",H({opacity:0,height:0,padding:0,border:0,margin:0})),Me("void",H({opacity:0,height:0,padding:0,border:0,margin:0})),Ie("show => hide",[q("{{ time }} {{ easing }}")],{params:{time:"300ms",easing:"ease-out"}}),Ie("hide => show",[q("{{ time }} {{ easing }}")],{params:{time:"300ms",easing:"ease-in"}}),Ie("show => void",[q("{{ time }} {{ easing }}")],{params:{time:"300ms",easing:"ease-out"}}),Ie("void => show",[q("{{ time }} {{ easing }}")],{params:{time:"300ms",easing:"ease-in"}})])]}})}return t})(),L0=(()=>{class t{toast=c(xn,{optional:!0});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-toast-body"]],hostAttrs:[1,"toast-body"],exportAs:["cToastBody"],ngContentSelectors:D,decls:1,vars:0,template:function(i,n){i&1&&(x(),C(0))},styles:["[_nghost-%COMP%]{display:block}"]})}return t})(),Ed=(()=>{class t{#e=c(wo);cToastClose=r();toggleOpen(e){e.preventDefault(),this.#e.setState({show:!1,toast:this.cToastClose()})}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cToastClose",""]],hostBindings:function(i,n){i&1&&oe("click",function(a){return n.toggleOpen(a)})},inputs:{cToastClose:[1,"cToastClose"]},exportAs:["cToastClose"]})}return t})(),H0=(()=>{class t{#e=c(xn,{optional:!0});toast=B(this.#e??void 0);closeButton=r(!0);static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-toast-header"]],hostAttrs:[1,"toast-header"],inputs:{closeButton:[1,"closeButton"]},exportAs:["cToastHeader"],ngContentSelectors:D,decls:3,vars:1,consts:[["aria-label","close","cButtonClose","",1,"ms-auto",3,"cToastClose","style"],["aria-label","close","cButtonClose","",1,"ms-auto",3,"cToastClose"]],template:function(i,n){i&1&&(x(),it(0),C(1),A(2,al,1,4,"button",0),nt()),i&2&&(f(2),E(n.closeButton()?2:-1))},dependencies:[Ed,mr],encapsulation:2})}return t})(),mo=(()=>{class t{viewContainerRef=c(tt);static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cToasterHost",""]],exportAs:["cToasterHost"]})}return t})(),Co=function(t){return t.Static="static",t.TopStart="top-start",t.TopCenter="top-center",t.TopEnd="top-end",t.MiddleStart="middle-start",t.MiddleCenter="middle-center",t.MiddleEnd="middle-end",t.BottomStart="bottom-start",t.BottomCenter="bottom-center",t.BottomEnd="bottom-end",t}(Co||{}),$0=(()=>{class t{#e=c(O);#t=c(X);#i=c(wo);#n=c(be);placements=Object.values(Co);toastsDynamic=[];placementInput=r(Co.TopEnd,{alias:"placement"});get placement(){return this.placementInput()}position=r("absolute");toasterHost=ti.required(mo);contentToasts=je(xn,{read:tt});hostClasses=d(()=>{let e=this.placement,i=this.position();return{toaster:!0,"toast-container":!0,[`position-${i}`]:!!i,"top-0":e.includes("top"),"top-50":e.includes("middle"),"bottom-0":e.includes("bottom"),"start-0":e.includes("start"),"start-50":e.includes("center"),"end-0":e.includes("end"),"translate-middle-x":e.includes("center")&&!e.includes("middle"),"translate-middle-y":e.includes("middle")&&!e.includes("center"),"translate-middle":e.includes("middle")&&e.includes("center")}});ngOnInit(){this.stateToasterSubscribe()}addToast(e,i,n){let s=this.toasterHost().viewContainerRef.createComponent(e,n);this.toastsDynamic.push(s);let a=this.toastsDynamic.indexOf(s);for(let[l,p]of Object.entries(i))s.setInput(l,p);return s.setInput("placement",this.placement),s.setInput("dynamic",!0),s.setInput("index",a),s.setInput("visible",!0),s.instance.visibleChange?.emit(!0),s.changeDetectorRef?.detectChanges(),s}removeToast(e){this.toastsDynamic?.forEach(i=>{e.toast?.dynamic()&&i.instance===e.toast&&(i.setInput("visible",!1),i.instance.visibleChange.emit(!1),i.destroy())}),this.contentToasts()?.forEach(i=>{e.toast&&i.element.nativeElement===e.toast.hostElement.nativeElement&&(e.toast.dynamic()||(e.toast.visible=!1))})}stateToasterSubscribe(){this.#i.toasterState$.pipe(ye(this.#n)).subscribe(e=>{e.show===!1&&this.removeToast(e)})}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-toaster"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentToasts,xn,4,tt),i&2&&ee()},viewQuery:function(i,n){i&1&&Jt(n.toasterHost,mo,5),i&2&&ee()},hostAttrs:[1,"toaster","toast-container"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{placementInput:[1,"placement","placementInput"],position:[1,"position"]},exportAs:["cToaster"],ngContentSelectors:D,decls:2,vars:0,consts:[["cToasterHost",""]],template:function(i,n){i&1&&(x(),R(0,cl,0,0,"ng-template",0),C(1,0,["cToasterHost",""]))},dependencies:[mo],encapsulation:2})}return t})();var kd=(()=>{class t{renderer=c(X);content=r("");#e=T(()=>{this.updateView(this.content())});visible=r(!1,{transform:v});id=r();role=r("tooltip");viewContainerRef=ti("tooltipTemplate",{read:tt});textNode;hostClasses=d(()=>({tooltip:!0,fade:!0,show:this.visible(),"bs-tooltip-auto":!0}));ngOnDestroy(){this.clear()}clear(){this.viewContainerRef()?.clear(),this.textNode&&this.renderer.removeChild(this.textNode.parentNode,this.textNode)}updateView(e){if(this.clear(),!!e)if(e instanceof en)this.viewContainerRef()?.createEmbeddedView(e);else{this.textNode=this.renderer.createText(e);let i=this.viewContainerRef()?.element.nativeElement;this.renderer.appendChild(i.parentNode,this.textNode)}}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-tooltip"]],viewQuery:function(i,n){i&1&&Jt(n.viewContainerRef,ll,5,tt),i&2&&ee()},hostAttrs:[1,"tooltip","fade","bs-tooltip-auto"],hostVars:4,hostBindings:function(i,n){i&2&&(M("role",n.role())("id",n.id()),g(n.hostClasses()))},inputs:{content:[1,"content"],visible:[1,"visible"],id:[1,"id"],role:[1,"role"]},decls:5,vars:0,consts:[["tooltipTemplate",""],["data-popper-arrow","",1,"tooltip-arrow"],[1,"tooltip-inner"]],template:function(i,n){i&1&&(it(0),K(1,"div",1),I(2,"div",2),W(3,null,0),w(),nt())},encapsulation:2})}return t})(),V0=(()=>{class t{#e=c(X);#t=c(O);#i=c(tt);#n=c(ui);#o=c(Mi);#s=c(Li);#a=c(be);#r=c(ce);content=r(void 0,{alias:"cTooltip"});#c=T(()=>{this.content()&&this.destroyTooltipElement()});popperOptions=r({},{alias:"cTooltipOptions"});#l=T(()=>{this._popperOptions=S(mt(S({},this._popperOptions),{placement:this.placement()}),this.popperOptions())});popperOptionsComputed=d(()=>S({placement:this.placement()},this._popperOptions));placement=r("top",{alias:"cTooltipPlacement"});reference=r(void 0,{alias:"cTooltipRef"});referenceRef=d(()=>this.reference()?.elementRef??this.#t);trigger=r("hover",{alias:"cTooltipTrigger"});visible=on(!1,{alias:"cTooltipVisible"});#d=T(()=>{this.visible()?this.addTooltipElement():this.removeTooltipElement()});get ariaDescribedBy(){return this.tooltipId?this.tooltipId:null}tooltip;tooltipId;tooltipRef;popperInstance;_popperOptions={modifiers:[{name:"offset",options:{offset:[0,5]}}]};ngAfterViewInit(){this.intersectionServiceSubscribe()}ngOnDestroy(){this.clearListeners(),this.destroyTooltipElement()}ngOnInit(){this.setListeners()}setListeners(){let e={hostElement:this.#t,trigger:this.trigger(),callbackToggle:()=>{this.visible.update(i=>!i)},callbackOff:()=>{this.visible.set(!1)},callbackOn:()=>{this.visible.set(!0)}};this.#n.setListeners(e)}clearListeners(){this.#n.clearListeners()}intersectionServiceSubscribe(){this.#s.createIntersectionObserver(this.referenceRef()),this.#s.intersecting$.pipe(Ge(e=>e.hostElement===this.referenceRef()),Bt(100),Yi(()=>{this.#s.unobserve(this.referenceRef())}),ye(this.#a)).subscribe(e=>{this.visible.set(e.isIntersecting?this.visible():!1)})}getUID(e){let i=e??"random-id";do i=`${e}-${Math.floor(Math.random()*1e6).toString(10)}`;while(this.#r.getElementById(i));return i}createTooltipElement(){this.tooltipRef||(this.tooltipRef=this.#i.createComponent(kd))}destroyTooltipElement(){this.tooltip?.remove(),this.tooltipRef?.destroy(),this.tooltipRef=void 0,this.popperInstance?.destroy(),this.#i?.detach(),this.#i?.clear()}addTooltipElement(){if(!this.content()){this.destroyTooltipElement();return}if(this.tooltipRef||this.createTooltipElement(),this.tooltipRef?.setInput("content",this.content()??""),this.tooltip=this.tooltipRef?.location.nativeElement,this.#e.addClass(this.tooltip,"d-none"),this.#e.addClass(this.tooltip,"fade"),this.popperInstance?.destroy(),this.#i.insert(this.tooltipRef.hostView),this.#e.appendChild(this.#r.body,this.tooltip),this.popperInstance=Ni(this.referenceRef().nativeElement,this.tooltip,S({},this.popperOptionsComputed())),!this.visible()){this.removeTooltipElement();return}setTimeout(()=>{this.tooltipId=this.getUID("tooltip"),this.tooltipRef?.setInput("id",this.tooltipId),this.#e.removeClass(this.tooltip,"d-none"),this.tooltipRef?.setInput("visible",this.visible()),this.popperInstance?.forceUpdate(),this.#o?.markForCheck()},100)}removeTooltipElement(){this.tooltipId="",this.tooltipRef&&(this.tooltipRef.setInput("visible",!1),this.tooltipRef.setInput("id",void 0),this.#o.markForCheck(),setTimeout(()=>{this.#i?.detach()},300))}static \u0275fac=function(i){return new(i||t)};static \u0275dir=_({type:t,selectors:[["","cTooltip",""]],hostVars:1,hostBindings:function(i,n){i&2&&M("aria-describedby",n.ariaDescribedBy)},inputs:{content:[1,"cTooltip","content"],popperOptions:[1,"cTooltipOptions","popperOptions"],placement:[1,"cTooltipPlacement","placement"],reference:[1,"cTooltipRef","reference"],trigger:[1,"cTooltipTrigger","trigger"],visible:[1,"cTooltipVisible","visible"]},outputs:{visible:"cTooltipVisibleChange"},exportAs:["cTooltip"],features:[Ne([ui,Li])]})}return t})();var z0=(()=>{class t extends hi{title=r();value=r();templates={};contentTemplates=je(ze,{descendants:!0});#e=T(()=>{this.contentTemplates().forEach(e=>{this.templates[e.id]=e.templateRef})});hostClasses=d(()=>{let e=this.color();return{card:!0,[`bg-${e}`]:!!e,"text-white":!!e}});get bodyClasses(){return{"pb-0":!0,"d-flex":!0,"justify-content-between":!0,"align-items-start":!0}}static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-widget-stat-a"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostAttrs:[1,"card"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{title:[1,"title"],value:[1,"value"]},exportAs:["cWidgetStatA"],features:[Qe],ngContentSelectors:pl,decls:15,vars:5,consts:[["defaultWidgetTitleTemplate",""],["defaultWidgetValueTemplate",""],["defaultWidgetChartTemplate",""],["defaultWidgetActionTemplate",""],[3,"ngClass"],[1,"fs-4","fw-semibold"],[4,"ngTemplateOutlet"]],template:function(i,n){if(i&1&&(x(dl),it(0),I(1,"c-card-body",4)(2,"div"),A(3,fl,2,1,"div",5),A(4,hl,2,1,"div"),w(),R(5,vl,1,0,"ng-container",6),w(),R(6,gl,1,0,"ng-container",6),nt(),R(7,bl,1,1,"ng-template",null,0,re)(9,yl,1,1,"ng-template",null,1,re)(11,Cl,1,0,"ng-template",null,2,re)(13,_l,1,0,"ng-template",null,3,re)),i&2){let s=Q(12),a=Q(14);f(),m("ngClass",n.bodyClasses),f(2),E(n.value()||n.templates!=null&&n.templates.widgetValueTemplate?3:-1),f(),E(n.title()||n.templates!=null&&n.templates.widgetTitleTemplate?4:-1),f(),m("ngTemplateOutlet",(n.templates==null?null:n.templates.widgetActionTemplate)||a),f(),m("ngTemplateOutlet",(n.templates==null?null:n.templates.widgetChartTemplate)||s)}},dependencies:[vi,we,Le],encapsulation:2})}return t})(),W0=(()=>{class t extends hi{constructor(){super()}title=r();text=r();value=r();inverse=r(!1,{transform:v});hostClasses=d(()=>{let e=this.color(),i=this.textColor();return{card:!0,[`bg-${e}`]:!!e,[`text-${i}`]:!!i,"text-white":this.inverse()}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-widget-stat-b"]],hostAttrs:[1,"card"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{title:[1,"title"],text:[1,"text"],value:[1,"value"],inverse:[1,"inverse"]},exportAs:["cWidgetStatB"],features:[Qe],ngContentSelectors:D,decls:5,vars:3,consts:[[1,"fs-4","fw-semibold"],[3,"ngClass"]],template:function(i,n){i&1&&(x(),I(0,"c-card-body"),A(1,xl,2,1,"div",0),A(2,wl,2,1,"div"),C(3),A(4,Il,2,2,"small",1),w()),i&2&&(f(),E(n.value()?1:-1),f(),E(n.title()?2:-1),f(2),E(n.text()?4:-1))},dependencies:[vi,we],encapsulation:2})}return t})(),U0=(()=>{class t extends hi{constructor(){super()}icon=r();title=r();value=r();inverse=r(!1,{transform:v});templates={};contentTemplates=je(ze,{descendants:!0});#e=T(()=>{this.contentTemplates().forEach(e=>{this.templates[e.id]=e.templateRef})});hostExtendedClass=d(()=>mt(S({},this.hostClasses()),{"text-white":this.inverse()}));titleClasses=d(()=>{let e=this.inverse();return{"text-body-secondary":!e,"text-white":e,"text-opacity-75":e,[`text-${this.textColor()}`]:!!this.textColor()}});valueClasses=d(()=>mt(S({"fs-4":!this.textColor(),"fw-semibold":!0},this.titleClasses()),{"text-opacity-75":!1}));iconClasses=d(()=>S({"mb-4":!this.textColor(),"text-end":!0},this.titleClasses()));static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-widget-stat-c"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostExtendedClass())},inputs:{icon:[1,"icon"],title:[1,"title"],value:[1,"value"],inverse:[1,"inverse"]},exportAs:["cWidgetStatC"],features:[Qe],ngContentSelectors:D,decls:9,vars:4,consts:[["defaultWidgetIconTemplate",""],["defaultWidgetProgressTemplate",""],[3,"ngClass"],[4,"ngTemplateOutlet"]],template:function(i,n){i&1&&(x(),I(0,"c-card-body"),A(1,Tl,2,2,"div",2),A(2,Ml,2,2,"div",2),A(3,Sl,2,2,"div",2),A(4,El,1,1,"ng-container"),w(),R(5,kl,1,1,"ng-template",null,0,re)(7,Ol,1,0,"ng-template",null,1,re)),i&2&&(f(),E(n.icon()||n.templates!=null&&n.templates.widgetIconTemplate?1:-1),f(),E(n.value()?2:-1),f(),E(n.title()?3:-1),f(),E(n.templates!=null&&n.templates.widgetProgressTemplate?4:-1))},dependencies:[vi,we,Le],encapsulation:2})}return t})(),K0=(()=>{class t extends hi{values=r();headerClasses=d(()=>({"position-relative":!0,"d-flex":!0,"justify-content-center":!0,"align-items-center":!0,[`bg-${this.color()}`]:this.color()}));static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-widget-stat-d"]],hostAttrs:[1,"card"],inputs:{values:[1,"values"]},exportAs:["cWidgetStatD"],features:[Qe],ngContentSelectors:D,decls:5,vars:1,consts:[[3,"ngClass"],["cRow","",1,"text-center"],[1,"vr"],[1,"fs-5","fw-semibold"],[1,"text-uppercase","text-body-secondary","small"]],template:function(i,n){i&1&&(x(),I(0,"c-card-header",0),C(1),w(),I(2,"c-card-body",1),Di(3,Bl,6,3,null,null,Ii),w()),i&2&&(m("ngClass",n.headerClasses()),f(3),Ti(n.values()))},dependencies:[ed,vi,md,gr,we],encapsulation:2})}return t})(),G0=(()=>{class t extends hi{constructor(){super()}title=r();value=r();titleClasses=d(()=>{let e=this.textColor();return{"text-body-secondary":!e,small:!0,"text-uppercase":!0,"fw-semibold":!0,[`text-${e}`]:!!e}});static \u0275fac=function(i){return new(i||t)};static \u0275cmp=h({type:t,selectors:[["c-widget-stat-e"]],inputs:{title:[1,"title"],value:[1,"value"]},exportAs:["cWidgetStatE"],features:[Qe],ngContentSelectors:D,decls:4,vars:2,consts:[[1,"text-center"],[3,"ngClass"],[1,"fs-6","fw-semibold","py-3"]],template:function(i,n){i&1&&(x(),I(0,"c-card-body",0),A(1,Pl,2,2,"div",1),A(2,Nl,2,1,"div",2),C(3),w()),i&2&&(f(),E(n.title()?1:-1),f(),E(n.value()?2:-1))},dependencies:[vi,we],encapsulation:2})}return t})(),Q0=(()=>{class t extends hi{footer=r();icon=r();padding=r(!1,{transform:v});title=r();value=r();templates={};contentTemplates=je(ze,{descendants:!0});#e=T(()=>{this.contentTemplates().forEach(e=>{this.templates[e.id]=e.templateRef})});cardBodyClasses=d(()=>({"d-flex":!0,"align-items-center":!0,"p-0":!this.padding()}));iconClasses=d(()=>{let e=this.color(),i=this.padding();return{"me-3":!this.textColor(),"text-white":!0,[`bg-${e}`]:!!e,"p-3":i,"p-4":!i,"rounded-start-1":!i}});titleClasses=d(()=>{let e=this.textColor();return{"text-body-secondary":!e,small:!0,"text-uppercase":!0,"fw-semibold":!0,[`text-${e}`]:!!e}});valueClasses=d(()=>{let e=this.textColor();return{"fs-6":!e,"fw-semibold":!0,[`text-${e}`]:!!e}});static \u0275fac=(()=>{let e;return function(n){return(e||(e=bt(t)))(n||t)}})();static \u0275cmp=h({type:t,selectors:[["c-widget-stat-f"]],contentQueries:function(i,n,s){i&1&&pe(s,n.contentTemplates,ze,5),i&2&&ee()},hostAttrs:[1,"card"],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClasses())},inputs:{footer:[1,"footer"],icon:[1,"icon"],padding:[1,"padding"],title:[1,"title"],value:[1,"value"]},exportAs:["cWidgetStatB"],features:[Qe],decls:14,vars:8,consts:[["defaultWidgetIconTemplate",""],["defaultFooterIconTemplate",""],[3,"ngClass"],[4,"ngTemplateOutlet"]],template:function(i,n){if(i&1&&(it(0),I(1,"c-card-body",2)(2,"div",2),R(3,Rl,1,0,"ng-container",3),w(),I(4,"div")(5,"div",2),z(6),w(),I(7,"div",2),z(8),w()()(),A(9,Ll,2,1,"c-card-footer"),nt(),R(10,Hl,2,1,"ng-template",null,0,re)(12,$l,2,1,"ng-template",null,1,re)),i&2){let s=Q(11);f(),m("ngClass",n.cardBodyClasses()),f(),m("ngClass",n.iconClasses()),f(),m("ngTemplateOutlet",(n.templates==null?null:n.templates.widgetIconTemplate)||s),f(2),m("ngClass",n.valueClasses()),f(),se(n.value()),f(),m("ngClass",n.titleClasses()),f(),se(n.title()),f(),E(n.footer()||n.templates!=null&&n.templates.widgetFooterTemplate?9:-1)}},dependencies:[vi,Jl,we,Le],encapsulation:2})}return t})();export{ye as a,dt as b,fh as c,mn as d,Sr as e,Ln as f,ze as g,Vl as h,ug as i,vo as j,fg as k,mg as l,hg as m,Ql as n,mr as o,vg as p,gg as q,bg as r,yg as s,Cg as t,_g as u,_o as v,xg as w,wg as x,Yl as y,Xl as z,Ig as A,Dg as B,Tg as C,Mg as D,hi as E,vi as F,Jl as G,Sg as H,ed as I,Ag as J,Eg as K,kg as L,Og as M,Fg as N,Bg as O,Pg as P,Ng as Q,Rg as R,jg as S,ar as T,Lg as U,Hg as V,$g as W,yn as X,cr as Y,vr as Z,lr as _,Vg as $,zg as aa,Wg as ba,dr as ca,Ug as da,Kg as ea,Gg as fa,Qg as ga,qg as ha,Yg as ia,Xg as ja,Zg as ka,Jg as la,e0 as ma,fd as na,md as oa,gr as pa,t0 as qa,i0 as ra,n0 as sa,o0 as ta,s0 as ua,r0 as va,a0 as wa,c0 as xa,l0 as ya,d0 as za,p0 as Aa,u0 as Ba,f0 as Ca,m0 as Da,h0 as Ea,v0 as Fa,g0 as Ga,b0 as Ha,y0 as Ia,C0 as Ja,pr as Ka,_0 as La,x0 as Ma,ur as Na,w0 as Oa,I0 as Pa,fo as Qa,yd as Ra,D0 as Sa,_d as Ta,T0 as Ua,xd as Va,M0 as Wa,S0 as Xa,A0 as Ya,yo as Za,E0 as _a,k0 as $a,O0 as ab,F0 as bb,B0 as cb,P0 as db,fr as eb,N0 as fb,R0 as gb,j0 as hb,xn as ib,L0 as jb,Ed as kb,H0 as lb,Co as mb,$0 as nb,V0 as ob,z0 as pb,W0 as qb,U0 as rb,K0 as sb,G0 as tb,Q0 as ub};
