{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,KAAK,EAAqB,mBAAmB,EAAE,MAAM,YAAY,CAAC;AAC3E,OAAO,EAEL,qBAAqB,GAEtB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EACL,sBAAsB,GAEvB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,yBAAyB,EAAE,MAAM,aAAa,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAsB,MAAM,aAAa,CAAC;AAC3D,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AACtD,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AACvF,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EACL,SAAS,EACT,KAAK,EACL,MAAM,EACN,UAAU,EACV,aAAa,EACb,MAAM,EACN,aAAa,EACb,WAAW,GACZ,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAqB,MAAM,oBAAoB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { delay, type DelayOptions, calculateRetryDelay } from \"./delay.js\";\nexport {\n  type AbortOptions,\n  cancelablePromiseRace,\n  type AbortablePromiseBuilder,\n} from \"./aborterUtils.js\";\nexport {\n  createAbortablePromise,\n  type CreateAbortablePromiseOptions,\n} from \"./createAbortablePromise.js\";\nexport { getRandomIntegerInclusive } from \"./random.js\";\nexport { isObject, type UnknownObject } from \"./object.js\";\nexport { isError, getErrorMessage } from \"./error.js\";\nexport { computeSha256Hash, computeSha256Hmac } from \"./sha256.js\";\nexport { isDefined, isObjectWithProperties, objectHasProperty } from \"./typeGuards.js\";\nexport { randomUUID } from \"./uuidUtils.js\";\nexport {\n  isBrowser,\n  isBun,\n  isNode,\n  isNodeLike,\n  isNodeRuntime,\n  isDeno,\n  isReactNative,\n  isWebWorker,\n} from \"./checkEnvironment.js\";\nexport { uint8ArrayToString, stringToUint8Array, type EncodingType } from \"./bytesEncoding.js\";\n"]}