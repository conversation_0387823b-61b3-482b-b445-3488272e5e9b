import{b as W}from"./chunk-3MARWV4R.js";import{E as D,Ea as q,F as P,Fa as A,Ga as O,Ha as F,I as L,Ia as z,Ja as R,Pa as X,n as w,o as k,oa as B,ob as I,qa as V}from"./chunk-Y7QKHPW3.js";import{e as T}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as l,Hb as n,Ib as t,Jb as m,Mb as u,Nb as C,Qb as v,Sb as x,Va as o,ac as r,eb as M,fc as e,ha as E,ia as f,kb as p,tc as y,zc as g}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var j=()=>({strategy:"fixed"});function K(i,d){i&1&&u(0)}function Q(i,d){i&1&&u(0)}function Z(i,d){i&1&&u(0)}function $(i,d){i&1&&u(0)}function ee(i,d){i&1&&u(0)}function te(i,d){i&1&&u(0)}function ne(i,d){i&1&&u(0)}function le(i,d){i&1&&u(0)}function ie(i,d){i&1&&u(0)}function oe(i,d){if(i&1){let a=C();n(0,"button",44),v("click",function(){E(a);let s=x();return f(s.toggleLiveDemo())}),e(1,"Launch demo modal"),t(),n(2,"c-modal",45),v("visibleChange",function(s){E(a);let S=x();return f(S.handleLiveDemoChange(s))}),n(3,"c-modal-header")(4,"h5",30),e(5,"Modal title"),t(),n(6,"button",46),v("click",function(){E(a);let s=x();return f(s.toggleLiveDemo())}),t()(),n(7,"c-modal-body"),e(8,"Woohoo, you're reading this text in a modal!"),t(),n(9,"c-modal-footer")(10,"button",47),v("click",function(){E(a);let s=x();return f(s.toggleLiveDemo())}),e(11," Close "),t(),n(12,"button",33),e(13,"Save changes"),t()()()}if(i&2){let a=x();o(2),l("visible",a.liveDemoVisible)}}function ae(i,d){if(i&1&&(n(0,"button",48),e(1,"Launch static backdrop modal"),t(),n(2,"c-modal",49,10)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body"),e(9,"I will not close if you click outside of me. Don't even try to press escape key."),t(),n(10,"c-modal-footer")(11,"button",51),e(12," Close "),t(),n(13,"button",33),e(14,"Understood"),t()()()),i&2){let a=r(3);l("cModalToggle",a.id),o(2),l("@.disabled",!1),o(5),l("cModalToggle",a.id),o(4),l("cModalToggle",a.id)}}function re(i,d){i&1&&u(0)}function de(i,d){if(i&1&&(n(0,"button",48),e(1,"Scrolling long content"),t(),n(2,"c-modal",52,11)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body"),p(9,re,1,0,"ng-container",35),t(),n(10,"c-modal-footer")(11,"button",51),e(12," Close "),t(),n(13,"button",33),e(14,"Save changes"),t()()()),i&2){let a=r(3);x();let c=r(283);l("cModalToggle",a.id),o(7),l("cModalToggle",a.id),o(2),l("ngTemplateOutlet",c),o(2),l("cModalToggle",a.id)}}function me(i,d){i&1&&u(0)}function ce(i,d){if(i&1&&(n(0,"button",48),e(1,"Scrollable long content"),t(),n(2,"c-modal",53,12)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body"),p(9,me,1,0,"ng-container",35),t(),n(10,"c-modal-footer")(11,"button",51),e(12," Close "),t(),n(13,"button",33),e(14,"Save changes"),t()()()),i&2){let a=r(3);x();let c=r(283);l("cModalToggle",a.id),o(7),l("cModalToggle",a.id),o(2),l("ngTemplateOutlet",c),o(2),l("cModalToggle",a.id)}}function se(i,d){if(i&1&&(n(0,"button",48),e(1,"Centered modal"),t(),n(2,"c-modal",54,13)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body"),e(9," Woohoo, you're reading this text in a modal! "),t(),n(10,"c-modal-footer")(11,"button",51),e(12," Close "),t(),n(13,"button",33),e(14,"Understood"),t()()()),i&2){let a=r(3);l("cModalToggle",a.id),o(7),l("cModalToggle",a.id),o(4),l("cModalToggle",a.id)}}function pe(i,d){if(i&1&&(n(0,"button",48),e(1,"Centered scrollable modal"),t(),n(2,"c-modal",55,14)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body")(9,"p"),e(10," This is some placeholder content to show a vertically centered modal. We've added some extra copy here to show how vertically centering the modal works when combined with scrollable modals. We also use some repeated line breaks to quickly extend the height of the content, thereby triggering the scrolling. When content becomes longer than the predefined max-height of modal, content will be cropped and scrollable within the modal. "),t(),m(11,"br")(12,"br")(13,"br")(14,"br")(15,"br")(16,"br")(17,"br")(18,"br")(19,"br")(20,"br"),n(21,"p"),e(22,"Just like that."),t()(),n(23,"c-modal-footer")(24,"button",51),e(25," Close "),t(),n(26,"button",33),e(27,"Understood"),t()()()),i&2){let a=r(3);l("cModalToggle",a.id),o(2),l("scrollable",!0),o(5),l("cModalToggle",a.id),o(17),l("cModalToggle",a.id)}}function ue(i,d){i&1&&(n(0,"h3",60),e(1," Popover title "),t(),n(2,"div",61),e(3," And here\u2019s some amazing content. It\u2019s very engaging. Right? "),t())}function ge(i,d){if(i&1&&(n(0,"button",48),e(1,"Modal with Popover"),t(),n(2,"c-modal",56,15)(4,"c-modal-header")(5,"h5",30),e(6,"Modal title"),t(),m(7,"button",50),t(),n(8,"c-modal-body")(9,"h5"),e(10,"Popover in a modal"),t(),e(11," This "),n(12,"button",57),e(13," button "),t(),e(14," triggers a popover on click. "),p(15,ue,4,0,"ng-template",null,16,g),m(17,"hr"),n(18,"h5"),e(19,"Tooltips in a modal"),t(),n(20,"p")(21,"a",58),e(22,"This link"),t(),e(23," and "),n(24,"a",59),e(25,"that link"),t(),e(26," have tooltips on hover. "),t()(),n(27,"c-modal-footer")(28,"button",51),e(29," Close "),t(),n(30,"button",33),e(31,"Understood"),t()()()),i&2){let a=r(3),c=r(16);l("cModalToggle",a.id),o(7),l("cModalToggle",a.id),o(5),l("cPopoverTrigger","click")("cPopover",c)("cPopoverOptions",y(7,j)),o(12),l("cTooltipOptions",y(8,j)),o(4),l("cModalToggle",a.id)}}function xe(i,d){if(i&1&&(n(0,"button",48),e(1,"Extra large modal"),t(),n(2,"button",48),e(3,"Large modal"),t(),n(4,"button",48),e(5,"Small modal"),t(),n(6,"c-modal",62,17)(8,"c-modal-header")(9,"h5",30),e(10,"Extra large modal"),t()(),n(11,"c-modal-body"),e(12,"..."),t()(),n(13,"c-modal",63,18)(15,"c-modal-header")(16,"h5",30),e(17,"Large modal"),t()(),n(18,"c-modal-body"),e(19,"..."),t()(),n(20,"c-modal",64,19)(22,"c-modal-header")(23,"h5",30),e(24,"Small modal"),t()(),n(25,"c-modal-body"),e(26,"..."),t()()),i&2){let a=r(7),c=r(14),s=r(21);l("cModalToggle",a.id),o(2),l("cModalToggle",c.id),o(2),l("cModalToggle",s.id)}}function Se(i,d){if(i&1&&(n(0,"button",48),e(1,"Full screen"),t(),n(2,"button",48),e(3,"Full screen below sm"),t(),n(4,"button",48),e(5,"Full screen below md"),t(),n(6,"button",48),e(7,"Full screen below lg"),t(),n(8,"button",48),e(9,"Full screen below xl"),t(),n(10,"button",48),e(11,"Full screen below xxl"),t(),n(12,"c-modal",65,8)(14,"c-modal-header")(15,"h5",30),e(16,"Full screen"),t(),m(17,"button",50),t(),n(18,"c-modal-body"),e(19,"..."),t(),n(20,"c-modal-footer")(21,"button",48),e(22,"Close"),t()()(),n(23,"c-modal",66,20)(25,"c-modal-header")(26,"h5",30),e(27,"Full screen below sm"),t(),m(28,"button",50),t(),n(29,"c-modal-body"),e(30,"..."),t()(),n(31,"c-modal",67,21)(33,"c-modal-header")(34,"h5",30),e(35,"Full screen below md"),t(),m(36,"button",50),t(),n(37,"c-modal-body"),e(38,"..."),t()(),n(39,"c-modal",68,22)(41,"c-modal-header")(42,"h5",30),e(43,"Full screen below lg"),t(),m(44,"button",50),t(),n(45,"c-modal-body"),e(46,"..."),t()(),n(47,"c-modal",69,23)(49,"c-modal-header")(50,"h5",30),e(51,"Full screen below xl"),t(),m(52,"button",50),t(),n(53,"c-modal-body"),e(54,"..."),t()(),n(55,"c-modal",70,24)(57,"c-modal-header")(58,"h5",30),e(59,"Full screen below xxl"),t(),m(60,"button",50),t(),n(61,"c-modal-body"),e(62,"..."),t()()),i&2){let a=r(13),c=r(24),s=r(32),S=r(40),h=r(48),_=r(56);l("cModalToggle",a.id),o(2),l("cModalToggle",c.id),o(2),l("cModalToggle",s.id),o(2),l("cModalToggle",S.id),o(2),l("cModalToggle",h.id),o(2),l("cModalToggle",_.id),o(2),l("fullscreen",!0),o(5),l("cModalToggle",a.id),o(4),l("cModalToggle",a.id),o(7),l("cModalToggle",c.id),o(8),l("cModalToggle",s.id),o(8),l("cModalToggle",S.id),o(8),l("cModalToggle",h.id),o(8),l("cModalToggle",_.id)}}function be(i,d){i&1&&(n(0,"p"),e(1," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(2,"p"),e(3," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(4,"p"),e(5," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t(),n(6,"p"),e(7," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(8,"p"),e(9," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(10,"p"),e(11," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t(),n(12,"p"),e(13," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(14,"p"),e(15," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(16,"p"),e(17," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t(),n(18,"p"),e(19," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(20,"p"),e(21," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(22,"p"),e(23," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t(),n(24,"p"),e(25," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(26,"p"),e(27," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(28,"p"),e(29," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t(),n(30,"p"),e(31," Cras mattis consectetur purus sit amet fermentum. Cras justo odio, dapibus ac facilisis in, egestas eget quam. Morbi leo risus, porta ac consectetur ac, vestibulum at eros. "),t(),n(32,"p"),e(33," Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "),t(),n(34,"p"),e(35," Aenean lacinia bibendum nulla sed consectetur. Praesent commodo cursus magna, vel scelerisque nisl consectetur et. Donec sed odio dui. Donec ullamcorper nulla non metus auctor fringilla. "),t())}var b=class b{constructor(){this.liveDemoVisible=!1}toggleLiveDemo(){this.liveDemoVisible=!this.liveDemoVisible}handleLiveDemoChange(d){this.liveDemoVisible=d}};b.\u0275fac=function(a){return new(a||b)},b.\u0275cmp=M({type:b,selectors:[["app-modals"]],decls:284,vars:11,consts:[["liveDemo",""],["staticBackdrop",""],["scrollingLongContent",""],["scrollableLongContent",""],["verticallyCentered",""],["verticallyCenteredScrollable",""],["withPopover",""],["optionalSizes",""],["fullScreen",""],["longContent",""],["staticBackdropModal",""],["scrollingLongContentModal",""],["scrollableLongContentModal",""],["verticallyCenteredModal",""],["verticallyCenteredScrollableModal",""],["withPopoverModal",""],["popoverHtml",""],["modalXl",""],["modalLg",""],["modalSm",""],["fullScreenSm",""],["fullScreenMd",""],["fullScreenLg",""],["fullScreenXl",""],["fullScreen2Xl",""],["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/modal"],["backdrop","static","id","modalStatic",1,"position-static","d-block","show",3,"keyboard","transition"],["cModalTitle",""],["cButtonClose",""],["cButton","","color","secondary"],["cButton","","color","primary"],["href","components/modal#live-demo"],[4,"ngTemplateOutlet"],["href","components/modal#static-backdrop"],["href","components/modal#scrolling-long-content"],["href","components/modal#vertically-centered"],["href","components/modal#tooltips-and-popovers"],[1,"table"],[1,"text-body-secondary"],["href","components/modal#optional-sizes"],["href","components/modal#fullscreen-modal"],["cButton","",3,"click"],["id","liveDemoModal",3,"visibleChange","visible"],["cButtonClose","",3,"click"],["cButton","","color","secondary",3,"click"],["cButton","",3,"cModalToggle"],["backdrop","static","id","staticBackdropModal"],["cButtonClose","",3,"cModalToggle"],["cButton","","color","secondary",3,"cModalToggle"],["id","scrollingLongContentModal"],["id","scrollableLongContentModal","scrollable",""],["alignment","center","id","verticallyCenteredModal"],["alignment","center","id","verticallyCenteredScrollableModal",3,"scrollable"],["alignment","center","id","withPopoverModal"],["cButton","",3,"cPopoverTrigger","cPopover","cPopoverOptions"],["cTooltip","Tooltip text","href",""],["cTooltip","Tooltip text","cTooltipPlacement","bottom","href","",3,"cTooltipOptions"],[1,"popover-header"],["id","",1,"popover-body"],["id","modalXl","size","xl"],["id","modalLg","size","lg"],["id","modalSm","size","sm"],["id","fullScreen",3,"fullscreen"],["fullscreen","sm","id","fullScreenSm"],["fullscreen","md","id","fullScreenMd"],["fullscreen","lg","id","fullScreenLg"],["fullscreen","xl","id","fullScreenXl"],["fullscreen","xxl","id","fullScreen2Xl"]],template:function(a,c){if(a&1&&(n(0,"c-row")(1,"c-col",25)(2,"c-card",26)(3,"c-card-header")(4,"strong"),e(5,"Angular Modal"),t()(),n(6,"c-card-body")(7,"p",27),e(8,`
          Below is a static modal example (meaning its `),n(9,"code"),e(10,"position"),t(),e(11,` and
          `),n(12,"code"),e(13,"display"),t(),e(14,` have been overridden). Included are the modal header, modal body
          (required for `),n(15,"code"),e(16,"padding"),t(),e(17,`), and modal footer (optional). We ask that you
          include modal headers with dismiss actions whenever possible, or provide another
          explicit dismiss action.
        `),t(),n(18,"app-docs-example",28)(19,"c-modal",29)(20,"c-modal-header")(21,"h5",30),e(22,"Modal title"),t(),m(23,"button",31),t(),n(24,"c-modal-body"),e(25,"Modal body text goes here."),t(),n(26,"c-modal-footer")(27,"button",32),e(28,"Close"),t(),n(29,"button",33),e(30,"Save changes"),t()()()()()()(),n(31,"c-col",25)(32,"c-card",26)(33,"c-card-header"),e(34,`
        `),n(35,"strong"),e(36,"Angular Modal"),t(),e(37," "),n(38,"small"),e(39,"Live demo"),t(),e(40,`
      `),t(),n(41,"c-card-body")(42,"p",27),e(43," Toggle a working modal demo by clicking the button below. It will slide down and fade in from the top of the page. "),t(),n(44,"app-docs-example",34),p(45,K,1,0,"ng-container",35),t()()()(),n(46,"c-col",25)(47,"c-card",26)(48,"c-card-header"),e(49,`
        `),n(50,"strong"),e(51,"Angular Modal"),t(),e(52," "),n(53,"small"),e(54,"Static backdrop"),t(),e(55,`
      `),t(),n(56,"c-card-body")(57,"p",27),e(58," If you don\u2019t provide an "),n(59,"code"),e(60,"(visibleChange)"),t(),e(61," handler to the Modal component, your modal will behave as though the backdrop is static, meaning it will not close when clicking outside it. Click the button below to try it. "),t(),n(62,"app-docs-example",36),p(63,Q,1,0,"ng-container",35),t()()()(),n(64,"c-col",25)(65,"c-card",26)(66,"c-card-header"),e(67,`
        `),n(68,"strong"),e(69,"Angular Modal"),t(),e(70," "),n(71,"small"),e(72,"Scrolling long content"),t(),e(73,`
      `),t(),n(74,"c-card-body")(75,"p",27),e(76," If your modals are too long for the user\u2019s viewport, they scroll the page by itself. "),t(),n(77,"app-docs-example",37),p(78,Z,1,0,"ng-container",35),t(),n(79,"p",27),e(80,`
          You can also create a scrollable modal that allows scroll the modal body by adding `),n(81,"code"),e(82,"scrollable"),t(),e(83,`
          prop.
        `),t(),n(84,"app-docs-example",37),p(85,$,1,0,"ng-container",35),t()()()(),n(86,"c-col",25)(87,"c-card",26)(88,"c-card-header"),e(89,`
        `),n(90,"strong"),e(91,"Angular Modal"),t(),e(92," "),n(93,"small"),e(94,"Vertically centered"),t(),e(95,`
      `),t(),n(96,"c-card-body")(97,"p",27),e(98," Add "),n(99,"code"),e(100,'alignment="center"'),t(),e(101," to "),n(102,"code"),e(103,"<c-modal>"),t(),e(104," to vertically center the modal. "),t(),n(105,"app-docs-example",38),p(106,ee,1,0,"ng-container",35),t(),n(107,"app-docs-example",38),p(108,te,1,0,"ng-container",35),t()()()(),n(109,"c-col",25)(110,"c-card",26)(111,"c-card-header"),e(112,`
        `),n(113,"strong"),e(114,"Angular Modal"),t(),e(115," "),n(116,"small"),e(117,"Tooltips and popovers"),t(),e(118,`
      `),t(),n(119,"c-card-body")(120,"p",27)(121,"code"),e(122,"cTooltip"),t(),e(123," and "),n(124,"code"),e(125,"cPopover"),t(),e(126," can be placed within modals as needed. When modals are closed, any tooltips and popovers within are also automatically dismissed. "),t(),n(127,"app-docs-example",39),p(128,ne,1,0,"ng-container",35),t()()()(),n(129,"c-col",25)(130,"c-card",26)(131,"c-card-header"),e(132,`
        `),n(133,"strong"),e(134,"Angular Modal"),t(),e(135," "),n(136,"small"),e(137,"Optional sizes"),t(),e(138,`
      `),t(),n(139,"c-card-body")(140,"p",27),e(141,`
          Modals have three optional sizes, available via modifier classes to be placed on a
          `),n(142,"code"),e(143,"<c-modal>"),t(),e(144,`. These sizes kick in at certain breakpoints to avoid
          horizontal scrollbars on narrower viewports.
        `),t(),n(145,"table",40)(146,"thead")(147,"tr")(148,"th"),e(149,"Size"),t(),n(150,"th"),e(151,"Property size"),t(),n(152,"th"),e(153,"Modal max-width"),t()()(),n(154,"tbody")(155,"tr")(156,"td"),e(157,"Small"),t(),n(158,"td")(159,"code"),e(160,"'sm'"),t()(),n(161,"td")(162,"code"),e(163,"300px"),t()()(),n(164,"tr")(165,"td"),e(166,"Default"),t(),n(167,"td",41),e(168,"None"),t(),n(169,"td")(170,"code"),e(171,"500px"),t()()(),n(172,"tr")(173,"td"),e(174,"Large"),t(),n(175,"td")(176,"code"),e(177,"'lg'"),t()(),n(178,"td")(179,"code"),e(180,"800px"),t()()(),n(181,"tr")(182,"td"),e(183,"Extra large"),t(),n(184,"td")(185,"code"),e(186,"'xl'"),t()(),n(187,"td")(188,"code"),e(189,"1140px"),t()()()()(),n(190,"app-docs-example",42),p(191,le,1,0,"ng-container",35),t()()()(),n(192,"c-col",25)(193,"c-card",26)(194,"c-card-header"),e(195,`
        `),n(196,"strong"),e(197,"Angular Modal"),t(),e(198," "),n(199,"small"),e(200,"Fullscreen Modal"),t(),e(201,`
      `),t(),n(202,"c-card-body")(203,"p",27),e(204," Another override is the option to pop up a modal that covers the user viewport, available via property "),n(205,"code"),e(206,"fullscreen"),t(),e(207,". "),t(),n(208,"table",40)(209,"thead")(210,"tr")(211,"th"),e(212,"Property fullscreen"),t(),n(213,"th"),e(214,"Availability"),t()()(),n(215,"tbody")(216,"tr")(217,"td")(218,"code"),e(219,"true"),t()(),n(220,"td"),e(221,"Always"),t()(),n(222,"tr")(223,"td")(224,"code"),e(225,"'sm'"),t()(),n(226,"td"),e(227," Below "),n(228,"code"),e(229,"576px"),t()()(),n(230,"tr")(231,"td")(232,"code"),e(233,"'md'"),t()(),n(234,"td"),e(235," Below "),n(236,"code"),e(237,"768px"),t()()(),n(238,"tr")(239,"td")(240,"code"),e(241,"'lg'"),t()(),n(242,"td"),e(243," Below "),n(244,"code"),e(245,"992px"),t()()(),n(246,"tr")(247,"td")(248,"code"),e(249,"'xl'"),t()(),n(250,"td"),e(251," Below "),n(252,"code"),e(253,"1200px"),t()()(),n(254,"tr")(255,"td")(256,"code"),e(257,"'xxl'"),t()(),n(258,"td"),e(259," Below "),n(260,"code"),e(261,"1400px"),t()()()()(),n(262,"app-docs-example",43),p(263,ie,1,0,"ng-container",35),t()()()()(),p(264,oe,14,1,"ng-template",null,0,g)(266,ae,15,4,"ng-template",null,1,g)(268,de,15,4,"ng-template",null,2,g)(270,ce,15,4,"ng-template",null,3,g)(272,se,15,3,"ng-template",null,4,g)(274,pe,28,4,"ng-template",null,5,g)(276,ge,32,9,"ng-template",null,6,g)(278,xe,27,3,"ng-template",null,7,g)(280,Se,63,14,"ng-template",null,8,g)(282,be,36,0,"ng-template",null,9,g)),a&2){let s=r(265),S=r(267),h=r(269),_=r(271),N=r(273),U=r(275),J=r(277),Y=r(279),G=r(281);o(19),l("keyboard",!1)("transition",!1),o(26),l("ngTemplateOutlet",s),o(18),l("ngTemplateOutlet",S),o(15),l("ngTemplateOutlet",h),o(7),l("ngTemplateOutlet",_),o(21),l("ngTemplateOutlet",N),o(2),l("ngTemplateOutlet",U),o(20),l("ngTemplateOutlet",J),o(63),l("ngTemplateOutlet",Y),o(72),l("ngTemplateOutlet",G)}},dependencies:[V,B,D,L,P,W,R,F,z,k,q,O,w,T,A,X,I],encapsulation:2});var H=b;export{H as ModalsComponent};
