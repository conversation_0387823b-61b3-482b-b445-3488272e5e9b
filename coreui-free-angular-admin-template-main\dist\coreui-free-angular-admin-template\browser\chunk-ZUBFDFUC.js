import{b as H}from"./chunk-3MARWV4R.js";import{E as F,F as U,I as $,P as D,Q as B,R as z,S as k,T as G,U as V,f as L,n as w,oa as W,qa as X}from"./chunk-Y7QKHPW3.js";import{t as M}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Eb as u,Fb as E,Gb as c,Hb as t,Ib as i,Jb as o,Qb as x,Sa as p,Sb as P,Va as l,ba as R,eb as I,fc as e,gc as C,hc as b,ja as v,ka as A,qc as S,ra as O,zb as N}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var g=(s,r)=>r.src;function q(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item"),e(2,`
                  `),o(3,"img",31),e(4,`
                `),i(),e(5,`
              `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",n.src,p)}}function Y(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item",32),e(2,`
                  `),o(3,"img",31),e(4,`
                `),i(),e(5,`
              `)),s&2){let n=r.$implicit,a=r.$index,d=P();l(),N("aria-label",(a+1).toString()+" of "+d.slides[0].length),l(2),c("alt",S(n.title))("src",n.src,p)}}function J(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item"),e(2,`
                  `),o(3,"img",31),e(4,`
                `),i(),e(5,`
              `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",n.src,p)}}function K(s,r){if(s&1&&(e(0,`
      `),t(1,"c-carousel-item"),e(2,`
        `),o(3,"img",31),e(4,`
      `),i(),e(5,`
    `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",n.src,p)}}function Q(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item"),e(2,`
                  `),o(3,"img",33),e(4,`
                  `),t(5,"c-carousel-caption",34),e(6,`
                    `),t(7,"h3"),e(8),i(),e(9,`
                    `),t(10,"p"),e(11),i(),e(12,`
                  `),i(),e(13,`
                `),i(),e(14,`
              `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",S(n.src),p),l(5),C(n.title),l(3),C(n.subtitle)}}function Z(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item"),e(2,`
                  `),o(3,"img",35),e(4,`
                  `),t(5,"c-carousel-caption",34),e(6,`
                    `),t(7,"h3"),e(8),i(),e(9,`
                    `),t(10,"p"),e(11),i(),e(12,`
                  `),i(),e(13,`
                `),i(),e(14,`
              `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",n.src,p),l(5),C(n.title),l(3),C(n.subtitle)}}function ee(s,r){if(s&1&&(e(0,`
                `),t(1,"c-carousel-item"),e(2,`
                  `),o(3,"img",31),e(4,`
                  `),t(5,"c-carousel-caption",34),e(6,`
                    `),t(7,"h3"),e(8),i(),e(9,`
                    `),t(10,"p"),e(11),i(),e(12,`
                  `),i(),e(13,`
                `),i(),e(14,`
              `)),s&2){let n=r.$implicit;l(3),c("alt",S(n.title))("src",n.src,p),l(5),C(n.title),l(3),C(n.subtitle)}}var h=class h{constructor(){this.domSanitizer=R(M);this.imageSrc=["assets/images/angular.jpg","assets/images/react.jpg","assets/images/vue.jpg","https://picsum.photos/id/1/800/400","https://picsum.photos/id/1026/800/400","https://picsum.photos/id/1031/800/400"];this.slidesLight=["data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_1607923e7e2%20text%20%7B%20fill%3A%23AAA%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_1607923e7e2%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23F5F5F5%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22285.9296875%22%20y%3D%22217.75625%22%3EFirst%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E","data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_15ba800aa20%20text%20%7B%20fill%3A%23BBB%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_15ba800aa20%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23EEE%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22247.3203125%22%20y%3D%22218.3%22%3ESecond%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E","data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22800%22%20height%3D%22400%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20800%20400%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_15ba800aa21%20text%20%7B%20fill%3A%23999%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A40pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_15ba800aa21%22%3E%3Crect%20width%3D%22800%22%20height%3D%22400%22%20fill%3D%22%23E5E5E5%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%22277%22%20y%3D%22218.3%22%3EThird%20slide%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E"];this.slides=[];this.interval=O(5e3);let r=this.domSanitizer;this.slides[0]=[{id:0,src:r.bypassSecurityTrustUrl(this.imageSrc[0]),title:"First slide",subtitle:"Nulla vitae elit libero, a pharetra augue mollis interdum."},{id:1,src:r.bypassSecurityTrustUrl(this.imageSrc[1]),title:"Second slide",subtitle:"Lorem ipsum dolor sit amet, consectetur adipiscing elit."},{id:2,src:r.bypassSecurityTrustUrl(this.imageSrc[2]),title:"Third slide",subtitle:"Praesent commodo cursus magna, vel scelerisque nisl consectetur."}],this.slides[1]=[{id:0,src:this.imageSrc[3],title:"First slide",subtitle:"Nulla vitae elit libero, a pharetra augue mollis interdum."},{id:1,src:this.imageSrc[4],title:"Second slide",subtitle:"Lorem ipsum dolor sit amet, consectetur adipiscing elit."},{id:2,src:this.imageSrc[5],title:"Third slide",subtitle:"Praesent commodo cursus magna, vel scelerisque nisl consectetur."}],this.slides[2]=[{id:0,src:r.bypassSecurityTrustUrl(this.slidesLight[0]),title:"First slide",subtitle:"Nulla vitae elit libero, a pharetra augue mollis interdum."},{id:1,src:r.bypassSecurityTrustUrl(this.slidesLight[1]),title:"Second slide",subtitle:"Lorem ipsum dolor sit amet, consectetur adipiscing elit."},{id:2,src:r.bypassSecurityTrustUrl(this.slidesLight[2]),title:"Third slide",subtitle:"Praesent commodo cursus magna, vel scelerisque nisl consectetur."}]}onItemChange(r){}toggleInterval(){this.interval.update(r=>r?0:2500)}};h.\u0275fac=function(n){return new(n||h)},h.\u0275cmp=I({type:h,selectors:[["app-carousels"]],decls:326,vars:14,consts:()=>{let r;r=$localize`Previous`;let n;n=$localize`Next`;let a;a=$localize`Previous`;let d;d=$localize`Next`;let m;m=$localize`Previous`;let f;f=$localize`Next`;let y;y=$localize`Previous`;let T;return T=$localize`Next`,[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/carousel"],[3,"itemChange","interval"],["href","components/carousel/#with-controls"],["aria-roledescription","carousel","aria-label","CoreUI Angular Carousel Example",3,"itemChange","interval"],["id","carousel-items"],["caption",r,"direction","prev","aria-controls","carousel-items","aria-label","Previous Slide"],["caption",n,"direction","next","aria-controls","carousel-items","aria-label","Next Slide"],["direction","prev",3,"tabIndex"],["cIcon","","name","cil-chevron-left","size","3xl"],[1,"visually-hidden"],["direction","next",3,"tabIndex"],["cIcon","","name","cil-chevron-right","size","3xl"],["href","components/carousel/#with-indicators"],["direction","prev",3,"itemChange","activeIndex","dark","interval"],["href","https://coreui.io/4.0/utilities/display"],["href","components/carousel/#with-captions"],["transition","slide",3,"itemChange","interval","wrap"],["caption",a,"direction","prev"],["caption",d,"direction","next"],["href","components/carousel/#crossfade"],["transition","crossfade",3,"itemChange","interval"],["caption",m,"direction","prev"],["caption",f,"direction","next"],["cButton","",3,"click"],["href","components/carousel/#dark-variant"],["transition","slide",3,"itemChange","dark","interval"],["caption",y,"direction","prev"],["caption",T,"direction","next"],["loading","lazy",1,"d-block","w-100",3,"src","alt"],["aria-roledescription","slide"],["loading","lazy",1,"d-block","w-100",3,"alt","src"],[1,"d-none","d-md-block"],[1,"d-block","w-100",3,"src","alt"]]},template:function(n,a){n&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",0),e(3,`
    `),t(4,"c-card",1),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Carousel"),i(),e(10," "),t(11,"small"),e(12,"Slide only"),i(),e(13,`
      `),i(),e(14,`
      `),t(15,"c-card-body"),e(16,`
        `),t(17,"p",2),e(18,"Here\u2019s a carousel with slides"),i(),e(19,`
        `),t(20,"app-docs-example",3),e(21,`
          `),t(22,"c-carousel",4),x("itemChange",function(m){return a.onItemChange(m)}),e(23,`
            `),t(24,"c-carousel-inner"),e(25,`
              `),u(26,q,6,3,null,null,g),i(),e(28,`
          `),i(),e(29,`
        `),i(),e(30,`
      `),i(),e(31,`
    `),i(),e(32,`
  `),i(),e(33,`
  `),t(34,"c-col",0),e(35,`
    `),t(36,"c-card",1),e(37,`
      `),t(38,"c-card-header"),e(39,`
        `),t(40,"strong"),e(41,"Angular Carousel"),i(),e(42," "),t(43,"small"),e(44,"with controls"),i(),e(45,`
      `),i(),e(46,`
      `),t(47,"c-card-body"),e(48,`
        `),t(49,"p",2),e(50,`
          Adding in the previous and next controls with `),t(51,"code"),e(52,"c-carousel-controls"),i(),e(53,` component.
        `),i(),e(54,`
        `),t(55,"app-docs-example",5),e(56,`
          `),t(57,"c-carousel",6),x("itemChange",function(m){return a.onItemChange(m)}),e(58,`
            `),t(59,"c-carousel-inner",7),e(60,`
              `),u(61,Y,6,4,null,null,g),i(),e(63,`
            `),o(64,"c-carousel-control",8),e(65,`
            `),o(66,"c-carousel-control",9),e(67,`
          `),i(),e(68,`
        `),i(),e(69,`
      `),i(),e(70,`
    `),i(),e(71,`
  `),i(),e(72,`
  `),t(73,"c-col",0),e(74,`
    `),t(75,"c-card",1),e(76,`
      `),t(77,"c-card-header"),e(78,`
        `),t(79,"strong"),e(80,"Angular Carousel"),i(),e(81," "),t(82,"small"),e(83,"with custom controls"),i(),e(84,`
      `),i(),e(85,`
      `),t(86,"c-card-body"),e(87,`
        `),t(88,"p",2),e(89,`
          Adding in the previous and next controls with custom content of `),t(90,"code"),e(91,"c-carousel-controls"),i(),e(92,` component.
        `),i(),e(93,`
        `),t(94,"app-docs-example",5),e(95,`
          `),t(96,"c-carousel",4),x("itemChange",function(m){return a.onItemChange(m)}),e(97,`
            `),t(98,"c-carousel-inner"),e(99,`
              `),u(100,J,6,3,null,null,g),i(),e(102,`
            `),t(103,"c-carousel-control",10),e(104,`
              `),v(),o(105,"svg",11),e(106,`
              `),A(),t(107,"span",12),e(108,"Previous"),i(),e(109,`
            `),i(),e(110,`
            `),t(111,"c-carousel-control",13),e(112,`
              `),v(),o(113,"svg",14),e(114,`
              `),A(),t(115,"span",12),e(116,"Next"),i(),e(117,`
            `),i(),e(118,`
          `),i(),e(119,`
        `),i(),e(120,`
      `),i(),e(121,`
    `),i(),e(122,`
  `),i(),e(123,`
  `),t(124,"c-col",0),e(125,`
    `),t(126,"c-card",1),e(127,`
      `),t(128,"c-card-header"),e(129,`
        `),t(130,"strong"),e(131,"Angular Carousel"),i(),e(132," "),t(133,"small"),e(134,"with indicators"),i(),e(135,`
      `),i(),e(136,`
      `),t(137,"c-card-body"),e(138,`
        `),t(139,"p",2),e(140,`
          You can attach the indicators to the carousel, lengthwise the controls, too.
        `),i(),e(141,`
        `),t(142,"app-docs-example",15),e(143,`
`),t(144,"c-carousel",16),x("itemChange",function(m){return a.onItemChange(m)}),e(145,`
  `),o(146,"c-carousel-indicators"),e(147,`
`),e(148,`
`),e(149,`
`),e(150,`
`),e(151,`
`),e(152,`
`),e(153,`
`),e(154,`
`),e(155,`
`),e(156,`
`),e(157,`
`),e(158,`
`),e(159,`
`),e(160,`
`),e(161,`
`),e(162,`
`),e(163,`
`),e(164,`
`),e(165,`
`),e(166,`
`),e(167,`
`),e(168,`
`),e(169,`
`),e(170,`
`),e(171,`
  `),t(172,"c-carousel-inner"),e(173,`
    `),u(174,K,6,3,null,null,g),i(),e(176,`
`),i(),e(177,`
        `),i(),e(178,`
      `),i(),e(179,`
    `),i(),e(180,`
  `),i(),e(181,`
  `),t(182,"c-col",0),e(183,`
    `),t(184,"c-card",1),e(185,`
      `),t(186,"c-card-header"),e(187,`
        Carousel with captions, controls and indicators
      `),i(),e(188,`
      `),t(189,"c-card-body"),e(190,`
        `),t(191,"p",2),e(192,`
          You can add captions to slides with the `),t(193,"code"),e(194,"<c-carousel-caption>"),i(),e(195,` element
          within any `),t(196,"code"),e(197,"<c-carousel-item>"),i(),e(198,`. They can be immediately hidden on
          smaller viewports, as shown below, with optional `),t(199,"a",17),e(200,`display
          utilities`),i(),e(201,`.
          We hide them with `),t(202,"code"),e(203,".d-none"),i(),e(204,` and draw them back on medium-sized devices with
          `),t(205,"code"),e(206,".d-md-block"),i(),e(207,`.
        `),i(),e(208,`
        `),t(209,"app-docs-example",18),e(210,`
          `),t(211,"c-carousel",19),x("itemChange",function(m){return a.onItemChange(m)}),e(212,`
            `),o(213,"c-carousel-indicators"),e(214,`
            `),t(215,"c-carousel-inner"),e(216,`
              `),u(217,Q,15,6,null,null,g),i(),e(219,`
            `),o(220,"c-carousel-control",20),e(221,`
            `),o(222,"c-carousel-control",21),e(223,`
          `),i(),e(224,`
        `),i(),e(225,`
      `),i(),e(226,`
    `),i(),e(227,`
  `),i(),e(228,`
  `),t(229,"c-col",0),e(230,`
    `),t(231,"c-card",1),e(232,`
      `),t(233,"c-card-header"),e(234,`
        `),t(235,"strong"),e(236,"Angular Carousel"),i(),e(237," "),t(238,"small"),e(239,"Crossfade"),i(),e(240,`
      `),i(),e(241,`
      `),t(242,"c-card-body"),e(243,`
        `),t(244,"p",2),e(245,`
          Add `),t(246,"code"),e(247,'transition="crossfade"'),i(),e(248,` to your carousel to animate slides
          with a fade transition instead of a slide.
        `),i(),e(249,`
        `),t(250,"app-docs-example",22),e(251,`
          `),t(252,"c-carousel",23),x("itemChange",function(m){return a.onItemChange(m)}),e(253,`
            `),t(254,"c-carousel-inner"),e(255,`
              `),u(256,Z,15,5,null,null,g),i(),e(258,`
            `),o(259,"c-carousel-control",24),e(260,`
            `),o(261,"c-carousel-control",25),e(262,`
          `),i(),e(263,`
          `),o(264,"hr"),e(265,`
          `),t(266,"button",26),x("click",function(){return a.toggleInterval()}),e(267,"Toggle interval"),i(),e(268),i(),e(269,`
      `),i(),e(270,`
    `),i(),e(271,`
  `),i(),e(272,`
  `),t(273,"c-col",0),e(274,`
    `),t(275,"c-card",1),e(276,`
      `),t(277,"c-card-header"),e(278,`
        `),t(279,"strong"),e(280,"Angular Carousel"),i(),e(281," "),t(282,"small"),e(283,"Dark variant"),i(),e(284,`
      `),i(),e(285,`
      `),t(286,"c-card-body"),e(287,`
        `),t(288,"p",2),e(289,`
          Add `),t(290,"code"),e(291,"dark"),i(),e(292," property to the "),t(293,"code"),e(294,"c-carousel"),i(),e(295,` for darker controls,
          indicators, and captions. Controls have been inverted from their default white fill
          with the `),t(296,"code"),e(297,"filter"),i(),e(298,` CSS property. Captions and controls have additional Sass
          variables that customize the `),t(299,"code"),e(300,"color"),i(),e(301," and "),t(302,"code"),e(303,"background-color"),i(),e(304,`.
        `),i(),e(305,`
        `),t(306,"app-docs-example",27),e(307,`
          `),t(308,"c-carousel",28),x("itemChange",function(m){return a.onItemChange(m)}),e(309,`
            `),o(310,"c-carousel-indicators"),e(311,`
            `),t(312,"c-carousel-inner"),e(313,`
              `),u(314,ee,15,5,null,null,g),i(),e(316,`
            `),o(317,"c-carousel-control",29),e(318,`
            `),o(319,"c-carousel-control",30),e(320,`
          `),i(),e(321,`
        `),i(),e(322,`
      `),i(),e(323,`
    `),i(),e(324,`
  `),i(),e(325,`
`),i()),n&2&&(l(22),c("interval",7e3),l(4),E(a.slides[0]),l(31),c("interval",0),l(4),E(a.slides[0]),l(35),c("interval",0),l(4),E(a.slides[0]),l(3),c("tabIndex",0),l(8),c("tabIndex",0),l(33),c("activeIndex",1)("dark",!0)("interval",5e3),l(30),E(a.slides[0]),l(37),c("interval",3e3)("wrap",!1),l(6),E(a.slides[1]),l(35),c("interval",a.interval()),l(4),E(a.slides[0]),l(12),b(`
          `,a.interval(),`
        `),l(40),c("dark",!0)("interval",3e3),l(6),E(a.slides[2]))},dependencies:[X,W,F,$,U,H,D,V,G,z,k,B,w,L],encapsulation:2});var j=h;export{j as CarouselsComponent};
