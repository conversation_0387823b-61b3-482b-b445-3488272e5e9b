var Tt=":";var $e;(function(e){e[e.Little=0]="Little",e[e.Big=1]="Big"})($e||($e={}));function Et(e,n){for(let a=1,t=1;a<e.length;a++,t++)if(n[t]==="\\")t++;else if(e[a]===Tt)return a;throw new Error(`Unterminated $localize metadata block in "${n}".`)}var Ce=function(e,...n){if(Ce.translate){let t=Ce.translate(e,n);e=t[0],n=t[1]}let a=Ye(e[0],e.raw[0]);for(let t=1;t<e.length;t++)a+=n[t-1]+Ye(e[t],e.raw[t]);return a},gt=":";function Ye(e,n){return n.charAt(0)===gt?e.substring(Et(e,n)+1):e}globalThis.$localize=Ce;var ce=globalThis;function te(e){return(ce.__Zone_symbol_prefix||"__zone_symbol__")+e}function pt(){let e=ce.performance;function n(M){e&&e.mark&&e.mark(M)}function a(M,s){e&&e.measure&&e.measure(M,s)}n("Zone");class t{static __symbol__=te;static assertZonePatched(){if(ce.Promise!==w.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let s=t.current;for(;s.parent;)s=s.parent;return s}static get current(){return b.zone}static get currentTask(){return S}static __load_patch(s,i,r=!1){if(w.hasOwnProperty(s)){let E=ce[te("forceDuplicateZoneCheck")]===!0;if(!r&&E)throw Error("Already loaded patch: "+s)}else if(!ce["__Zone_disable_"+s]){let E="Zone:"+s;n(E),w[s]=i(ce,t,C),a(E,E)}}get parent(){return this._parent}get name(){return this._name}_parent;_name;_properties;_zoneDelegate;constructor(s,i){this._parent=s,this._name=i?i.name||"unnamed":"<root>",this._properties=i&&i.properties||{},this._zoneDelegate=new f(this,this._parent&&this._parent._zoneDelegate,i)}get(s){let i=this.getZoneWith(s);if(i)return i._properties[s]}getZoneWith(s){let i=this;for(;i;){if(i._properties.hasOwnProperty(s))return i;i=i._parent}return null}fork(s){if(!s)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,s)}wrap(s,i){if(typeof s!="function")throw new Error("Expecting function got: "+s);let r=this._zoneDelegate.intercept(this,s,i),E=this;return function(){return E.runGuarded(r,this,arguments,i)}}run(s,i,r,E){b={parent:b,zone:this};try{return this._zoneDelegate.invoke(this,s,i,r,E)}finally{b=b.parent}}runGuarded(s,i=null,r,E){b={parent:b,zone:this};try{try{return this._zoneDelegate.invoke(this,s,i,r,E)}catch(B){if(this._zoneDelegate.handleError(this,B))throw B}}finally{b=b.parent}}runTask(s,i,r){if(s.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(s.zone||K).name+"; Execution: "+this.name+")");let E=s,{type:B,data:{isPeriodic:ee=!1,isRefreshable:Z=!1}={}}=s;if(s.state===q&&(B===z||B===p))return;let he=s.state!=A;he&&E._transitionTo(A,d);let _e=S;S=E,b={parent:b,zone:this};try{B==p&&s.data&&!ee&&!Z&&(s.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,E,i,r)}catch(Q){if(this._zoneDelegate.handleError(this,Q))throw Q}}finally{let Q=s.state;if(Q!==q&&Q!==X)if(B==z||ee||Z&&Q===y)he&&E._transitionTo(d,A,y);else{let Te=E._zoneDelegates;this._updateTaskCount(E,-1),he&&E._transitionTo(q,A,q),Z&&(E._zoneDelegates=Te)}b=b.parent,S=_e}}scheduleTask(s){if(s.zone&&s.zone!==this){let r=this;for(;r;){if(r===s.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${s.zone.name}`);r=r.parent}}s._transitionTo(y,q);let i=[];s._zoneDelegates=i,s._zone=this;try{s=this._zoneDelegate.scheduleTask(this,s)}catch(r){throw s._transitionTo(X,y,q),this._zoneDelegate.handleError(this,r),r}return s._zoneDelegates===i&&this._updateTaskCount(s,1),s.state==y&&s._transitionTo(d,y),s}scheduleMicroTask(s,i,r,E){return this.scheduleTask(new g(U,s,i,r,E,void 0))}scheduleMacroTask(s,i,r,E,B){return this.scheduleTask(new g(p,s,i,r,E,B))}scheduleEventTask(s,i,r,E,B){return this.scheduleTask(new g(z,s,i,r,E,B))}cancelTask(s){if(s.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(s.zone||K).name+"; Execution: "+this.name+")");if(!(s.state!==d&&s.state!==A)){s._transitionTo(x,d,A);try{this._zoneDelegate.cancelTask(this,s)}catch(i){throw s._transitionTo(X,x),this._zoneDelegate.handleError(this,i),i}return this._updateTaskCount(s,-1),s._transitionTo(q,x),s.runCount=-1,s}}_updateTaskCount(s,i){let r=s._zoneDelegates;i==-1&&(s._zoneDelegates=null);for(let E=0;E<r.length;E++)r[E]._updateTaskCount(s.type,i)}}let c={name:"",onHasTask:(M,s,i,r)=>M.hasTask(i,r),onScheduleTask:(M,s,i,r)=>M.scheduleTask(i,r),onInvokeTask:(M,s,i,r,E,B)=>M.invokeTask(i,r,E,B),onCancelTask:(M,s,i,r)=>M.cancelTask(i,r)};class f{get zone(){return this._zone}_zone;_taskCounts={microTask:0,macroTask:0,eventTask:0};_parentDelegate;_forkDlgt;_forkZS;_forkCurrZone;_interceptDlgt;_interceptZS;_interceptCurrZone;_invokeDlgt;_invokeZS;_invokeCurrZone;_handleErrorDlgt;_handleErrorZS;_handleErrorCurrZone;_scheduleTaskDlgt;_scheduleTaskZS;_scheduleTaskCurrZone;_invokeTaskDlgt;_invokeTaskZS;_invokeTaskCurrZone;_cancelTaskDlgt;_cancelTaskZS;_cancelTaskCurrZone;_hasTaskDlgt;_hasTaskDlgtOwner;_hasTaskZS;_hasTaskCurrZone;constructor(s,i,r){this._zone=s,this._parentDelegate=i,this._forkZS=r&&(r&&r.onFork?r:i._forkZS),this._forkDlgt=r&&(r.onFork?i:i._forkDlgt),this._forkCurrZone=r&&(r.onFork?this._zone:i._forkCurrZone),this._interceptZS=r&&(r.onIntercept?r:i._interceptZS),this._interceptDlgt=r&&(r.onIntercept?i:i._interceptDlgt),this._interceptCurrZone=r&&(r.onIntercept?this._zone:i._interceptCurrZone),this._invokeZS=r&&(r.onInvoke?r:i._invokeZS),this._invokeDlgt=r&&(r.onInvoke?i:i._invokeDlgt),this._invokeCurrZone=r&&(r.onInvoke?this._zone:i._invokeCurrZone),this._handleErrorZS=r&&(r.onHandleError?r:i._handleErrorZS),this._handleErrorDlgt=r&&(r.onHandleError?i:i._handleErrorDlgt),this._handleErrorCurrZone=r&&(r.onHandleError?this._zone:i._handleErrorCurrZone),this._scheduleTaskZS=r&&(r.onScheduleTask?r:i._scheduleTaskZS),this._scheduleTaskDlgt=r&&(r.onScheduleTask?i:i._scheduleTaskDlgt),this._scheduleTaskCurrZone=r&&(r.onScheduleTask?this._zone:i._scheduleTaskCurrZone),this._invokeTaskZS=r&&(r.onInvokeTask?r:i._invokeTaskZS),this._invokeTaskDlgt=r&&(r.onInvokeTask?i:i._invokeTaskDlgt),this._invokeTaskCurrZone=r&&(r.onInvokeTask?this._zone:i._invokeTaskCurrZone),this._cancelTaskZS=r&&(r.onCancelTask?r:i._cancelTaskZS),this._cancelTaskDlgt=r&&(r.onCancelTask?i:i._cancelTaskDlgt),this._cancelTaskCurrZone=r&&(r.onCancelTask?this._zone:i._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let E=r&&r.onHasTask,B=i&&i._hasTaskZS;(E||B)&&(this._hasTaskZS=E?r:c,this._hasTaskDlgt=i,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,r.onScheduleTask||(this._scheduleTaskZS=c,this._scheduleTaskDlgt=i,this._scheduleTaskCurrZone=this._zone),r.onInvokeTask||(this._invokeTaskZS=c,this._invokeTaskDlgt=i,this._invokeTaskCurrZone=this._zone),r.onCancelTask||(this._cancelTaskZS=c,this._cancelTaskDlgt=i,this._cancelTaskCurrZone=this._zone))}fork(s,i){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,s,i):new t(s,i)}intercept(s,i,r){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,s,i,r):i}invoke(s,i,r,E,B){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,s,i,r,E,B):i.apply(r,E)}handleError(s,i){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,s,i):!0}scheduleTask(s,i){let r=i;if(this._scheduleTaskZS)this._hasTaskZS&&r._zoneDelegates.push(this._hasTaskDlgtOwner),r=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,s,i),r||(r=i);else if(i.scheduleFn)i.scheduleFn(i);else if(i.type==U)V(i);else throw new Error("Task is missing scheduleFn.");return r}invokeTask(s,i,r,E){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,s,i,r,E):i.callback.apply(r,E)}cancelTask(s,i){let r;if(this._cancelTaskZS)r=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,s,i);else{if(!i.cancelFn)throw Error("Task is not cancelable");r=i.cancelFn(i)}return r}hasTask(s,i){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,s,i)}catch(r){this.handleError(s,r)}}_updateTaskCount(s,i){let r=this._taskCounts,E=r[s],B=r[s]=E+i;if(B<0)throw new Error("More tasks executed then were scheduled.");if(E==0||B==0){let ee={microTask:r.microTask>0,macroTask:r.macroTask>0,eventTask:r.eventTask>0,change:s};this.hasTask(this._zone,ee)}}}class g{type;source;invoke;callback;data;scheduleFn;cancelFn;_zone=null;runCount=0;_zoneDelegates=null;_state="notScheduled";constructor(s,i,r,E,B,ee){if(this.type=s,this.source=i,this.data=E,this.scheduleFn=B,this.cancelFn=ee,!r)throw new Error("callback is not defined");this.callback=r;let Z=this;s===z&&E&&E.useG?this.invoke=g.invokeTask:this.invoke=function(){return g.invokeTask.call(ce,Z,this,arguments)}}static invokeTask(s,i,r){s||(s=this),J++;try{return s.runCount++,s.zone.runTask(s,i,r)}finally{J==1&&Y(),J--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(q,y)}_transitionTo(s,i,r){if(this._state===i||this._state===r)this._state=s,s==q&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${s}', expecting state '${i}'${r?" or '"+r+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let T=te("setTimeout"),k=te("Promise"),D=te("then"),_=[],R=!1,L;function H(M){if(L||ce[k]&&(L=ce[k].resolve(0)),L){let s=L[D];s||(s=L.then),s.call(L,M)}else ce[T](M,0)}function V(M){J===0&&_.length===0&&H(Y),M&&_.push(M)}function Y(){if(!R){for(R=!0;_.length;){let M=_;_=[];for(let s=0;s<M.length;s++){let i=M[s];try{i.zone.runTask(i,null,null)}catch(r){C.onUnhandledError(r)}}}C.microtaskDrainDone(),R=!1}}let K={name:"NO ZONE"},q="notScheduled",y="scheduling",d="scheduled",A="running",x="canceling",X="unknown",U="microTask",p="macroTask",z="eventTask",w={},C={symbol:te,currentZoneFrame:()=>b,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:V,showUncaughtError:()=>!t[te("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:H},b={parent:null,zone:new t(null,null)},S=null,J=0;function W(){}return a("Zone","Zone"),t}function mt(){let e=globalThis,n=e[te("forceDuplicateZoneCheck")]===!0;if(e.Zone&&(n||typeof e.Zone.__symbol__!="function"))throw new Error("Zone already loaded.");return e.Zone??=pt(),e.Zone}var ye=Object.getOwnPropertyDescriptor,Ae=Object.defineProperty,je=Object.getPrototypeOf,kt=Object.create,yt=Array.prototype.slice,He="addEventListener",xe="removeEventListener",Ie=te(He),Le=te(xe),ae="true",le="false",ve=te("");function Be(e,n){return Zone.current.wrap(e,n)}function Ue(e,n,a,t,c){return Zone.current.scheduleMacroTask(e,n,a,t,c)}var j=te,Se=typeof window<"u",be=Se?window:void 0,$=Se&&be||globalThis,vt="removeAttribute";function Fe(e,n){for(let a=e.length-1;a>=0;a--)typeof e[a]=="function"&&(e[a]=Be(e[a],n+"_"+a));return e}function bt(e,n){let a=e.constructor.name;for(let t=0;t<n.length;t++){let c=n[t],f=e[c];if(f){let g=ye(e,c);if(!rt(g))continue;e[c]=(T=>{let k=function(){return T.apply(this,Fe(arguments,a+"."+c))};return fe(k,T),k})(f)}}}function rt(e){return e?e.writable===!1?!1:!(typeof e.get=="function"&&typeof e.set>"u"):!0}var ot=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,we=!("nw"in $)&&typeof $.process<"u"&&$.process.toString()==="[object process]",Ge=!we&&!ot&&!!(Se&&be.HTMLElement),st=typeof $.process<"u"&&$.process.toString()==="[object process]"&&!ot&&!!(Se&&be.HTMLElement),De={},Rt=j("enable_beforeunload"),Ke=function(e){if(e=e||$.event,!e)return;let n=De[e.type];n||(n=De[e.type]=j("ON_PROPERTY"+e.type));let a=this||e.target||$,t=a[n],c;if(Ge&&a===be&&e.type==="error"){let f=e;c=t&&t.call(this,f.message,f.filename,f.lineno,f.colno,f.error),c===!0&&e.preventDefault()}else c=t&&t.apply(this,arguments),e.type==="beforeunload"&&$[Rt]&&typeof c=="string"?e.returnValue=c:c!=null&&!c&&e.preventDefault();return c};function Je(e,n,a){let t=ye(e,n);if(!t&&a&&ye(a,n)&&(t={enumerable:!0,configurable:!0}),!t||!t.configurable)return;let c=j("on"+n+"patched");if(e.hasOwnProperty(c)&&e[c])return;delete t.writable,delete t.value;let f=t.get,g=t.set,T=n.slice(2),k=De[T];k||(k=De[T]=j("ON_PROPERTY"+T)),t.set=function(D){let _=this;if(!_&&e===$&&(_=$),!_)return;typeof _[k]=="function"&&_.removeEventListener(T,Ke),g?.call(_,null),_[k]=D,typeof D=="function"&&_.addEventListener(T,Ke,!1)},t.get=function(){let D=this;if(!D&&e===$&&(D=$),!D)return null;let _=D[k];if(_)return _;if(f){let R=f.call(this);if(R)return t.set.call(this,R),typeof D[vt]=="function"&&D.removeAttribute(n),R}return null},Ae(e,n,t),e[c]=!0}function it(e,n,a){if(n)for(let t=0;t<n.length;t++)Je(e,"on"+n[t],a);else{let t=[];for(let c in e)c.slice(0,2)=="on"&&t.push(c);for(let c=0;c<t.length;c++)Je(e,t[c],a)}}var oe=j("originalInstance");function ke(e){let n=$[e];if(!n)return;$[j(e)]=n,$[e]=function(){let c=Fe(arguments,e);switch(c.length){case 0:this[oe]=new n;break;case 1:this[oe]=new n(c[0]);break;case 2:this[oe]=new n(c[0],c[1]);break;case 3:this[oe]=new n(c[0],c[1],c[2]);break;case 4:this[oe]=new n(c[0],c[1],c[2],c[3]);break;default:throw new Error("Arg list too long.")}},fe($[e],n);let a=new n(function(){}),t;for(t in a)e==="XMLHttpRequest"&&t==="responseBlob"||function(c){typeof a[c]=="function"?$[e].prototype[c]=function(){return this[oe][c].apply(this[oe],arguments)}:Ae($[e].prototype,c,{set:function(f){typeof f=="function"?(this[oe][c]=Be(f,e+"."+c),fe(this[oe][c],f)):this[oe][c]=f},get:function(){return this[oe][c]}})}(t);for(t in n)t!=="prototype"&&n.hasOwnProperty(t)&&($[e][t]=n[t])}function ue(e,n,a){let t=e;for(;t&&!t.hasOwnProperty(n);)t=je(t);!t&&e[n]&&(t=e);let c=j(n),f=null;if(t&&(!(f=t[c])||!t.hasOwnProperty(c))){f=t[c]=t[n];let g=t&&ye(t,n);if(rt(g)){let T=a(f,c,n);t[n]=function(){return T(this,arguments)},fe(t[n],f)}}return f}function Ct(e,n,a){let t=null;function c(f){let g=f.data;return g.args[g.cbIdx]=function(){f.invoke.apply(this,arguments)},t.apply(g.target,g.args),f}t=ue(e,n,f=>function(g,T){let k=a(g,T);return k.cbIdx>=0&&typeof T[k.cbIdx]=="function"?Ue(k.name,T[k.cbIdx],k,c):f.apply(g,T)})}function fe(e,n){e[j("OriginalDelegate")]=n}var Qe=!1,Me=!1;function Pt(){if(Qe)return Me;Qe=!0;try{let e=be.navigator.userAgent;(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1||e.indexOf("Edge/")!==-1)&&(Me=!0)}catch{}return Me}function et(e){return typeof e=="function"}function tt(e){return typeof e=="number"}var Dt={useG:!0},ne={},ct={},at=new RegExp("^"+ve+"(\\w+)(true|false)$"),lt=j("propagationStopped");function ut(e,n){let a=(n?n(e):e)+le,t=(n?n(e):e)+ae,c=ve+a,f=ve+t;ne[e]={},ne[e][le]=c,ne[e][ae]=f}function St(e,n,a,t){let c=t&&t.add||He,f=t&&t.rm||xe,g=t&&t.listeners||"eventListeners",T=t&&t.rmAll||"removeAllListeners",k=j(c),D="."+c+":",_="prependListener",R="."+_+":",L=function(y,d,A){if(y.isRemoved)return;let x=y.callback;typeof x=="object"&&x.handleEvent&&(y.callback=p=>x.handleEvent(p),y.originalDelegate=x);let X;try{y.invoke(y,d,[A])}catch(p){X=p}let U=y.options;if(U&&typeof U=="object"&&U.once){let p=y.originalDelegate?y.originalDelegate:y.callback;d[f].call(d,A.type,p,U)}return X};function H(y,d,A){if(d=d||e.event,!d)return;let x=y||d.target||e,X=x[ne[d.type][A?ae:le]];if(X){let U=[];if(X.length===1){let p=L(X[0],x,d);p&&U.push(p)}else{let p=X.slice();for(let z=0;z<p.length&&!(d&&d[lt]===!0);z++){let w=L(p[z],x,d);w&&U.push(w)}}if(U.length===1)throw U[0];for(let p=0;p<U.length;p++){let z=U[p];n.nativeScheduleMicroTask(()=>{throw z})}}}let V=function(y){return H(this,y,!1)},Y=function(y){return H(this,y,!0)};function K(y,d){if(!y)return!1;let A=!0;d&&d.useG!==void 0&&(A=d.useG);let x=d&&d.vh,X=!0;d&&d.chkDup!==void 0&&(X=d.chkDup);let U=!1;d&&d.rt!==void 0&&(U=d.rt);let p=y;for(;p&&!p.hasOwnProperty(c);)p=je(p);if(!p&&y[c]&&(p=y),!p||p[k])return!1;let z=d&&d.eventNameToString,w={},C=p[k]=p[c],b=p[j(f)]=p[f],S=p[j(g)]=p[g],J=p[j(T)]=p[T],W;d&&d.prepend&&(W=p[j(d.prepend)]=p[d.prepend]);function M(o,u){return u?typeof o=="boolean"?{capture:o,passive:!0}:o?typeof o=="object"&&o.passive!==!1?{...o,passive:!0}:o:{passive:!0}:o}let s=function(o){if(!w.isExisting)return C.call(w.target,w.eventName,w.capture?Y:V,w.options)},i=function(o){if(!o.isRemoved){let u=ne[o.eventName],v;u&&(v=u[o.capture?ae:le]);let P=v&&o.target[v];if(P){for(let m=0;m<P.length;m++)if(P[m]===o){P.splice(m,1),o.isRemoved=!0,o.removeAbortListener&&(o.removeAbortListener(),o.removeAbortListener=null),P.length===0&&(o.allRemoved=!0,o.target[v]=null);break}}}if(o.allRemoved)return b.call(o.target,o.eventName,o.capture?Y:V,o.options)},r=function(o){return C.call(w.target,w.eventName,o.invoke,w.options)},E=function(o){return W.call(w.target,w.eventName,o.invoke,w.options)},B=function(o){return b.call(o.target,o.eventName,o.invoke,o.options)},ee=A?s:r,Z=A?i:B,he=function(o,u){let v=typeof u;return v==="function"&&o.callback===u||v==="object"&&o.originalDelegate===u},_e=d?.diff||he,Q=Zone[j("UNPATCHED_EVENTS")],Te=e[j("PASSIVE_EVENTS")];function h(o){if(typeof o=="object"&&o!==null){let u={...o};return o.signal&&(u.signal=o.signal),u}return o}let l=function(o,u,v,P,m=!1,O=!1){return function(){let N=this||e,I=arguments[0];d&&d.transferEventName&&(I=d.transferEventName(I));let F=arguments[1];if(!F)return o.apply(this,arguments);if(we&&I==="uncaughtException")return o.apply(this,arguments);let G=!1;if(typeof F!="function"){if(!F.handleEvent)return o.apply(this,arguments);G=!0}if(x&&!x(o,F,N,arguments))return;let de=!!Te&&Te.indexOf(I)!==-1,se=h(M(arguments[2],de)),Ee=se?.signal;if(Ee?.aborted)return;if(Q){for(let ie=0;ie<Q.length;ie++)if(I===Q[ie])return de?o.call(N,I,F,se):o.apply(this,arguments)}let Oe=se?typeof se=="boolean"?!0:se.capture:!1,Ve=se&&typeof se=="object"?se.once:!1,_t=Zone.current,Ne=ne[I];Ne||(ut(I,z),Ne=ne[I]);let ze=Ne[Oe?ae:le],ge=N[ze],We=!1;if(ge){if(We=!0,X){for(let ie=0;ie<ge.length;ie++)if(_e(ge[ie],F))return}}else ge=N[ze]=[];let Re,qe=N.constructor.name,Xe=ct[qe];Xe&&(Re=Xe[I]),Re||(Re=qe+u+(z?z(I):I)),w.options=se,Ve&&(w.options.once=!1),w.target=N,w.capture=Oe,w.eventName=I,w.isExisting=We;let me=A?Dt:void 0;me&&(me.taskData=w),Ee&&(w.options.signal=void 0);let re=_t.scheduleEventTask(Re,F,me,v,P);if(Ee){w.options.signal=Ee;let ie=()=>re.zone.cancelTask(re);o.call(Ee,"abort",ie,{once:!0}),re.removeAbortListener=()=>Ee.removeEventListener("abort",ie)}if(w.target=null,me&&(me.taskData=null),Ve&&(w.options.once=!0),typeof re.options!="boolean"&&(re.options=se),re.target=N,re.capture=Oe,re.eventName=I,G&&(re.originalDelegate=F),O?ge.unshift(re):ge.push(re),m)return N}};return p[c]=l(C,D,ee,Z,U),W&&(p[_]=l(W,R,E,Z,U,!0)),p[f]=function(){let o=this||e,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=arguments[2],P=v?typeof v=="boolean"?!0:v.capture:!1,m=arguments[1];if(!m)return b.apply(this,arguments);if(x&&!x(b,m,o,arguments))return;let O=ne[u],N;O&&(N=O[P?ae:le]);let I=N&&o[N];if(I)for(let F=0;F<I.length;F++){let G=I[F];if(_e(G,m)){if(I.splice(F,1),G.isRemoved=!0,I.length===0&&(G.allRemoved=!0,o[N]=null,!P&&typeof u=="string")){let de=ve+"ON_PROPERTY"+u;o[de]=null}return G.zone.cancelTask(G),U?o:void 0}}return b.apply(this,arguments)},p[g]=function(){let o=this||e,u=arguments[0];d&&d.transferEventName&&(u=d.transferEventName(u));let v=[],P=ft(o,z?z(u):u);for(let m=0;m<P.length;m++){let O=P[m],N=O.originalDelegate?O.originalDelegate:O.callback;v.push(N)}return v},p[T]=function(){let o=this||e,u=arguments[0];if(u){d&&d.transferEventName&&(u=d.transferEventName(u));let v=ne[u];if(v){let P=v[le],m=v[ae],O=o[P],N=o[m];if(O){let I=O.slice();for(let F=0;F<I.length;F++){let G=I[F],de=G.originalDelegate?G.originalDelegate:G.callback;this[f].call(this,u,de,G.options)}}if(N){let I=N.slice();for(let F=0;F<I.length;F++){let G=I[F],de=G.originalDelegate?G.originalDelegate:G.callback;this[f].call(this,u,de,G.options)}}}}else{let v=Object.keys(o);for(let P=0;P<v.length;P++){let m=v[P],O=at.exec(m),N=O&&O[1];N&&N!=="removeListener"&&this[T].call(this,N)}this[T].call(this,"removeListener")}if(U)return this},fe(p[c],C),fe(p[f],b),J&&fe(p[T],J),S&&fe(p[g],S),!0}let q=[];for(let y=0;y<a.length;y++)q[y]=K(a[y],t);return q}function ft(e,n){if(!n){let f=[];for(let g in e){let T=at.exec(g),k=T&&T[1];if(k&&(!n||k===n)){let D=e[g];if(D)for(let _=0;_<D.length;_++)f.push(D[_])}}return f}let a=ne[n];a||(ut(n),a=ne[n]);let t=e[a[le]],c=e[a[ae]];return t?c?t.concat(c):t.slice():c?c.slice():[]}function wt(e,n){let a=e.Event;a&&a.prototype&&n.patchMethod(a.prototype,"stopImmediatePropagation",t=>function(c,f){c[lt]=!0,t&&t.apply(c,f)})}function Ot(e,n){n.patchMethod(e,"queueMicrotask",a=>function(t,c){Zone.current.scheduleMicroTask("queueMicrotask",c[0])})}var Pe=j("zoneTask");function pe(e,n,a,t){let c=null,f=null;n+=t,a+=t;let g={};function T(D){let _=D.data;_.args[0]=function(){return D.invoke.apply(this,arguments)};let R=c.apply(e,_.args);return tt(R)?_.handleId=R:(_.handle=R,_.isRefreshable=et(R.refresh)),D}function k(D){let{handle:_,handleId:R}=D.data;return f.call(e,_??R)}c=ue(e,n,D=>function(_,R){if(et(R[0])){let L={isRefreshable:!1,isPeriodic:t==="Interval",delay:t==="Timeout"||t==="Interval"?R[1]||0:void 0,args:R},H=R[0];R[0]=function(){try{return H.apply(this,arguments)}finally{let{handle:A,handleId:x,isPeriodic:X,isRefreshable:U}=L;!X&&!U&&(x?delete g[x]:A&&(A[Pe]=null))}};let V=Ue(n,R[0],L,T,k);if(!V)return V;let{handleId:Y,handle:K,isRefreshable:q,isPeriodic:y}=V.data;if(Y)g[Y]=V;else if(K&&(K[Pe]=V,q&&!y)){let d=K.refresh;K.refresh=function(){let{zone:A,state:x}=V;return x==="notScheduled"?(V._state="scheduled",A._updateTaskCount(V,1)):x==="running"&&(V._state="scheduling"),d.call(this)}}return K??Y??V}else return D.apply(e,R)}),f=ue(e,a,D=>function(_,R){let L=R[0],H;tt(L)?(H=g[L],delete g[L]):(H=L?.[Pe],H?L[Pe]=null:H=L),H?.type?H.cancelFn&&H.zone.cancelTask(H):D.apply(e,R)})}function Nt(e,n){let{isBrowser:a,isMix:t}=n.getGlobalObjects();if(!a&&!t||!e.customElements||!("customElements"in e))return;let c=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];n.patchCallbacks(n,e.customElements,"customElements","define",c)}function It(e,n){if(Zone[n.symbol("patchEventTarget")])return;let{eventNames:a,zoneSymbolEventNames:t,TRUE_STR:c,FALSE_STR:f,ZONE_SYMBOL_PREFIX:g}=n.getGlobalObjects();for(let k=0;k<a.length;k++){let D=a[k],_=D+f,R=D+c,L=g+_,H=g+R;t[D]={},t[D][f]=L,t[D][c]=H}let T=e.EventTarget;if(!(!T||!T.prototype))return n.patchEventTarget(e,n,[T&&T.prototype]),!0}function Lt(e,n){n.patchEventPrototype(e,n)}function ht(e,n,a){if(!a||a.length===0)return n;let t=a.filter(f=>f.target===e);if(t.length===0)return n;let c=t[0].ignoreProperties;return n.filter(f=>c.indexOf(f)===-1)}function nt(e,n,a,t){if(!e)return;let c=ht(e,n,a);it(e,c,t)}function Ze(e){return Object.getOwnPropertyNames(e).filter(n=>n.startsWith("on")&&n.length>2).map(n=>n.substring(2))}function Mt(e,n){if(we&&!st||Zone[e.symbol("patchEvents")])return;let a=n.__Zone_ignore_on_properties,t=[];if(Ge){let c=window;t=t.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let f=[];nt(c,Ze(c),a&&a.concat(f),je(c))}t=t.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let c=0;c<t.length;c++){let f=n[t[c]];f?.prototype&&nt(f.prototype,Ze(f.prototype),a)}}function Zt(e){e.__load_patch("legacy",n=>{let a=n[e.__symbol__("legacyPatch")];a&&a()}),e.__load_patch("timers",n=>{let a="set",t="clear";pe(n,a,t,"Timeout"),pe(n,a,t,"Interval"),pe(n,a,t,"Immediate")}),e.__load_patch("requestAnimationFrame",n=>{pe(n,"request","cancel","AnimationFrame"),pe(n,"mozRequest","mozCancel","AnimationFrame"),pe(n,"webkitRequest","webkitCancel","AnimationFrame")}),e.__load_patch("blocking",(n,a)=>{let t=["alert","prompt","confirm"];for(let c=0;c<t.length;c++){let f=t[c];ue(n,f,(g,T,k)=>function(D,_){return a.current.run(g,n,_,k)})}}),e.__load_patch("EventTarget",(n,a,t)=>{Lt(n,t),It(n,t);let c=n.XMLHttpRequestEventTarget;c&&c.prototype&&t.patchEventTarget(n,t,[c.prototype])}),e.__load_patch("MutationObserver",(n,a,t)=>{ke("MutationObserver"),ke("WebKitMutationObserver")}),e.__load_patch("IntersectionObserver",(n,a,t)=>{ke("IntersectionObserver")}),e.__load_patch("FileReader",(n,a,t)=>{ke("FileReader")}),e.__load_patch("on_property",(n,a,t)=>{Mt(t,n)}),e.__load_patch("customElements",(n,a,t)=>{Nt(n,t)}),e.__load_patch("XHR",(n,a)=>{D(n);let t=j("xhrTask"),c=j("xhrSync"),f=j("xhrListener"),g=j("xhrScheduled"),T=j("xhrURL"),k=j("xhrErrorBeforeScheduled");function D(_){let R=_.XMLHttpRequest;if(!R)return;let L=R.prototype;function H(C){return C[t]}let V=L[Ie],Y=L[Le];if(!V){let C=_.XMLHttpRequestEventTarget;if(C){let b=C.prototype;V=b[Ie],Y=b[Le]}}let K="readystatechange",q="scheduled";function y(C){let b=C.data,S=b.target;S[g]=!1,S[k]=!1;let J=S[f];V||(V=S[Ie],Y=S[Le]),J&&Y.call(S,K,J);let W=S[f]=()=>{if(S.readyState===S.DONE)if(!b.aborted&&S[g]&&C.state===q){let s=S[a.__symbol__("loadfalse")];if(S.status!==0&&s&&s.length>0){let i=C.invoke;C.invoke=function(){let r=S[a.__symbol__("loadfalse")];for(let E=0;E<r.length;E++)r[E]===C&&r.splice(E,1);!b.aborted&&C.state===q&&i.call(C)},s.push(C)}else C.invoke()}else!b.aborted&&S[g]===!1&&(S[k]=!0)};return V.call(S,K,W),S[t]||(S[t]=C),z.apply(S,b.args),S[g]=!0,C}function d(){}function A(C){let b=C.data;return b.aborted=!0,w.apply(b.target,b.args)}let x=ue(L,"open",()=>function(C,b){return C[c]=b[2]==!1,C[T]=b[1],x.apply(C,b)}),X="XMLHttpRequest.send",U=j("fetchTaskAborting"),p=j("fetchTaskScheduling"),z=ue(L,"send",()=>function(C,b){if(a.current[p]===!0||C[c])return z.apply(C,b);{let S={target:C,url:C[T],isPeriodic:!1,args:b,aborted:!1},J=Ue(X,d,S,y,A);C&&C[k]===!0&&!S.aborted&&J.state===q&&J.invoke()}}),w=ue(L,"abort",()=>function(C,b){let S=H(C);if(S&&typeof S.type=="string"){if(S.cancelFn==null||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(a.current[U]===!0)return w.apply(C,b)})}}),e.__load_patch("geolocation",n=>{n.navigator&&n.navigator.geolocation&&bt(n.navigator.geolocation,["getCurrentPosition","watchPosition"])}),e.__load_patch("PromiseRejectionEvent",(n,a)=>{function t(c){return function(f){ft(n,c).forEach(T=>{let k=n.PromiseRejectionEvent;if(k){let D=new k(c,{promise:f.promise,reason:f.rejection});T.invoke(D)}})}}n.PromiseRejectionEvent&&(a[j("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),a[j("rejectionHandledHandler")]=t("rejectionhandled"))}),e.__load_patch("queueMicrotask",(n,a,t)=>{Ot(n,t)})}function At(e){e.__load_patch("ZoneAwarePromise",(n,a,t)=>{let c=Object.getOwnPropertyDescriptor,f=Object.defineProperty;function g(h){if(h&&h.toString===Object.prototype.toString){let l=h.constructor&&h.constructor.name;return(l||"")+": "+JSON.stringify(h)}return h?h.toString():Object.prototype.toString.call(h)}let T=t.symbol,k=[],D=n[T("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,_=T("Promise"),R=T("then"),L="__creationTrace__";t.onUnhandledError=h=>{if(t.showUncaughtError()){let l=h&&h.rejection;l?console.error("Unhandled Promise rejection:",l instanceof Error?l.message:l,"; Zone:",h.zone.name,"; Task:",h.task&&h.task.source,"; Value:",l,l instanceof Error?l.stack:void 0):console.error(h)}},t.microtaskDrainDone=()=>{for(;k.length;){let h=k.shift();try{h.zone.runGuarded(()=>{throw h.throwOriginal?h.rejection:h})}catch(l){V(l)}}};let H=T("unhandledPromiseRejectionHandler");function V(h){t.onUnhandledError(h);try{let l=a[H];typeof l=="function"&&l.call(this,h)}catch{}}function Y(h){return h&&typeof h.then=="function"}function K(h){return h}function q(h){return Z.reject(h)}let y=T("state"),d=T("value"),A=T("finally"),x=T("parentPromiseValue"),X=T("parentPromiseState"),U="Promise.then",p=null,z=!0,w=!1,C=0;function b(h,l){return o=>{try{M(h,l,o)}catch(u){M(h,!1,u)}}}let S=function(){let h=!1;return function(o){return function(){h||(h=!0,o.apply(null,arguments))}}},J="Promise resolved with itself",W=T("currentTaskTrace");function M(h,l,o){let u=S();if(h===o)throw new TypeError(J);if(h[y]===p){let v=null;try{(typeof o=="object"||typeof o=="function")&&(v=o&&o.then)}catch(P){return u(()=>{M(h,!1,P)})(),h}if(l!==w&&o instanceof Z&&o.hasOwnProperty(y)&&o.hasOwnProperty(d)&&o[y]!==p)i(o),M(h,o[y],o[d]);else if(l!==w&&typeof v=="function")try{v.call(o,u(b(h,l)),u(b(h,!1)))}catch(P){u(()=>{M(h,!1,P)})()}else{h[y]=l;let P=h[d];if(h[d]=o,h[A]===A&&l===z&&(h[y]=h[X],h[d]=h[x]),l===w&&o instanceof Error){let m=a.currentTask&&a.currentTask.data&&a.currentTask.data[L];m&&f(o,W,{configurable:!0,enumerable:!1,writable:!0,value:m})}for(let m=0;m<P.length;)r(h,P[m++],P[m++],P[m++],P[m++]);if(P.length==0&&l==w){h[y]=C;let m=o;try{throw new Error("Uncaught (in promise): "+g(o)+(o&&o.stack?`
`+o.stack:""))}catch(O){m=O}D&&(m.throwOriginal=!0),m.rejection=o,m.promise=h,m.zone=a.current,m.task=a.currentTask,k.push(m),t.scheduleMicroTask()}}}return h}let s=T("rejectionHandledHandler");function i(h){if(h[y]===C){try{let l=a[s];l&&typeof l=="function"&&l.call(this,{rejection:h[d],promise:h})}catch{}h[y]=w;for(let l=0;l<k.length;l++)h===k[l].promise&&k.splice(l,1)}}function r(h,l,o,u,v){i(h);let P=h[y],m=P?typeof u=="function"?u:K:typeof v=="function"?v:q;l.scheduleMicroTask(U,()=>{try{let O=h[d],N=!!o&&A===o[A];N&&(o[x]=O,o[X]=P);let I=l.run(m,void 0,N&&m!==q&&m!==K?[]:[O]);M(o,!0,I)}catch(O){M(o,!1,O)}},o)}let E="function ZoneAwarePromise() { [native code] }",B=function(){},ee=n.AggregateError;class Z{static toString(){return E}static resolve(l){return l instanceof Z?l:M(new this(null),z,l)}static reject(l){return M(new this(null),w,l)}static withResolvers(){let l={};return l.promise=new Z((o,u)=>{l.resolve=o,l.reject=u}),l}static any(l){if(!l||typeof l[Symbol.iterator]!="function")return Promise.reject(new ee([],"All promises were rejected"));let o=[],u=0;try{for(let m of l)u++,o.push(Z.resolve(m))}catch{return Promise.reject(new ee([],"All promises were rejected"))}if(u===0)return Promise.reject(new ee([],"All promises were rejected"));let v=!1,P=[];return new Z((m,O)=>{for(let N=0;N<o.length;N++)o[N].then(I=>{v||(v=!0,m(I))},I=>{P.push(I),u--,u===0&&(v=!0,O(new ee(P,"All promises were rejected")))})})}static race(l){let o,u,v=new this((O,N)=>{o=O,u=N});function P(O){o(O)}function m(O){u(O)}for(let O of l)Y(O)||(O=this.resolve(O)),O.then(P,m);return v}static all(l){return Z.allWithCallback(l)}static allSettled(l){return(this&&this.prototype instanceof Z?this:Z).allWithCallback(l,{thenCallback:u=>({status:"fulfilled",value:u}),errorCallback:u=>({status:"rejected",reason:u})})}static allWithCallback(l,o){let u,v,P=new this((I,F)=>{u=I,v=F}),m=2,O=0,N=[];for(let I of l){Y(I)||(I=this.resolve(I));let F=O;try{I.then(G=>{N[F]=o?o.thenCallback(G):G,m--,m===0&&u(N)},G=>{o?(N[F]=o.errorCallback(G),m--,m===0&&u(N)):v(G)})}catch(G){v(G)}m++,O++}return m-=2,m===0&&u(N),P}constructor(l){let o=this;if(!(o instanceof Z))throw new Error("Must be an instanceof Promise.");o[y]=p,o[d]=[];try{let u=S();l&&l(u(b(o,z)),u(b(o,w)))}catch(u){M(o,!1,u)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return Z}then(l,o){let u=this.constructor?.[Symbol.species];(!u||typeof u!="function")&&(u=this.constructor||Z);let v=new u(B),P=a.current;return this[y]==p?this[d].push(P,v,l,o):r(this,P,v,l,o),v}catch(l){return this.then(null,l)}finally(l){let o=this.constructor?.[Symbol.species];(!o||typeof o!="function")&&(o=Z);let u=new o(B);u[A]=A;let v=a.current;return this[y]==p?this[d].push(v,u,l,l):r(this,v,u,l,l),u}}Z.resolve=Z.resolve,Z.reject=Z.reject,Z.race=Z.race,Z.all=Z.all;let he=n[_]=n.Promise;n.Promise=Z;let _e=T("thenPatched");function Q(h){let l=h.prototype,o=c(l,"then");if(o&&(o.writable===!1||!o.configurable))return;let u=l.then;l[R]=u,h.prototype.then=function(v,P){return new Z((O,N)=>{u.call(this,O,N)}).then(v,P)},h[_e]=!0}t.patchThen=Q;function Te(h){return function(l,o){let u=h.apply(l,o);if(u instanceof Z)return u;let v=u.constructor;return v[_e]||Q(v),u}}return he&&(Q(he),ue(n,"fetch",h=>Te(h))),Promise[a.__symbol__("uncaughtPromiseErrors")]=k,Z})}function jt(e){e.__load_patch("toString",n=>{let a=Function.prototype.toString,t=j("OriginalDelegate"),c=j("Promise"),f=j("Error"),g=function(){if(typeof this=="function"){let _=this[t];if(_)return typeof _=="function"?a.call(_):Object.prototype.toString.call(_);if(this===Promise){let R=n[c];if(R)return a.call(R)}if(this===Error){let R=n[f];if(R)return a.call(R)}}return a.call(this)};g[t]=a,Function.prototype.toString=g;let T=Object.prototype.toString,k="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?k:T.call(this)}})}function Ht(e,n,a,t,c){let f=Zone.__symbol__(t);if(n[f])return;let g=n[f]=n[t];n[t]=function(T,k,D){return k&&k.prototype&&c.forEach(function(_){let R=`${a}.${t}::`+_,L=k.prototype;try{if(L.hasOwnProperty(_)){let H=e.ObjectGetOwnPropertyDescriptor(L,_);H&&H.value?(H.value=e.wrapWithCurrentZone(H.value,R),e._redefineProperty(k.prototype,_,H)):L[_]&&(L[_]=e.wrapWithCurrentZone(L[_],R))}else L[_]&&(L[_]=e.wrapWithCurrentZone(L[_],R))}catch{}}),g.call(n,T,k,D)},e.attachOriginToPatched(n[t],g)}function xt(e){e.__load_patch("util",(n,a,t)=>{let c=Ze(n);t.patchOnProperties=it,t.patchMethod=ue,t.bindArguments=Fe,t.patchMacroTask=Ct;let f=a.__symbol__("BLACK_LISTED_EVENTS"),g=a.__symbol__("UNPATCHED_EVENTS");n[g]&&(n[f]=n[g]),n[f]&&(a[f]=a[g]=n[f]),t.patchEventPrototype=wt,t.patchEventTarget=St,t.isIEOrEdge=Pt,t.ObjectDefineProperty=Ae,t.ObjectGetOwnPropertyDescriptor=ye,t.ObjectCreate=kt,t.ArraySlice=yt,t.patchClass=ke,t.wrapWithCurrentZone=Be,t.filterProperties=ht,t.attachOriginToPatched=fe,t._redefineProperty=Object.defineProperty,t.patchCallbacks=Ht,t.getGlobalObjects=()=>({globalSources:ct,zoneSymbolEventNames:ne,eventNames:c,isBrowser:Ge,isMix:st,isNode:we,TRUE_STR:ae,FALSE_STR:le,ZONE_SYMBOL_PREFIX:ve,ADD_EVENT_LISTENER_STR:He,REMOVE_EVENT_LISTENER_STR:xe})})}function Bt(e){At(e),jt(e),xt(e)}var dt=mt();Bt(dt);Zt(dt);
