import{D as W,f as Q,xa as X,ya as Y,za as Z}from"./chunk-Y7QKHPW3.js";import{e as B,y as K}from"./chunk-VLR5A2CC.js";import{Ab as z,Bb as P,Cc as y,Dc as G,Ec as J,Gb as s,Hb as r,Hc as i,Ib as a,Jb as v,Mb as A,Nc as V,Sa as p,Sb as L,Tb as b,Ub as x,Va as o,ac as F,ba as U,eb as m,fc as c,gc as $,ja as k,jc as O,ka as D,kb as I,nc as R,oc as T,pc as j,qc as H,zc as q}from"./chunk-J5YWIVYY.js";import{e as M,f as C}from"./chunk-L3UST63Y.js";var u={name:"coreui-free-angular-admin-template",version:"5.5.1",copyright:"Copyright 2024 creativeLabs \u0141<PERSON><PERSON>",license:"MIT",author:"The CoreUI Team (https://github.com/orgs/coreui/people) and contributors",homepage:"https://coreui.io/angular",config:{theme:"default",coreui_library_short_version:"5.5",coreui_library_docs_url:"https://coreui.io/angular/docs/"},scripts:{ng:"ng",start:"ng serve -o",build:"ng build",watch:"ng build --watch --configuration development",test:"ng test"},private:!0,dependencies:{"@angular/animations":"^20.0.3","@angular/cdk":"^20.0.3","@angular/common":"^20.0.3","@angular/compiler":"^20.0.3","@angular/core":"^20.0.3","@angular/forms":"^20.0.3","@angular/language-service":"^20.0.3","@angular/localize":"^20.0.3","@angular/platform-browser":"^20.0.3","@angular/platform-browser-dynamic":"^20.0.3","@angular/router":"^20.0.3","@coreui/angular":"~5.5.1","@coreui/angular-chartjs":"~5.5.1","@coreui/chartjs":"~4.1.0","@coreui/coreui":"~5.4.0","@coreui/icons":"^3.0.1","@coreui/icons-angular":"~5.5.1","@coreui/utils":"^2.0.2","chart.js":"^4.5.0","lodash-es":"^4.17.21","md5-typescript":"^1.0.5","ngx-scrollbar":"^13.0.3",rxjs:"~7.8.2",tslib:"^2.8.1","zone.js":"~0.15.1"},devDependencies:{"@angular/build":"^20.0.2","@angular/cli":"^20.0.2","@angular/compiler-cli":"^20.0.3","@types/jasmine":"^5.1.8","@types/lodash-es":"^4.17.12","@types/node":"^22.15.32","jasmine-core":"^5.8.0",karma:"^6.4.4","karma-chrome-launcher":"^3.2.0","karma-coverage":"^2.2.1","karma-jasmine":"^5.1.0","karma-jasmine-html-reporter":"^2.1.0",typescript:"~5.8.3"},engines:{node:"^20.19.0 || ^22.12.0 || ^24.0.0",npm:">= 9"},bugs:{url:"https://github.com/coreui/coreui-free-angular-admin-template/issues"},repository:{type:"git",url:"git+https://github.com/coreui/coreui-free-angular-admin-template.git"}};var oe=["*"];function re(l,n){l&1&&A(0)}function ae(l,n){if(l&1&&(r(0,"p"),c(1),a()),l&2){L();let e=j(0),t=j(1);o(),O(" An Angular ",e," component",t?"s":""," ",t?"have":"has"," been created as a native Angular version of Bootstrap ",e,". ",e," ",t?"are":"is"," delivered with some new features, variants, and unique design that matches CoreUI Design System requirements. ")}}function ie(l,n){if(l&1&&(R(0)(1),z(2,ae,2,6,"p"),x(3),v(4,"br"),c(5," For more information please visit our official "),r(6,"a",3),c(7,"documentation of CoreUI Components Library for Angular."),a()),l&2){let e=L(),t=T(e.name());o(),T(e.plural()),o(),P(t?2:-1),o(4),s("href",H(e.href()),p)}}var d=class d{constructor(){this.name=i("");this.hrefInput=i("https://coreui.io/angular/docs/",{alias:"href"});this.plural=y(()=>this.name()?.slice(-1)==="s");this.href=y(()=>{let n=u?.config?.coreui_library_short_version,e=u?.config?.coreui_library_docs_url??"https://coreui.io/angular/",h=this.hrefInput();return`${e}${h}`})}};d.\u0275fac=function(e){return new(e||d)},d.\u0275cmp=m({type:d,selectors:[["app-docs-callout"]],inputs:{name:[1,"name"],hrefInput:[1,"href","hrefInput"]},ngContentSelectors:oe,decls:4,vars:1,consts:[["defaultTpl",""],["color","info",1,"bg-white:dark:bg-transparent"],[4,"ngTemplateOutlet"],["target","_blank",3,"href"]],template:function(e,t){if(e&1&&(b(),r(0,"c-callout",1),I(1,re,1,0,"ng-container",2),a(),I(2,ie,8,5,"ng-template",null,0,q)),e&2){let h=F(3);o(),s("ngTemplateOutlet",h)}},dependencies:[W,B],encapsulation:2});var w=d;var ce=["*"],_,f=class f{constructor(){C(this,_,U(V));this.hrefInput=i("https://coreui.io/angular/docs/",{alias:"href"});this.fragment=i();this.href=y(()=>{let n=u?.config?.coreui_library_short_version,e=u?.config?.coreui_library_docs_url??"https://coreui.io/angular/",h=this.hrefInput();return M(this,_).markForCheck(),`${e}${h}`})}};_=new WeakMap,f.\u0275fac=function(e){return new(e||f)},f.\u0275cmp=m({type:f,selectors:[["app-docs-example"]],hostAttrs:[1,"example"],inputs:{hrefInput:[1,"href","hrefInput"],fragment:[1,"fragment"]},ngContentSelectors:ce,decls:12,vars:3,consts:[["variant","underline-border",1,"border-bottom","w-100"],["cNavLink","",3,"active","fragment","routerLink"],["cIcon","","name","cilMediaPlay",1,"me-2"],["cNavLink","","target","_blank",3,"href"],["cIcon","","name","cilCode",1,"me-2"],[1,"tab-content","rounded-bottom"],[1,"tab-pane","active","show","p-3","preview","fade"]],template:function(e,t){e&1&&(b(),r(0,"c-nav",0)(1,"c-nav-item")(2,"a",1),k(),v(3,"svg",2),c(4," Preview "),a()(),D(),r(5,"c-nav-item")(6,"a",3),k(),v(7,"svg",4),c(8," Code "),a()()(),D(),r(9,"div",5)(10,"div",6),x(11),a()()),e&2&&(o(2),s("active",!0)("fragment",t.fragment()),o(4),s("href",t.href(),p))},dependencies:[Z,Y,X,K,Q],styles:["[_nghost-%COMP%]{display:block}"],changeDetection:0});var N=f;var E,g=class g{constructor(){this.hrefInput=i("https://coreui.io/angular/docs/",{alias:"href"});this.href=J(this.hrefInput);this.name=i();this.text=i();C(this,E,G(()=>{let n=this.name();this.href.update(e=>n?`https://coreui.io/angular/docs/components/${n}`:e)}))}};E=new WeakMap,g.\u0275fac=function(e){return new(e||g)},g.\u0275cmp=m({type:g,selectors:[["app-docs-link"]],hostAttrs:[1,"float-end"],inputs:{hrefInput:[1,"href","hrefInput"],name:[1,"name"],text:[1,"text"]},decls:4,vars:2,consts:[[1,"float-end"],["rel","noreferrer noopener","target","_blank",1,"card-header-action",3,"href"],[1,"text-body-secondary"]],template:function(e,t){e&1&&(r(0,"div",0)(1,"a",1)(2,"small",2),c(3),a()()()),e&2&&(o(),s("href",t.href(),p),o(2),$(t.text()??"docs"))},encapsulation:2});var S=g;export{w as a,N as b,S as c};
