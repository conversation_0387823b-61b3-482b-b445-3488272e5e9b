{"_from": "@firebase/auth-interop-types@0.1.6", "_id": "@firebase/auth-interop-types@0.1.6", "_inBundle": false, "_integrity": "sha512-etIi92fW3CctsmR9e3sYM3Uqnoq861M0Id9mdOPF6PWIg38BXL5k4upCNBggGUpLIS0H1grMOvy/wn1xymwe2g==", "_location": "/@firebase/auth-interop-types", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@firebase/auth-interop-types@0.1.6", "name": "@firebase/auth-interop-types", "escapedName": "@firebase%2fauth-interop-types", "scope": "@firebase", "rawSpec": "0.1.6", "saveSpec": null, "fetchSpec": "0.1.6"}, "_requiredBy": ["/@firebase/database"], "_resolved": "https://registry.npmjs.org/@firebase/auth-interop-types/-/auth-interop-types-0.1.6.tgz", "_shasum": "5ce13fc1c527ad36f1bb1322c4492680a6cf4964", "_spec": "@firebase/auth-interop-types@0.1.6", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic\\node_modules\\@firebase\\database", "author": {"name": "Firebase", "email": "<EMAIL>", "url": "https://firebase.google.com/"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "bundleDependencies": false, "deprecated": false, "description": "@firebase/auth interop Types", "devDependencies": {"typescript": "4.2.2"}, "files": ["index.d.ts"], "homepage": "https://github.com/firebase/firebase-js-sdk#readme", "license": "Apache-2.0", "name": "@firebase/auth-interop-types", "peerDependencies": {"@firebase/util": "1.x", "@firebase/app-types": "0.x"}, "repository": {"directory": "packages/auth-types", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "scripts": {"test": "tsc", "test:ci": "node ../../scripts/run_tests_in_ci.js"}, "version": "0.1.6"}