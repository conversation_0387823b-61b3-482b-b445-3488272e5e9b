import{b}from"./chunk-3MARWV4R.js";import{E as x,F as u,I as g,Pa as E,n as d,oa as P,qa as f}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Gb as i,Hb as t,Ib as n,Va as o,ac as c,eb as v,fc as e,kb as p,zc as s}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";function T(l,m){l&1&&(e(0,`
              `),t(1,"h3",12),e(2,`
                Popover Title
              `),n(),e(3,`
              `),t(4,"div",13),e(5,`
                And here\u2019s some amazing content. It\u2019s very engaging. `),t(6,"span",14),e(7,"Right?"),n(),e(8,`
              `),n(),e(9,`
            `))}function B(l,m){l&1&&(e(0,`
              `),t(1,"div",13),e(2,`
                Vivamus sagittis lacus vel augue laoreet rutrum faucibus. Top!
              `),n(),e(3,`
            `))}var r=class r{constructor(){this.visible=!0}ngOnInit(){setTimeout(()=>{this.visible=!this.visible},3e3)}};r.\u0275fac=function(a){return new(a||r)},r.\u0275cmp=v({type:r,selectors:[["app-popovers"]],decls:68,vars:8,consts:[["popoverHtml",""],["tooltipHtml",""],["xs","12"],[1,"mb-4"],["href","components/popover"],["cPopoverPlacement","right","cButton","","color","danger","size","lg",1,"m-1",3,"cPopover","cPopoverTrigger","cPopoverVisible"],[1,"text-body-secondary","small"],["href","components/popover#four-directions"],["cButton","","cPopoverPlacement","top","color","secondary",1,"me-1",3,"cPopoverTrigger","cPopover"],["cButton","","cPopover","Vivamus sagittis lacus vel augue laoreet rutrum faucibus. Right!","cPopoverPlacement","right","color","secondary",1,"me-1",3,"cPopoverTrigger"],["cButton","","cPopover","Vivamus sagittis lacus vel augue laoreet rutrum faucibus. Bottom!","cPopoverPlacement","bottom","color","secondary",1,"me-1",3,"cPopoverTrigger"],["cButton","","cPopover","Vivamus sagittis lacus vel augue laoreet rutrum faucibus. Left!","cPopoverPlacement","left","color","secondary",1,"me-1",3,"cPopoverTrigger"],[1,"popover-header"],[1,"popover-body"],[1,"text-danger"]],template:function(a,h){if(a&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",2),e(3,`
    `),t(4,"c-card",3),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Popover"),n(),e(10," "),t(11,"small"),e(12,"Basic example"),n(),e(13,`
      `),n(),e(14,`
      `),t(15,"c-card-body"),e(16,`
        `),t(17,"app-docs-example",4),e(18,`
          `),t(19,"button",5),e(20,`
            Click to toggle popover
            `),p(21,T,10,0,"ng-template",null,0,s),e(23,`
          `),n(),e(24,`
        `),n(),e(25,`
      `),n(),e(26,`
    `),n(),e(27,`
  `),n(),e(28,`
  `),t(29,"c-col",2),e(30,`
    `),t(31,"c-card",3),e(32,`
      `),t(33,"c-card-header"),e(34,`
        `),t(35,"strong"),e(36,"Angular Popover"),n(),e(37," "),t(38,"small"),e(39,"Four directions"),n(),e(40,`
      `),n(),e(41,`
      `),t(42,"c-card-body"),e(43,`
        `),t(44,"p",6),e(45,`
          Four options are available: top, right, bottom, and left aligned.
        `),n(),e(46,`
        `),t(47,"app-docs-example",7),e(48,`
          `),t(49,"button",8),e(50,`Popover on top
            `),p(51,B,4,0,"ng-template",null,1,s),e(53,`
          `),n(),e(54,`
          `),t(55,"button",9),e(56,`Popover on right
          `),n(),e(57,`
          `),t(58,"button",10),e(59,`Popover on bottom
          `),n(),e(60,`
          `),t(61,"button",11),e(62,`Popover on left
          `),n(),e(63,`
        `),n(),e(64,`
      `),n(),e(65,`
    `),n(),e(66,`
  `),n(),e(67,`
`),n()),a&2){let y=c(22),C=c(52);o(19),i("cPopover",y)("cPopoverTrigger","click")("cPopoverVisible",h.visible),o(30),i("cPopoverTrigger","hover")("cPopover",C),o(6),i("cPopoverTrigger","hover"),o(3),i("cPopoverTrigger","hover"),o(3),i("cPopoverTrigger","hover")}},dependencies:[f,P,x,g,u,b,d,E],encapsulation:2});var S=r;export{S as PopoversComponent};
