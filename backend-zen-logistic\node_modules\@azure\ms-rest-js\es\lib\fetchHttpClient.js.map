{"version": 3, "file": "fetchHttpClient.js", "sourceRoot": "", "sources": ["../../lib/fetchHttpClient.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,+FAA+F;;AAE/F,OAAO,eAAe,MAAM,kBAAkB,CAAC;AAC/C,OAAO,QAAQ,MAAM,WAAW,CAAC;AAKjC,OAAO,EAAE,WAAW,EAAmB,MAAM,eAAe,CAAC;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAY,SAAS,EAAE,MAAM,QAAQ,CAAC;AAsB7C;IAAA;IAwMA,CAAC;IAvMO,qCAAW,GAAjB,UAAkB,WAA4B;;;;;;wBAC5C,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;4BACnD,MAAM,IAAI,KAAK,CACb,qFAAqF,CACtF,CAAC;yBACH;wBAEK,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;wBAE9C,IAAI,WAAW,CAAC,WAAW,EAAE;4BAC3B,IAAI,WAAW,CAAC,WAAW,CAAC,OAAO,EAAE;gCACnC,MAAM,IAAI,SAAS,CACjB,yBAAyB,EACzB,SAAS,CAAC,qBAAqB,EAC/B,SAAS,EACT,WAAW,CACZ,CAAC;6BACH;4BAED,aAAa,GAAG,UAAC,KAAY;gCAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;oCAC1B,eAAe,CAAC,KAAK,EAAE,CAAC;iCACzB;4BACH,CAAC,CAAC;4BACF,WAAW,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;yBAClE;wBAED,IAAI,WAAW,CAAC,OAAO,EAAE;4BACvB,UAAU,CAAC;gCACT,eAAe,CAAC,KAAK,EAAE,CAAC;4BAC1B,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;yBACzB;wBAED,IAAI,WAAW,CAAC,QAAQ,EAAE;4BAClB,QAAQ,GAAQ,WAAW,CAAC,QAAQ,CAAC;4BACrC,gBAAc,IAAI,QAAQ,EAAE,CAAC;4BAC7B,eAAe,GAAG,UAAC,GAAW,EAAE,KAAU;gCAC9C,0FAA0F;gCAC1F,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;oCAC/B,KAAK,GAAG,KAAK,EAAE,CAAC;iCACjB;gCACD,IAAI,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;oCAC7E,aAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;iCACrD;qCAAM;oCACL,aAAW,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iCAChC;4BACH,CAAC,CAAC;4BACF,WAA2C,EAArB,KAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAArB,cAAqB,EAArB,IAAqB,EAAE;gCAAlC,OAAO;gCACV,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC;gCACpC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;oCAC5B,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wCACzC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;qCACxC;iCACF;qCAAM;oCACL,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iCACrC;6BACF;4BAED,WAAW,CAAC,IAAI,GAAG,aAAW,CAAC;4BAC/B,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC;4BAC3B,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;4BAC5D,IAAI,WAAW,IAAI,WAAW,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,EAAE;gCACpE,IAAI,OAAO,aAAW,CAAC,WAAW,KAAK,UAAU,EAAE;oCACjD,WAAW,CAAC,OAAO,CAAC,GAAG,CACrB,cAAc,EACd,mCAAiC,aAAW,CAAC,WAAW,EAAI,CAC7D,CAAC;iCACH;qCAAM;oCACL,kEAAkE;oCAClE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;iCAC5C;6BACF;yBACF;wBAEG,IAAI,GAAG,WAAW,CAAC,IAAI;4BACzB,CAAC,CAAC,OAAO,WAAW,CAAC,IAAI,KAAK,UAAU;gCACtC,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE;gCACpB,CAAC,CAAC,WAAW,CAAC,IAAI;4BACpB,CAAC,CAAC,SAAS,CAAC;wBACd,IAAI,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,IAAI,EAAE;4BAChD,gBAAc,CAAC,CAAC;4BACd,kBAAkB,GAAG,IAAI,SAAS,CAAC;gCACvC,SAAS,EAAE,UAAC,KAAsB,EAAE,SAAS,EAAE,QAAQ;oCACrD,aAAW,IAAI,KAAK,CAAC,MAAM,CAAC;oCAC5B,WAAW,CAAC,gBAAiB,CAAC,EAAE,WAAW,eAAA,EAAE,CAAC,CAAC;oCAC/C,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gCAC7B,CAAC;6BACF,CAAC,CAAC;4BAEH,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;gCAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;6BAC/B;iCAAM;gCACL,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;6BAC9B;4BAED,IAAI,GAAG,kBAAkB,CAAC;yBAC3B;wBAEyD,qBAAM,IAAI,CAAC,cAAc,CACjF,WAAW,CACZ,EAAA;;wBAFK,2BAA2B,GAAyB,SAEzD;wBAEK,WAAW,cACf,IAAI,EAAE,IAAI,EACV,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EACzC,MAAM,EAAE,WAAW,CAAC,MAAM,EAC1B,MAAM,EAAE,eAAe,CAAC,MAAM,EAC9B,QAAQ,EAAE,QAAQ,IACf,2BAA2B,CAC/B,CAAC;;;;wBAIiC,qBAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,EAAA;;wBAAzE,QAAQ,GAAmB,SAA8C;wBAEzE,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;;4BAE7C,OAAO,EAAE,OAAO;4BAChB,OAAO,EAAE,WAAW;4BACpB,MAAM,EAAE,QAAQ,CAAC,MAAM;4BACvB,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;gCAChD,CAAC,CAAG,QAAQ,CAAC,IAA0C;gCACvD,CAAC,CAAC,SAAS;;6BACD,CAAC,WAAW,CAAC,kBAAkB,EAA/B,wBAA+B;wBAAG,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;wBAArB,KAAA,SAAqB,CAAA;;;wBAAG,KAAA,SAAS,CAAA;;;wBAPjF,iBAAiB,IAOf,aAAU,KAAqE;4BAC/E,aAAU,GAAE,QAAQ,CAAC,UAAU;4BAC/B,MAAG,GAAE,QAAQ,CAAC,GAAG;+BAClB,CAAC;wBAEI,uBAAqB,WAAW,CAAC,kBAAkB,CAAC;wBAC1D,IAAI,oBAAkB,EAAE;4BAChB,YAAY,GAA2C,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC;4BAExF,IAAI,gBAAgB,CAAC,YAAY,CAAC,EAAE;gCAC9B,gBAAc,CAAC,CAAC;gCACd,oBAAoB,GAAG,IAAI,SAAS,CAAC;oCACzC,SAAS,EAAE,UAAC,KAAsB,EAAE,SAAS,EAAE,QAAQ;wCACrD,aAAW,IAAI,KAAK,CAAC,MAAM,CAAC;wCAC5B,oBAAkB,CAAC,EAAE,WAAW,eAAA,EAAE,CAAC,CAAC;wCACpC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;oCAC7B,CAAC;iCACF,CAAC,CAAC;gCACH,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gCACxC,iBAAiB,CAAC,kBAAkB,GAAG,oBAAoB,CAAC;6BAC7D;iCAAM;gCACC,WAAS,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAE,CAAC,IAAI,SAAS,CAAC;gCACrE,IAAI,QAAM,EAAE;oCACV,wEAAwE;oCACxE,oBAAkB,CAAC,EAAE,WAAW,EAAE,QAAM,EAAE,CAAC,CAAC;iCAC7C;6BACF;yBACF;wBAED,qBAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,EAAA;;wBAA5C,SAA4C,CAAC;wBAE7C,sBAAO,iBAAiB,EAAC;;;wBAEnB,UAAU,GAAe,OAAK,CAAC;wBACrC,IAAI,UAAU,CAAC,IAAI,KAAK,WAAW,EAAE;4BACnC,MAAM,IAAI,SAAS,CACjB,UAAU,CAAC,OAAO,EAClB,SAAS,CAAC,kBAAkB,EAC5B,SAAS,EACT,WAAW,CACZ,CAAC;yBACH;6BAAM,IAAI,UAAU,CAAC,IAAI,KAAK,SAAS,EAAE;4BACxC,MAAM,IAAI,SAAS,CACjB,yBAAyB,EACzB,SAAS,CAAC,qBAAqB,EAC/B,SAAS,EACT,WAAW,CACZ,CAAC;yBACH;wBAED,MAAM,UAAU,CAAC;;wBAEjB,0BAA0B;wBAC1B,IAAI,WAAW,CAAC,WAAW,IAAI,aAAa,EAAE;4BACxC,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;4BACzC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;gCAC1B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;6BAC3C;4BACG,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;4BAC3C,IAAI,gBAAgB,CAAC,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,kBAAkB,CAAC,EAAE;gCAC3D,kBAAkB,GAAG,gBAAgB,CAAC,iBAAkB,CAAC,kBAAkB,CAAC,CAAC;6BAC9E;4BAED,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;iCAChD,IAAI,CAAC;;gCACJ,MAAA,WAAW,CAAC,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAc,EAAE;gCACtE,OAAO;4BACT,CAAC,CAAC;iCACD,KAAK,CAAC,UAAC,EAAE,IAAM,CAAC,CAAC,CAAC;yBACtB;;;;;;KAEJ;IAKH,sBAAC;AAAD,CAAC,AAxMD,IAwMC;;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAgB;IACxC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;QACzB,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,OAAgB;IAC3C,IAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;IAEtC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG;QACzB,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,OAAO,WAAW,CAAC;AACrB,CAAC"}