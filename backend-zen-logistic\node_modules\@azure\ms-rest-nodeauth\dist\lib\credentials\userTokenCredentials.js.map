{"version": 3, "file": "userTokenCredentials.js", "sourceRoot": "", "sources": ["../../../lib/credentials/userTokenCredentials.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,+FAA+F;;;;;;;;;;AAE/F,iEAA8D;AAK9D,MAAa,oBAAqB,SAAQ,2CAAoB;IAI5D;;;;;;;;;;;;;;OAcG;IACH,YACE,QAAgB,EAChB,MAAc,EACd,QAAgB,EAChB,QAAgB,EAChB,aAA6B,EAC7B,WAAyB,EACzB,UAAuB;QAEvB,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACnD,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACvD;QAED,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,OAAO,EAAE,KAAK,QAAQ,EAAE;YACvD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;SACzD;QAED,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAEhE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAEO,2BAA2B,CAAC,QAAgB,EAAE,eAAuB;QAC3E,+FAA+F;QAC/F,qFAAqF;QACrF,OAAO,QAAQ,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,WAAW,EAAE,CAAC;IAClE,CAAC;IAED;;;;OAIG;IACU,QAAQ;;YACnB,IAAI;gBACF,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpD;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,GAAG,IAAI,CAAC;gBAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBAErD,OAAO,IAAI,OAAO,CAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBACpD,IAAI,CAAC,WAAW,CAAC,gCAAgC,CAC/C,QAAQ,EACR,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,QAAQ,EACb,CAAC,KAAY,EAAE,aAA4C,EAAE,EAAE;wBAC7D,IAAI,KAAK,EAAE;4BACT,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;yBACtB;wBAED,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,gBAAgB,EAAE;4BACzD,OAAO,MAAM,CAAC,aAAa,CAAC,CAAC;yBAC9B;wBAED,aAAa,GAAG,aAA8B,CAAC;wBAC/C,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,MAAO,CAAC,EAAE;4BAC1E,OAAO,OAAO,CAAC,aAA8B,CAAC,CAAC;yBAChD;6BAAM;4BACL,OAAO,MAAM,CACX,eAAe,aAAa,CAAC,MAAM,iDAAiD,IAAI,CAAC,QAAQ,mCAAmC,CACrI,CAAC;yBACH;oBACH,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;KAAA;CACF;AAhGD,oDAgGC"}