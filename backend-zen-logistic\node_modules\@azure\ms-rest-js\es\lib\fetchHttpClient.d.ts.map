{"version": 3, "file": "fetchHttpClient.d.ts", "sourceRoot": "", "sources": ["../../lib/fetchHttpClient.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAE,MAAM,eAAe,CAAC;AAChD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAe,eAAe,EAAE,MAAM,eAAe,CAAC;AAU7D,oBAAY,iBAAiB,GAAG,MAAM,CAAC;AAEvC,oBAAY,iBAAiB,GAAG,IAAI,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,GAAG;IACjF,IAAI,CAAC,EAAE,GAAG,CAAC;IACX,OAAO,CAAC,EAAE,GAAG,CAAC;IACd,MAAM,CAAC,EAAE,GAAG,CAAC;CACd,CAAC;AAEF,oBAAY,cAAc,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC,GAAG;IAC7E,IAAI,EAAE,GAAG,CAAC;IACV,OAAO,EAAE,GAAG,CAAC;IACb,QAAQ,EAAE,GAAG,CAAC;CACf,CAAC;AAEF,8BAAsB,eAAgB,YAAW,UAAU;IACnD,WAAW,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,qBAAqB,CAAC;aAoMhE,cAAc,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;aAC3E,cAAc,CAAC,iBAAiB,EAAE,qBAAqB,GAAG,OAAO,CAAC,IAAI,CAAC;aACvE,KAAK,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,cAAc,CAAC;CAClG;AAcD,wBAAgB,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,eAAe,CAQ9D"}