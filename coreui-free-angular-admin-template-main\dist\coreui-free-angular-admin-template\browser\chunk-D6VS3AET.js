import{b as y}from"./chunk-3MARWV4R.js";import{E as p,F as h,I as x,J as C,M as g,N as S,Na as w,Oa as f,n as m,na as E,oa as P,qa as u,r as s}from"./chunk-Y7QKHPW3.js";import{y as d}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as t,Ib as l,Jb as n,eb as r,fc as e,ja as o,ka as c}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var i=class i{};i.\u0275fac=function(a){return new(a||i)},i.\u0275cmp=r({type:i,selectors:[["app-placeholders"]],decls:149,vars:0,consts:[["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/placeholder"],[1,"d-flex","justify-content-around","p-3"],[2,"width","18rem"],["cCardImg","top","loading","lazy","src","./assets/images/angular.jpg"],["cCardTitle",""],["cCardText",""],["cButton","","cCol","7","routerLink","./"],["aria-label","Placeholder","cCardImg","top","focusable","false","height","162","preserveAspectRatio","xMidYMid slice","role","img","width","100%","xmlns","http://www.w3.org/2000/svg"],["fill","#868e96","height","100%","width","100%"],["cCardTitle","","cPlaceholderAnimation","glow"],["cCol","6","cPlaceholder",""],["cCardText","","cPlaceholderAnimation","glow"],["cCol","","xs","7","cPlaceholder","",1,"me-1"],["cCol","4","cPlaceholder","",1,"me-1"],["cCol","6","cPlaceholder","",1,"me-1"],["cCol","8","cPlaceholder","",1,"me-1"],["cPlaceholderAnimation","glow"],["cButton","","cCol","7","cPlaceholder","","color","primary","disabled","","routerLink","./"],["aria-hidden","true"],["cButton","","cCol","4","cPlaceholder","","disabled",""],["href","components/placeholder#width"],["cPlaceholder","",1,"w-75"],["cPlaceholder","",2,"width","30%"],["href","components/placeholder#color"],["cCol","12","cPlaceholder",""],["cBgColor","primary","cCol","12","cPlaceholder",""],["cBgColor","secondary","cCol","12","cPlaceholder",""],["cBgColor","success","cCol","12","cPlaceholder",""],["cBgColor","danger","cCol","12","cPlaceholder",""],["cBgColor","warning","cCol","12","cPlaceholder",""],["cBgColor","info","cCol","12","cPlaceholder",""],["cBgColor","light","cCol","12","cPlaceholder",""],["cBgColor","dark","cCol","12","cPlaceholder",""],["href","components/placeholder#sizing"],["cCol","12","cPlaceholder","","cPlaceholderSize","lg"],["cCol","12","cPlaceholder","","cPlaceholderSize","sm"],["cCol","12","cPlaceholder","","cPlaceholderSize","xs"],["href","components/placeholder#animation"],["cPlaceholderAnimation","wave"]],template:function(a,A){a&1&&(t(0,"c-row")(1,"c-col",0)(2,"c-card",1)(3,"c-card-header")(4,"strong"),e(5,"Angular Placeholder"),l()(),t(6,"c-card-body")(7,"p",2),e(8,' In the example below, we take a typical card component and recreate it with placeholders applied to create a "loading card". Size and proportions are the same between the two. '),l(),t(9,"app-docs-example",3)(10,"div",4)(11,"c-card",5),n(12,"img",6),t(13,"c-card-body")(14,"h5",7),e(15,"Card title"),l(),t(16,"p",8),e(17," Some quick example text to build on the card title and make up the bulk of the card's content. "),l(),t(18,"a",9),e(19,"Go somewhere"),l()()(),t(20,"c-card",5),o(),t(21,"svg",10)(22,"title"),e(23,"Placeholder"),l(),n(24,"rect",11),l(),c(),t(25,"c-card-body")(26,"h5",12),n(27,"span",13),l(),t(28,"p",14),n(29,"span",15)(30,"span",16)(31,"span",16)(32,"span",17)(33,"span",18),l(),t(34,"p",19),n(35,"a",20),l()()()()()()(),t(36,"c-card",1)(37,"c-card-header")(38,"strong"),e(39,"Angular Placeholder"),l()(),t(40,"c-card-body")(41,"p",2),e(42," Create placeholders with the "),t(43,"code"),e(44,"cPlaceholder"),l(),e(45," directive and a grid column cCol directive (e.g., "),t(46,"code"),e(47,'cCol="6"'),l(),e(48,") to set the "),t(49,"code"),e(50,"width"),l(),e(51,". They can replace the text inside an element or be added as a modifier to an existing component. "),l(),t(52,"app-docs-example",3)(53,"p",21),n(54,"span",13),l(),n(55,"button",22),l()()(),t(56,"c-card",1)(57,"c-card-header")(58,"strong"),e(59,"Angular Placeholder"),l(),t(60,"small"),e(61," Width"),l()(),t(62,"c-card-body")(63,"p",2),e(64," You can change the "),t(65,"code"),e(66,"width"),l(),e(67," through grid column classes, width utilities, or inline styles. "),l(),t(68,"app-docs-example",23),n(69,"span",13)(70,"span",24)(71,"span",25),l()()(),t(72,"c-card",1)(73,"c-card-header")(74,"strong"),e(75,"Angular Placeholder"),l(),t(76,"small"),e(77," Color"),l()(),t(78,"c-card-body")(79,"p",2),e(80," By default, the "),t(81,"code"),e(82,"cPlaceholder"),l(),e(83," uses "),t(84,"code"),e(85,"currentColor"),l(),e(86,". This can be overridden with a custom color or utility class. "),l(),t(87,"app-docs-example",26),n(88,"span",27)(89,"span",28)(90,"span",29)(91,"span",30)(92,"span",31)(93,"span",32)(94,"span",33)(95,"span",34)(96,"span",35),l()()(),t(97,"c-card",1)(98,"c-card-header")(99,"strong"),e(100,"Angular Placeholder"),l(),t(101,"small"),e(102," Sizing"),l()(),t(103,"c-card-body")(104,"p",2),e(105," The size of "),t(106,"code"),e(107,"cPlaceholder"),l(),e(108,"s are based on the typographic style of the parent element. Customize them with "),t(109,"code"),e(110,"size"),l(),e(111," prop: "),t(112,"code"),e(113,"lg"),l(),e(114,", "),t(115,"code"),e(116,"sm"),l(),e(117,", or "),t(118,"code"),e(119,"xs"),l(),e(120,". "),l(),t(121,"app-docs-example",36),n(122,"span",37)(123,"span",27)(124,"span",38)(125,"span",39),l()()(),t(126,"c-card",1)(127,"c-card-header")(128,"strong"),e(129,"Angular Placeholder"),l(),t(130,"small"),e(131," Animation"),l()(),t(132,"c-card-body")(133,"p",2),e(134," Animate placeholders with "),t(135,"code"),e(136,'cPlaceholderAnimation="glow"'),l(),e(137," or "),t(138,"code"),e(139,'cPlaceholderAnimation="wave"'),l(),e(140," to better convey the perception of something being "),t(141,"em"),e(142,"actively"),l(),e(143," loaded. "),l(),t(144,"app-docs-example",40)(145,"p",19),n(146,"span",27),l(),t(147,"p",41),n(148,"span",27),l()()()()()())},dependencies:[u,P,p,x,h,y,C,S,g,m,E,d,f,w,s],encapsulation:2});var b=i;export{b as PlaceholdersComponent};
