{"//metadata": {"migrationDate": "2023-03-08T18:36:03.000Z"}, "_args": [["@azure/abort-controller@2.1.2", "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic"]], "_from": "@azure/abort-controller@2.1.2", "_id": "@azure/abort-controller@2.1.2", "_inBundle": false, "_integrity": "sha512-nBrLsEWm4J2u5LpAPjxADTlq3trDgVZZXHNKabeXZtpq3d3AbN/KGO82R87rdDz5/lYB024rtEf10/q0urNgsA==", "_location": "/@azure/abort-controller", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@azure/abort-controller@2.1.2", "name": "@azure/abort-controller", "escapedName": "@azure%2fabort-controller", "scope": "@azure", "rawSpec": "2.1.2", "saveSpec": null, "fetchSpec": "2.1.2"}, "_requiredBy": ["/@azure/core-auth", "/@azure/core-util"], "_resolved": "https://registry.npmjs.org/@azure/abort-controller/-/abort-controller-2.1.2.tgz", "_spec": "2.1.2", "_where": "C:\\Users\\<USER>\\Documents\\Back\\backend-zen-logistic", "author": {"name": "Microsoft Corporation"}, "browser": "./dist/browser/index.js", "bugs": {"url": "https://github.com/Azure/azure-sdk-for-js/issues"}, "dependencies": {"tslib": "^2.6.2"}, "description": "Microsoft Azure SDK for JavaScript - Aborter", "devDependencies": {"@azure/dev-tool": "^1.0.0", "@azure/eslint-plugin-azure-sdk": "^3.0.0", "@microsoft/api-extractor": "^7.40.3", "@types/node": "^18.0.0", "@vitest/browser": "^1.3.1", "@vitest/coverage-istanbul": "^1.3.1", "eslint": "^8.56.0", "playwright": "^1.41.2", "prettier": "^3.2.5", "rimraf": "^5.0.5", "tshy": "^1.13.0", "typescript": "~5.3.3", "vitest": "^1.3.1"}, "engines": {"node": ">=18.0.0"}, "exports": {"./package.json": "./package.json", ".": {"browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.js"}, "react-native": {"types": "./dist/react-native/index.d.ts", "default": "./dist/react-native/index.js"}, "import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "files": ["dist/", "README.md", "LICENSE"], "homepage": "https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/core/abort-controller/README.md", "keywords": ["azure", "aborter", "abortsignal", "cancellation", "node.js", "typescript", "javascript", "browser", "cloud"], "license": "MIT", "main": "./dist/commonjs/index.js", "name": "@azure/abort-controller", "repository": {"type": "git", "url": "git+https://github.com/Azure/azure-sdk-for-js.git"}, "scripts": {"build": "npm run clean && tshy && api-extractor run --local", "build:samples": "echo Obsolete", "build:test": "npm run clean && tshy && dev-tool run build-test", "check-format": "dev-tool run vendored prettier --list-different --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,mjs,cjs,json}\"", "clean": "rimraf --glob dist dist-* temp types *.tgz *.log", "execute:samples": "echo skipped", "extract-api": "tshy && api-extractor run --local", "format": "dev-tool run vendored prettier --write --config ../../../.prettierrc.json --ignore-path ../../../.prettierignore \"src/**/*.{ts,cts,mts}\" \"test/**/*.{ts,cts,mts}\" \"*.{js,mjs,cjs,json}\"", "integration-test": "npm run integration-test:node && npm run integration-test:browser", "integration-test:browser": "echo skipped", "integration-test:node": "echo skipped", "lint": "eslint package.json api-extractor.json src test --ext .ts --ext .cts --ext .mts", "lint:fix": "eslint package.json api-extractor.json src test --ext .ts --ext .cts --ext .mts --fix --fix-type [problem,suggestion]", "pack": "npm pack 2>&1", "test": "npm run clean && tshy && npm run unit-test:node && dev-tool run build-test && npm run unit-test:browser && npm run integration-test", "test:browser": "npm run clean && npm run build:test && npm run unit-test:browser && npm run integration-test:browser", "test:node": "npm run clean && tshy && npm run unit-test:node && npm run integration-test:node", "unit-test": "npm run unit-test:node && npm run unit-test:browser", "unit-test:browser": "npm run build:test && dev-tool run test:vitest --no-test-proxy --browser", "unit-test:node": "dev-tool run test:vitest --no-test-proxy"}, "sdk-type": "client", "sideEffects": false, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}, "dialects": ["esm", "commonjs"], "esmDialects": ["browser", "react-native"], "selfLink": false}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "2.1.2"}