import{a as c}from"./chunk-5MBWQABE.js";import{c as u}from"./chunk-EN2VGXKU.js";import{x as C}from"./chunk-VLR5A2CC.js";import{A as f,E as A,Y as v,aa as b,ba as l,l as R,p as i}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var r=class r{constructor(e){this.authService=e}getRole(){let e=this.authService.getUser();return e&&(e.role||e.type_utilisateur)||null}getRole$(){return this.authService.currentUser$.pipe(i(e=>e&&(e.role||e.type_utilisateur)||null))}hasRole(e){return this.getRole()===e}hasAnyRole(e){let t=this.getRole();return e.some(n=>t===n)}hasAllRoles(e){let t=this.getRole();return e.length===1&&e[0]===t}hasRole$(e){return this.getRole$().pipe(i(t=>t===e))}hasAnyRole$(e){return this.getRole$().pipe(i(t=>e.some(n=>t===n)))}getAvailableRoles(){return["SuperAdmin","Administrateur","Client","Chef Departement","GS","GE","Depot","Inspection","FACTURATION","MAGASIN","Conducteur"]}isValidRole(e){return this.getAvailableRoles().includes(e)}};r.\u0275fac=function(t){return new(t||r)(b(u))},r.\u0275prov=v({token:r,factory:r.\u0275fac,providedIn:"root"});var p=r;var h=(o,e)=>{let t=l(u),n=l(p),m=l(C),a=o.data.roles||o.data.expectedRole||[];return a.length===0?!0:t.currentUser$.pipe(A(1),i(s=>{if(!s)return console.log("Role guard: No user found, redirecting to login"),m.navigate(["/login"]),!1;let d=s.role||s.type_utilisateur;return a.some(g=>d===g||n.hasRole(g)||n.hasAnyRole(a))?(console.log(`Role guard: Access granted. User role: ${d}, Required roles: ${a.join(", ")}`),!0):(console.log(`Role guard: Access denied. User role: ${d}, Required roles: ${a.join(", ")}`),m.navigate(["/unauthorized"]),!1)}),f(s=>(console.error("Role guard error:",s),m.navigate(["/unauthorized"]),R(!1))))};var T=[{path:"",canActivate:[c],data:{title:"My Pages"},children:[{path:"commandes",loadChildren:()=>import("./chunk-Z4R2YTY2.js").then(o=>o.routes),canActivate:[c,h],data:{title:"Commandes",roles:["Client","Chef Departement","GS","GE","Depot","SuperAdmin","Administrateur"]}},{path:"inspection",loadChildren:()=>import("./chunk-2I63Q4BC.js").then(o=>o.routes),canActivate:[c,h],data:{title:"Inspection",roles:["Inspection","SuperAdmin","Administrateur"]}}]}];export{T as routes};
