import{a as G,b as z}from"./chunk-L3UST63Y.js";function E(e){return typeof e=="function"}function Yt(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Lr=Yt(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function yt(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var F=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(E(r))try{r()}catch(i){t=i instanceof Lr?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{hl(i)}catch(s){t=t??[],s instanceof Lr?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Lr(t)}}add(t){var n;if(t&&t!==this)if(this.closed)hl(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&yt(n,t)}remove(t){let{_finalizers:n}=this;n&&yt(n,t),t instanceof e&&t._removeParent(this)}};F.EMPTY=(()=>{let e=new F;return e.closed=!0,e})();var ji=F.EMPTY;function Fr(e){return e instanceof F||e&&"closed"in e&&E(e.remove)&&E(e.add)&&E(e.unsubscribe)}function hl(e){E(e)?e():e.unsubscribe()}var Ie={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Jt={setTimeout(e,t,...n){let{delegate:r}=Jt;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Jt;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function jr(e){Jt.setTimeout(()=>{let{onUnhandledError:t}=Ie;if(t)t(e);else throw e})}function He(){}var gl=Vi("C",void 0,void 0);function ml(e){return Vi("E",void 0,e)}function yl(e){return Vi("N",e,void 0)}function Vi(e,t,n){return{kind:e,value:t,error:n}}var vt=null;function Kt(e){if(Ie.useDeprecatedSynchronousErrorHandling){let t=!vt;if(t&&(vt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=vt;if(vt=null,n)throw r}}else e()}function vl(e){Ie.useDeprecatedSynchronousErrorHandling&&vt&&(vt.errorThrown=!0,vt.error=e)}var It=class extends F{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Fr(t)&&t.add(this)):this.destination=Uh}static create(t,n,r){return new Xt(t,n,r)}next(t){this.isStopped?Bi(yl(t),this):this._next(t)}error(t){this.isStopped?Bi(ml(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Bi(gl,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Bh=Function.prototype.bind;function Hi(e,t){return Bh.call(e,t)}var $i=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Vr(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Vr(r)}else Vr(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Vr(n)}}},Xt=class extends It{constructor(t,n,r){super();let o;if(E(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ie.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Hi(t.next,i),error:t.error&&Hi(t.error,i),complete:t.complete&&Hi(t.complete,i)}):o=t}this.destination=new $i(o)}};function Vr(e){Ie.useDeprecatedSynchronousErrorHandling?vl(e):jr(e)}function $h(e){throw e}function Bi(e,t){let{onStoppedNotification:n}=Ie;n&&Jt.setTimeout(()=>n(e,t))}var Uh={closed:!0,next:He,error:$h,complete:He};var en=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Y(e){return e}function qh(...e){return Ui(e)}function Ui(e){return e.length===0?Y:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var M=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Gh(n)?n:new Xt(n,r,o);return Kt(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Il(r),new r((o,i)=>{let s=new Xt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[en](){return this}pipe(...n){return Ui(n)(this)}toPromise(n){return n=Il(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Il(e){var t;return(t=e??Ie.Promise)!==null&&t!==void 0?t:Promise}function Wh(e){return e&&E(e.next)&&E(e.error)&&E(e.complete)}function Gh(e){return e&&e instanceof It||Wh(e)&&Fr(e)}var El=Yt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var _e=(()=>{class e extends M{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Hr(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new El}next(n){Kt(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Kt(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Kt(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ji:(this.currentObservers=null,i.push(n),new F(()=>{this.currentObservers=null,yt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new M;return n.source=this,n}}return e.create=(t,n)=>new Hr(t,n),e})(),Hr=class extends _e{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ji}};function qi(e){return E(e?.lift)}function I(e){return t=>{if(qi(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function v(e,t,n,r,o){return new Wi(e,t,n,r,o)}var Wi=class extends It{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function tt(e,t){return I((n,r)=>{let o=0;n.subscribe(v(r,i=>e.call(t,i,o++)&&r.next(i)))})}function wl(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Dl(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Et(e){return this instanceof Et?(this.v=e,this):new Et(e)}function Cl(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(N,b){i.push([f,g,N,b])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{l(r[f](h))}catch(g){p(i[0][3],g)}}function l(f){f.value instanceof Et?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function bl(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Dl=="function"?Dl(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var tn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Br(e){return E(e?.then)}function $r(e){return E(e[en])}function Ur(e){return Symbol.asyncIterator&&E(e?.[Symbol.asyncIterator])}function qr(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function zh(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Wr=zh();function Gr(e){return E(e?.[Wr])}function zr(e){return Cl(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Et(n.read());if(o)return yield Et(void 0);yield yield Et(r)}}finally{n.releaseLock()}})}function Qr(e){return E(e?.getReader)}function x(e){if(e instanceof M)return e;if(e!=null){if($r(e))return Qh(e);if(tn(e))return Zh(e);if(Br(e))return Yh(e);if(Ur(e))return Tl(e);if(Gr(e))return Jh(e);if(Qr(e))return Kh(e)}throw qr(e)}function Qh(e){return new M(t=>{let n=e[en]();if(E(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Zh(e){return new M(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Yh(e){return new M(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,jr)})}function Jh(e){return new M(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Tl(e){return new M(t=>{Xh(e,t).catch(n=>t.error(n))})}function Kh(e){return Tl(zr(e))}function Xh(e,t){var n,r,o,i;return wl(this,void 0,void 0,function*(){try{for(n=bl(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function _l(e){return I((t,n)=>{x(e).subscribe(v(n,()=>n.complete(),He)),!n.closed&&t.subscribe(n)})}function Gi(){return I((e,t)=>{let n=null;e._refCount++;let r=v(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var zi=class extends M{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,qi(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new F;let n=this.getSubject();t.add(this.source.subscribe(v(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=F.EMPTY)}return t}refCount(){return Gi()(this)}};var nn={schedule(e){let t=requestAnimationFrame,n=cancelAnimationFrame,{delegate:r}=nn;r&&(t=r.requestAnimationFrame,n=r.cancelAnimationFrame);let o=t(i=>{n=void 0,e(i)});return new F(()=>n?.(o))},requestAnimationFrame(...e){let{delegate:t}=nn;return(t?.requestAnimationFrame||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){let{delegate:t}=nn;return(t?.cancelAnimationFrame||cancelAnimationFrame)(...e)},delegate:void 0};var jn=class extends _e{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Vn={now(){return(Vn.delegate||Date).now()},delegate:void 0};var Qi=class extends _e{constructor(t=1/0,n=1/0,r=Vn){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let c=1;c<r.length&&r[c]<=s;c+=2)a=c;a&&r.splice(0,a+1)}}};var Zr=class extends F{constructor(t,n){super()}schedule(t,n=0){return this}};var Hn={setInterval(e,t,...n){let{delegate:r}=Hn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=Hn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var rn=class extends Zr{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return Hn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&Hn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,yt(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var on=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};on.now=Vn.now;var sn=class extends on{constructor(t,n=on.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var Dt=new sn(rn),Ml=Dt;var Yr=class extends rn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}requestAsyncId(t,n,r=0){return r!==null&&r>0?super.requestAsyncId(t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=nn.requestAnimationFrame(()=>t.flush(void 0))))}recycleAsyncId(t,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(t,n,r);let{actions:i}=t;n!=null&&n===t._scheduled&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(nn.cancelAnimationFrame(n),t._scheduled=void 0)}};var Jr=class extends sn{flush(t){this._active=!0;let n;t?n=t.id:(n=this._scheduled,this._scheduled=void 0);let{actions:r}=this,o;t=t||r.shift();do if(o=t.execute(t.state,t.delay))break;while((t=r[0])&&t.id===n&&r.shift());if(this._active=!1,o){for(;(t=r[0])&&t.id===n&&r.shift();)t.unsubscribe();throw o}}};var eg=new Jr(Yr);var Be=new M(e=>e.complete());function Kr(e){return e&&E(e.schedule)}function Zi(e){return e[e.length-1]}function nt(e){return E(Zi(e))?e.pop():void 0}function Me(e){return Kr(Zi(e))?e.pop():void 0}function Nl(e,t){return typeof Zi(e)=="number"?e.pop():t}function oe(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Xr(e,t=0){return I((n,r)=>{n.subscribe(v(r,o=>oe(r,e,()=>r.next(o),t),()=>oe(r,e,()=>r.complete(),t),o=>oe(r,e,()=>r.error(o),t)))})}function eo(e,t=0){return I((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function xl(e,t){return x(e).pipe(eo(t),Xr(t))}function Sl(e,t){return x(e).pipe(eo(t),Xr(t))}function Rl(e,t){return new M(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Al(e,t){return new M(n=>{let r;return oe(n,t,()=>{r=e[Wr](),oe(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>E(r?.return)&&r.return()})}function to(e,t){if(!e)throw new Error("Iterable cannot be null");return new M(n=>{oe(n,t,()=>{let r=e[Symbol.asyncIterator]();oe(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Ol(e,t){return to(zr(e),t)}function kl(e,t){if(e!=null){if($r(e))return xl(e,t);if(tn(e))return Rl(e,t);if(Br(e))return Sl(e,t);if(Ur(e))return to(e,t);if(Gr(e))return Al(e,t);if(Qr(e))return Ol(e,t)}throw qr(e)}function Ne(e,t){return t?kl(e,t):x(e)}function tg(...e){let t=Me(e);return Ne(e,t)}function ng(e,t){let n=E(e)?e:()=>e,r=o=>o.error(n());return new M(t?o=>t.schedule(r,0,o):r)}function rg(e){return!!e&&(e instanceof M||E(e.lift)&&E(e.subscribe))}var wt=Yt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Pl(e){return e instanceof Date&&!isNaN(e)}function xe(e,t){return I((n,r)=>{let o=0;n.subscribe(v(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:og}=Array;function ig(e,t){return og(t)?e(...t):e(t)}function an(e){return xe(t=>ig(e,t))}var{isArray:sg}=Array,{getPrototypeOf:ag,prototype:cg,keys:lg}=Object;function no(e){if(e.length===1){let t=e[0];if(sg(t))return{args:t,keys:null};if(ug(t)){let n=lg(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function ug(e){return e&&typeof e=="object"&&ag(e)===cg}function ro(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function dg(...e){let t=Me(e),n=nt(e),{args:r,keys:o}=no(e);if(r.length===0)return Ne([],t);let i=new M(fg(r,t,o?s=>ro(o,s):Y));return n?i.pipe(an(n)):i}function fg(e,t,n=Y){return r=>{Ll(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Ll(t,()=>{let l=Ne(e[c],t),u=!1;l.subscribe(v(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Ll(e,t,n){e?oe(n,e,t):t()}function oo(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=g=>l<r?h(g):c.push(g),h=g=>{i&&t.next(g),l++;let N=!1;x(n(g,u++)).subscribe(v(t,b=>{o?.(b),i?f(b):t.next(b)},()=>{N=!0},void 0,()=>{if(N)try{for(l--;c.length&&l<r;){let b=c.shift();s?oe(t,s,()=>h(b)):h(b)}p()}catch(b){t.error(b)}}))};return e.subscribe(v(t,f,()=>{d=!0,p()})),()=>{a?.()}}function ue(e,t,n=1/0){return E(t)?ue((r,o)=>xe((i,s)=>t(r,i,o,s))(x(e(r,o))),n):(typeof t=="number"&&(n=t),I((r,o)=>oo(r,o,e,n)))}function Bn(e=1/0){return ue(Y,e)}function Fl(){return Bn(1)}function cn(...e){return Fl()(Ne(e,Me(e)))}function pg(e){return new M(t=>{x(e()).subscribe(t)})}function hg(...e){let t=nt(e),{args:n,keys:r}=no(e),o=new M(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;x(n[u]).subscribe(v(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?ro(r,a):a),i.complete())}))}});return t?o.pipe(an(t)):o}var gg=["addListener","removeListener"],mg=["addEventListener","removeEventListener"],yg=["on","off"];function Yi(e,t,n,r){if(E(n)&&(r=n,n=void 0),r)return Yi(e,t,n).pipe(an(r));let[o,i]=Eg(e)?mg.map(s=>a=>e[s](t,a,n)):vg(e)?gg.map(jl(e,t)):Ig(e)?yg.map(jl(e,t)):[];if(!o&&tn(e))return ue(s=>Yi(s,t,n))(x(e));if(!o)throw new TypeError("Invalid event target");return new M(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function jl(e,t){return n=>r=>e[n](t,r)}function vg(e){return E(e.addListener)&&E(e.removeListener)}function Ig(e){return E(e.on)&&E(e.off)}function Eg(e){return E(e.addEventListener)&&E(e.removeEventListener)}function io(e=0,t,n=Ml){let r=-1;return t!=null&&(Kr(t)?n=t:r=t),new M(o=>{let i=Pl(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function Dg(...e){let t=Me(e),n=Nl(e,1/0),r=e;return r.length?r.length===1?x(r[0]):Bn(n)(Ne(r,t)):Be}var{isArray:wg}=Array;function Vl(e){return e.length===1&&wg(e[0])?e[0]:e}function Hl(...e){let t=nt(e),n=Vl(e);return n.length?new M(r=>{let o=n.map(()=>[]),i=n.map(()=>!1);r.add(()=>{o=i=null});for(let s=0;!r.closed&&s<n.length;s++)x(n[s]).subscribe(v(r,a=>{if(o[s].push(a),o.every(c=>c.length)){let c=o.map(l=>l.shift());r.next(t?t(...c):c),o.some((l,u)=>!l.length&&i[u])&&r.complete()}},()=>{i[s]=!0,!o[s].length&&r.complete()}));return()=>{o=i=null}}):Be}function Bl(e){return I((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let l=o;o=null,n.next(l)}s&&n.complete()},c=()=>{i=null,s&&n.complete()};t.subscribe(v(n,l=>{r=!0,o=l,i||x(e(l)).subscribe(i=v(n,a,c))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function Cg(e,t=Dt){return Bl(()=>io(e,t))}function Ji(e){return I((t,n)=>{let r=null,o=!1,i;r=t.subscribe(v(n,void 0,void 0,s=>{i=x(e(s,Ji(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function $l(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(v(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function bg(e,t){return E(t)?ue(e,t,1):ue(e,1)}function Ul(e,t=Dt){return I((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let l=i;i=null,r.next(l)}};function c(){let l=s+e,u=t.now();if(u<l){o=this.schedule(void 0,l-u),r.add(o);return}a()}n.subscribe(v(r,l=>{i=l,s=t.now(),o||(o=t.schedule(c,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function $n(e){return I((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Ct(e){return e<=0?()=>Be:I((t,n)=>{let r=0;t.subscribe(v(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function ql(){return I((e,t)=>{e.subscribe(v(t,He))})}function Wl(e){return xe(()=>e)}function Ki(e,t){return t?n=>cn(t.pipe(Ct(1),ql()),n.pipe(Ki(e))):ue((n,r)=>x(e(n,r)).pipe(Ct(1),Wl(n)))}function Tg(e,t=Dt){let n=io(e,t);return Ki(()=>n)}function Gl(e,t=Y){return e=e??_g,I((n,r)=>{let o,i=!0;n.subscribe(v(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function _g(e,t){return e===t}function so(e=Mg){return I((t,n)=>{let r=!1;t.subscribe(v(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Mg(){return new wt}function Ng(e,t=1/0,n){return t=(t||0)<1?1/0:t,I((r,o)=>oo(r,o,e,t,void 0,!0,n))}function zl(e){return I((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function xg(e,t){let n=arguments.length>=2;return r=>r.pipe(e?tt((o,i)=>e(o,i,r)):Y,Ct(1),n?$n(t):so(()=>new wt))}function Xi(e){return e<=0?()=>Be:I((t,n)=>{let r=[];t.subscribe(v(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Sg(e,t){let n=arguments.length>=2;return r=>r.pipe(e?tt((o,i)=>e(o,i,r)):Y,Xi(1),n?$n(t):so(()=>new wt))}function Rg(){return I((e,t)=>{let n,r=!1;e.subscribe(v(t,o=>{let i=n;n=o,r&&t.next([i,o]),r=!0}))})}function Ag(e,t){return I($l(e,t,arguments.length>=2,!0))}function Og(e){return tt((t,n)=>e<=n)}function kg(...e){let t=Me(e);return I((n,r)=>{(t?cn(e,n,t):cn(e,n)).subscribe(r)})}function Ql(e,t){return I((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(v(r,c=>{o?.unsubscribe();let l=0,u=i++;x(e(c,u)).subscribe(o=v(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Pg(e,t=!1){return I((n,r)=>{let o=0;n.subscribe(v(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Zl(e,t,n){let r=E(e)||t||n?{next:e,error:t,complete:n}:e;return r?I((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(v(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Y}function Lg(...e){let t=nt(e);return I((n,r)=>{let o=e.length,i=new Array(o),s=e.map(()=>!1),a=!1;for(let c=0;c<o;c++)x(e[c]).subscribe(v(r,l=>{i[c]=l,!a&&!s[c]&&(s[c]=!0,(a=s.every(Y))&&(s=null))},He));n.subscribe(v(r,c=>{if(a){let l=[c,...i];r.next(t?t(...l):l)}}))})}function Yl(...e){return I((t,n)=>{Hl(t,...e).subscribe(n)})}function Fg(...e){return Yl(...e)}var es;function ts(){return es}function $e(e){let t=es;return es=e,t}var jg=Symbol("NotFound"),ao=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function ln(e){return e===jg||e?.name==="\u0275NotFound"}function qn(e,t){return Object.is(e,t)}var U=null,co=!1,ns=1,Vg=null,j=Symbol("SIGNAL");function D(e){let t=U;return U=e,t}function lo(){return U}var qe={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function We(e){if(co)throw new Error("");if(U===null)return;U.consumerOnSignalRead(e);let t=U.nextProducerIndex++;if(fo(U),t<U.producerNode.length&&U.producerNode[t]!==e&&Un(U)){let n=U.producerNode[t];uo(n,U.producerIndexOfThis[t])}U.producerNode[t]!==e&&(U.producerNode[t]=e,U.producerIndexOfThis[t]=Un(U)?Kl(e,U,t):0),U.producerLastReadVersion[t]=e.version}function Jl(){ns++}function Tt(e){if(!(Un(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ns)){if(!e.producerMustRecompute(e)&&!_t(e)){un(e);return}e.producerRecomputeValue(e),un(e)}}function rs(e){if(e.liveConsumerNode===void 0)return;let t=co;co=!0;try{for(let n of e.liveConsumerNode)n.dirty||Hg(n)}finally{co=t}}function os(){return U?.consumerAllowSignalWrites!==!1}function Hg(e){e.dirty=!0,rs(e),e.consumerMarkedDirty?.(e)}function un(e){e.dirty=!1,e.lastCleanEpoch=ns}function Se(e){return e&&(e.nextProducerIndex=0),D(e)}function Ge(e,t){if(D(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Un(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)uo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function _t(e){fo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Tt(n),r!==n.version))return!0}return!1}function dn(e){if(fo(e),Un(e))for(let t=0;t<e.producerNode.length;t++)uo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Kl(e,t,n){if(Xl(e),e.liveConsumerNode.length===0&&eu(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Kl(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function uo(e,t){if(Xl(e),e.liveConsumerNode.length===1&&eu(e))for(let r=0;r<e.producerNode.length;r++)uo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];fo(o),o.producerIndexOfThis[r]=t}}function Un(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function fo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Xl(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function eu(e){return e.producerNode!==void 0}function Wn(e){Vg?.(e)}function Gn(e,t){let n=Object.create(Bg);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Tt(n),We(n),n.value===Ue)throw n.error;return n.value};return r[j]=n,Wn(n),r}var rt=Symbol("UNSET"),bt=Symbol("COMPUTING"),Ue=Symbol("ERRORED"),Bg=z(G({},qe),{value:rt,dirty:!0,error:null,equal:qn,kind:"computed",producerMustRecompute(e){return e.value===rt||e.value===bt},producerRecomputeValue(e){if(e.value===bt)throw new Error("");let t=e.value;e.value=bt;let n=Se(e),r,o=!1;try{r=e.computation(),D(null),o=t!==rt&&t!==Ue&&r!==Ue&&e.equal(t,r)}catch(i){r=Ue,e.error=i}finally{Ge(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function $g(){throw new Error}var tu=$g;function nu(e){tu(e)}function is(e){tu=e}var Ug=null;function ss(e,t){let n=Object.create(zn);n.value=e,t!==void 0&&(n.equal=t);let r=()=>ru(n);return r[j]=n,Wn(n),[r,s=>ot(n,s),s=>po(n,s)]}function ru(e){return We(e),e.value}function ot(e,t){os()||nu(e),e.equal(e.value,t)||(e.value=t,qg(e))}function po(e,t){os()||nu(e),ot(e,t(e.value))}var zn=z(G({},qe),{equal:qn,value:void 0,kind:"signal"});function qg(e){e.version++,Jl(),rs(e),Ug?.(e)}function as(e,t,n){let r=Object.create(Wg);r.source=e,r.computation=t,n!=null&&(r.equal=n);let i=()=>{if(Tt(r),We(r),r.value===Ue)throw r.error;return r.value};return i[j]=r,Wn(r),i}function ou(e,t){Tt(e),ot(e,t),un(e)}function iu(e,t){Tt(e),po(e,t),un(e)}var Wg=z(G({},qe),{value:rt,dirty:!0,error:null,equal:qn,kind:"linkedSignal",producerMustRecompute(e){return e.value===rt||e.value===bt},producerRecomputeValue(e){if(e.value===bt)throw new Error("");let t=e.value;e.value=bt;let n=Se(e),r;try{let o=e.source(),i=t===rt||t===Ue?void 0:{source:e.sourceValue,value:t};r=e.computation(o,i),e.sourceValue=o}catch(o){r=Ue,e.error=o}finally{Ge(e,n)}if(t!==rt&&r!==Ue&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function su(e){let t=D(null);try{return e()}finally{D(t)}}var vo="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(Kn(t,n)),this.code=t}};function Gg(e){return`NG0${Math.abs(e)}`}function Kn(e,t){return`${Gg(e)}${t?": "+t:""}`}var pn=globalThis;function P(e){for(let t in e)if(e[t]===P)return t;throw Error("")}function uu(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ie(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ie).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Io(e,t){return e?t?`${e} ${t}`:e:t||""}var zg=P({__forward_ref__:P});function Eo(e){return e.__forward_ref__=Eo,e.toString=function(){return ie(this())},e}function q(e){return Is(e)?e():e}function Is(e){return typeof e=="function"&&e.hasOwnProperty(zg)&&e.__forward_ref__===Eo}function du(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function V(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function fu(e){return{providers:e.providers||[],imports:e.imports||[]}}function Xn(e){return Zg(e,Do)}function Qg(e){return Xn(e)!==null}function Zg(e,t){return e.hasOwnProperty(t)&&e[t]||null}function Yg(e){let t=e?.[Do]??null;return t||null}function ls(e){return e&&e.hasOwnProperty(go)?e[go]:null}var Do=P({\u0275prov:P}),go=P({\u0275inj:P}),S=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=V({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Es(e){return e&&!!e.\u0275providers}var Ds=P({\u0275cmp:P}),ws=P({\u0275dir:P}),Cs=P({\u0275pipe:P}),bs=P({\u0275mod:P}),Zn=P({\u0275fac:P}),Rt=P({__NG_ELEMENT_ID__:P}),au=P({__NG_ENV_ID__:P});function X(e){return typeof e=="string"?e:e==null?"":String(e)}function pu(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():X(e)}function Ts(e,t){throw new _(-200,e)}function wo(e,t){throw new _(-201,!1)}var us;function hu(){return us}function K(e){let t=us;return us=e,t}function _s(e,t,n){let r=Xn(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;wo(e,"Injector")}var Jg={},Mt=Jg,ds="__NG_DI_FLAG__",fs=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=Nt(n)||0;try{return this.injector.get(t,r&8?null:Mt,r)}catch(o){if(ln(o))return o;throw o}}},mo="ngTempTokenPath",Kg="ngTokenPath",Xg=/\n/gm,em="\u0275",cu="__source";function tm(e,t=0){let n=ts();if(n===void 0)throw new _(-203,!1);if(n===null)return _s(e,void 0,t);{let r=nm(t),o=n.retrieve(e,r);if(ln(o)){if(r.optional)return null;throw o}return o}}function Re(e,t=0){return(hu()||tm)(q(e),t)}function w(e,t){return Re(e,Nt(t))}function Nt(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function nm(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function ps(e){let t=[];for(let n=0;n<e.length;n++){let r=q(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=rm(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(Re(o,i))}else t.push(Re(r))}return t}function Ms(e,t){return e[ds]=t,e.prototype[ds]=t,e}function rm(e){return e[ds]}function om(e,t,n,r){let o=e[mo];throw t[cu]&&o.unshift(t[cu]),e.message=im(`
`+e.message,o,n,r),e[Kg]=o,e[mo]=null,e}function im(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==em?e.slice(2):e;let o=ie(t);if(Array.isArray(t))o=t.map(ie).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ie(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Xg,`
  `)}`}function it(e,t){let n=e.hasOwnProperty(Zn);return n?e[Zn]:null}function gu(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function mu(e){return e.flat(Number.POSITIVE_INFINITY)}function Co(e,t){e.forEach(n=>Array.isArray(n)?Co(n,t):t(n))}function Ns(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function er(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function yu(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function vu(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function tr(e,t,n){let r=hn(e,t);return r>=0?e[r|1]=n:(r=~r,vu(e,r,t,n)),r}function bo(e,t){let n=hn(e,t);if(n>=0)return e[n|1]}function hn(e,t){return sm(e,t,1)}function sm(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Ee={},J=[],At=new S(""),xs=new S("",-1),Ss=new S(""),Yn=class{get(t,n=Mt){if(n===Mt)throw new ao(`NullInjectorError: No provider for ${ie(t)}!`);return n}};function Rs(e){return e[bs]||null}function De(e){return e[Ds]||null}function nr(e){return e[ws]||null}function As(e){return e[Cs]||null}function Os(e){return{\u0275providers:e}}function Iu(...e){return{\u0275providers:To(!0,e),\u0275fromNgModule:!0}}function To(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Co(t,s=>{let a=s;yo(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Eu(o,i),n}function Eu(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ks(o,i=>{t(i,r)})}}function yo(e,t,n,r){if(e=q(e),!e)return!1;let o=null,i=ls(e),s=!i&&De(e);if(!i&&!s){let c=e.ngModule;if(i=ls(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)yo(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Co(i.imports,u=>{yo(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Eu(l,t)}if(!a){let l=it(o)||(()=>new o);t({provide:o,useFactory:l,deps:J},o),t({provide:Ss,useValue:o,multi:!0},o),t({provide:At,useValue:()=>Re(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ks(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ks(e,t){for(let n of e)Es(n)&&(n=n.\u0275providers),Array.isArray(n)?ks(n,t):t(n)}var am=P({provide:String,useValue:P});function Du(e){return e!==null&&typeof e=="object"&&am in e}function cm(e){return!!(e&&e.useExisting)}function lm(e){return!!(e&&e.useFactory)}function xt(e){return typeof e=="function"}function wu(e){return!!e.useClass}var Ps=new S(""),ho={},lu={},cs;function gn(){return cs===void 0&&(cs=new Yn),cs}var ce=class{},St=class extends ce{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,gs(t,s=>this.processProvider(s)),this.records.set(xs,fn(void 0,this)),o.has("environment")&&this.records.set(ce,fn(void 0,this));let i=this.records.get(Ps);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ss,J,{self:!0}))}retrieve(t,n){let r=Nt(n)||0;try{return this.get(t,Mt,r)}catch(o){if(ln(o))return o;throw o}}destroy(){Qn(this),this._destroyed=!0;let t=D(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),D(t)}}onDestroy(t){return Qn(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){Qn(this);let n=$e(this),r=K(void 0),o;try{return t()}finally{$e(n),K(r)}}get(t,n=Mt,r){if(Qn(this),t.hasOwnProperty(au))return t[au](this);let o=Nt(r),i,s=$e(this),a=K(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let u=hm(t)&&Xn(t);u&&this.injectableDefInScope(u)?l=fn(hs(t),ho):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let c=o&2?gn():this.parent;return n=o&8&&n===Mt?null:n,c.get(t,n)}catch(c){if(ln(c)){if((c[mo]=c[mo]||[]).unshift(ie(t)),s)throw c;return om(c,t,"R3InjectorError",this.source)}else throw c}finally{K(a),$e(s)}}resolveInjectorInitializers(){let t=D(null),n=$e(this),r=K(void 0),o;try{let i=this.get(At,J,{self:!0});for(let s of i)s()}finally{$e(n),K(r),D(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ie(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=q(t);let n=xt(t)?t:q(t&&t.provide),r=dm(t);if(!xt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=fn(void 0,ho,!0),o.factory=()=>ps(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=D(null);try{return n.value===lu?Ts(ie(t)):n.value===ho&&(n.value=lu,n.value=n.factory()),typeof n.value=="object"&&n.value&&pm(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{D(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=q(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function hs(e){let t=Xn(e),n=t!==null?t.factory:it(e);if(n!==null)return n;if(e instanceof S)throw new _(204,!1);if(e instanceof Function)return um(e);throw new _(204,!1)}function um(e){if(e.length>0)throw new _(204,!1);let n=Yg(e);return n!==null?()=>n.factory(e):()=>new e}function dm(e){if(Du(e))return fn(void 0,e.useValue);{let t=Ls(e);return fn(t,ho)}}function Ls(e,t,n){let r;if(xt(e)){let o=q(e);return it(o)||hs(o)}else if(Du(e))r=()=>q(e.useValue);else if(lm(e))r=()=>e.useFactory(...ps(e.deps||[]));else if(cm(e))r=()=>Re(q(e.useExisting));else{let o=q(e&&(e.useClass||e.provide));if(fm(e))r=()=>new o(...ps(e.deps));else return it(o)||hs(o)}return r}function Qn(e){if(e.destroyed)throw new _(205,!1)}function fn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function fm(e){return!!e.deps}function pm(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function hm(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function gs(e,t){for(let n of e)Array.isArray(n)?gs(n,t):n&&Es(n)?gs(n.\u0275providers,t):t(n)}function _o(e,t){let n;e instanceof St?(Qn(e),n=e):n=new fs(e);let r,o=$e(n),i=K(void 0);try{return t()}finally{$e(o),K(i)}}function Fs(){return hu()!==void 0||ts()!=null}function js(e){if(!Fs())throw new _(-203,!1)}var pe=0,y=1,C=2,B=3,he=4,ee=5,Ot=6,mn=7,H=8,se=9,Oe=10,O=11,yn=12,Vs=13,kt=14,te=15,at=16,Pt=17,ke=18,rr=19,Hs=20,ze=21,Mo=22,Qe=23,le=24,Lt=25,k=26,Bs=1,ct=6,lt=7,or=8,Ft=9,Q=10;function Pe(e){return Array.isArray(e)&&typeof e[Bs]=="object"}function we(e){return Array.isArray(e)&&e[Bs]===!0}function No(e){return(e.flags&4)!==0}function ut(e){return e.componentOffset>-1}function ir(e){return(e.flags&1)===1}function ge(e){return!!e.template}function vn(e){return(e[C]&512)!==0}function dt(e){return(e[C]&256)===256}var $s="svg",Cu="math";function me(e){for(;Array.isArray(e);)e=e[pe];return e}function Us(e){for(;Array.isArray(e);){if(typeof e[Bs]=="object")return e;e=e[pe]}return null}function qs(e,t){return me(t[e])}function Ce(e,t){return me(t[e.index])}function jt(e,t){return e.data[t]}function sr(e,t){return e[t]}function ar(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function ye(e,t){let n=t[e];return Pe(n)?n:n[pe]}function bu(e){return(e[C]&4)===4}function xo(e){return(e[C]&128)===128}function Tu(e){return we(e[B])}function be(e,t){return t==null?null:e[t]}function Ws(e){e[Pt]=0}function Gs(e){e[C]&1024||(e[C]|=1024,xo(e)&&ft(e))}function _u(e,t){for(;e>0;)t=t[kt],e--;return t}function cr(e){return!!(e[C]&9216||e[le]?.dirty)}function So(e){e[Oe].changeDetectionScheduler?.notify(8),e[C]&64&&(e[C]|=1024),cr(e)&&ft(e)}function ft(e){e[Oe].changeDetectionScheduler?.notify(0);let t=st(e);for(;t!==null&&!(t[C]&8192||(t[C]|=8192,!xo(t)));)t=st(t)}function Ro(e,t){if(dt(e))throw new _(911,!1);e[ze]===null&&(e[ze]=[]),e[ze].push(t)}function zs(e,t){if(e[ze]===null)return;let n=e[ze].indexOf(t);n!==-1&&e[ze].splice(n,1)}function st(e){let t=e[B];return we(t)?t[B]:t}function Qs(e){return e[mn]??=[]}function Zs(e){return e.cleanup??=[]}function Mu(e,t,n,r){let o=Qs(t);o.push(n),e.firstCreatePass&&Zs(e).push(r,o.length-1)}var T={lFrame:Bu(null),bindingsEnabled:!0,skipHydrationRootTNode:null},lr=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(lr||{}),gm=0,ms=!1;function Nu(){return T.lFrame.elementDepthCount}function xu(){T.lFrame.elementDepthCount++}function Su(){T.lFrame.elementDepthCount--}function Ao(){return T.bindingsEnabled}function Ys(){return T.skipHydrationRootTNode!==null}function Ru(e){return T.skipHydrationRootTNode===e}function Au(){T.skipHydrationRootTNode=null}function m(){return T.lFrame.lView}function R(){return T.lFrame.tView}function Ou(e){return T.lFrame.contextLView=e,e[H]}function ku(e){return T.lFrame.contextLView=null,e}function L(){let e=Js();for(;e!==null&&e.type===64;)e=e.parent;return e}function Js(){return T.lFrame.currentTNode}function Pu(){let e=T.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ze(e,t){let n=T.lFrame;n.currentTNode=e,n.isParent=t}function Oo(){return T.lFrame.isParent}function ko(){T.lFrame.isParent=!1}function Ks(){return T.lFrame.contextLView}function Xs(e){du("Must never be called in production mode"),gm=e}function ea(){return ms}function In(e){let t=ms;return ms=e,t}function En(){let e=T.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function ta(){return T.lFrame.bindingIndex}function Lu(e){return T.lFrame.bindingIndex=e}function Le(){return T.lFrame.bindingIndex++}function ur(e){let t=T.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Fu(){return T.lFrame.inI18n}function ju(e,t){let n=T.lFrame;n.bindingIndex=n.bindingRootIndex=e,Po(t)}function Vu(){return T.lFrame.currentDirectiveIndex}function Po(e){T.lFrame.currentDirectiveIndex=e}function Lo(e){let t=T.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Fo(){return T.lFrame.currentQueryIndex}function dr(e){T.lFrame.currentQueryIndex=e}function mm(e){let t=e[y];return t.type===2?t.declTNode:t.type===1?e[ee]:null}function na(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=mm(i),o===null||(i=i[kt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=T.lFrame=Hu();return r.currentTNode=t,r.lView=e,!0}function jo(e){let t=Hu(),n=e[y];T.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Hu(){let e=T.lFrame,t=e===null?null:e.child;return t===null?Bu(e):t}function Bu(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function $u(){let e=T.lFrame;return T.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var ra=$u;function Vo(){let e=$u();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Uu(e){return(T.lFrame.contextLView=_u(e,T.lFrame.contextLView))[H]}function ve(){return T.lFrame.selectedIndex}function pt(e){T.lFrame.selectedIndex=e}function Dn(){let e=T.lFrame;return jt(e.tView,e.selectedIndex)}function qu(){T.lFrame.currentNamespace=$s}function Wu(){ym()}function ym(){T.lFrame.currentNamespace=null}function Gu(){return T.lFrame.currentNamespace}var zu=!0;function fr(){return zu}function pr(e){zu=e}function ys(e,t=null,n=null,r){let o=oa(e,t,n,r);return o.resolveInjectorInitializers(),o}function oa(e,t=null,n=null,r,o=new Set){let i=[n||J,Iu(e)];return r=r||(typeof e=="object"?void 0:ie(e)),new St(i,t||gn(),r||null,o)}var de=class e{static THROW_IF_NOT_FOUND=Mt;static NULL=new Yn;static create(t,n){if(Array.isArray(t))return ys({name:""},n,t,"");{let r=t.name??"";return ys({name:r},t.parent,t.providers,r)}}static \u0275prov=V({token:e,providedIn:"any",factory:()=>Re(xs)});static __NG_ELEMENT_ID__=-1},vm=new S(""),Ye=(()=>{class e{static __NG_ELEMENT_ID__=Im;static __NG_ENV_ID__=n=>n}return e})(),Jn=class extends Ye{_lView;constructor(t){super(),this._lView=t}get destroyed(){return dt(this._lView)}onDestroy(t){let n=this._lView;return Ro(n,t),()=>zs(n,t)}};function Im(){return new Jn(m())}var Ae=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Je=new S("",{providedIn:"root",factory:()=>{let e=w(ce),t;return n=>{t??=e.get(Ae),t.handleError(n)}}}),Qu={provide:At,useValue:()=>void w(Ae),multi:!0};function ia(e){return typeof e=="function"&&e[j]!==void 0}function Ho(e,t){let[n,r,o]=ss(e,t?.equal),i=n,s=i[j];return i.set=r,i.update=o,i.asReadonly=hr.bind(i),i}function hr(){let e=this[j];if(e.readonlyFn===void 0){let t=()=>this();t[j]=e,e.readonlyFn=t}return e.readonlyFn}function sa(e){return ia(e)&&typeof e.set=="function"}var fe=class{},Bo=new S("",{providedIn:"root",factory:()=>!1});var aa=new S(""),ca=new S("");var Vt=(()=>{class e{view;node;constructor(n,r){this.view=n,this.node=r}static __NG_ELEMENT_ID__=Em}return e})();function Em(){return new Vt(m(),L())}var Ht=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new jn(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new M(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})(),$o=(()=>{class e{internalPendingTasks=w(Ht);scheduler=w(fe);errorHandler=w(Je);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})();function Bt(...e){}var gr=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>new vs})}return e})(),vs=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function Sn(e){return{toString:e}.toString()}var Uo="__parameters__";function Mm(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Fd(e,t,n){return Sn(()=>{let r=Mm(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(Uo)?c[Uo]:Object.defineProperty(c,Uo,{value:[]})[Uo];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var jd=Ms(Fd("Optional"),8);var Vd=Ms(Fd("SkipSelf"),4);function Nm(e){return typeof e=="function"}var Xo=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Hd(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var xm=(()=>{let e=()=>Bd;return e.ngInherit=!0,e})();function Bd(e){return e.type.prototype.ngOnChanges&&(e.setInput=Rm),Sm}function Sm(){let e=Ud(this),t=e?.current;if(t){let n=e.previous;if(n===Ee)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Rm(e,t,n,r,o){let i=this.declaredInputs[r],s=Ud(e)||Am(e,{previous:Ee,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Xo(l&&l.currentValue,n,c===Ee),Hd(e,t,o,n)}var $d="__ngSimpleChanges__";function Ud(e){return e[$d]||null}function Am(e,t){return e[$d]=t}var Zu=[];var A=function(e,t=null,n){for(let r=0;r<Zu.length;r++){let o=Zu[r];o(e,t,n)}};function Om(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Bd(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function ac(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Qo(e,t,n){qd(e,t,3,n)}function Zo(e,t,n,r){(e[C]&3)===n&&qd(e,t,n,r)}function la(e,t){let n=e[C];(n&3)===t&&(n&=16383,n+=1,e[C]=n)}function qd(e,t,n,r){let o=r!==void 0?e[Pt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Pt]+=65536),(a<i||i==-1)&&(km(e,n,t,c),e[Pt]=(e[Pt]&**********)+c+2),c++}function Yu(e,t){A(4,e,t);let n=D(null);try{t.call(e)}finally{D(n),A(5,e,t)}}function km(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[C]>>14<e[Pt]>>16&&(e[C]&3)===t&&(e[C]+=16384,Yu(a,i)):Yu(a,i)}var Cn=-1,Ut=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function Pm(e){return(e.flags&8)!==0}function Lm(e){return(e.flags&16)!==0}function Fm(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];jm(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Wd(e){return e===3||e===4||e===6}function jm(e){return e.charCodeAt(0)===64}function Tn(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ju(e,n,o,null,t[++r]):Ju(e,n,o,null,null))}}return e}function Ju(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function Gd(e){return e!==Cn}function ei(e){return e&32767}function Vm(e){return e>>16}function ti(e,t){let n=Vm(e),r=t;for(;n>0;)r=r[kt],n--;return r}var wa=!0;function ni(e){let t=wa;return wa=e,t}var Hm=256,zd=Hm-1,Qd=5,Bm=0,Fe={};function $m(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Rt)&&(r=n[Rt]),r==null&&(r=n[Rt]=Bm++);let o=r&zd,i=1<<o;t.data[e+(o>>Qd)]|=i}function ri(e,t){let n=Zd(e,t);if(n!==-1)return n;let r=t[y];r.firstCreatePass&&(e.injectorIndex=t.length,ua(r.data,e),ua(t,null),ua(r.blueprint,null));let o=cc(e,t),i=e.injectorIndex;if(Gd(o)){let s=ei(o),a=ti(o,t),c=a[y].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function ua(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Zd(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function cc(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=ef(o),r===null)return Cn;if(n++,o=o[kt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Cn}function Ca(e,t,n){$m(e,t,n)}function Um(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Wd(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Yd(e,t,n){if(n&8||e!==void 0)return e;wo(t,"NodeInjector")}function Jd(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[se],i=K(void 0);try{return o?o.get(t,r,n&8):_s(t,r,n&8)}finally{K(i)}}return Yd(r,t,n)}function Kd(e,t,n,r=0,o){if(e!==null){if(t[C]&2048&&!(r&2)){let s=Qm(e,t,n,r,Fe);if(s!==Fe)return s}let i=Xd(e,t,n,r,Fe);if(i!==Fe)return i}return Jd(t,n,r,o)}function Xd(e,t,n,r,o){let i=Wm(n);if(typeof i=="function"){if(!na(t,e,r))return r&1?Yd(o,n,r):Jd(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))wo(n);else return s}finally{ra()}}else if(typeof i=="number"){let s=null,a=Zd(e,t),c=Cn,l=r&1?t[te][ee]:null;for((a===-1||r&4)&&(c=a===-1?cc(e,t):t[a+8],c===Cn||!Xu(r,!1)?a=-1:(s=t[y],a=ei(c),t=ti(c,t)));a!==-1;){let u=t[y];if(Ku(i,a,u.data)){let d=qm(a,t,n,s,r,l);if(d!==Fe)return d}c=t[a+8],c!==Cn&&Xu(r,t[y].data[a+8]===l)&&Ku(i,a,t)?(s=u,a=ei(c),t=ti(c,t)):a=-1}}return o}function qm(e,t,n,r,o,i){let s=t[y],a=s.data[e+8],c=r==null?ut(a)&&wa:r!=s&&(a.type&3)!==0,l=o&1&&i===a,u=Yo(a,s,n,c,l);return u!==null?vr(t,s,u,a):Fe}function Yo(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&ge(f)&&f.type===n)return c}return null}function vr(e,t,n,r){let o=e[n],i=t.data;if(o instanceof Ut){let s=o;s.resolving&&Ts(pu(i[n]));let a=ni(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],l,u=s.injectImpl?K(s.injectImpl):null,d=na(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Om(n,i[n],t)}finally{u!==null&&K(u),ni(a),s.resolving=!1,ra()}}return o}function Wm(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Rt)?e[Rt]:void 0;return typeof t=="number"?t>=0?t&zd:Gm:t}function Ku(e,t,n){let r=1<<e;return!!(n[t+(e>>Qd)]&r)}function Xu(e,t){return!(e&2)&&!(e&1&&t)}var $t=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Kd(this._tNode,this._lView,t,Nt(r),n)}};function Gm(){return new $t(L(),m())}function zm(e){return Sn(()=>{let t=e.prototype.constructor,n=t[Zn]||ba(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Zn]||ba(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ba(e){return Is(e)?()=>{let t=ba(q(e));return t&&t()}:it(e)}function Qm(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[C]&2048&&!vn(s);){let a=Xd(i,s,n,r|2,Fe);if(a!==Fe)return a;let c=i.parent;if(!c){let l=s[Hs];if(l){let u=l.get(n,Fe,r);if(u!==Fe)return u}c=ef(s),s=s[kt]}i=c}return o}function ef(e){let t=e[y],n=t.type;return n===2?t.declTNode:n===1?e[ee]:null}function tf(e){return Um(L(),e)}function Zm(){return Rn(L(),m())}function Rn(e,t){return new Mr(Ce(e,t))}var Mr=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=Zm}return e})();function nf(e){return e instanceof Mr?e.nativeElement:e}function Ym(){return this._results[Symbol.iterator]()}var oi=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new _e}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=mu(t);(this._changesDetected=!gu(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Ym};function rf(e){return(e.flags&128)===128}var lc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(lc||{}),of=new Map,Jm=0;function Km(){return Jm++}function Xm(e){of.set(e[rr],e)}function Ta(e){of.delete(e[rr])}var ed="__ngContext__";function An(e,t){Pe(t)?(e[ed]=t[rr],Xm(t)):e[ed]=t}function sf(e){return cf(e[yn])}function af(e){return cf(e[he])}function cf(e){for(;e!==null&&!we(e);)e=e[he];return e}var _a;function ey(e){_a=e}function uc(){if(_a!==void 0)return _a;if(typeof document<"u")return document;throw new _(210,!1)}var ty=new S("",{providedIn:"root",factory:()=>ny}),ny="ng",lf=new S(""),ry=new S("",{providedIn:"platform",factory:()=>"unknown"});var oy=new S(""),iy=new S("",{providedIn:"root",factory:()=>uc().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var sy="h",ay="b";var uf="r";var dc="di",fc="s";var df=!1,ff=new S("",{providedIn:"root",factory:()=>df});var pf=new S("");var cy=(e,t,n,r)=>{};function ly(e,t,n,r){cy(e,t,n,r)}var hf=new S("");var uy=()=>null;function gf(e,t,n=!1){return uy(e,t,n)}function mf(e){return e.get(pf,!1,{optional:!0})}function yf(e,t){let n=e.contentQueries;if(n!==null){let r=D(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];dr(i),a.contentQueries(2,t[s],s)}}}finally{D(r)}}}function Ma(e,t,n){dr(0);let r=D(null);try{t(e,n)}finally{D(r)}}function pc(e,t,n){if(No(t)){let r=D(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{D(r)}}}var _n=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(_n||{}),qo;function dy(){if(qo===void 0&&(qo=null,pn.trustedTypes))try{qo=pn.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return qo}function Ii(e){return dy()?.createHTML(e)||e}var Wo;function vf(){if(Wo===void 0&&(Wo=null,pn.trustedTypes))try{Wo=pn.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Wo}function td(e){return vf()?.createHTML(e)||e}function nd(e){return vf()?.createScriptURL(e)||e}var Xe=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${vo})`}},Na=class extends Xe{getTypeName(){return"HTML"}},xa=class extends Xe{getTypeName(){return"Style"}},Sa=class extends Xe{getTypeName(){return"Script"}},Ra=class extends Xe{getTypeName(){return"URL"}},Aa=class extends Xe{getTypeName(){return"ResourceURL"}};function zt(e){return e instanceof Xe?e.changingThisBreaksApplicationSecurity:e}function Ei(e,t){let n=If(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${vo})`)}return n===t}function If(e){return e instanceof Xe&&e.getTypeName()||null}function fy(e){return new Na(e)}function py(e){return new xa(e)}function hy(e){return new Sa(e)}function gy(e){return new Ra(e)}function my(e){return new Aa(e)}function yy(e){let t=new ka(e);return vy()?new Oa(t):t}var Oa=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Ii(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},ka=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Ii(t),n}};function vy(){try{return!!new window.DOMParser().parseFromString(Ii(""),"text/html")}catch{return!1}}var Iy=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function hc(e){return e=String(e),e.match(Iy)?e:"unsafe:"+e}function et(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Nr(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Ef=et("area,br,col,hr,img,wbr"),Df=et("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),wf=et("rp,rt"),Ey=Nr(wf,Df),Dy=Nr(Df,et("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),wy=Nr(wf,et("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),rd=Nr(Ef,Dy,wy,Ey),Cf=et("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Cy=et("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),by=et("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),Ty=Nr(Cf,Cy,by),_y=et("script,style,template"),Pa=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=xy(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Ny(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=od(t).toLowerCase();if(!rd.hasOwnProperty(n))return this.sanitizedSomething=!0,!_y.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!Ty.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Cf[a]&&(c=hc(c)),this.buf.push(" ",s,'="',id(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=od(t).toLowerCase();rd.hasOwnProperty(n)&&!Ef.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(id(t))}};function My(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Ny(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw bf(t);return t}function xy(e){let t=e.firstChild;if(t&&My(e,t))throw bf(t);return t}function od(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function bf(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Sy=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,Ry=/([^\#-~ |!])/g;function id(e){return e.replace(/&/g,"&amp;").replace(Sy,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(Ry,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Go;function Tf(e,t){let n=null;try{Go=Go||yy(e);let r=t?String(t):"";n=Go.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Go.getInertBodyElement(r)}while(r!==i);let a=new Pa().sanitizeChildren(sd(n)||n);return Ii(a)}finally{if(n){let r=sd(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function sd(e){return"content"in e&&Ay(e)?e.content:null}function Ay(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var xr=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(xr||{});function Oy(e){let t=gc();return t?td(t.sanitize(xr.HTML,e)||""):Ei(e,"HTML")?td(zt(e)):Tf(uc(),X(e))}function _f(e){let t=gc();return t?t.sanitize(xr.URL,e)||"":Ei(e,"URL")?zt(e):hc(X(e))}function Mf(e){let t=gc();if(t)return nd(t.sanitize(xr.RESOURCE_URL,e)||"");if(Ei(e,"ResourceURL"))return nd(zt(e));throw new _(904,!1)}function ky(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Mf:_f}function Py(e,t,n){return ky(t,n)(e)}function gc(){let e=m();return e&&e[Oe].sanitizer}var Ly=/^>|^->|<!--|-->|--!>|<!-$/g,Fy=/(<|>)/g,jy="\u200B$1\u200B";function Vy(e){return e.replace(Ly,t=>t.replace(Fy,jy))}function Hy(e){return e.ownerDocument}function Nf(e){return e instanceof Function?e():e}function By(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var xf="ng-template";function $y(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&By(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(mc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function mc(e){return e.type===4&&e.value!==xf}function Uy(e,t,n){let r=e.type===4&&!n?xf:e.value;return t===r}function qy(e,t,n){let r=4,o=e.attrs,i=o!==null?zy(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Te(r)&&!Te(c))return!1;if(s&&Te(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Uy(e,c,n)||c===""&&t.length===1){if(Te(r))return!1;s=!0}}else if(r&8){if(o===null||!$y(e,o,c,n)){if(Te(r))return!1;s=!0}}else{let l=t[++a],u=Wy(c,o,mc(e),n);if(u===-1){if(Te(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(Te(r))return!1;s=!0}}}}return Te(r)||s}function Te(e){return(e&1)===0}function Wy(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Qy(t,e)}function Sf(e,t,n=!1){for(let r=0;r<t.length;r++)if(qy(e,t[r],n))return!0;return!1}function Gy(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function zy(e){for(let t=0;t<e.length;t++){let n=e[t];if(Wd(n))return t}return e.length}function Qy(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Zy(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function ad(e,t){return e?":not("+t.trim()+")":t}function Yy(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Te(s)&&(t+=ad(i,o),o=""),r=s,i=i||!Te(r);n++}return o!==""&&(t+=ad(i,o)),t}function Jy(e){return e.map(Yy).join(",")}function Ky(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Te(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Z={};function Xy(e,t){return e.createText(t)}function ev(e,t,n){e.setValue(t,n)}function tv(e,t){return e.createComment(Vy(t))}function Rf(e,t,n){return e.createElement(t,n)}function ii(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Af(e,t,n){e.appendChild(t,n)}function cd(e,t,n,r,o){r!==null?ii(e,t,n,r,o):Af(e,t,n)}function Of(e,t,n){e.removeChild(null,t,n)}function nv(e,t,n){e.setAttribute(t,"style",n)}function rv(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function kf(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&Fm(e,t,r),o!==null&&rv(e,t,o),i!==null&&nv(e,t,i)}function yc(e,t,n,r,o,i,s,a,c,l,u){let d=k+r,p=d+o,f=ov(d,p),h=typeof l=="function"?l():l;return f[y]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:u}}function ov(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Z);return n}function iv(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=yc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function vc(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[pe]=o,d[C]=r|4|128|8|64|1024,(l!==null||e&&e[C]&2048)&&(d[C]|=2048),Ws(d),d[B]=d[kt]=e,d[H]=n,d[Oe]=s||e&&e[Oe],d[O]=a||e&&e[O],d[se]=c||e&&e[se]||null,d[ee]=i,d[rr]=Km(),d[Ot]=u,d[Hs]=l,d[te]=t.type==2?e[te]:d,d}function sv(e,t,n){let r=Ce(t,e),o=iv(n),i=e[Oe].rendererFactory,s=Ic(e,vc(e,o,null,Pf(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function Pf(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Lf(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ic(e,t){return e[yn]?e[Vs][he]=t:e[yn]=t,e[Vs]=t,t}function av(e=1){Ff(R(),m(),ve()+e,!1)}function Ff(e,t,n,r){if(!r)if((t[C]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Qo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Zo(t,i,0,n)}pt(n)}var Di=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Di||{});function La(e,t,n,r){let o=D(null);try{let[i,s,a]=e.inputs[n],c=null;(s&Di.SignalBased)!==0&&(c=t[i][j]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):Hd(t,c,i,r)}finally{D(o)}}function jf(e,t,n,r,o){let i=ve(),s=r&2;try{pt(-1),s&&t.length>k&&Ff(e,t,k,!1),A(s?2:0,o,n),n(r,o)}finally{pt(i),A(s?3:1,o,n)}}function wi(e,t,n){pv(e,t,n),(n.flags&64)===64&&hv(e,t,n)}function Ec(e,t,n=Ce){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function cv(e,t,n,r){let i=r.get(ff,df)||n===_n.ShadowDom,s=e.selectRootElement(t,i);return lv(s),s}function lv(e){uv(e)}var uv=()=>null;function dv(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Vf(e,t,n,r,o,i){let s=t[y];if(bc(e,s,t,n,r)){ut(e)&&fv(t,e.index);return}Dc(e,t,n,r,o,i)}function Dc(e,t,n,r,o,i){if(e.type&3){let s=Ce(e,t);n=dv(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function fv(e,t){let n=ye(t,e);n[C]&16||(n[C]|=64)}function pv(e,t,n){let r=n.directiveStart,o=n.directiveEnd;ut(n)&&sv(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||ri(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=vr(t,e,s,n);if(An(c,t),i!==null&&vv(t,s-r,c,a,n,i),ge(a)){let l=ye(n.index,t);l[H]=vr(t,e,s,n)}}}function hv(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Vu();try{pt(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Po(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&gv(c,l)}}finally{pt(-1),Po(s)}}function gv(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function wc(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];Sf(t,i.selectors,!1)&&(r??=[],ge(i)?r.unshift(i):r.push(i))}return r}function mv(e,t,n,r,o,i){let s=Ce(e,t);yv(t[O],s,i,e.value,n,r,o)}function yv(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?X(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function vv(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];La(r,n,c,l)}}function Hf(e,t,n){return(e===null||ge(e))&&(n=Us(n[t.index])),n[O]}function Cc(e,t){let n=e[se];if(!n)return;n.get(Je,null)?.(t)}function bc(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];La(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];La(u,l,r,o),a=!0}return a}function Iv(e,t){let n=ye(t,e),r=n[y];Ev(r,n);let o=n[pe];o!==null&&n[Ot]===null&&(n[Ot]=gf(o,n[se])),A(18),Tc(r,n,n[H]),A(19,n[H])}function Ev(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Tc(e,t,n){jo(t);try{let r=e.viewQuery;r!==null&&Ma(1,r,n);let o=e.template;o!==null&&jf(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ke]?.finishViewCreation(e),e.staticContentQueries&&yf(e,t),e.staticViewQueries&&Ma(2,e.viewQuery,n);let i=e.components;i!==null&&Dv(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[C]&=-5,Vo()}}function Dv(e,t){for(let n=0;n<t.length;n++)Iv(e,t[n])}function On(e,t,n,r){let o=D(null);try{let i=t.tView,a=e[C]&4096?4096:16,c=vc(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[at]=l;let u=e[ke];return u!==null&&(c[ke]=u.createEmbeddedView(i)),Tc(i,c,n),c}finally{D(o)}}function qt(e,t){return!t||t.firstChild===null||rf(e)}var ld=!1,wv=new S(""),Cv;function _c(e,t){return Cv(e,t)}var si=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(si||{});function Ci(e){return(e.flags&32)===32}function wn(e,t,n,r,o){if(r!=null){let i,s=!1;we(r)?i=r:Pe(r)&&(s=!0,r=r[pe]);let a=me(r);e===0&&n!==null?o==null?Af(t,n,a):ii(t,n,a,o||null,!0):e===1&&n!==null?ii(t,n,a,o||null,!0):e===2?Of(t,a,s):e===3&&t.destroyNode(a),i!=null&&Ov(t,e,i,n,o)}}function bv(e,t){Bf(e,t),t[pe]=null,t[ee]=null}function Tv(e,t,n,r,o,i){r[pe]=o,r[ee]=t,_i(e,r,n,1,o,i)}function Bf(e,t){t[Oe].changeDetectionScheduler?.notify(9),_i(e,t,t[O],2,null,null)}function _v(e){let t=e[yn];if(!t)return da(e[y],e);for(;t;){let n=null;if(Pe(t))n=t[yn];else{let r=t[Q];r&&(n=r)}if(!n){for(;t&&!t[he]&&t!==e;)Pe(t)&&da(t[y],t),t=t[B];t===null&&(t=e),Pe(t)&&da(t[y],t),n=t&&t[he]}t=n}}function Mc(e,t){let n=e[Ft],r=n.indexOf(t);n.splice(r,1)}function bi(e,t){if(dt(t))return;let n=t[O];n.destroyNode&&_i(e,t,n,3,null,null),_v(t)}function da(e,t){if(dt(t))return;let n=D(null);try{t[C]&=-129,t[C]|=256,t[le]&&dn(t[le]),Nv(e,t),Mv(e,t),t[y].type===1&&t[O].destroy();let r=t[at];if(r!==null&&we(t[B])){r!==t[B]&&Mc(r,t);let o=t[ke];o!==null&&o.detachView(e)}Ta(t)}finally{D(n)}}function Mv(e,t){let n=e.cleanup,r=t[mn];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[mn]=null);let o=t[ze];if(o!==null){t[ze]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Qe];if(i!==null){t[Qe]=null;for(let s of i)s.destroy()}}function Nv(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Ut)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];A(4,a,c);try{c.call(a)}finally{A(5,a,c)}}else{A(4,o,i);try{i.call(o)}finally{A(5,o,i)}}}}}function $f(e,t,n){return xv(e,t.parent,n)}function xv(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[pe];if(ut(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===_n.None||o===_n.Emulated)return null}return Ce(r,n)}function Uf(e,t,n){return Rv(e,t,n)}function Sv(e,t,n){return e.type&40?Ce(e,n):null}var Rv=Sv,ud;function Ti(e,t,n,r){let o=$f(e,r,t),i=t[O],s=r.parent||t[ee],a=Uf(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)cd(i,o,n[c],a,!1);else cd(i,o,n,a,!1);ud!==void 0&&ud(i,r,t,n,o)}function mr(e,t){if(t!==null){let n=t.type;if(n&3)return Ce(t,e);if(n&4)return Fa(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return mr(e,r);{let o=e[t.index];return we(o)?Fa(-1,o):me(o)}}else{if(n&128)return mr(e,t.next);if(n&32)return _c(t,e)()||me(e[t.index]);{let r=qf(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=st(e[te]);return mr(o,r)}else return mr(e,t.next)}}}return null}function qf(e,t){if(t!==null){let r=e[te][ee],o=t.projection;return r.projection[o]}return null}function Fa(e,t){let n=Q+e+1;if(n<t.length){let r=t[n],o=r[y].firstChild;if(o!==null)return mr(r,o)}return t[lt]}function Nc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&An(me(a),r),n.flags|=2),!Ci(n))if(c&8)Nc(e,t,n.child,r,o,i,!1),wn(t,e,o,a,i);else if(c&32){let l=_c(n,r),u;for(;u=l();)wn(t,e,o,u,i);wn(t,e,o,a,i)}else c&16?Wf(e,t,r,n,o,i):wn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function _i(e,t,n,r,o,i){Nc(n,r,e.firstChild,t,o,i,!1)}function Av(e,t,n){let r=t[O],o=$f(e,n,t),i=n.parent||t[ee],s=Uf(i,n,t);Wf(r,0,t,n,o,s)}function Wf(e,t,n,r,o,i){let s=n[te],c=s[ee].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];wn(t,e,o,u,i)}else{let l=c,u=s[B];rf(r)&&(l.flags|=128),Nc(e,t,l,u,o,i,!0)}}function Ov(e,t,n,r,o){let i=n[lt],s=me(n);i!==s&&wn(t,e,r,i,o);for(let a=Q;a<n.length;a++){let c=n[a];_i(c[y],c,e,t,r,i)}}function kv(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:si.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=si.Important),e.setStyle(n,r,o,i))}}function Ir(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(me(i)),we(i)&&Gf(i,r);let s=n.type;if(s&8)Ir(e,t,n.child,r);else if(s&32){let a=_c(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=qf(t,n);if(Array.isArray(a))r.push(...a);else{let c=st(t[te]);Ir(c[y],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Gf(e,t){for(let n=Q;n<e.length;n++){let r=e[n],o=r[y].firstChild;o!==null&&Ir(r[y],r,o,t)}e[lt]!==e[pe]&&t.push(e[lt])}function zf(e){if(e[Lt]!==null){for(let t of e[Lt])t.impl.addSequence(t);e[Lt].length=0}}var Qf=[];function Pv(e){return e[le]??Lv(e)}function Lv(e){let t=Qf.pop()??Object.create(jv);return t.lView=e,t}function Fv(e){e.lView[le]!==e&&(e.lView=null,Qf.push(e))}var jv=z(G({},qe),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ft(e.lView)},consumerOnSignalRead(){this.lView[le]=this}});function Vv(e){let t=e[le]??Object.create(Hv);return t.lView=e,t}var Hv=z(G({},qe),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=st(e.lView);for(;t&&!Zf(t[y]);)t=st(t);t&&Gs(t)},consumerOnSignalRead(){this.lView[le]=this}});function Zf(e){return e.type!==2}function Yf(e){if(e[Qe]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Qe])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[C]&8192)}}var Bv=100;function xc(e,t=0){let r=e[Oe].rendererFactory,o=!1;o||r.begin?.();try{$v(e,t)}finally{o||r.end?.()}}function $v(e,t){let n=ea();try{In(!0),ja(e,t);let r=0;for(;cr(e);){if(r===Bv)throw new _(103,!1);r++,ja(e,1)}}finally{In(n)}}function Jf(e,t){Xs(t?lr.Exhaustive:lr.OnlyDirtyViews);try{xc(e)}finally{Xs(lr.Off)}}function Uv(e,t,n,r){if(dt(t))return;let o=t[C],i=!1,s=!1;jo(t);let a=!0,c=null,l=null;i||(Zf(e)?(l=Pv(t),c=Se(l)):lo()===null?(a=!1,l=Vv(t),c=Se(l)):t[le]&&(dn(t[le]),t[le]=null));try{Ws(t),Lu(e.bindingStartIndex),n!==null&&jf(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&Qo(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Zo(t,f,0,null),la(t,0)}if(s||qv(t),Yf(t),Kf(t,0),e.contentQueries!==null&&yf(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&Qo(t,f)}else{let f=e.contentHooks;f!==null&&Zo(t,f,1),la(t,1)}Gv(e,t);let d=e.components;d!==null&&ep(t,d,0);let p=e.viewQuery;if(p!==null&&Ma(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&Qo(t,f)}else{let f=e.viewHooks;f!==null&&Zo(t,f,2),la(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Mo]){for(let f of t[Mo])f();t[Mo]=null}i||(zf(t),t[C]&=-73)}catch(u){throw i||ft(t),u}finally{l!==null&&(Ge(l,c),a&&Fv(l)),Vo()}}function Kf(e,t){for(let n=sf(e);n!==null;n=af(n))for(let r=Q;r<n.length;r++){let o=n[r];Xf(o,t)}}function qv(e){for(let t=sf(e);t!==null;t=af(t)){if(!(t[C]&2))continue;let n=t[Ft];for(let r=0;r<n.length;r++){let o=n[r];Gs(o)}}}function Wv(e,t,n){A(18);let r=ye(t,e);Xf(r,n),A(19,r[H])}function Xf(e,t){xo(e)&&ja(e,t)}function ja(e,t){let r=e[y],o=e[C],i=e[le],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&_t(i)),s||=!1,i&&(i.dirty=!1),e[C]&=-9217,s)Uv(r,e,r.template,e[H]);else if(o&8192){let a=D(null);try{Yf(e),Kf(e,1);let c=r.components;c!==null&&ep(e,c,1),zf(e)}finally{D(a)}}}function ep(e,t,n){for(let r=0;r<t.length;r++)Wv(e,t[r],n)}function Gv(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)pt(~o);else{let i=o,s=n[++r],a=n[++r];ju(s,i);let c=t[i];A(24,c),a(2,c),A(25,c)}}}finally{pt(-1)}}function Mi(e,t){let n=ea()?64:1088;for(e[Oe].changeDetectionScheduler?.notify(t);e;){e[C]|=n;let r=st(e);if(vn(e)&&!r)return e;e=r}return null}function tp(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function np(e,t){let n=Q+t;if(n<e.length)return e[n]}function kn(e,t,n,r=!0){let o=t[y];if(zv(o,t,e,n),r){let s=Fa(n,e),a=t[O],c=a.parentNode(e[lt]);c!==null&&Tv(o,e[ee],a,t,c,s)}let i=t[Ot];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Sc(e,t){let n=Er(e,t);return n!==void 0&&bi(n[y],n),n}function Er(e,t){if(e.length<=Q)return;let n=Q+t,r=e[n];if(r){let o=r[at];o!==null&&o!==e&&Mc(o,r),t>0&&(e[n-1][he]=r[he]);let i=er(e,Q+t);bv(r[y],r);let s=i[ke];s!==null&&s.detachView(i[y]),r[B]=null,r[he]=null,r[C]&=-129}return r}function zv(e,t,n,r){let o=Q+r,i=n.length;r>0&&(n[o-1][he]=t),r<i-Q?(t[he]=n[o],Ns(n,Q+r,t)):(n.push(t),t[he]=null),t[B]=n;let s=t[at];s!==null&&n!==s&&rp(s,t);let a=t[ke];a!==null&&a.insertView(e),So(t),t[C]|=128}function rp(e,t){let n=e[Ft],r=t[B];if(Pe(r))e[C]|=2;else{let o=r[B][te];t[te]!==o&&(e[C]|=2)}n===null?e[Ft]=[t]:n.push(t)}var gt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[y];return Ir(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[H]}set context(t){this._lView[H]=t}get destroyed(){return dt(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[B];if(we(t)){let n=t[or],r=n?n.indexOf(this):-1;r>-1&&(Er(t,r),er(n,r))}this._attachedToViewContainer=!1}bi(this._lView[y],this._lView)}onDestroy(t){Ro(this._lView,t)}markForCheck(){Mi(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[C]&=-129}reattach(){So(this._lView),this._lView[C]|=128}detectChanges(){this._lView[C]|=1024,xc(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[se].get(wv,ld)}catch{this.exhaustive=ld}}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=vn(this._lView),n=this._lView[at];n!==null&&!t&&Mc(n,this._lView),Bf(this._lView[y],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=vn(this._lView),r=this._lView[at];r!==null&&!n&&rp(r,this._lView),So(this._lView)}};var Dr=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Qv;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=On(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new gt(i)}}return e})();function Qv(){return Ni(L(),m())}function Ni(e,t){return e.type&4?new Dr(t,e,Rn(e,t)):null}function Pn(e,t,n,r,o){let i=e.data[t];if(i===null)i=Zv(e,t,n,r,o),Fu()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Pu();i.injectorIndex=s===null?-1:s.injectorIndex}return Ze(i,!0),i}function Zv(e,t,n,r,o){let i=Js(),s=Oo(),a=s?i:i&&i.parent,c=e.data[t]=Jv(e,a,n,t,r,o);return Yv(e,c,i,s),c}function Yv(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function Jv(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Ys()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var EA=new RegExp(`^(\\d+)*(${ay}|${sy})*(.*)`);function Kv(e){let t=e[ct]??[],r=e[B][O],o=[];for(let i of t)i.data[dc]!==void 0?o.push(i):Xv(i,r);e[ct]=o}function Xv(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[uf];for(;n<o;){let i=r.nextSibling;Of(t,r,!1),r=i,n++}}}var eI=()=>null,tI=()=>null;function ai(e,t){return eI(e,t)}function op(e,t,n){return tI(e,t,n)}var ip=class{},xi=class{},Va=class{resolveComponentFactory(t){throw new _(917,!1)}},Sr=class{static NULL=new Va},wr=class{},nI=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>rI()}return e})();function rI(){let e=m(),t=L(),n=ye(t.index,e);return(Pe(n)?n:e)[O]}var sp=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:()=>null})}return e})();var Jo={},bn=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,Jo,r);return o!==Jo||n===Jo?o:this.parentInjector.get(t,n,r)}};function Ha(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Io(o,a);else if(i==2){let c=a,l=t[++s];r=Io(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function Rr(e,t=0){let n=m();if(n===null)return Re(e,t);let r=L();return Kd(r,n,q(e),t)}function oI(){let e="invalid";throw new Error(e)}function Rc(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,c,l]=u.resolveHostDirectives(s);break}aI(e,t,n,a,i,c,l)}i!==null&&r!==null&&iI(n,r,i)}function iI(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function sI(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function aI(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&ge(f)&&(c=!0,sI(e,n,p)),Ca(ri(n,t),e,f.type)}pI(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=Lf(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=Tn(n.mergedAttrs,f.hostAttrs),lI(e,n,t,d,f),fI(d,f,o),s!==null&&s.has(f)){let[g,N]=s.get(f);n.directiveToIndex.set(f.type,[d,g+n.directiveStart,N+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let h=f.type.prototype;!l&&(h.ngOnChanges||h.ngOnInit||h.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(h.ngOnChanges||h.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}cI(e,n,i)}function cI(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))dd(0,t,o,r),dd(1,t,o,r),pd(t,r,!1);else{let i=n.get(o);fd(0,t,i,r),fd(1,t,i,r),pd(t,r,!0)}}}function dd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),ap(t,i)}}function fd(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),ap(t,s)}}function ap(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function pd(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||mc(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function lI(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=it(o.type,!0)),s=new Ut(i,ge(o),Rr);e.blueprint[r]=s,n[r]=s,uI(e,t,r,Lf(e,n,o.hostVars,Z),o)}function uI(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;dI(s)!=a&&s.push(a),s.push(n,r,i)}}function dI(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function fI(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;ge(t)&&(n[""]=e)}}function pI(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function cp(e,t,n,r,o,i,s,a){let c=t.consts,l=be(c,s),u=Pn(t,e,2,r,l);return i&&Rc(t,n,u,be(c,a),o),u.mergedAttrs=Tn(u.mergedAttrs,u.attrs),u.attrs!==null&&Ha(u,u.attrs,!1),u.mergedAttrs!==null&&Ha(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function lp(e,t){ac(e,t),No(t)&&e.queries.elementEnd(t)}function Ac(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Oc(e,t,n){return e[t]=n}function hI(e,t){return e[t]}function ae(e,t,n){if(n===Z)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Cr(e,t,n,r){let o=ae(e,t,n);return ae(e,t+1,r)||o}function gI(e,t,n,r,o,i){let s=Cr(e,t,n,r);return Cr(e,t+2,o,i)||s}function fa(e,t,n){return function r(o){let i=ut(e)?ye(e.index,t):t;Mi(i,5);let s=t[H],a=hd(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=hd(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function hd(e,t,n,r){let o=D(null);try{return A(6,t,n),n(r)!==!1}catch(i){return Cc(e,i),!1}finally{A(7,t,n),D(o)}}function mI(e,t,n,r,o,i,s,a){let c=ir(e),l=!1,u=null;if(!r&&c&&(u=yI(t,n,i,e.index)),u!==null){let d=u.__ngLastListenerFn__||u;d.__ngNextListenerFn__=s,u.__ngLastListenerFn__=s,l=!0}else{let d=Ce(e,n),p=r?r(d):d;ly(n,p,i,a);let f=o.listen(p,i,a),h=r?g=>r(me(g[e.index])):e.index;up(h,t,n,i,a,f,!1)}return l}function yI(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[mn],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function up(e,t,n,r,o,i,s){let a=t.firstCreatePass?Zs(t):null,c=Qs(n),l=c.length;c.push(o,i),a&&a.push(r,e,l,(l+1)*(s?-1:1))}function gd(e,t,n,r,o,i){let s=t[n],a=t[y],l=a.data[n].outputs[r],d=s[l].subscribe(i);up(e.index,a,t,o,i,d,!0)}var Ba=Symbol("BINDING");var ci=class extends Sr{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=De(t);return new mt(n,this.ngModule)}};function vI(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&Di.SignalBased)!==0};return o&&(i.transform=o),i})}function II(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function EI(e,t,n){let r=t instanceof ce?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new bn(n,r):n}function DI(e){let t=e.get(wr,null);if(t===null)throw new _(407,!1);let n=e.get(sp,null),r=e.get(fe,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function wI(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Rf(t,n,n==="svg"?$s:n==="math"?Cu:null)}var mt=class extends xi{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=vI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=II(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=Jy(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){A(22);let a=D(null);try{let c=this.componentDef,l=CI(r,c,s,i),u=EI(c,o||this.ngModule,t),d=DI(u),p=d.rendererFactory.createRenderer(null,c),f=r?cv(p,r,c.encapsulation,u):wI(c,p),h=s?.some(md)||i?.some(b=>typeof b!="function"&&b.bindings.some(md)),g=vc(null,l,null,512|Pf(c),null,null,d,p,u,null,gf(f,u,!0));g[k]=f,jo(g);let N=null;try{let b=cp(k,l,g,"#host",()=>l.directiveRegistry,!0,0);f&&(kf(p,f,b),An(f,g)),wi(l,g,b),pc(l,b,g),lp(l,b),n!==void 0&&TI(b,this.ngContentSelectors,n),N=ye(b.index,g),g[H]=N[H],Tc(l,g,null)}catch(b){throw N!==null&&Ta(N),Ta(g),b}finally{A(23),Vo()}return new li(this.componentType,g,!!h)}finally{D(a)}}};function CI(e,t,n,r){let o=e?["ng-version","20.0.3"]:Ky(t.selectors[0]),i=null,s=null,a=0;if(n)for(let u of n)a+=u[Ba].requiredVars,u.create&&(u.targetIdx=0,(i??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(r)for(let u=0;u<r.length;u++){let d=r[u];if(typeof d!="function")for(let p of d.bindings){a+=p[Ba].requiredVars;let f=u+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let u of r){let d=typeof u=="function"?u:u.type,p=nr(d);c.push(p)}return yc(0,null,bI(i,s),1,a,c,null,null,null,[o],null)}function bI(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function md(e){let t=e[Ba].kind;return t==="input"||t==="twoWay"}var li=class extends ip{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=jt(n[y],k),this.location=Rn(this._tNode,n),this.instance=ye(this._tNode.index,n)[H],this.hostView=this.changeDetectorRef=new gt(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=bc(r,o[y],o,t,n);this.previousInputValues.set(t,n);let s=ye(r.index,o);Mi(s,1)}get injector(){return new $t(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function TI(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Si=(()=>{class e{static __NG_ELEMENT_ID__=_I}return e})();function _I(){let e=L();return fp(e,m())}var MI=Si,dp=class extends MI{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Rn(this._hostTNode,this._hostLView)}get injector(){return new $t(this._hostTNode,this._hostLView)}get parentInjector(){let t=cc(this._hostTNode,this._hostLView);if(Gd(t)){let n=ti(t,this._hostLView),r=ei(t),o=n[y].data[r+8];return new $t(o,n)}else return new $t(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=yd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Q}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=ai(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,qt(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!Nm(t),l;if(c)l=n;else{let N=n||{};l=N.index,r=N.injector,o=N.projectableNodes,i=N.environmentInjector||N.ngModuleRef,s=N.directives,a=N.bindings}let u=c?t:new mt(De(t)),d=r||this.parentInjector;if(!i&&u.ngModule==null){let b=(c?d:this.parentInjector).get(ce,null);b&&(i=b)}let p=De(u.componentType??{}),f=ai(this._lContainer,p?.id??null),h=f?.firstChild??null,g=u.create(d,o,h,i,s,a);return this.insertImpl(g.hostView,l,qt(this._hostTNode,f)),g}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Tu(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[B],l=new dp(c,c[ee],c[B]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return kn(s,o,i,r),t.attachToViewContainerRef(),Ns(pa(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=yd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Er(this._lContainer,n);r&&(er(pa(this._lContainer),n),bi(r[y],r))}detach(t){let n=this._adjustIndex(t,-1),r=Er(this._lContainer,n);return r&&er(pa(this._lContainer),n)!=null?new gt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function yd(e){return e[or]}function pa(e){return e[or]||(e[or]=[])}function fp(e,t){let n,r=t[e.index];return we(r)?n=r:(n=tp(r,t,null,e),t[e.index]=n,Ic(t,n)),xI(n,t,e,r),new dp(n,e,t)}function NI(e,t){let n=e[O],r=n.createComment(""),o=Ce(t,e),i=n.parentNode(o);return ii(n,i,r,n.nextSibling(o),!1),r}var xI=RI,SI=()=>!1;function pp(e,t,n){return SI(e,t,n)}function RI(e,t,n,r){if(e[lt])return;let o;n.type&8?o=me(r):o=NI(t,n),e[lt]=o}var $a=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Ua=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Pc(t,n).matches!==null&&this.queries[n].setDirty()}},ui=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=LI(t):this.predicate=t}},qa=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},Wa=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,AI(n,i)),this.matchTNodeWithReadOption(t,n,Yo(n,t,i,!1,!1))}else r===Dr?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Yo(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Mr||o===Si||o===Dr&&n.type&4)this.addMatch(n.index,-2);else{let i=Yo(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function AI(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function OI(e,t){return e.type&11?Rn(e,t):e.type&4?Ni(e,t):null}function kI(e,t,n,r){return n===-1?OI(t,e):n===-2?PI(e,t,r):vr(e,e[y],n,t)}function PI(e,t,n){if(n===Mr)return Rn(t,e);if(n===Dr)return Ni(t,e);if(n===Si)return fp(t,e)}function hp(e,t,n,r){let o=t[ke].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(kI(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function Ga(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=hp(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=Q;d<u.length;d++){let p=u[d];p[at]===p[B]&&Ga(p[y],p,l,r)}if(u[Ft]!==null){let d=u[Ft];for(let p=0;p<d.length;p++){let f=d[p];Ga(f[y],f,l,r)}}}}}return r}function kc(e,t){return e[ke].queries[t].queryList}function gp(e,t,n){let r=new oi((n&4)===4);return Mu(e,t,r,r.destroy),(t[ke]??=new Ua).queries.push(new $a(r))-1}function mp(e,t,n){let r=R();return r.firstCreatePass&&(vp(r,new ui(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),gp(r,m(),t)}function yp(e,t,n,r){let o=R();if(o.firstCreatePass){let i=L();vp(o,new ui(t,n,r),i.index),FI(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return gp(o,m(),n)}function LI(e){return e.split(",").map(t=>t.trim())}function vp(e,t,n){e.queries===null&&(e.queries=new qa),e.queries.track(new Wa(t,n))}function FI(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function Pc(e,t){return e.queries.getByIndex(t)}function Ip(e,t){let n=e[y],r=Pc(n,t);return r.crossesNgTemplate?Ga(n,e,t,[]):hp(n,e,r,t)}function Lc(e,t,n){let r,o=Gn(()=>{r._dirtyCounter();let i=jI(r,e);if(t&&i===void 0)throw new _(-951,!1);return i});return r=o[j],r._dirtyCounter=Ho(0),r._flatValue=void 0,o}function Fc(e){return Lc(!0,!1,e)}function jc(e){return Lc(!0,!0,e)}function Vc(e){return Lc(!1,!1,e)}function Ep(e,t){let n=e[j];n._lView=m(),n._queryIndex=t,n._queryList=kc(n._lView,t),n._queryList.onDirty(()=>n._dirtyCounter.update(r=>r+1))}function jI(e,t){let n=e._lView,r=e._queryIndex;if(n===void 0||r===void 0||n[C]&4)return t?void 0:J;let o=kc(n,r),i=Ip(n,r);return o.reset(i,nf),t?o.first:o._changesDetected||e._flatValue===void 0?e._flatValue=o.toArray():e._flatValue}var vd=new Set;function je(e){vd.has(e)||(vd.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Mn=class{},Dp=class{};var di=class extends Mn{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new ci(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=Rs(t);this._bootstrapComponents=Nf(i.bootstrap),this._r3Injector=oa(t,n,[{provide:Mn,useValue:this},{provide:Sr,useValue:this.componentFactoryResolver},...r],ie(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},fi=class extends Dp{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new di(this.moduleType,t,[])}};var br=class extends Mn{injector;componentFactoryResolver=new ci(this);instance=null;constructor(t){super();let n=new St([...t.providers,{provide:Mn,useValue:this},{provide:Sr,useValue:this.componentFactoryResolver}],t.parent||gn(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Hc(e,t,n=null){return new br({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var VI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=To(!1,n.type),o=r.length>0?Hc([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=V({token:e,providedIn:"environment",factory:()=>new e(Re(ce))})}return e})();function HI(e){return Sn(()=>{let t=wp(e),n=z(G({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===lc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(VI).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||_n.Emulated,styles:e.styles||J,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&je("NgStandalone"),Cp(n);let r=e.dependencies;return n.directiveDefs=Id(r,!1),n.pipeDefs=Id(r,!0),n.id=QI(n),n})}function BI(e){return De(e)||nr(e)}function $I(e){return e!==null}function UI(e){return Sn(()=>({type:e.type,bootstrap:e.bootstrap||J,declarations:e.declarations||J,imports:e.imports||J,exports:e.exports||J,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function qI(e,t){if(e==null)return Ee;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=Di.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function WI(e){if(e==null)return Ee;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function GI(e){return Sn(()=>{let t=wp(e);return Cp(t),t})}function zI(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function wp(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||Ee,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||J,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:qI(e.inputs,t),outputs:WI(e.outputs),debugInfo:null}}function Cp(e){e.features?.forEach(t=>t(e))}function Id(e,t){if(!e)return null;let n=t?As:BI;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter($I)}function QI(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function ZI(e){return Object.getPrototypeOf(e.prototype).constructor}function bp(e){let t=ZI(e.type),n=!0,r=[e];for(;t;){let o;if(ge(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=ha(e.inputs),s.declaredInputs=ha(e.declaredInputs),s.outputs=ha(e.outputs);let a=o.hostBindings;a&&eE(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&KI(e,c),l&&XI(e,l),YI(e,o),uu(e.outputs,o.outputs),ge(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===bp&&(n=!1)}}t=Object.getPrototypeOf(t)}JI(r)}function YI(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function JI(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Tn(o.hostAttrs,n=Tn(n,o.hostAttrs))}}function ha(e){return e===Ee?{}:e===J?[]:e}function KI(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function XI(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function eE(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function tE(e){let t=n=>{let r=Array.isArray(e);n.hostDirectives===null?(n.resolveHostDirectives=nE,n.hostDirectives=r?e.map(za):[e]):r?n.hostDirectives.unshift(...e.map(za)):n.hostDirectives.unshift(e)};return t.ngInherit=!0,t}function nE(e){let t=[],n=!1,r=null,o=null;for(let i=0;i<e.length;i++){let s=e[i];if(s.hostDirectives!==null){let a=t.length;r??=new Map,o??=new Map,Tp(s,t,r),o.set(s,[a,t.length-1])}i===0&&ge(s)&&(n=!0,t.push(s))}for(let i=n?1:0;i<e.length;i++)t.push(e[i]);return[t,r,o]}function Tp(e,t,n){if(e.hostDirectives!==null)for(let r of e.hostDirectives)if(typeof r=="function"){let o=r();for(let i of o)Ed(za(i),t,n)}else Ed(r,t,n)}function Ed(e,t,n){let r=nr(e.directive);rE(r.declaredInputs,e.inputs),Tp(r,t,n),n.set(r,e),t.push(r)}function za(e){return typeof e=="function"?{directive:q(e),inputs:Ee,outputs:Ee}:{directive:q(e.directive),inputs:Dd(e.inputs),outputs:Dd(e.outputs)}}function Dd(e){if(e===void 0||e.length===0)return Ee;let t={};for(let n=0;n<e.length;n+=2)t[e[n]]=e[n+1];return t}function rE(e,t){for(let n in t)if(t.hasOwnProperty(n)){let r=t[n],o=e[n];e[r]=o}}function oE(e,t,n,r,o,i,s,a,c){let l=t.consts,u=Pn(t,e,4,s||null,a||null);Ao()&&Rc(t,n,u,be(l,c),wc),u.mergedAttrs=Tn(u.mergedAttrs,u.attrs),ac(t,u);let d=u.tView=yc(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function Wt(e,t,n,r,o,i,s,a,c,l,u){let d=n+k,p=t.firstCreatePass?oE(d,t,e,r,o,i,s,a,l):t.data[d];c&&(p.flags|=c),Ze(p,!1);let f=iE(t,e,p,n);fr()&&Ti(t,e,f,p),An(f,e);let h=tp(f,e,f,p);return e[d]=h,Ic(e,h),pp(h,p,e),ir(p)&&wi(t,e,p),l!=null&&Ec(e,p,u),p}function _p(e,t,n,r,o,i,s,a){let c=m(),l=R(),u=be(l.consts,i);return Wt(c,l,e,t,n,r,o,u,void 0,s,a),_p}var iE=sE;function sE(e,t,n,r){return pr(!0),t[O].createComment("")}var ne=function(e){return e[e.NOT_STARTED=0]="NOT_STARTED",e[e.IN_PROGRESS=1]="IN_PROGRESS",e[e.COMPLETE=2]="COMPLETE",e[e.FAILED=3]="FAILED",e}(ne||{}),wd=0,aE=1,$=function(e){return e[e.Placeholder=0]="Placeholder",e[e.Loading=1]="Loading",e[e.Complete=2]="Complete",e[e.Error=3]="Error",e}($||{}),Mp=function(e){return e[e.Initial=-1]="Initial",e}(Mp||{}),cE=0,Ri=1;var lE=4,uE=5,dE=6,fE=7,ga=8,pE=9,Bc=function(e){return e[e.Manual=0]="Manual",e[e.Playthrough=1]="Playthrough",e}(Bc||{});function $c(e,t,n){let r=xp(e);t[r]===null&&(t[r]=[]),t[r].push(n)}function Ko(e,t){let n=xp(e),r=t[n];if(r!==null){for(let o of r)o();t[n]=null}}function Np(e){Ko(1,e),Ko(0,e),Ko(2,e)}function xp(e){let t=lE;return e===1?t=uE:e===2&&(t=pE),t}var Ai=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ai||{}),Ln=new S(""),Sp=!1,Qa=class extends _e{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,Fs()&&(this.destroyRef=w(Ye,{optional:!0})??void 0,this.pendingTasks=w(Ht,{optional:!0})??void 0)}emit(t){let n=D(null);try{super.next(t)}finally{D(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof F&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},Ke=Qa;function Rp(e){let t,n;function r(){e=Bt;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Cd(e){return queueMicrotask(()=>e()),()=>{e=Bt}}var Uc="isAngularZone",pi=Uc+"_ID",hE=0,re=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new Ke(!1);onMicrotaskEmpty=new Ke(!1);onStable=new Ke(!1);onError=new Ke(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Sp}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,yE(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Uc)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,gE,Bt,Bt);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},gE={};function qc(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function mE(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){Rp(()=>{e.callbackScheduled=!1,Za(e),e.isCheckStableRunning=!0,qc(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),Za(e)}function yE(e){let t=()=>{mE(e)},n=hE++;e._inner=e._inner.fork({name:"angular",properties:{[Uc]:!0,[pi]:n,[pi+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(vE(c))return r.invokeTask(i,s,a,c);try{return bd(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Td(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return bd(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!IE(c)&&t(),Td(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Za(e),qc(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Za(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function bd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Td(e){e._nesting--,qc(e)}var hi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new Ke;onMicrotaskEmpty=new Ke;onStable=new Ke;onError=new Ke;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function vE(e){return Ap(e,"__ignore_ng_zone__")}function IE(e){return Ap(e,"__scheduler_tick__")}function Ap(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Oi=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})(),Wc=[0,1,2,3],Gc=(()=>{class e{ngZone=w(re);scheduler=w(fe);errorHandler=w(Ae,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){w(Ln,{optional:!0})}execute(){let n=this.sequences.size>0;n&&A(16),this.executing=!0;for(let r of Wc)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),n&&A(17)}register(n){let{view:r}=n;r!==void 0?((r[Lt]??=[]).push(n),ft(r),r[C]|=8192):this.executing?this.deferredRegistrations.add(n):this.addSequence(n)}addSequence(n){this.sequences.add(n),this.scheduler.notify(7)}unregister(n){this.executing&&this.sequences.has(n)?(n.erroredOrDestroyed=!0,n.pipelinedValue=void 0,n.once=!0):(this.sequences.delete(n),this.deferredRegistrations.delete(n))}maybeTrace(n,r){return r?r.run(Ai.AFTER_NEXT_RENDER,n):n()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})(),Tr=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(t,n,r,o,i,s=null){this.impl=t,this.hooks=n,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let t=this.view?.[Lt];t&&(this.view[Lt]=t.filter(n=>n!==this))}};function EE(e,t){let n=t?.injector??w(de);return je("NgAfterRender"),kp(e,n,t,!1)}function Op(e,t){!t?.injector&&js(Op);let n=t?.injector??w(de);return je("NgAfterNextRender"),kp(e,n,t,!0)}function DE(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function kp(e,t,n,r){let o=t.get(Oi);o.impl??=t.get(Gc);let i=t.get(Ln,null,{optional:!0}),s=n?.manualCleanup!==!0?t.get(Ye):null,a=t.get(Vt,null,{optional:!0}),c=new Tr(o.impl,DE(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}function ki(e){return e+1}function Qt(e,t){let n=e[y],r=ki(t.index);return e[r]}function wE(e,t,n){let r=e[y],o=ki(t);e[o]=n}function Zt(e,t){let n=ki(t.index);return e.data[n]}function CE(e,t,n){let r=ki(t);e.data[r]=n}function bE(e,t,n){let r=t[y],o=Zt(r,n);switch(e){case $.Complete:return o.primaryTmplIndex;case $.Loading:return o.loadingTmplIndex;case $.Error:return o.errorTmplIndex;case $.Placeholder:return o.placeholderTmplIndex;default:return null}}function _d(e,t){return t===$.Placeholder?e.placeholderBlockConfig?.[wd]??null:t===$.Loading?e.loadingBlockConfig?.[wd]??null:null}function TE(e){return e.loadingBlockConfig?.[aE]??null}function Md(e,t){if(!e||e.length===0)return t;let n=new Set(e);for(let r of t)n.add(r);return e.length===n.size?e:Array.from(n)}function _E(e,t){let n=t.primaryTmplIndex+k;return jt(e,n)}function Pp(e,t){let n=t.get(xE),r=()=>n.remove(e);return n.add(e),r}var ME=()=>typeof requestIdleCallback<"u"?requestIdleCallback:setTimeout,NE=()=>typeof requestIdleCallback<"u"?cancelIdleCallback:clearTimeout,xE=(()=>{class e{executingCallbacks=!1;idleId=null;current=new Set;deferred=new Set;ngZone=w(re);requestIdleCallbackFn=ME().bind(globalThis);cancelIdleCallbackFn=NE().bind(globalThis);add(n){(this.executingCallbacks?this.deferred:this.current).add(n),this.idleId===null&&this.scheduleIdleCallback()}remove(n){let{current:r,deferred:o}=this;r.delete(n),o.delete(n),r.size===0&&o.size===0&&this.cancelIdleCallback()}scheduleIdleCallback(){let n=()=>{this.cancelIdleCallback(),this.executingCallbacks=!0;for(let r of this.current)r();if(this.current.clear(),this.executingCallbacks=!1,this.deferred.size>0){for(let r of this.deferred)this.current.add(r);this.deferred.clear(),this.scheduleIdleCallback()}};this.idleId=this.requestIdleCallbackFn(()=>this.ngZone.run(n))}cancelIdleCallback(){this.idleId!==null&&(this.cancelIdleCallbackFn(this.idleId),this.idleId=null)}ngOnDestroy(){this.cancelIdleCallback(),this.current.clear(),this.deferred.clear()}static \u0275prov=V({token:e,providedIn:"root",factory:()=>new e})}return e})();var SE=(()=>{class e{cachedInjectors=new Map;getOrCreateInjector(n,r,o,i){if(!this.cachedInjectors.has(n)){let s=o.length>0?Hc(o,r,i):null;this.cachedInjectors.set(n,s)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=V({token:e,providedIn:"environment",factory:()=>new e})}return e})();var Lp=new S("");function ma(e,t,n){return e.get(SE).getOrCreateInjector(t,e,n,"")}function RE(e,t,n){if(e instanceof bn){let o=e.injector,i=e.parentInjector,s=ma(i,t,n);return new bn(o,s)}let r=e.get(ce);if(r!==e){let o=ma(r,t,n);return new bn(e,o)}return ma(e,t,n)}function ht(e,t,n,r=!1){let o=n[B],i=o[y];if(dt(o))return;let s=Qt(o,t),a=s[Ri],c=s[fE];if(!(c!==null&&e<c)&&Nd(a,e)&&Nd(s[cE]??-1,e)){let l=Zt(i,t),d=!r&&!0&&(TE(l)!==null||_d(l,$.Loading)!==null||_d(l,$.Placeholder))?PE:OE;try{d(e,s,n,t,o)}catch(p){Cc(o,p)}}}function AE(e,t){let n=e[ct]?.findIndex(o=>o.data[fc]===t[Ri])??-1;return{dehydratedView:n>-1?e[ct][n]:null,dehydratedViewIx:n}}function OE(e,t,n,r,o){A(20);let i=bE(e,o,r);if(i!==null){t[Ri]=e;let s=o[y],a=i+k,c=jt(s,a),l=0;Sc(n,l);let u;if(e===$.Complete){let h=Zt(s,r),g=h.providers;g&&g.length>0&&(u=RE(o[se],h,g))}let{dehydratedView:d,dehydratedViewIx:p}=AE(n,t),f=On(o,c,null,{injector:u,dehydratedView:d});if(kn(n,f,l,qt(c,d)),Mi(f,2),p>-1&&n[ct]?.splice(p,1),(e===$.Complete||e===$.Error)&&Array.isArray(t[ga])){for(let h of t[ga])h();t[ga]=null}}A(21)}function Nd(e,t){return e<t}function kE(e,t){let n=e[t.index];ht($.Placeholder,t,n)}function xd(e,t,n){e.loadingPromise.then(()=>{e.loadingState===ne.COMPLETE?ht($.Complete,t,n):e.loadingState===ne.FAILED&&ht($.Error,t,n)})}var PE=null;var LE=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Fp=new S("");function zc(e){return!!e&&typeof e.then=="function"}function jp(e){return!!e&&typeof e.subscribe=="function"}var Qc=new S("");function FE(e){return Os([{provide:Qc,multi:!0,useValue:e}])}var Zc=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=w(Qc,{optional:!0})??[];injector=w(de);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=_o(this.injector,o);if(zc(i))n.push(i);else if(jp(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Vp=new S("");function Hp(){is(()=>{let e="";throw new _(600,e)})}function Bp(e){return e.isBoundToModule}var jE=10;var Ar=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=w(Je);afterRenderManager=w(Oi);zonelessEnabled=w(Bo);rootEffectScheduler=w(gr);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new _e;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=w(Ht);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(xe(n=>!n))}constructor(){w(Ln,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=w(ce);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=de.NULL){return this._injector.get(re).run(()=>{A(10);let s=n instanceof xi;if(!this._injector.get(Zc).done){let h="";throw new _(405,h)}let c;s?c=n:c=this._injector.get(Sr).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=Bp(c)?void 0:this._injector.get(Mn),u=r||c.selector,d=c.create(o,[],u,l),p=d.location.nativeElement,f=d.injector.get(Fp,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),yr(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),A(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){A(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ai.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=D(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,D(n),this.afterTick.next(),A(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(wr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<jE;)A(14),this.synchronizeOnce(),A(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!cr(o))continue;let i=r&&!this.zonelessEnabled?0:1;xc(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>cr(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;yr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(Vp,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>yr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function yr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function VE(e){let t=m(),n=L();if(kE(t,n),!$p(0,t))return;let r=t[se],o=Qt(t,n),i=e(()=>$E(0,t,n),r);$c(0,o,i)}function HE(e,t){let n=m(),r=n[se],o=L(),i=n[y],s=Zt(i,o);if(s.loadingState===ne.NOT_STARTED){let a=Qt(n,o),l=e(()=>BE(s,n,o),r);$c(1,a,l)}}function BE(e,t,n){Yc(e,t,n)}function Yc(e,t,n){let r=t[se],o=t[y];if(e.loadingState!==ne.NOT_STARTED)return e.loadingPromise??Promise.resolve();let i=Qt(t,n),s=_E(o,e);e.loadingState=ne.IN_PROGRESS,Ko(1,i);let a=e.dependencyResolverFn,c=r.get($o).add();return a?(e.loadingPromise=Promise.allSettled(a()).then(l=>{let u=!1,d=[],p=[];for(let f of l)if(f.status==="fulfilled"){let h=f.value,g=De(h)||nr(h);if(g)d.push(g);else{let N=As(h);N&&p.push(N)}}else{u=!0;break}if(u){if(e.loadingState=ne.FAILED,e.errorTmplIndex===null){let f="",h=new _(-750,!1);Cc(t,h)}}else{e.loadingState=ne.COMPLETE;let f=s.tView;if(d.length>0){f.directiveRegistry=Md(f.directiveRegistry,d);let h=d.map(N=>N.type),g=To(!1,...h);e.providers=g}p.length>0&&(f.pipeRegistry=Md(f.pipeRegistry,p))}}),e.loadingPromise.finally(()=>{e.loadingPromise=null,c()})):(e.loadingPromise=Promise.resolve().then(()=>{e.loadingPromise=null,e.loadingState=ne.COMPLETE,c()}),e.loadingPromise)}function $p(e,t){return t[se].get(Lp,null,{optional:!0})?.behavior!==Bc.Manual}function $E(e,t,n){let r=t[y],o=t[n.index];if(!$p(e,t))return;let i=Qt(t,n),s=Zt(r,n);switch(Np(i),s.loadingState){case ne.NOT_STARTED:ht($.Loading,n,o),Yc(s,t,n),s.loadingState===ne.IN_PROGRESS&&xd(s,n,o);break;case ne.IN_PROGRESS:ht($.Loading,n,o),xd(s,n,o);break;case ne.COMPLETE:ht($.Complete,n,o);break;case ne.FAILED:ht($.Error,n,o);break;default:}}function Up(e,t,n){return e===0?Sd(t,n):e===2?!Sd(t,n):!0}function UE(e){return e!=null&&(e&1)===1}function Sd(e,t){let n=e[se],r=Zt(e[y],t),o=mf(n),i=UE(r.flags),a=Qt(e,t)[dE]!==null;return!(i&&a&&o)}function qE(e,t,n,r,o,i,s,a,c,l){let u=m(),d=R(),p=e+k,f=Wt(u,d,e,null,0,0),h=u[se];if(d.firstCreatePass){je("NgDefer");let Fn={primaryTmplIndex:t,loadingTmplIndex:r??null,placeholderTmplIndex:o??null,errorTmplIndex:i??null,placeholderBlockConfig:null,loadingBlockConfig:null,dependencyResolverFn:n??null,loadingState:ne.NOT_STARTED,loadingPromise:null,providers:null,hydrateTriggers:null,debug:null,flags:l??0};c?.(d,Fn,a,s),CE(d,p,Fn)}let g=u[p];pp(g,f,u);let N=null,b=null;if(g[ct]?.length>0){let Fn=g[ct][0].data;b=Fn[dc]??null,N=Fn[fc]}let Ve=[null,Mp.Initial,null,null,null,null,b,N,null,null];wE(u,p,Ve);let Fi=null;b!==null&&(Fi=h.get(hf),Fi.add(b,{lView:u,tNode:f,lContainer:g}));let pl=()=>{Np(Ve),b!==null&&Fi?.cleanup([b])};$c(0,Ve,()=>zs(u,pl)),Ro(u,pl)}function WE(){let e=m(),t=L();Up(0,e,t)&&VE(Pp)}function GE(){let e=m(),t=L();Up(1,e,t)&&HE(Pp)}function qp(e,t,n,r){let o=m(),i=Le();if(ae(o,i,t)){let s=R(),a=Dn();mv(a,o,e,t,n,r)}return qp}var Ya=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function ya(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function zE(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=ya(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],h=ya(s,p,c,f,n);if(h!==0){h<0&&e.updateValue(s,f),s--,c--;continue}let g=n(i,l),N=n(s,p),b=n(i,u);if(Object.is(b,N)){let Ve=n(c,f);Object.is(Ve,g)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new gi,o??=Ad(e,i,s,n),Ja(e,r,i,b))e.updateValue(i,u),i++,s++;else if(o.has(b))r.set(g,e.detach(i)),s--;else{let Ve=e.create(i,t[i]);e.attach(i,Ve),i++,s++}}for(;i<=c;)Rd(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=ya(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new gi,o??=Ad(e,i,s,n);let f=n(i,d);if(Ja(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let h=n(i,u);r.set(h,e.detach(i)),s--}}}for(;!l.done;)Rd(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function Ja(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function Rd(e,t,n,r,o){if(Ja(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Ad(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var gi=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function QE(e,t,n,r,o,i,s,a){je("NgControlFlow");let c=m(),l=R(),u=be(l.consts,i);return Wt(c,l,e,t,n,r,o,u,256,s,a),Jc}function Jc(e,t,n,r,o,i,s,a){je("NgControlFlow");let c=m(),l=R(),u=be(l.consts,i);return Wt(c,l,e,t,n,r,o,u,512,s,a),Jc}function ZE(e,t){je("NgControlFlow");let n=m(),r=Le(),o=n[r]!==Z?n[r]:-1,i=o!==-1?mi(n,k+o):void 0,s=0;if(ae(n,r,e)){let a=D(null);try{if(i!==void 0&&Sc(i,s),e!==-1){let c=k+e,l=mi(n,c),u=tc(n[y],c),d=op(l,u,n),p=On(n,u,t,{dehydratedView:d});kn(l,p,s,qt(u,d))}}finally{D(a)}}else if(i!==void 0){let a=np(i,s);a!==void 0&&(a[H]=t)}}var Ka=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-Q}};function YE(e){return e}function JE(e,t){return t}var Xa=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function KE(e,t,n,r,o,i,s,a,c,l,u,d,p){je("NgControlFlow");let f=m(),h=R(),g=c!==void 0,N=m(),b=a?s.bind(N[te][H]):s,Ve=new Xa(g,b);N[k+e]=Ve,Wt(f,h,e+1,t,n,r,o,be(h.consts,i),256),g&&Wt(f,h,e+2,c,l,u,d,be(h.consts,p),512)}var ec=class extends Ya{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-Q}at(t){return this.getLView(t)[H].$implicit}attach(t,n){let r=n[Ot];this.needsIndexUpdate||=t!==this.length,kn(this.lContainer,n,t,qt(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,eD(this.lContainer,t)}create(t,n){let r=ai(this.lContainer,this.templateTNode.tView.ssrId),o=On(this.hostLView,this.templateTNode,new Ka(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){bi(t[y],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[H].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[H].$index=t}getLView(t){return tD(this.lContainer,t)}};function XE(e){let t=D(null),n=ve();try{let r=m(),o=r[y],i=r[n],s=n+1,a=mi(r,s);if(i.liveCollection===void 0){let l=tc(o,s);i.liveCollection=new ec(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(zE(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=Le(),u=c.length===0;if(ae(r,l,u)){let d=n+2,p=mi(r,d);if(u){let f=tc(o,d),h=op(p,f,r),g=On(r,f,void 0,{dehydratedView:h});kn(p,g,0,qt(f,h))}else o.firstUpdatePass&&Kv(p),Sc(p,0)}}}finally{D(t)}}function mi(e,t){return e[t]}function eD(e,t){return Er(e,t)}function tD(e,t){return np(e,t)}function tc(e,t){return jt(e,t)}function Wp(e,t,n){let r=m(),o=Le();if(ae(r,o,t)){let i=R(),s=Dn();Vf(s,r,e,t,r[O],n)}return Wp}function nc(e,t,n,r,o){bc(t,e,n,o?"class":"style",r)}function Kc(e,t,n,r){let o=m(),i=R(),s=k+e,a=o[O],c=i.firstCreatePass?cp(s,i,o,t,wc,Ao(),n,r):i.data[s],l=nD(i,o,c,a,t,e);o[s]=l;let u=ir(c);return Ze(c,!0),kf(a,l,c),!Ci(c)&&fr()&&Ti(i,o,l,c),(Nu()===0||u)&&An(l,o),xu(),u&&(wi(i,o,c),pc(i,c,o)),r!==null&&Ec(o,c),Kc}function Xc(){let e=L();Oo()?ko():(e=e.parent,Ze(e,!1));let t=e;Ru(t)&&Au(),Su();let n=R();return n.firstCreatePass&&lp(n,t),t.classesWithoutHost!=null&&Pm(t)&&nc(n,t,m(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&Lm(t)&&nc(n,t,m(),t.stylesWithoutHost,!1),Xc}function Gp(e,t,n,r){return Kc(e,t,n,r),Xc(),Gp}var nD=(e,t,n,r,o,i)=>(pr(!0),Rf(r,o,Gu()));function rD(e,t,n,r,o){let i=t.consts,s=be(i,r),a=Pn(t,e,8,"ng-container",s);s!==null&&Ha(a,s,!0);let c=be(i,o);return Ao()&&Rc(t,n,a,c,wc),a.mergedAttrs=Tn(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function el(e,t,n){let r=m(),o=R(),i=e+k,s=o.firstCreatePass?rD(i,o,r,t,n):o.data[i];Ze(s,!0);let a=oD(o,r,s,e);return r[i]=a,fr()&&Ti(o,r,a,s),An(a,r),ir(s)&&(wi(o,r,s),pc(o,s,r)),n!=null&&Ec(r,s),el}function tl(){let e=L(),t=R();return Oo()?ko():(e=e.parent,Ze(e,!1)),t.firstCreatePass&&(ac(t,e),No(e)&&t.queries.elementEnd(e)),tl}function zp(e,t,n){return el(e,t,n),tl(),zp}var oD=(e,t,n,r)=>(pr(!0),tv(t[O],""));function iD(){return m()}function Qp(e,t,n){let r=m(),o=Le();if(ae(r,o,t)){let i=R(),s=Dn();Dc(s,r,e,t,r[O],n)}return Qp}function Zp(e,t,n){let r=m(),o=Le();if(ae(r,o,t)){let i=R(),s=Dn(),a=Lo(i.data),c=Hf(a,s,r);Dc(s,r,e,t,c,n)}return Zp}var Or="en-US";var sD=Or;function Yp(e){typeof e=="string"&&(sD=e.toLowerCase().replace(/_/g,"-"))}function Jp(e,t,n){let r=m(),o=R(),i=L();return nl(o,r,r[O],i,e,t,n),Jp}function Kp(e,t){let n=L(),r=m(),o=R(),i=Lo(o.data),s=Hf(i,n,r);return nl(o,r,s,n,e,t),Kp}function nl(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=fa(r,t,i),mI(r,e,t,s,n,o,i,c)&&(a=!1)),a){let l=r.outputs?.[o],u=r.hostDirectiveOutputs?.[o];if(u&&u.length)for(let d=0;d<u.length;d+=2){let p=u[d],f=u[d+1];c??=fa(r,t,i),gd(r,t,p,f,o,c)}if(l&&l.length)for(let d of l)c??=fa(r,t,i),gd(r,t,d,o,o,c)}}function aD(e=1){return Uu(e)}function cD(e,t){let n=null,r=Gy(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Sf(e,i,!0):Zy(r,i))return o}return n}function lD(e){let t=m()[te][ee];if(!t.projection){let n=e?e.length:1,r=t.projection=yu(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?cD(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function uD(e,t=0,n,r,o,i){let s=m(),a=R(),c=r?e+1:null;c!==null&&Wt(s,a,c,r,o,i,null,n);let l=Pn(a,k+e,16,null,n||null);l.projection===null&&(l.projection=t),ko();let d=!s[Ot]||Ys();s[te][ee].projection[l.projection]===null&&c!==null?dD(s,a,c):d&&!Ci(l)&&Av(a,s,l)}function dD(e,t,n){let r=k+n,o=t.data[r],i=e[r],s=ai(i,o.tView.ssrId),a=On(e,o,void 0,{dehydratedView:s});kn(i,a,0,qt(o,s))}function fD(e,t,n,r){yp(e,t,n,r)}function pD(e,t,n){mp(e,t,n)}function hD(e){let t=m(),n=R(),r=Fo();dr(r+1);let o=Pc(n,r);if(e.dirty&&bu(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Ip(t,r);e.reset(i,nf),e.notifyOnChanges()}return!0}return!1}function gD(){return kc(m(),Fo())}function mD(e,t,n,r,o){Ep(t,yp(e,n,r,o))}function yD(e,t,n,r){Ep(e,mp(t,n,r))}function vD(e=1){dr(Fo()+e)}function ID(e){let t=Ks();return sr(t,k+e)}function zo(e,t){return e<<17|t<<2}function Gt(e){return e>>17&32767}function ED(e){return(e&2)==2}function DD(e,t){return e&131071|t<<17}function rc(e){return e|2}function Nn(e){return(e&131068)>>2}function va(e,t){return e&-131069|t<<2}function wD(e){return(e&1)===1}function oc(e){return e|1}function CD(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Gt(s),c=Nn(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||hn(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=Gt(e[a+1]);e[r+1]=zo(p,a),p!==0&&(e[p+1]=va(e[p+1],r)),e[a+1]=DD(e[a+1],r)}else e[r+1]=zo(a,0),a!==0&&(e[a+1]=va(e[a+1],r)),a=r;else e[r+1]=zo(c,0),a===0?a=r:e[c+1]=va(e[c+1],r),c=r;l&&(e[r+1]=rc(e[r+1])),Od(e,u,r,!0),Od(e,u,r,!1),bD(t,u,e,r,i),s=zo(a,c),i?t.classBindings=s:t.styleBindings=s}function bD(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&hn(i,t)>=0&&(n[r+1]=oc(n[r+1]))}function Od(e,t,n,r){let o=e[n+1],i=t===null,s=r?Gt(o):Nn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];TD(c,t)&&(a=!0,e[s+1]=r?oc(l):rc(l)),s=r?Gt(l):Nn(l)}a&&(e[n+1]=r?rc(o):oc(o))}function TD(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?hn(e,t)>=0:!1}var W={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Xp(e){return e.substring(W.key,W.keyEnd)}function _D(e){return e.substring(W.value,W.valueEnd)}function MD(e){return nh(e),eh(e,xn(e,0,W.textEnd))}function eh(e,t){let n=W.textEnd;return n===t?-1:(t=W.keyEnd=xD(e,W.key=t,n),xn(e,t,n))}function ND(e){return nh(e),th(e,xn(e,0,W.textEnd))}function th(e,t){let n=W.textEnd,r=W.key=xn(e,t,n);return n===r?-1:(r=W.keyEnd=SD(e,r,n),r=kd(e,r,n,58),r=W.value=xn(e,r,n),r=W.valueEnd=RD(e,r,n),kd(e,r,n,59))}function nh(e){W.key=0,W.keyEnd=0,W.value=0,W.valueEnd=0,W.textEnd=e.length}function xn(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function xD(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function SD(e,t,n){let r;for(;t<n&&((r=e.charCodeAt(t))===45||r===95||(r&-33)>=65&&(r&-33)<=90||r>=48&&r<=57);)t++;return t}function kd(e,t,n,r){return t=xn(e,t,n),t<n&&t++,t}function RD(e,t,n){let r=-1,o=-1,i=-1,s=t,a=s;for(;s<n;){let c=e.charCodeAt(s++);if(c===59)return a;c===34||c===39?a=s=Pd(e,c,s,n):t===s-4&&i===85&&o===82&&r===76&&c===40?a=s=Pd(e,41,s,n):c>32&&(a=s),i=o,o=r,r=c&-33}return a}function Pd(e,t,n,r){let o=-1,i=n;for(;i<r;){let s=e.charCodeAt(i++);if(s==t&&o!==92)return i;s==92&&o===92?o=0:o=s}throw new Error}function rh(e,t,n){return ih(e,t,n,!1),rh}function oh(e,t){return ih(e,t,null,!0),oh}function AD(e){sh(lh,OD,e,!1)}function OD(e,t){for(let n=ND(t);n>=0;n=th(t,n))lh(e,Xp(t),_D(t))}function kD(e){sh(BD,PD,e,!0)}function PD(e,t){for(let n=MD(t);n>=0;n=eh(t,n))tr(e,Xp(t),!0)}function ih(e,t,n,r){let o=m(),i=R(),s=ur(2);if(i.firstUpdatePass&&ch(i,e,s,r),t!==Z&&ae(o,s,t)){let a=i.data[ve()];uh(i,a,o,o[O],e,o[s+1]=UD(t,n),r,s)}}function sh(e,t,n,r){let o=R(),i=ur(2);o.firstUpdatePass&&ch(o,null,i,r);let s=m();if(n!==Z&&ae(s,i,n)){let a=o.data[ve()];if(dh(a,r)&&!ah(o,i)){let c=r?a.classesWithoutHost:a.stylesWithoutHost;c!==null&&(n=Io(c,n||"")),nc(o,a,s,n,r)}else $D(o,a,s,s[O],s[i+1],s[i+1]=HD(e,t,n),r,i)}}function ah(e,t){return t>=e.expandoStartIndex}function ch(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[ve()],s=ah(e,n);dh(i,r)&&t===null&&!s&&(t=!1),t=LD(o,i,t,r),CD(o,i,t,n,s,r)}}function LD(e,t,n,r){let o=Lo(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Ia(null,e,t,n,r),n=_r(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Ia(o,e,t,n,r),i===null){let c=FD(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Ia(null,e,t,c[1],r),c=_r(c,t.attrs,r),jD(e,t,r,c))}else i=VD(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function FD(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Nn(r)!==0)return e[Gt(r)]}function jD(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Gt(o)]=r}function VD(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=_r(r,s,n)}return _r(r,t.attrs,n)}function Ia(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=_r(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function _r(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),tr(e,s,n?!0:t[++i]))}return e===void 0?null:e}function HD(e,t,n){if(n==null||n==="")return J;let r=[],o=zt(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function lh(e,t,n){tr(e,t,zt(n))}function BD(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&tr(e,r,n)}function $D(e,t,n,r,o,i,s,a){o===Z&&(o=J);let c=0,l=0,u=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;u!==null||d!==null;){let p=c<o.length?o[c+1]:void 0,f=l<i.length?i[l+1]:void 0,h=null,g;u===d?(c+=2,l+=2,p!==f&&(h=d,g=f)):d===null||u!==null&&u<d?(c+=2,h=u):(l+=2,h=d,g=f),h!==null&&uh(e,t,n,r,h,g,s,a),u=c<o.length?o[c]:null,d=l<i.length?i[l]:null}}function uh(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=wD(l)?Ld(c,t,n,o,Nn(l),s):void 0;if(!yi(u)){yi(i)||ED(l)&&(i=Ld(c,null,n,o,a,s));let d=qs(ve(),n);kv(r,s,d,o,i)}}function Ld(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===Z&&(p=d?J:void 0);let f=d?bo(p,r):u===r?p:void 0;if(l&&!yi(f)&&(f=bo(c,r)),yi(f)&&(a=f,s))return a;let h=e[o+1];o=s?Gt(h):Nn(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=bo(c,r))}return a}function yi(e){return e!==void 0}function UD(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=ie(zt(e)))),e}function dh(e,t){return(e.flags&(t?8:16))!==0}function qD(e,t=""){let n=m(),r=R(),o=e+k,i=r.firstCreatePass?Pn(r,o,1,t,null):r.data[o],s=WD(r,n,i,t,e);n[o]=s,fr()&&Ti(r,n,s,i),Ze(i,!1)}var WD=(e,t,n,r,o)=>(pr(!0),Xy(t[O],r));function fh(e,t,n,r=""){return ae(e,Le(),n)?t+X(n)+r:Z}function GD(e,t,n,r,o,i=""){let s=ta(),a=Cr(e,s,n,o);return ur(2),a?t+X(n)+r+X(o)+i:Z}function zD(e,t,n,r,o,i,s,a,c,l,u,d,p,f=""){let h=ta(),g=gI(e,h,n,o,s,c);return g=Cr(e,h+4,u,p)||g,ur(6),g?t+X(n)+r+X(o)+i+X(s)+a+X(c)+l+X(u)+d+X(p)+f:Z}function ph(e){return rl("",e),ph}function rl(e,t,n){let r=m(),o=fh(r,e,t,n);return o!==Z&&ol(r,ve(),o),rl}function hh(e,t,n,r,o){let i=m(),s=GD(i,e,t,n,r,o);return s!==Z&&ol(i,ve(),s),hh}function gh(e,t,n,r,o,i,s,a,c,l,u,d,p){let f=m(),h=zD(f,e,t,n,r,o,i,s,a,c,l,u,d,p);return h!==Z&&ol(f,ve(),h),gh}function ol(e,t,n){let r=qs(t,e);ev(e[O],r,n)}function mh(e,t,n){sa(t)&&(t=t());let r=m(),o=Le();if(ae(r,o,t)){let i=R(),s=Dn();Vf(s,r,e,t,r[O],n)}return mh}function QD(e,t){let n=sa(e);return n&&e.set(t),n}function yh(e,t){let n=m(),r=R(),o=L();return nl(r,n,n[O],o,e,t),yh}var vh={};function Ih(e){let t=R(),n=m(),r=e+k,o=Pn(t,r,128,null,null);return Ze(o,!1),ar(t,n,r,vh),Ih}function ZD(e){je("NgLet");let t=R(),n=m(),r=ve();return ar(t,n,r,e),e}function YD(e){let t=Ks(),n=sr(t,k+e);if(n===vh)throw new _(314,!1);return n}function JD(e){return ae(m(),Le(),e)?X(e):Z}function KD(e,t,n=""){return fh(m(),e,t,n)}function XD(e,t,n){let r=R();if(r.firstCreatePass){let o=ge(e);ic(n,r.data,r.blueprint,o,!0),ic(t,r.data,r.blueprint,o,!1)}}function ic(e,t,n,r,o){if(e=q(e),Array.isArray(e))for(let i=0;i<e.length;i++)ic(e[i],t,n,r,o);else{let i=R(),s=m(),a=L(),c=xt(e)?e:q(e.provide),l=Ls(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(xt(e)||!e.multi){let f=new Ut(l,o,Rr),h=Da(c,t,o?u:u+p,d);h===-1?(Ca(ri(a,s),i,c),Ea(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=Da(c,t,u+p,d),h=Da(c,t,u,u+p),g=f>=0&&n[f],N=h>=0&&n[h];if(o&&!N||!o&&!g){Ca(ri(a,s),i,c);let b=nw(o?tw:ew,n.length,o,r,l);!o&&N&&(n[h].providerFactory=b),Ea(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(b),s.push(b)}else{let b=Eh(n[o?h:f],l,!o&&r);Ea(i,e,f>-1?f:h,b)}!o&&r&&N&&n[h].componentProviders++}}}function Ea(e,t,n,r){let o=xt(t),i=wu(t);if(o||i){let c=(i?q(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Eh(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Da(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function ew(e,t,n,r){return sc(this.multi,[])}function tw(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=vr(n,n[y],this.providerFactory.index,r);i=a.slice(0,s),sc(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],sc(o,i);return i}function sc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function nw(e,t,n,r,o){let i=new Ut(e,n,Rr);return i.multi=[],i.index=t,i.componentProviders=0,Eh(i,o,r&&!n),i}function rw(e,t=[]){return n=>{n.providersResolver=(r,o)=>XD(r,o?o(e):e,t)}}function ow(e,t,n){let r=En()+e,o=m();return o[r]===Z?Oc(o,r,n?t.call(n):t()):hI(o,r)}function iw(e,t,n,r){return wh(m(),En(),e,t,n,r)}function sw(e,t,n,r,o){return Ch(m(),En(),e,t,n,r,o)}function Dh(e,t){let n=e[t];return n===Z?void 0:n}function wh(e,t,n,r,o,i){let s=t+n;return ae(e,s,o)?Oc(e,s+1,i?r.call(i,o):r(o)):Dh(e,s+1)}function Ch(e,t,n,r,o,i,s){let a=t+n;return Cr(e,a,o,i)?Oc(e,a+2,s?r.call(s,o,i):r(o,i)):Dh(e,a+2)}function aw(e,t){let n=R(),r,o=e+k;n.firstCreatePass?(r=cw(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=it(r.type,!0)),s,a=K(Rr);try{let c=ni(!1),l=i();return ni(c),ar(n,m(),o,l),l}finally{K(a)}}function cw(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function lw(e,t,n){let r=e+k,o=m(),i=sr(o,r);return bh(o,r)?wh(o,En(),t,i.transform,n,i):i.transform(n)}function uw(e,t,n,r){let o=e+k,i=m(),s=sr(i,o);return bh(i,o)?Ch(i,En(),t,s.transform,n,r,s):s.transform(n,r)}function bh(e,t){return e[y].data[t].pure}function dw(e,t){return Ni(e,t)}var vi=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},fw=(()=>{class e{compileModuleSync(n){return new fi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Rs(n),i=Nf(o.declarations).reduce((s,a)=>{let c=De(a);return c&&s.push(new mt(c)),s},[]);return new vi(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var pw=(()=>{class e{zone=w(re);changeDetectionScheduler=w(fe);applicationRef=w(Ar);applicationErrorHandler=w(Je);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Th({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new re(z(G({},_h()),{scheduleInRootZone:n})),[{provide:re,useFactory:e},{provide:At,multi:!0,useFactory:()=>{let r=w(pw,{optional:!0});return()=>r.initialize()}},{provide:At,multi:!0,useFactory:()=>{let r=w(hw);return()=>{r.initialize()}}},t===!0?{provide:aa,useValue:!0}:[],{provide:ca,useValue:n??Sp},{provide:Je,useFactory:()=>{let r=w(re),o=w(ce),i;return s=>{i??=o.get(Ae),r.runOutsideAngular(()=>i.handleError(s))}}}]}function _h(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var hw=(()=>{class e{subscription=new F;initialized=!1;zone=w(re);pendingTasks=w(Ht);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{re.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{re.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Mh=(()=>{class e{applicationErrorHandler=w(Je);appRef=w(Ar);taskService=w(Ht);ngZone=w(re);zonelessEnabled=w(Bo);tracing=w(Ln,{optional:!0});disableScheduling=w(aa,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new F;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(pi):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(w(ca,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof hi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Cd:Rp;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(pi+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Cd(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=V({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function gw(){return typeof $localize<"u"&&$localize.locale||Or}var il=new S("",{providedIn:"root",factory:()=>w(il,{optional:!0,skipSelf:!0})||gw()});var kr=class{destroyed=!1;listeners=null;errorHandler=w(Ae,{optional:!0});destroyRef=w(Ye);constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0,this.listeners=null})}subscribe(t){if(this.destroyed)throw new _(953,!1);return(this.listeners??=[]).push(t),{unsubscribe:()=>{let n=this.listeners?.indexOf(t);n!==void 0&&n!==-1&&this.listeners?.splice(n,1)}}}emit(t){if(this.destroyed){console.warn(Kn(953,!1));return}if(this.listeners===null)return;let n=D(null);try{for(let r of this.listeners)try{r(t)}catch(o){this.errorHandler?.handleError(o)}}finally{D(n)}}};function mw(e){return su(e)}function yw(e,t){return Gn(e,t?.equal)}var sl=class{[j];constructor(t){this[j]=t}destroy(){this[j].destroy()}};function vw(e,t){let n=t?.injector??w(de),r=t?.manualCleanup!==!0?n.get(Ye):null,o,i=n.get(Vt,null,{optional:!0}),s=n.get(fe);return i!==null?(o=Dw(i.view,s,e),r instanceof Jn&&r._lView===i.view&&(r=null)):o=ww(e,n.get(gr),s),o.injector=n,r!==null&&(o.onDestroyFn=r.onDestroy(()=>o.destroy())),new sl(o)}var xh=z(G({},qe),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,dirty:!0,hasRun:!1,cleanupFns:void 0,zone:null,kind:"effect",onDestroyFn:Bt,run(){if(this.dirty=!1,this.hasRun&&!_t(this))return;this.hasRun=!0;let e=r=>(this.cleanupFns??=[]).push(r),t=Se(this),n=In(!1);try{this.maybeCleanup(),this.fn(e)}finally{In(n),Ge(this,t)}},maybeCleanup(){if(!this.cleanupFns?.length)return;let e=D(null);try{for(;this.cleanupFns.length;)this.cleanupFns.pop()()}finally{this.cleanupFns=[],D(e)}}}),Iw=z(G({},xh),{consumerMarkedDirty(){this.scheduler.schedule(this),this.notifier.notify(12)},destroy(){dn(this),this.onDestroyFn(),this.maybeCleanup(),this.scheduler.remove(this)}}),Ew=z(G({},xh),{consumerMarkedDirty(){this.view[C]|=8192,ft(this.view),this.notifier.notify(13)},destroy(){dn(this),this.onDestroyFn(),this.maybeCleanup(),this.view[Qe]?.delete(this)}});function Dw(e,t,n){let r=Object.create(Ew);return r.view=e,r.zone=typeof Zone<"u"?Zone.current:null,r.notifier=t,r.fn=n,e[Qe]??=new Set,e[Qe].add(r),r.consumerMarkedDirty(r),r}function ww(e,t,n){let r=Object.create(Iw);return r.fn=e,r.scheduler=t,r.notifier=n,r.zone=typeof Zone<"u"?Zone.current:null,r.scheduler.add(r),r.notifier.notify(12),r}var Cw=e=>e;function bw(e,t){if(typeof e=="function"){let n=as(e,Cw,t?.equal);return Nh(n)}else{let n=as(e.source,e.computation,e.equal);return Nh(n)}}function Nh(e){let t=e[j],n=e;return n.set=r=>ou(t,r),n.update=r=>iu(t,r),n.asReadonly=hr.bind(e),n}var Li=Symbol("InputSignalNode#UNSET"),Fh=z(G({},zn),{transformFn:void 0,applyValueToInputSignal(e,t){ot(e,t)}});function jh(e,t){let n=Object.create(Fh);n.value=e,n.transformFn=t?.transform;function r(){if(We(n),n.value===Li){let o=null;throw new _(-950,o)}return n.value}return r[j]=n,r}var Sh=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>tf(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},_w=new S("");_w.__NG_ELEMENT_ID__=e=>{let t=L();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new _(204,!1)};function FF(e){return new kr}function Rh(e,t){return jh(e,t)}function Mw(e){return jh(Li,e)}var jF=(Rh.required=Mw,Rh);function Ah(e,t){return Fc(t)}function Nw(e,t){return jc(t)}var VF=(Ah.required=Nw,Ah);function HF(e,t){return Vc(t)}function Oh(e,t){return Fc(t)}function xw(e,t){return jc(t)}var BF=(Oh.required=xw,Oh);function $F(e,t){return Vc(t)}function Vh(e,t){let n=Object.create(Fh),r=new kr;n.value=e;function o(){return We(n),kh(n.value),n.value}return o[j]=n,o.asReadonly=hr.bind(o),o.set=i=>{n.equal(n.value,i)||(ot(n,i),r.emit(i))},o.update=i=>{kh(n.value),o.set(i(n.value))},o.subscribe=r.subscribe.bind(r),o.destroyRef=r.destroyRef,o}function kh(e){if(e===Li)throw new _(952,!1)}function Ph(e,t){return Vh(e,t)}function Sw(e){return Vh(Li,e)}var UF=(Ph.required=Sw,Ph);var cl=new S(""),Rw=new S("");function Pr(e){return!e.moduleRef}function Aw(e){let t=Pr(e)?e.r3Injector:e.moduleRef.injector,n=t.get(re);return n.run(()=>{Pr(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Je),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Pr(e)){let i=()=>t.destroy(),s=e.platformInjector.get(cl);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(cl);s.add(i),e.moduleRef.onDestroy(()=>{yr(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return kw(r,n,()=>{let i=t.get(Zc);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get(il,Or);if(Yp(s||Or),!t.get(Rw,!0))return Pr(e)?t.get(Ar):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Pr(e)){let c=t.get(Ar);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return Ow?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var Ow;function kw(e,t,n){try{let r=n();return zc(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var Pi=null;function Pw(e=[],t){return de.create({name:t,providers:[{provide:Ps,useValue:"platform"},{provide:cl,useValue:new Set([()=>Pi=null])},...e]})}function Lw(e=[]){if(Pi)return Pi;let t=Pw(e);return Pi=t,Hp(),Fw(t),t}function Fw(e){let t=e.get(lf,null);_o(e,()=>{t?.forEach(n=>n())})}var qF=(()=>{class e{static __NG_ELEMENT_ID__=jw}return e})();function jw(e){return Vw(L(),m(),(e&16)===16)}function Vw(e,t,n){if(ut(e)&&!n){let r=ye(e.index,t);return new gt(r,r)}else if(e.type&175){let r=t[te];return new gt(r,t)}return null}var ll=class{constructor(){}supports(t){return t instanceof Map||Ac(t)}create(){return new ul}},ul=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Ac(t)))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new dl(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},dl=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(t){this.key=t}};function Lh(){return new Hw([new ll])}var Hw=(()=>{class e{static \u0275prov=V({token:e,providedIn:"root",factory:Lh});factories;constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Lh()),deps:[[e,new Vd,new jd]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new _(901,!1)}}return e})();function WF(e){A(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Lw(r),i=[Th({}),{provide:fe,useExisting:Mh},Qu,...n||[]],s=new br({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return Aw({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{A(9)}}function GF(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function zF(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}var al=Symbol("NOT_SET"),Hh=new Set,Bw=z(G({},zn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!0,value:al,cleanup:null,consumerMarkedDirty(){if(this.sequence.impl.executing){if(this.sequence.lastPhase===null||this.sequence.lastPhase<this.phase)return;this.sequence.erroredOrDestroyed=!0}this.sequence.scheduler.notify(7)},phaseFn(e){if(this.sequence.lastPhase=this.phase,!this.dirty)return this.signal;if(this.dirty=!1,this.value!==al&&!_t(this))return this.signal;try{for(let o of this.cleanup??Hh)o()}finally{this.cleanup?.clear()}let t=[];e!==void 0&&t.push(e),t.push(this.registerCleanupFn);let n=Se(this),r;try{r=this.userFn.apply(null,t)}finally{Ge(this,n)}return(this.value===al||!this.equal(this.value,r))&&(this.value=r,this.version++),this.signal}}),fl=class extends Tr{scheduler;lastPhase=null;nodes=[void 0,void 0,void 0,void 0];constructor(t,n,r,o,i,s=null){super(t,[void 0,void 0,void 0,void 0],r,!1,i,s),this.scheduler=o;for(let a of Wc){let c=n[a];if(c===void 0)continue;let l=Object.create(Bw);l.sequence=this,l.phase=a,l.userFn=c,l.dirty=!0,l.signal=()=>(We(l),l.value),l.signal[j]=l,l.registerCleanupFn=u=>(l.cleanup??=new Set).add(u),this.nodes[a]=l,this.hooks[a]=u=>l.phaseFn(u)}}afterRun(){super.afterRun(),this.lastPhase=null}destroy(){super.destroy();for(let t of this.nodes)for(let n of t?.cleanup??Hh)n()}};function QF(e,t){let n=t?.injector??w(de),r=n.get(fe),o=n.get(Oi),i=n.get(Ln,null,{optional:!0});o.impl??=n.get(Gc);let s=e;typeof s=="function"&&(s={mixedReadWrite:e});let a=n.get(Vt,null,{optional:!0}),c=new fl(o.impl,[s.earlyRead,s.write,s.mixedReadWrite,s.read],a?.view,r,n.get(Ye),i?.snapshot(null));return o.impl.register(c),c}function ZF(e,t){let n=De(e),r=t.elementInjector||gn();return new mt(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}function YF(e){let t=De(e);if(!t)return null;let n=new mt(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}export{F as a,qh as b,M as c,Gi as d,zi as e,_e as f,jn as g,Qi as h,eg as i,Be as j,Ne as k,tg as l,ng as m,rg as n,wt as o,xe as p,dg as q,ue as r,Bn as s,cn as t,pg as u,hg as v,Yi as w,Dg as x,tt as y,Cg as z,Ji as A,bg as B,Ul as C,$n as D,Ct as E,Tg as F,Gl as G,Ng as H,zl as I,xg as J,Xi as K,Sg as L,Rg as M,Ag as N,Og as O,kg as P,Ql as Q,_l as R,Pg as S,Zl as T,Lg as U,Fg as V,_ as W,Eo as X,V as Y,fu as Z,Qg as _,S as $,Re as aa,w as ba,Os as ca,Iu as da,Ps as ea,ce as fa,_o as ga,Ou as ha,ku as ia,qu as ja,Wu as ka,de as la,vm as ma,Ye as na,Ae as oa,Je as pa,ia as qa,Ho as ra,fe as sa,Ht as ta,$o as ua,xm as va,zm as wa,tf as xa,Mr as ya,oi as za,ey as Aa,ty as Ba,lf as Ca,ry as Da,oy as Ea,iy as Fa,_n as Ga,zt as Ha,Ei as Ia,fy as Ja,py as Ka,hy as La,gy as Ma,my as Na,hc as Oa,Tf as Pa,xr as Qa,Oy as Ra,_f as Sa,Py as Ta,Hy as Ua,av as Va,si as Wa,Dr as Xa,wr as Ya,nI as Za,Rr as _a,oI as $a,Si as ab,je as bb,Dp as cb,Hc as db,HI as eb,UI as fb,GI as gb,zI as hb,bp as ib,tE as jb,_p as kb,Ln as lb,Ke as mb,re as nb,EE as ob,Op as pb,LE as qb,zc as rb,jp as sb,FE as tb,Vp as ub,Ar as vb,qE as wb,WE as xb,GE as yb,qp as zb,QE as Ab,ZE as Bb,YE as Cb,JE as Db,KE as Eb,XE as Fb,Wp as Gb,Kc as Hb,Xc as Ib,Gp as Jb,el as Kb,tl as Lb,zp as Mb,iD as Nb,Qp as Ob,Zp as Pb,Jp as Qb,Kp as Rb,aD as Sb,lD as Tb,uD as Ub,fD as Vb,pD as Wb,hD as Xb,gD as Yb,mD as Zb,yD as _b,vD as $b,ID as ac,rh as bc,oh as cc,AD as dc,kD as ec,qD as fc,ph as gc,rl as hc,hh as ic,gh as jc,mh as kc,QD as lc,yh as mc,Ih as nc,ZD as oc,YD as pc,JD as qc,KD as rc,rw as sc,ow as tc,iw as uc,sw as vc,aw as wc,lw as xc,uw as yc,dw as zc,fw as Ac,mw as Bc,yw as Cc,vw as Dc,bw as Ec,Sh as Fc,FF as Gc,jF as Hc,VF as Ic,HF as Jc,BF as Kc,$F as Lc,UF as Mc,qF as Nc,Hw as Oc,WF as Pc,GF as Qc,zF as Rc,QF as Sc,ZF as Tc,YF as Uc};
