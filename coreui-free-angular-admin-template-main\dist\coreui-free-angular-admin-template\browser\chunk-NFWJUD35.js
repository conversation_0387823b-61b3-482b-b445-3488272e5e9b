import{E as w,F as E,I as K,db as H,eb as k,f as T,fb as A,gb as M,hb as $,oa as I,qa as _,t as f}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Cb as b,Eb as h,Fb as v,Gb as a,Hb as t,Ib as i,Jb as r,Qb as u,Va as n,eb as x,fc as e,gc as S,hc as C,ja as c,ka as m,ra as g}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";function L(d,o){if(d&1&&(t(0,"button",7),c(),r(1,"svg",17),e(2),i()),d&2){let s=o.$implicit,l=o.$index,y=o.$count;a("itemKey",l)("disabled",l===y-1),n(),a("name",s.icon),n(),C(" ",s.name," ")}}function P(d,o){if(d&1&&(t(0,"c-tab-panel",15),e(1," This is some placeholder content the "),t(2,"strong"),e(3),i(),e(4," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i()),d&2){let s=o.$implicit,l=o.$index;a("itemKey",l),n(3),S(s.name)}}var p=class p{constructor(){this.panes=[{name:"Home 01",id:"tab-01",icon:"cilHome"},{name:"Profile 02",id:"tab-02",icon:"cilUser"},{name:"Contact 03",id:"tab-03",icon:"cilCode"}];this.activeItem=g(0)}handleActiveItemChange(o){this.activeItem.set(o)}};p.\u0275fac=function(s){return new(s||p)},p.\u0275cmp=x({type:p,selectors:[["app-tabs"]],decls:95,vars:15,consts:[["xs","12"],[1,"mb-3"],[3,"activeItemKeyChange","activeItemKey"],["variant","underline-border"],["cTab","",3,"itemKey"],["cIcon","","name","cilHome",1,"me-2"],["cIcon","","name","cilUser",1,"me-2"],["cTab","",3,"itemKey","disabled"],["cIcon","","name","cilCode",1,"me-2"],[1,"p-3",3,"itemKey"],[1,"text-info"],[1,"text-success"],[1,"text-warning"],[3,"activeItemKey"],["variant","tabs"],["cRounded","bottom",1,"p-3","preview",3,"itemKey"],["variant","pills"],["cIcon","",1,"me-2",3,"name"]],template:function(s,l){s&1&&(t(0,"c-row")(1,"c-col",0)(2,"c-card",1)(3,"c-card-header"),e(4,`
        `),t(5,"strong"),e(6,"Angular Tabs"),i(),e(7," "),t(8,"small"),e(9,"underline"),i(),e(10,`
      `),i(),t(11,"c-card-body")(12,"c-tabs",2),u("activeItemKeyChange",function(J){return l.handleActiveItemChange(J)}),t(13,"c-tabs-list",3)(14,"button",4),c(),r(15,"svg",5),e(16," Home "),i(),m(),t(17,"button",4),c(),r(18,"svg",6),e(19," Profile "),i(),m(),t(20,"button",7),c(),r(21,"svg",8),e(22," Contact "),i()(),m(),t(23,"c-tabs-content")(24,"c-tab-panel",9),e(25," This is some placeholder content the "),t(26,"strong",10),e(27,"Home"),i(),e(28," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i(),t(29,"c-tab-panel",9),e(30," This is some placeholder content the "),t(31,"strong",11),e(32,"Profile"),i(),e(33," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i(),t(34,"c-tab-panel",9),e(35," This is some placeholder content the "),t(36,"strong",12),e(37,"Contact"),i(),e(38," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i()()()()()(),t(39,"c-col",0)(40,"c-card",1)(41,"c-card-header"),e(42,`
        `),t(43,"strong"),e(44,"Angular Tabs"),i(),e(45," "),t(46,"small"),e(47,"tabs"),i(),e(48,`
      `),i(),t(49,"c-card-body")(50,"c-tabs",13)(51,"c-tabs-list",14),h(52,L,3,4,"button",7,b),i(),t(54,"c-tabs-content"),h(55,P,5,2,"c-tab-panel",15,b),i()()()()(),t(57,"c-col",0)(58,"c-card",1)(59,"c-card-header"),e(60,`
        `),t(61,"strong"),e(62,"Angular Tabs"),i(),e(63," "),t(64,"small"),e(65,"pills"),i(),e(66,`
      `),i(),t(67,"c-card-body")(68,"c-tabs")(69,"c-tabs-list",16)(70,"button",4),c(),r(71,"svg",5),e(72," Home "),i(),m(),t(73,"button",4),c(),r(74,"svg",6),e(75," Profile "),i(),m(),t(76,"button",4),c(),r(77,"svg",8),e(78," Contact "),i()(),m(),t(79,"c-tabs-content")(80,"c-tab-panel",9),e(81," This is some placeholder content the "),t(82,"strong"),e(83,"Home"),i(),e(84," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i(),t(85,"c-tab-panel",9),e(86," This is some placeholder content the "),t(87,"strong"),e(88,"Profile"),i(),e(89," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i(),t(90,"c-tab-panel",9),e(91," This is some placeholder content the "),t(92,"strong"),e(93,"Contact"),i(),e(94," tab's associated content. Clicking another tab will toggle the visibility of this one for the next. The tab JavaScript swaps classes to control the content visibility and styling. You can use it with tabs, pills, and any other .nav-powered navigation. "),i()()()()()()()),s&2&&(n(12),a("activeItemKey",0),n(2),a("itemKey",0),n(3),a("itemKey",1),n(3),a("itemKey",2)("disabled",l.activeItem()===0),n(4),a("itemKey",0),n(5),a("itemKey",1),n(5),a("itemKey",2),n(16),a("activeItemKey",0),n(2),v(l.panes),n(3),v(l.panes),n(15),a("itemKey",0),n(3),a("itemKey",1),n(3),a("itemKey",2),n(4),a("itemKey",0),n(5),a("itemKey",1),n(5),a("itemKey",2))},dependencies:[E,w,K,I,f,_,k,$,H,M,A,T],encapsulation:2});var G=p;export{G as AppTabsComponent};
