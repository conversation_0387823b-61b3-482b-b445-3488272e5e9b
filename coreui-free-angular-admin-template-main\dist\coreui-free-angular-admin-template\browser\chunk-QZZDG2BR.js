import"./chunk-L3UST63Y.js";var o=[{path:"",data:{title:"Forms"},children:[{path:"",redirectTo:"form-control",pathMatch:"full"},{path:"form-control",loadComponent:()=>import("./chunk-V5QDCN65.js").then(t=>t.FormControlsComponent),data:{title:"Form Control"}},{path:"select",loadComponent:()=>import("./chunk-QAHMZWZS.js").then(t=>t.SelectComponent),data:{title:"Select"}},{path:"checks-radios",loadComponent:()=>import("./chunk-665BDSKF.js").then(t=>t.ChecksRadiosComponent),data:{title:"Checks & Radios"}},{path:"range",loadComponent:()=>import("./chunk-VFFSPAKT.js").then(t=>t.RangesComponent),data:{title:"Range"}},{path:"input-group",loadComponent:()=>import("./chunk-V22WUHFY.js").then(t=>t.InputGroupsComponent),data:{title:"Input Group"}},{path:"floating-labels",loadComponent:()=>import("./chunk-JXT5VNMX.js").then(t=>t.FloatingLabelsComponent),data:{title:"Floating Labels"}},{path:"layout",loadComponent:()=>import("./chunk-QIRA24DH.js").then(t=>t.LayoutComponent),data:{title:"Layout"}},{path:"validation",loadComponent:()=>import("./chunk-B22ULUMX.js").then(t=>t.ValidationComponent),data:{title:"Validation"}}]}];export{o as routes};
