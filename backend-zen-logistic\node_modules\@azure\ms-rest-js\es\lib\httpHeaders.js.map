{"version": 3, "file": "httpHeaders.js", "sourceRoot": "", "sources": ["../../lib/httpHeaders.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,+FAA+F;AAE/F;;GAEG;AACH,SAAS,YAAY,CAAC,UAAkB;IACtC,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;AA4ED,MAAM,UAAU,iBAAiB,CAAC,MAAY;IAC5C,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,KAAK,CAAC;KACd;IAED,IACE,OAAO,MAAM,CAAC,UAAU,KAAK,UAAU;QACvC,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU;QAClC,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU;QAChC,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU;QAChC,OAAO,MAAM,CAAC,QAAQ,KAAK,UAAU;QACrC,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU;QACnC,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU;QACzC,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU;QACzC,OAAO,MAAM,CAAC,WAAW,KAAK,UAAU;QACxC,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU,EACnC;QACA,OAAO,IAAI,CAAC;KACb;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH;IAGE,qBAAY,UAA2B;QACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,UAAU,EAAE;YACd,KAAK,IAAM,UAAU,IAAI,UAAU,EAAE;gBACnC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;aAC9C;SACF;IACH,CAAC;IAED;;;;;OAKG;IACI,yBAAG,GAAV,UAAW,UAAkB,EAAE,WAA4B;QACzD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,GAAG;YAC3C,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;SAC9B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,yBAAG,GAAV,UAAW,UAAkB;QAC3B,IAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QACtE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,8BAAQ,GAAf,UAAgB,UAAkB;QAChC,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACI,4BAAM,GAAb,UAAc,UAAkB;QAC9B,IAAM,MAAM,GAAY,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,gCAAU,GAAjB;QACE,IAAM,MAAM,GAAmB,EAAE,CAAC;QAClC,KAAK,IAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,IAAM,MAAM,GAAe,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;SAClD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,kCAAY,GAAnB;QACE,IAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,KAAK,IAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;SAC3C;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,iCAAW,GAAlB;QACE,IAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACnC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,kCAAY,GAAnB;QACE,IAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAM,OAAO,GAAiB,IAAI,CAAC,YAAY,EAAE,CAAC;QAClD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACvC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,4BAAM,GAAb;QACE,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACI,8BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,2BAAK,GAAZ;QACE,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;IAC5C,CAAC;IACH,kBAAC;AAAD,CAAC,AAxHD,IAwHC"}