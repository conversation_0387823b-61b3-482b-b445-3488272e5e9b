import{b as w}from"./chunk-3MARWV4R.js";import{E as B,F as C,I as _,f as v,n as f,oa as k,qa as I}from"./chunk-Y7QKHPW3.js";import{y}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Db as d,Eb as c,Fb as x,Gb as m,Hb as e,Ib as n,Jb as u,Sb as p,Va as l,eb as g,fc as t,hc as s,ja as b,tc as E}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var h=()=>[];function T(a,r){if(a&1&&(t(0,`
                  `),e(1,"button",39),t(2),n(),t(3,`
                `)),a&2){let i=r.$implicit,o=p().$implicit;l(),m("active",o==="active")("color",i)("disabled",o==="disabled"),l(),s(`
                    `,i.charAt(0).toUpperCase()+i.slice(1),`
                  `)}}function F(a,r){if(a&1&&(t(0,`
            `),e(1,"c-row",36),t(2,`
              `),e(3,"c-col",37),t(4),n(),t(5,`
              `),e(6,"c-col"),t(7,`
                `),c(8,T,4,4,null,null,d),e(10,"button",38),t(11,`Link
                `),n(),t(12,`
              `),n(),t(13,`
            `),n(),t(14,`
          `)),a&2){let i=r.$implicit,o=p();l(4),s(`
                `,i.charAt(0).toUpperCase()+i.slice(1),`
              `),l(4),x(o.colors),l(2),m("active",i==="active")("disabled",i==="disabled")}}function z(a,r){if(a&1&&(t(0,`
                  `),e(1,"button",39),t(2,`
                    `),b(),u(3,"svg",40),t(4),n(),t(5,`
                `)),a&2){let i=r.$implicit,o=p().$implicit;l(),m("active",o==="active")("color",i)("disabled",o==="disabled"),l(3),s(`
                    `,i.charAt(0).toUpperCase()+i.slice(1),`
                  `)}}function U(a,r){if(a&1&&(t(0,`
            `),e(1,"c-row",36),t(2,`
              `),e(3,"c-col",37),t(4),n(),t(5,`
              `),e(6,"c-col"),t(7,`
                `),c(8,z,6,4,null,null,d),e(10,"button",38),t(11,`
                  `),b(),u(12,"svg",40),t(13,`
                  Link
                `),n(),t(14,`
              `),n(),t(15,`
            `),n(),t(16,`
          `)),a&2){let i=r.$implicit,o=p();l(4),s(`
                `,i.charAt(0).toUpperCase()+i.slice(1),`
              `),l(4),x(o.colors),l(2),m("active",i==="active")("disabled",i==="disabled")}}function L(a,r){if(a&1&&(t(0,`
                  `),e(1,"button",41),t(2),n(),t(3,`
                `)),a&2){let i=r.$implicit,o=p().$implicit;l(),m("active",o==="active")("color",i)("disabled",o==="disabled"),l(),s(`
                    `,i.charAt(0).toUpperCase()+i.slice(1),`
                  `)}}function $(a,r){if(a&1&&(t(0,`
            `),e(1,"c-row",36),t(2,`
              `),e(3,"c-col",37),t(4),n(),t(5,`
              `),e(6,"c-col"),t(7,`
                `),c(8,L,4,4,null,null,d),n(),t(10,`
            `),n(),t(11,`
          `)),a&2){let i=r.$implicit,o=p();l(4),s(`
                `,i.charAt(0).toUpperCase()+i.slice(1),`
              `),l(4),x(o.colors)}}function D(a,r){if(a&1&&(t(0,`
                  `),e(1,"button",42),t(2),n(),t(3,`
                `)),a&2){let i=r.$implicit,o=p().$implicit;l(),m("active",o==="active")("color",i)("disabled",o==="disabled"),l(),s(`
                    `,i.charAt(0).toUpperCase()+i.slice(1),`
                  `)}}function P(a,r){if(a&1&&(t(0,`
            `),e(1,"c-row",36),t(2,`
              `),e(3,"c-col",37),t(4),n(),t(5,`
              `),e(6,"c-col"),t(7,`
                `),c(8,D,4,4,null,null,d),n(),t(10,`
            `),n(),t(11,`
          `)),a&2){let i=r.$implicit,o=p();l(4),s(`
                `,i.charAt(0).toUpperCase()+i.slice(1),`
              `),l(4),x(o.colors)}}function R(a,r){if(a&1&&(t(0,`
            `),e(1,"button",43),t(2),n(),t(3,`
          `)),a&2){let i=r.$implicit;l(),m("color",i),l(),s(`
              `,i.charAt(0).toUpperCase()+i.slice(1),`
            `)}}function j(a,r){if(a&1&&(t(0,`
            `),e(1,"button",44),t(2),n(),t(3,`
          `)),a&2){let i=r.$implicit;l(),m("color",i),l(),s(`
              `,i.charAt(0).toUpperCase()+i.slice(1),`
            `)}}var S=class S{constructor(){this.states=["normal","active","disabled"];this.colors=["primary","secondary","success","danger","warning","info","light","dark"]}};S.\u0275fac=function(i){return new(i||S)},S.\u0275cmp=g({type:S,selectors:[["app-buttons"]],decls:430,vars:6,consts:[["xs","12"],["id","AngularButton",1,"mb-4"],[1,"text-body-secondary","small"],["fragment","AngularButton","href","components/button"],["id","AngularButtonWithIcons",1,"mb-4"],["href","https://icons.coreui.io/"],["fragment","AngularButtonWithIcons","href","components/button"],[1,"mb-4"],["ngPreserveWhitespaces",""],["href","components/button#button-components"],["cButton","","color","primary",1,"me-1",3,"routerLink"],["cButton","","color","primary","type","submit",1,"me-1"],["cButton","","color","primary","type","button","value","Input",1,"me-1"],["cButton","","color","primary","type","submit","value","Submit",1,"me-1"],["cButton","","color","primary","type","reset","value","Reset",1,"me-1"],["href","components/button#outline-buttons"],["href","components/button#ghost-buttons"],["href","components/button#sizes"],["cButton","","color","primary","size","lg",1,"mb-3"],["cButton","","color","secondary","size","lg",1,"mb-3"],["cButton","","color","primary","size","sm",1,"mb-3"],["cButton","","color","secondary","size","sm",1,"mb-3"],["href","components/button#pill-buttons"],["href","components/button#square"],["href","components/button#disabled-state"],["cButton","","color","primary","disabled","","size","lg"],["cButton","","color","secondary","disabled","","size","lg"],["cButton","","color","primary","disabled","","size","lg",3,"routerLink"],["cButton","","color","secondary","disabled","","size","lg",3,"routerLink"],["href","components/button#block-buttons"],[1,"d-grid","gap-2"],["cButton","","color","primary"],[1,"d-grid","gap-2","d-md-block"],[1,"d-grid","gap-2","col-6","mx-auto"],[1,"d-grid","gap-2","d-md-flex","justify-content-md-end"],["cButton","","color","primary",1,"me-md-2"],[1,"align-items-center","mb-3"],["xl","2","xs","12",1,"mb-3","mb-xl-0"],["cButton","","color","link",3,"active","disabled"],["cButton","",3,"active","color","disabled"],["cIcon","","name","cil-bell",1,"me-2"],["cButton","","variant","outline",3,"active","color","disabled"],["cButton","","variant","ghost",3,"active","color","disabled"],["cButton","","shape","rounded-pill",1,"me-1",3,"color"],["cButton","","shape","rounded-0",1,"me-1",3,"color"]],template:function(i,o){i&1&&(e(0,"c-row"),t(1,`
  `),e(2,"c-col",0),t(3,`
    `),e(4,"c-card",1),t(5,`
      `),e(6,"c-card-header"),t(7,`
        `),e(8,"strong"),t(9,"Angular Button"),n(),t(10,`
      `),n(),t(11,`
      `),e(12,"c-card-body"),t(13,`
        `),e(14,"p",2),t(15,`
          CoreUI includes a bunch of predefined buttons components, each serving its own
          semantic purpose. Buttons show what action will happen when the user clicks or touches
          it. CoreUI buttons are used to initialize operations, both in the background or
          foreground of an experience.
        `),n(),t(16,`
        `),e(17,"app-docs-example",3),t(18,`
          `),c(19,F,15,3,null,null,d),n(),t(21,`
      `),n(),t(22,`
    `),n(),t(23,`
  `),n(),t(24,`
  `),e(25,"c-col",0),t(26,`
    `),e(27,"c-card",4),t(28,`
      `),e(29,"c-card-header"),t(30,`
        `),e(31,"strong"),t(32,"Angular Button"),n(),t(33," "),e(34,"small"),t(35,"with icons"),n(),t(36,`
      `),n(),t(37,`
      `),e(38,"c-card-body"),t(39,`
        `),e(40,"p",2),t(41,`
          You can combine button with our `),e(42,"a",5),t(43,"CoreUI Icons"),n(),t(44,`.
        `),n(),t(45,`
        `),e(46,"app-docs-example",6),t(47,`
          `),c(48,U,17,3,null,null,d),n(),t(50,`
      `),n(),t(51,`
    `),n(),t(52,`
  `),n(),t(53,`
  `),e(54,"c-col",0),t(55,`
    `),e(56,"c-card",7),t(57,`
      `),e(58,"c-card-header",8),t(59,`
        `),e(60,"strong"),t(61,"Angular Button"),n(),t(62," "),e(63,"small"),t(64,"Button components"),n(),t(65,`
      `),n(),t(66,`
      `),e(67,"c-card-body"),t(68,`
        `),e(69,"p",2),t(70,`
          The `),e(71,"code"),t(72,"<button>"),n(),t(73,` component are designed for
          `),e(74,"code"),t(75,"<button>"),n(),t(76," , "),e(77,"code"),t(78,"<a>"),n(),t(79," or "),e(80,"code"),t(81,"<input>"),n(),t(82,`
          elements (though some browsers may apply a slightly different rendering).
        `),n(),t(83,`
        `),e(84,"p",2),t(85,`
          If you're using `),e(86,"code"),t(87,"<button>"),n(),t(88," component as "),e(89,"code"),t(90,"<a>"),n(),t(91,`
          elements that are used to trigger functionality ex. collapsing content, these links
          should be given a `),e(92,"code"),t(93,'role="button"'),n(),t(94,` to adequately communicate their
          meaning to assistive technologies such as screen readers.
        `),n(),t(95,`
        `),e(96,"app-docs-example",9),t(97,`
          `),e(98,"a",10),t(99,`
            Link
          `),n(),t(100,`
          `),e(101,"button",11),t(102,`
            Button
          `),n(),t(103,`
          `),u(104,"input",12),t(105,`
          `),u(106,"input",13),t(107,`
          `),u(108,"input",14),t(109,`
        `),n(),t(110,`
      `),n(),t(111,`
    `),n(),t(112,`
  `),n(),t(113,`
  `),e(114,"c-col",0),t(115,`
    `),e(116,"c-card",7),t(117,`
      `),e(118,"c-card-header"),t(119,`
        `),e(120,"strong"),t(121,"Angular Button"),n(),t(122," "),e(123,"small"),t(124,"outline"),n(),t(125,`
      `),n(),t(126,`
      `),e(127,"c-card-body"),t(128,`
        `),e(129,"p",2),t(130,`
          If you need a button, but without the strong background colors. Set
          `),e(131,"code"),t(132,'variant="outline"'),n(),t(133,` prop to remove all background colors.
        `),n(),t(134,`
        `),e(135,"app-docs-example",15),t(136,`
          `),c(137,$,12,1,null,null,d),n(),t(139,`
      `),n(),t(140,`
    `),n(),t(141,`
  `),n(),t(142,`
  `),e(143,"c-col",0),t(144,`
    `),e(145,"c-card",7),t(146,`
      `),e(147,"c-card-header"),t(148,`
        `),e(149,"strong"),t(150,"Angular Button"),n(),t(151," "),e(152,"small"),t(153,"ghost"),n(),t(154,`
      `),n(),t(155,`
      `),e(156,"c-card-body"),t(157,`
        `),e(158,"p",2),t(159,`
          If you need a ghost variant of button, set `),e(160,"code"),t(161,'variant="ghost"'),n(),t(162,` prop
          to remove all background colors.
        `),n(),t(163,`
        `),e(164,"app-docs-example",16),t(165,`
          `),c(166,P,12,1,null,null,d),n(),t(168,`
      `),n(),t(169,`
    `),n(),t(170,`
  `),n(),t(171,`
  `),e(172,"c-col",0),t(173,`
    `),e(174,"c-card",7),t(175,`
      `),e(176,"c-card-header"),t(177,`
        `),e(178,"strong"),t(179,"Angular Button"),n(),t(180," "),e(181,"small"),t(182,"Sizes"),n(),t(183,`
      `),n(),t(184,`
      `),e(185,"c-card-body"),t(186,`
        `),e(187,"p",2),t(188,`
          Larger or smaller buttons? Add `),e(189,"code"),t(190,'size="lg"'),n(),t(191,`
          `),e(192,"code"),t(193,'size="sm"'),n(),t(194,` for additional sizes.
        `),n(),t(195,`
        `),e(196,"app-docs-example",17),t(197,`
          `),e(198,"button",18),t(199,`
            Large button
          `),n(),t(200,`
          `),e(201,"button",19),t(202,`
            Large button
          `),n(),t(203,`
        `),n(),t(204,`
        `),e(205,"app-docs-example",17),t(206,`
          `),u(207,"br"),t(208,`
          `),e(209,"button",20),t(210,`
            Small button
          `),n(),t(211,`
          `),e(212,"button",21),t(213,`
            Small button
          `),n(),t(214,`
        `),n(),t(215,`
      `),n(),t(216,`
    `),n(),t(217,`
  `),n(),t(218,`
  `),e(219,"c-col",0),t(220,`
    `),e(221,"c-card",7),t(222,`
      `),e(223,"c-card-header"),t(224,`
        `),e(225,"strong"),t(226,"Angular Button"),n(),t(227," "),e(228,"small"),t(229,"Pill"),n(),t(230,`
      `),n(),t(231,`
      `),e(232,"c-card-body"),t(233,`
        `),e(234,"app-docs-example",22),t(235,`
          `),c(236,R,4,2,null,null,d),n(),t(238,`
      `),n(),t(239,`
    `),n(),t(240,`
  `),n(),t(241,`
  `),e(242,"c-col",0),t(243,`
    `),e(244,"c-card",7),t(245,`
      `),e(246,"c-card-header"),t(247,`
        `),e(248,"strong"),t(249,"Angular Button"),n(),t(250," "),e(251,"small"),t(252,"Square"),n(),t(253,`
      `),n(),t(254,`
      `),e(255,"c-card-body"),t(256,`
        `),e(257,"app-docs-example",23),t(258,`
          `),c(259,j,4,2,null,null,d),n(),t(261,`
      `),n(),t(262,`
    `),n(),t(263,`
  `),n(),t(264,`
  `),e(265,"c-col",0),t(266,`
    `),e(267,"c-card",7),t(268,`
      `),e(269,"c-card-header"),t(270,`
        `),e(271,"strong"),t(272,"Angular Button"),n(),t(273," "),e(274,"small"),t(275,"Disabled state"),n(),t(276,`
      `),n(),t(277,`
      `),e(278,"c-card-body"),t(279,`
        `),e(280,"p",2),t(281,`
          Add the `),e(282,"code"),t(283,"disabled"),n(),t(284," boolean prop to any "),e(285,"code"),t(286,"<button>"),n(),t(287,`
          component to make buttons look inactive. Disabled button has
          `),e(288,"code"),t(289,"pointer-events: none"),n(),t(290,` applied to, disabling hover and active states from
          triggering.
        `),n(),t(291,`
        `),e(292,"app-docs-example",24),t(293,`
          `),e(294,"button",25),t(295,`
            Primary button
          `),n(),t(296,`
          `),e(297,"button",26),t(298,`
            Button
          `),n(),t(299,`
        `),n(),t(300,`
        `),e(301,"p",2),t(302,`
          Disabled buttons using the `),e(303,"code"),t(304,"<a>"),n(),t(305,` component act a little different:
        `),n(),t(306,`
        `),e(307,"p",2),t(308,`
          `),e(309,"code"),t(310,"<a>"),n(),t(311,"s don'tsupport the "),e(312,"code"),t(313,"disabled"),n(),t(314,` attribute, so
          CoreUI has to add `),e(315,"code"),t(316,".disabled"),n(),t(317,` class to make buttons look inactive.
          CoreUI also has to add to the disabled button component
          `),e(318,"code"),t(319,'aria-disabled="true"'),n(),t(320,` attribute to show the state of the component
          to assistive technologies.
        `),n(),t(321,`
        `),e(322,"app-docs-example",24),t(323,`
          `),e(324,"a",27),t(325,`
            Primary link
          `),n(),t(326,`
          `),e(327,"a",28),t(328,`
            Link
          `),n(),t(329,`
        `),n(),t(330,`
      `),n(),t(331,`
    `),n(),t(332,`
  `),n(),t(333,`
  `),e(334,"c-col",0),t(335,`
    `),e(336,"c-card",7),t(337,`
      `),e(338,"c-card-header"),t(339,`
        `),e(340,"strong"),t(341,"Angular Button"),n(),t(342," "),e(343,"small"),t(344,"Block buttons"),n(),t(345,`
      `),n(),t(346,`
      `),e(347,"c-card-body"),t(348,`
        `),e(349,"p",2),t(350,`
          Create buttons that span the full width of a parent\u2014by using utilities.
        `),n(),t(351,`
        `),e(352,"app-docs-example",29),t(353,`
          `),e(354,"div",30),t(355,`
            `),e(356,"button",31),t(357,"Button"),n(),t(358,`
            `),e(359,"button",31),t(360,"Button"),n(),t(361,`
          `),n(),t(362,`
        `),n(),t(363,`
        `),e(364,"p",2),t(365,`
          Here we create a responsive variation, starting with vertically stacked buttons until
          the `),e(366,"code"),t(367,"md"),n(),t(368," breakpoint, where "),e(369,"code"),t(370,".d-md-block"),n(),t(371,` replaces the
          `),e(372,"code"),t(373,".d-grid"),n(),t(374," class, thus nullifying the "),e(375,"code"),t(376,"gap-2"),n(),t(377,` utility. Resize
          your browser to see them change.
        `),n(),t(378,`
        `),e(379,"app-docs-example",29),t(380,`
          `),e(381,"div",32),t(382,`
            `),e(383,"button",31),t(384,"Button"),n(),t(385,`
            `),e(386,"button",31),t(387,"Button"),n(),t(388,`
          `),n(),t(389,`
        `),n(),t(390,`
        `),e(391,"p",2),t(392,`
          You can adjust the width of your block buttons with grid column width classes. For
          example, for a half-width "block button", use `),e(393,"code"),t(394,".col-6"),n(),t(395,`. Center it
          horizontally with `),e(396,"code"),t(397,".mx-auto"),n(),t(398,`, too.
        `),n(),t(399,`
        `),e(400,"app-docs-example",29),t(401,`
          `),e(402,"div",33),t(403,`
            `),e(404,"button",31),t(405,"Button"),n(),t(406,`
            `),e(407,"button",31),t(408,"Button"),n(),t(409,`
          `),n(),t(410,`
        `),n(),t(411,`
        `),e(412,"p",2),t(413,`
          Additional utilities can be used to adjust the alignment of buttons when horizontal.
          Here we've taken our previous responsive example and added some flex utilities and
          a margin utility on the button to right align the buttons when they're no longer
          stacked.
        `),n(),t(414,`
        `),e(415,"app-docs-example",29),t(416,`
          `),e(417,"div",34),t(418,`
            `),e(419,"button",35),t(420,`
              Button
            `),n(),t(421,`
            `),e(422,"button",31),t(423,"Button"),n(),t(424,`
          `),n(),t(425,`
        `),n(),t(426,`
      `),n(),t(427,`
    `),n(),t(428,`
  `),n(),t(429,`
`),n()),i&2&&(l(19),x(o.states),l(29),x(o.states),l(50),m("routerLink",E(3,h)),l(39),x(o.states),l(29),x(o.states),l(70),x(o.colors),l(23),x(o.colors),l(65),m("routerLink",E(4,h)),l(3),m("routerLink",E(5,h)))},dependencies:[I,k,B,_,C,w,f,v,y],encapsulation:2});var A=S;export{A as ButtonsComponent};
