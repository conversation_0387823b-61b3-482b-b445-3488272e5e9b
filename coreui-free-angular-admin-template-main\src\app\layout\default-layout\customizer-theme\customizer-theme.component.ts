import {
  Component,
  OnInit,
  AfterViewInit,
  OnDestroy,
  ViewChild,
  ElementRef,
  Renderer2,
  Output,
  EventEmitter,
  HostListener
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IconDirective } from '@coreui/icons-angular';

import { LayoutService, ConfigService, CustomerService, SessionStorageService } from '../../../../services';
import {
  CustomizerOptions,
  TemplateConfig,
  SIDEBAR_COLORS,
  SIDEBAR_SIZES
} from '../../../../models';

@Component({
  selector: 'app-customizer-theme',
  standalone: true,
  imports: [CommonModule, FormsModule, IconDirective],
  templateUrl: './customizer-theme.component.html',
  styleUrl: './customizer-theme.component.scss'
})
export class CustomizerThemeComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
  @ViewChild('customizer', { static: false }) customizer!: ElementRef;
  @Output() directionEvent = new EventEmitter<CustomizerOptions>();

  private isOpenSubject = new BehaviorSubject<boolean>(false);
  isOpen$ = this.isOpenSubject.asObservable();

  options: CustomizerOptions = {
    direction: 'ltr',
    bgColor: SIDEBAR_COLORS.BLACK,
    bgImage: 'assets/img/sidebar-bg/01.jpg',
    bgImageDisplay: true,
    compactMenu: false,
    sidebarSize: SIDEBAR_SIZES.MEDIUM
  };

  size: string = SIDEBAR_SIZES.MEDIUM;
  isOpen = true;
  public config: TemplateConfig = {} as TemplateConfig;
  layoutSub?: Subscription;
  isBgImageDisplay = true;
  selectedBgColor: string = SIDEBAR_COLORS.BLACK;
  selectedBgImage: string = 'assets/img/sidebar-bg/01.jpg';


  // Constants for template
  readonly SIDEBAR_COLORS = SIDEBAR_COLORS;
  readonly SIDEBAR_SIZES = SIDEBAR_SIZES;

  constructor(
    private renderer: Renderer2,
    private layoutService: LayoutService,
    private configService: ConfigService,
    private customerService: CustomerService,
    private sessionStorageService: SessionStorageService
  ) {
    this.layoutSub = this.layoutService.customizerChangeEmitted$.subscribe(
      (change: CustomizerOptions | string) => {
        if (typeof change === 'object' && change) {
          if (change.bgColor) {
            this.selectedBgColor = change.bgColor;
            this.selectedBgImage = change.bgImage;
          }
        }
      }
    );
  }

  ngOnInit(): void {
    const color = sessionStorage.getItem('color') !== 'null' ?
      sessionStorage.getItem('color') : SIDEBAR_COLORS.PRIMARY;

    if (color) {
      this.changeSidebarBgColor(color);
    }

    this.layoutService.customizerChangeEmitted$.subscribe((change) => {
      if (typeof change === 'string' && change === 'toggle') {
        this.isOpen = !this.isOpen;
      }
    });

    this.config = this.configService.getConfig();
    this.isOpen = !this.config.layout.customizer.hidden;

    if (this.config.layout.sidebar.size) {
      this.options.sidebarSize = this.config.layout.sidebar.size;
      this.size = this.config.layout.sidebar.size;
    }

    setTimeout(() => {
      if (color) {
        this.changeSidebarBgColor(color);
      }
    }, 100);
  }

  ngAfterViewInit(): void {
    // Initialize customizer with default sidebar settings
    setTimeout(() => {
      // Set default sidebar color based on current theme
      this.options.bgColor = SIDEBAR_COLORS.BLACK;
      this.selectedBgColor = SIDEBAR_COLORS.BLACK;
      if (this.isBgImageDisplay) {
        this.options.bgImageDisplay = true;
      }
    }, 0);
  }

  ngOnDestroy(): void {
    if (this.layoutSub) {
      this.layoutSub.unsubscribe();
    }
  }

  sendOptions(): void {
    this.directionEvent.emit(this.options);
    this.layoutService.emitChange(this.options);
  }

  bgImageDisplay(event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
      this.options.bgImageDisplay = true;
      this.isBgImageDisplay = true;
    } else {
      this.options.bgImageDisplay = false;
      this.isBgImageDisplay = false;
    }
    this.layoutService.emitCustomizerChange(this.options);
  }

  toggleCompactMenu(event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
      this.options.compactMenu = true;
    } else {
      this.options.compactMenu = false;
    }
    this.layoutService.emitCustomizerChange(this.options);
  }

  changeSidebarWidth(value: string): void {
    this.options.sidebarSize = value;
    this.size = value;
    this.layoutService.emitCustomizerChange(this.options);
  }





  toggleCustomizer(): void {
    if (this.isOpen) {
      this.renderer.removeClass(this.customizer.nativeElement, 'open');
      this.isOpen = false;
    } else {
      this.renderer.addClass(this.customizer.nativeElement, 'open');
      this.isOpen = true;
    }
  }

  closeCustomizer(): void {
    this.renderer.removeClass(this.customizer.nativeElement, 'open');
    this.updateClientColor();
    this.isOpen = false;
  }

  changeSidebarBgColor(color: string): void {
    this.selectedBgColor = color;
    this.options.bgColor = color;
    if (this.isBgImageDisplay) {
      this.options.bgImageDisplay = true;
    }
    this.layoutService.emitCustomizerChange(this.options);

    // Immediately save color to sessionStorage and backend
    this.updateClientColor();
  }

  changeSidebarBgImage(url: string): void {
    this.selectedBgImage = url;
    this.options.bgImage = url;
    if (this.isBgImageDisplay) {
      this.options.bgImageDisplay = true;
    }
    this.layoutService.emitCustomizerChange(this.options);
  }

  @HostListener('document:dblclick', ['$event'])
  onClickOutside(event: Event): void {
    if (!this.isOpen || this.isDescendant(event.target as HTMLElement, 'customizer')) {
      return;
    }
    this.updateClientColor();
    this.isOpen = false;
  }

  private isDescendant(target: HTMLElement | null, id: string): boolean {
    while (target) {
      if (target.id === id) {
        return true;
      }
      target = target.parentNode as HTMLElement;
    }
    return false;
  }

  toggle(): void {
    this.isOpenSubject.next(!this.isOpenSubject.value);
  }

  private updateClientColor(): void {
    const currentColor = sessionStorage.getItem('color');
    if (this.options.bgColor !== currentColor) {
      sessionStorage.setItem('color', this.options.bgColor);

      const sessionData = this.sessionStorageService.getSession();
      if (sessionData?.iduser) {
        const colorData = { color: this.options.bgColor };
        this.customerService.updateCustomerColor(sessionData.iduser.toString(), colorData)
          .subscribe({
            next: (response) => {
              console.log('Color updated successfully:', response);
            },
            error: (error) => {
              console.error('Error updating color:', error);
            }
          });
      }
    }
  }
}
