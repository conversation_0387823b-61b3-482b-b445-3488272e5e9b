{"version": 3, "file": "uuidUtils-browser.mjs", "sourceRoot": "", "sources": ["../../src/uuidUtils-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAElC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AAUrD,gEAAgE;AAChE,MAAM,YAAY,GAChB,OAAO,CAAA,MAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,MAAM,0CAAE,UAAU,CAAA,KAAK,UAAU;IAClD,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;IACtD,CAAC,CAAC,YAAY,CAAC;AAEnB;;;;GAIG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,YAAY,EAAE,CAAC;AACxB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { generateUUID } from \"./uuidUtils.common.js\";\n\ninterface Crypto {\n  randomUUID(): string;\n}\n\ndeclare const globalThis: {\n  crypto: Crypto;\n};\n\n// NOTE: This could be undefined if not used in a secure context\nconst uuidFunction =\n  typeof globalThis?.crypto?.randomUUID === \"function\"\n    ? globalThis.crypto.randomUUID.bind(globalThis.crypto)\n    : generateUUID;\n\n/**\n * Generated Universally Unique Identifier\n *\n * @returns RFC4122 v4 UUID.\n */\nexport function randomUUID(): string {\n  return uuidFunction();\n}\n"]}