import{b}from"./chunk-3MARWV4R.js";import{E as c,F as s,I as p,n as l,oa as g,qa as x,s as d,x as m}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as t,Ib as n,Jb as r,eb as o,fc as e}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var i=class i{constructor(){}};i.\u0275fac=function(a){return new(a||i)},i.\u0275cmp=o({type:i,selectors:[["app-badges"]],decls:174,vars:0,consts:[["lg","6"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","components/badge"],["color","secondary"],["cButton","","color","primary"],[1,"visually-hidden"],["href","components/badge#contextual-variations"],["color","primary",1,"me-1"],["color","success",1,"me-1"],["color","danger",1,"me-1"],["color","warning",1,"me-1"],["color","info",1,"me-1"],["color","light","textColor","dark",1,"me-1"],["color","dark",1,"me-1"],["href","components/badge#pill-badges"],["color","primary","shape","rounded-pill",1,"me-1"],["color","success","shape","rounded-pill",1,"me-1"],["color","danger","shape","rounded-pill",1,"me-1"],["color","warning","shape","rounded-pill",1,"me-1"],["color","info","shape","rounded-pill",1,"me-1"],["color","light","shape","rounded-pill","textColor","dark",1,"me-1"],["color","dark","shape","rounded-pill",1,"me-1"],["href","components/badge#positioned"],["cButton","","color","primary",1,"position-relative","mx-2"],["color","danger","position","top-start","shape","rounded-pill"],["color","danger","position","top-end","shape","rounded-pill"],["color","danger","position","bottom-start","shape","rounded-pill"],["color","danger","position","bottom-end","shape","rounded-pill"],["cButton","","color","primary",1,"position-relative"],["color","danger","position","top-end","shape","rounded-circle","cBorder","light",1,"p-2"]],template:function(a,h){a&1&&(t(0,"c-row")(1,"c-col",0)(2,"c-card",1)(3,"c-card-header"),e(4,`
        `),t(5,"strong"),e(6,"Angular Badges"),n(),e(7,`
      `),n(),t(8,"c-card-body")(9,"p",2),e(10," Bootstrap badge scale to suit the size of the parent element by using relative font sizing and "),t(11,"code"),e(12,"em"),n(),e(13," units. "),n(),t(14,"app-docs-example",3)(15,"h1"),e(16," Example heading "),t(17,"c-badge",4),e(18,"New"),n()(),t(19,"h2"),e(20," Example heading "),t(21,"c-badge",4),e(22,"New"),n()(),t(23,"h3"),e(24," Example heading "),t(25,"c-badge",4),e(26,"New"),n()(),t(27,"h4"),e(28," Example heading "),t(29,"c-badge",4),e(30,"New"),n()(),t(31,"h5"),e(32," Example heading "),t(33,"c-badge",4),e(34,"New"),n()(),t(35,"h6"),e(36," Example heading "),t(37,"c-badge",4),e(38,"New"),n()()(),t(39,"p",2),e(40," Badges can be used as part of links or buttons to provide a counter. "),n(),t(41,"app-docs-example",3)(42,"button",5),e(43," Notifications "),t(44,"c-badge",4),e(45,"4"),n()()(),t(46,"p",2),e(47," Remark that depending on how you use them, badges may be complicated for users of screen readers and related assistive technologies. "),n(),t(48,"p",2),e(49," Unless the context is clear, consider including additional context with a visually hidden piece of additional text. "),n(),t(50,"app-docs-example",3)(51,"button",5),e(52," Profile "),t(53,"c-badge",4),e(54,"9"),n(),t(55,"span",6),e(56,"unread messages"),n()()()()()(),t(57,"c-col",0)(58,"c-card",1)(59,"c-card-header"),e(60,`
        `),t(61,"strong"),e(62,"AngularBadges"),n(),e(63," "),t(64,"small"),e(65,"Contextual variations"),n(),e(66,`
      `),n(),t(67,"c-card-body")(68,"p",2),e(69," Add any of the below-mentioned "),t(70,"code"),e(71,"color"),n(),e(72," props to modify the presentation of a badge. "),n(),t(73,"app-docs-example",7)(74,"c-badge",8),e(75,"primary"),n(),t(76,"c-badge",9),e(77,"success"),n(),t(78,"c-badge",10),e(79,"danger"),n(),t(80,"c-badge",11),e(81,"warning"),n(),t(82,"c-badge",12),e(83,"info"),n(),t(84,"c-badge",13),e(85,"light"),n(),t(86,"c-badge",14),e(87,"dark"),n()()()(),t(88,"c-card",1)(89,"c-card-header"),e(90,`
        `),t(91,"strong"),e(92,"Angular Badges"),n(),e(93," "),t(94,"small"),e(95,"Pill badges"),n(),e(96,`
      `),n(),t(97,"c-card-body")(98,"p",2),e(99," Apply the "),t(100,"code"),e(101,'shape="rounded-pill"'),n(),e(102," prop to make badges rounded. "),n(),t(103,"app-docs-example",15)(104,"c-badge",16),e(105," primary "),n(),t(106,"c-badge",17),e(107," success "),n(),t(108,"c-badge",18),e(109," danger "),n(),t(110,"c-badge",19),e(111," warning "),n(),t(112,"c-badge",20),e(113," info "),n(),t(114,"c-badge",21),e(115," light "),n(),t(116,"c-badge",22),e(117," dark "),n()()()(),t(118,"c-card",1)(119,"c-card-header"),e(120,`
        `),t(121,"strong"),e(122,"Angular Badges"),n(),e(123," "),t(124,"small"),e(125,"Positioned"),n(),e(126,`
      `),n(),t(127,"c-card-body")(128,"p",2),e(129," Use position prop to modify a component and position it in the corner of a link or button. "),n(),t(130,"app-docs-example",23)(131,"button",24),e(132," Profile "),t(133,"c-badge",25),e(134,"99+"),n(),t(135,"span",6),e(136,"unread messages"),n()(),t(137,"button",24),e(138," Profile "),t(139,"c-badge",26),e(140,"99+"),n(),t(141,"span",6),e(142,"unread messages"),n()(),r(143,"br"),t(144,"button",24),e(145," Profile "),t(146,"c-badge",27),e(147,"99+"),n(),t(148,"span",6),e(149,"unread messages"),n()(),t(150,"button",24),e(151," Profile "),t(152,"c-badge",28),e(153,"99+"),n(),t(154,"span",6),e(155,"unread messages"),n()()()()(),t(156,"c-card",1)(157,"c-card-header"),e(158,`
        `),t(159,"strong"),e(160,"Angular Badges"),n(),e(161," "),t(162,"small"),e(163,"Indicator"),n(),e(164,`
      `),n(),t(165,"c-card-body")(166,"p",2),e(167," You can also create more generic indicators without a counter using a few more utilities. "),n(),t(168,"app-docs-example",23)(169,"button",29),e(170," Profile "),t(171,"c-badge",30)(172,"span",6),e(173,"unread messages"),n()()()()()()()())},dependencies:[x,g,c,p,s,b,m,l,d],styles:['.dark-theme[_nghost-%COMP%]     c-badge.bg-light:not([class*="dark:"]), .dark-theme   [_nghost-%COMP%]     c-badge.bg-light:not([class*="dark:"]){color:var(--cui-body-bg)}.dark-theme[_nghost-%COMP%]     c-badge.bg-light-gradient:not([class*="dark:"]), .dark-theme   [_nghost-%COMP%]     c-badge.bg-light-gradient:not([class*="dark:"]){color:var(--cui-body-bg)}']});var S=i;export{S as BadgesComponent};
