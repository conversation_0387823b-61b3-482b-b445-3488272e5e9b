import{$ as y,$a as Ci,A as De,Aa as ci,Ac as <PERSON>,B as ne,Ba as Wn,Bc as ut,Ca as li,D as Bn,<PERSON> as st,<PERSON> as Ue,<PERSON>a as Yn,Fc as Ni,Ga as Zn,Ha as Se,Hc as <PERSON>,I as ye,Ia as $e,J as ce,Ja as di,Jb as _i,K as jn,Ka as hi,L as ni,La as fi,Ma as pi,N as ri,Na as gi,Nc as Ie,Oa as mi,Oc as ki,P as ii,Pa as vi,Pc as xi,Q as k,Qa as Re,Qb as Ii,Qc as ct,R as $n,T as O,Ta as Di,Uc as Ui,Vb as Mi,W as D,Wa as be,Xa as yi,Xb as Fi,Y as g,Ya as wi,Yb as Oi,Z as we,Za as Ae,_ as oi,_a as _,a as Xr,aa as p,ab as at,b as Jr,ba as l,bb as Kn,c as $t,ca as ot,cb as Ei,d as xn,db as Ht,e as Un,ea as si,eb as Si,f as V,fa as re,fb as Te,g as j,ga as $,gb as Z,hb as Gt,j as te,k as F,l as f,la as Be,lb as Ri,m as rt,ma as T,mb as _e,n as Qr,na as zn,nb as ie,o as ei,oa as Vn,p as w,pa as je,pb as bi,q as zt,qb as Xn,r as L,ra as ai,rb as qt,s as it,sb as Ai,t as ti,ta as ui,tb as Jn,u as Vt,ua as Hn,ub as Qn,va as Ce,vb as er,wa as Gn,xa as qn,y as H,ya as Ee,zb as Ti}from"./chunk-J5YWIVYY.js";import{a as d,b as P,g as jt}from"./chunk-L3UST63Y.js";var $i=null;function oe(){return $i}function tr(t){$i??=t}var lt=class{},dt=(()=>{class t{historyGo(e){throw new Error("")}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(zi),providedIn:"platform"})}return t})(),nr=new y(""),zi=(()=>{class t extends dt{_location;_history;_doc=l(T);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return oe().getBaseHref(this._doc)}onPopState(e){let n=oe().getGlobalEventTarget(this._doc,"window");return n.addEventListener("popstate",e,!1),()=>n.removeEventListener("popstate",e)}onHashChange(e){let n=oe().getGlobalEventTarget(this._doc,"window");return n.addEventListener("hashchange",e,!1),()=>n.removeEventListener("hashchange",e)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(e){this._location.pathname=e}pushState(e,n,i){this._history.pushState(e,n,i)}replaceState(e,n,i){this._history.replaceState(e,n,i)}forward(){this._history.forward()}back(){this._history.back()}historyGo(e=0){this._history.go(e)}getState(){return this._history.state}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>new t,providedIn:"platform"})}return t})();function Wt(t,r){return t?r?t.endsWith("/")?r.startsWith("/")?t+r.slice(1):t+r:r.startsWith("/")?t+r:`${t}/${r}`:t:r}function Bi(t){let r=t.search(/#|\?|$/);return t[r-1]==="/"?t.slice(0,r-1)+t.slice(r):t}function G(t){return t&&t[0]!=="?"?`?${t}`:t}var z=(()=>{class t{historyGo(e){throw new Error("")}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(Zt),providedIn:"root"})}return t})(),Yt=new y(""),Zt=(()=>{class t extends z{_platformLocation;_baseHref;_removeListenerFns=[];constructor(e,n){super(),this._platformLocation=e,this._baseHref=n??this._platformLocation.getBaseHrefFromDOM()??l(T).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(e){this._removeListenerFns.push(this._platformLocation.onPopState(e),this._platformLocation.onHashChange(e))}getBaseHref(){return this._baseHref}prepareExternalUrl(e){return Wt(this._baseHref,e)}path(e=!1){let n=this._platformLocation.pathname+G(this._platformLocation.search),i=this._platformLocation.hash;return i&&e?`${n}${i}`:n}pushState(e,n,i,o){let s=this.prepareExternalUrl(i+G(o));this._platformLocation.pushState(e,n,s)}replaceState(e,n,i,o){let s=this.prepareExternalUrl(i+G(o));this._platformLocation.replaceState(e,n,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(e=0){this._platformLocation.historyGo?.(e)}static \u0275fac=function(n){return new(n||t)(p(dt),p(Yt,8))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),le=(()=>{class t{_subject=new V;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(e){this._locationStrategy=e;let n=this._locationStrategy.getBaseHref();this._basePath=Ss(Bi(ji(n))),this._locationStrategy.onPopState(i=>{this._subject.next({url:this.path(!0),pop:!0,state:i.state,type:i.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(e=!1){return this.normalize(this._locationStrategy.path(e))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(e,n=""){return this.path()==this.normalize(e+G(n))}normalize(e){return t.stripTrailingSlash(Es(this._basePath,ji(e)))}prepareExternalUrl(e){return e&&e[0]!=="/"&&(e="/"+e),this._locationStrategy.prepareExternalUrl(e)}go(e,n="",i=null){this._locationStrategy.pushState(i,"",e,n),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+G(n)),i)}replaceState(e,n="",i=null){this._locationStrategy.replaceState(i,"",e,n),this._notifyUrlChangeListeners(this.prepareExternalUrl(e+G(n)),i)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(e=0){this._locationStrategy.historyGo?.(e)}onUrlChange(e){return this._urlChangeListeners.push(e),this._urlChangeSubscription??=this.subscribe(n=>{this._notifyUrlChangeListeners(n.url,n.state)}),()=>{let n=this._urlChangeListeners.indexOf(e);this._urlChangeListeners.splice(n,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(e="",n){this._urlChangeListeners.forEach(i=>i(e,n))}subscribe(e,n,i){return this._subject.subscribe({next:e,error:n??void 0,complete:i??void 0})}static normalizeQueryParams=G;static joinWithSlash=Wt;static stripTrailingSlash=Bi;static \u0275fac=function(n){return new(n||t)(p(z))};static \u0275prov=g({token:t,factory:()=>Cs(),providedIn:"root"})}return t})();function Cs(){return new le(p(z))}function Es(t,r){if(!t||!r.startsWith(t))return r;let e=r.substring(t.length);return e===""||["/",";","?","#"].includes(e[0])?e:r}function ji(t){return t.replace(/\/index.html$/,"")}function Ss(t){if(new RegExp("^(https?:)?//").test(t)){let[,e]=t.split(/\/\/[^\/]+/);return e}return t}var Xt=(()=>{class t extends z{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(e,n){super(),this._platformLocation=e,n!=null&&(this._baseHref=n)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(e){this._removeListenerFns.push(this._platformLocation.onPopState(e),this._platformLocation.onHashChange(e))}getBaseHref(){return this._baseHref}path(e=!1){let n=this._platformLocation.hash??"#";return n.length>0?n.substring(1):n}prepareExternalUrl(e){let n=Wt(this._baseHref,e);return n.length>0?"#"+n:n}pushState(e,n,i,o){let s=this.prepareExternalUrl(i+G(o))||this._platformLocation.pathname;this._platformLocation.pushState(e,n,s)}replaceState(e,n,i,o){let s=this.prepareExternalUrl(i+G(o))||this._platformLocation.pathname;this._platformLocation.replaceState(e,n,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(e=0){this._platformLocation.historyGo?.(e)}static \u0275fac=function(n){return new(n||t)(p(dt),p(Yt,8))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();var rr=/\s+/,Vi=[],Rs=(()=>{class t{_ngEl;_renderer;initialClasses=Vi;rawClass;stateMap=new Map;constructor(e,n){this._ngEl=e,this._renderer=n}set klass(e){this.initialClasses=e!=null?e.trim().split(rr):Vi}set ngClass(e){this.rawClass=typeof e=="string"?e.trim().split(rr):e}ngDoCheck(){for(let n of this.initialClasses)this._updateState(n,!0);let e=this.rawClass;if(Array.isArray(e)||e instanceof Set)for(let n of e)this._updateState(n,!0);else if(e!=null)for(let n of Object.keys(e))this._updateState(n,!!e[n]);this._applyStateDiff()}_updateState(e,n){let i=this.stateMap.get(e);i!==void 0?(i.enabled!==n&&(i.changed=!0,i.enabled=n),i.touched=!0):this.stateMap.set(e,{enabled:n,changed:!0,touched:!0})}_applyStateDiff(){for(let e of this.stateMap){let n=e[0],i=e[1];i.changed?(this._toggleClass(n,i.enabled),i.changed=!1):i.touched||(i.enabled&&this._toggleClass(n,!1),this.stateMap.delete(n)),i.touched=!1}}_toggleClass(e,n){e=e.trim(),e.length>0&&e.split(rr).forEach(i=>{n?this._renderer.addClass(this._ngEl.nativeElement,i):this._renderer.removeClass(this._ngEl.nativeElement,i)})}static \u0275fac=function(n){return new(n||t)(_(Ee),_(Ae))};static \u0275dir=Z({type:t,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return t})();var bs=(()=>{class t{_viewContainer;_context=new Kt;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(e,n){this._viewContainer=e,this._thenTemplateRef=n}set ngIf(e){this._context.$implicit=this._context.ngIf=e,this._updateView()}set ngIfThen(e){Hi(e,!1),this._thenTemplateRef=e,this._thenViewRef=null,this._updateView()}set ngIfElse(e){Hi(e,!1),this._elseTemplateRef=e,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(e,n){return!0}static \u0275fac=function(n){return new(n||t)(_(at),_(yi))};static \u0275dir=Z({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return t})(),Kt=class{$implicit=null;ngIf=null};function Hi(t,r){if(t&&!t.createEmbeddedView)throw new D(2020,!1)}var As=(()=>{class t{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(e,n,i){this._ngEl=e,this._differs=n,this._renderer=i}set ngStyle(e){this._ngStyle=e,!this._differ&&e&&(this._differ=this._differs.find(e).create())}ngDoCheck(){if(this._differ){let e=this._differ.diff(this._ngStyle);e&&this._applyChanges(e)}}_setStyle(e,n){let[i,o]=e.split("."),s=i.indexOf("-")===-1?void 0:be.DashCase;n!=null?this._renderer.setStyle(this._ngEl.nativeElement,i,o?`${n}${o}`:n,s):this._renderer.removeStyle(this._ngEl.nativeElement,i,s)}_applyChanges(e){e.forEachRemovedItem(n=>this._setStyle(n.key,null)),e.forEachAddedItem(n=>this._setStyle(n.key,n.currentValue)),e.forEachChangedItem(n=>this._setStyle(n.key,n.currentValue))}static \u0275fac=function(n){return new(n||t)(_(Ee),_(ki),_(Ae))};static \u0275dir=Z({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return t})(),Ts=(()=>{class t{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(e){this._viewContainerRef=e}ngOnChanges(e){if(this._shouldRecreateView(e)){let n=this._viewContainerRef;if(this._viewRef&&n.remove(n.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let i=this._createContextForwardProxy();this._viewRef=n.createEmbeddedView(this.ngTemplateOutlet,i,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(e){return!!e.ngTemplateOutlet||!!e.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(e,n,i)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,n,i):!1,get:(e,n,i)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,n,i)}})}static \u0275fac=function(n){return new(n||t)(_(at))};static \u0275dir=Z({type:t,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Ce]})}return t})();function qi(t,r){return new D(2100,!1)}var ir=class{createSubscription(r,e,n){return ut(()=>r.subscribe({next:e,error:n}))}dispose(r){ut(()=>r.unsubscribe())}},or=class{createSubscription(r,e,n){return r.then(i=>e?.(i),i=>n?.(i)),{unsubscribe:()=>{e=null,n=null}}}dispose(r){r.unsubscribe()}},_s=new or,Is=new ir,Ms=(()=>{class t{_ref;_latestValue=null;markForCheckOnValueUpdate=!0;_subscription=null;_obj=null;_strategy=null;applicationErrorHandler=l(je);constructor(e){this._ref=e}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(e){if(!this._obj){if(e)try{this.markForCheckOnValueUpdate=!1,this._subscribe(e)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return e!==this._obj?(this._dispose(),this.transform(e)):this._latestValue}_subscribe(e){this._obj=e,this._strategy=this._selectStrategy(e),this._subscription=this._strategy.createSubscription(e,n=>this._updateLatestValue(e,n),n=>this.applicationErrorHandler(n))}_selectStrategy(e){if(qt(e))return _s;if(Ai(e))return Is;throw qi(t,e)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(e,n){e===this._obj&&(this._latestValue=n,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static \u0275fac=function(n){return new(n||t)(_(Ie,16))};static \u0275pipe=Gt({name:"async",type:t,pure:!1})}return t})();var Fs=(()=>{class t{transform(e){return JSON.stringify(e,null,2)}static \u0275fac=function(n){return new(n||t)};static \u0275pipe=Gt({name:"json",type:t,pure:!1})}return t})();var Os=(()=>{class t{transform(e,n,i){if(e==null)return null;if(!(typeof e=="string"||Array.isArray(e)))throw qi(t,e);return e.slice(n,i)}static \u0275fac=function(n){return new(n||t)};static \u0275pipe=Gt({name:"slice",type:t,pure:!1})}return t})();var Wi=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275mod=Te({type:t});static \u0275inj=we({})}return t})();function ht(t,r){r=encodeURIComponent(r);for(let e of t.split(";")){let n=e.indexOf("="),[i,o]=n==-1?[e,""]:[e.slice(0,n),e.slice(n+1)];if(i.trim()===r)return decodeURIComponent(o)}return null}var Fe=class{};var ar="browser",ks="server";function rd(t){return t===ar}function Yi(t){return t===ks}var ur=(()=>{class t{static \u0275prov=g({token:t,providedIn:"root",factory:()=>new sr(l(T),window)})}return t})(),sr=class{document;window;offset=()=>[0,0];constructor(r,e){this.document=r,this.window=e}setOffset(r){Array.isArray(r)?this.offset=()=>r:this.offset=r}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(r,e){this.window.scrollTo(P(d({},e),{left:r[0],top:r[1]}))}scrollToAnchor(r,e){let n=xs(this.document,r);n&&(this.scrollToElement(n,e),n.focus())}setHistoryScrollRestoration(r){this.window.history.scrollRestoration=r}scrollToElement(r,e){let n=r.getBoundingClientRect(),i=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(P(d({},e),{left:i-s[0],top:o-s[1]}))}};function xs(t,r){let e=t.getElementById(r)||t.getElementsByName(r)[0];if(e)return e;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let n=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),i=n.currentNode;for(;i;){let o=i.shadowRoot;if(o){let s=o.getElementById(r)||o.querySelector(`[name="${r}"]`);if(s)return s}i=n.nextNode()}}return null}var en=new y(""),hr=(()=>{class t{_zone;_plugins;_eventNameToPlugin=new Map;constructor(e,n){this._zone=n,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,n,i,o){return this._findPluginFor(n).addEventListener(e,n,i,o)}getZone(){return this._zone}_findPluginFor(e){let n=this._eventNameToPlugin.get(e);if(n)return n;if(n=this._plugins.find(o=>o.supports(e)),!n)throw new D(5101,!1);return this._eventNameToPlugin.set(e,n),n}static \u0275fac=function(n){return new(n||t)(p(en),p(ie))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),ft=class{_doc;constructor(r){this._doc=r}manager},Jt="ng-app-id";function Zi(t){for(let r of t)r.remove()}function Ki(t,r){let e=r.createElement("style");return e.textContent=t,e}function Bs(t,r,e,n){let i=t.head?.querySelectorAll(`style[${Jt}="${r}"],link[${Jt}="${r}"]`);if(i)for(let o of i)o.removeAttribute(Jt),o instanceof HTMLLinkElement?n.set(o.href.slice(o.href.lastIndexOf("/")+1),{usage:0,elements:[o]}):o.textContent&&e.set(o.textContent,{usage:0,elements:[o]})}function lr(t,r){let e=r.createElement("link");return e.setAttribute("rel","stylesheet"),e.setAttribute("href",t),e}var fr=(()=>{class t{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(e,n,i,o={}){this.doc=e,this.appId=n,this.nonce=i,this.isServer=Yi(o),Bs(e,n,this.inline,this.external),this.hosts.add(e.head)}addStyles(e,n){for(let i of e)this.addUsage(i,this.inline,Ki);n?.forEach(i=>this.addUsage(i,this.external,lr))}removeStyles(e,n){for(let i of e)this.removeUsage(i,this.inline);n?.forEach(i=>this.removeUsage(i,this.external))}addUsage(e,n,i){let o=n.get(e);o?o.usage++:n.set(e,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,i(e,this.doc)))})}removeUsage(e,n){let i=n.get(e);i&&(i.usage--,i.usage<=0&&(Zi(i.elements),n.delete(e)))}ngOnDestroy(){for(let[,{elements:e}]of[...this.inline,...this.external])Zi(e);this.hosts.clear()}addHost(e){this.hosts.add(e);for(let[n,{elements:i}]of this.inline)i.push(this.addElement(e,Ki(n,this.doc)));for(let[n,{elements:i}]of this.external)i.push(this.addElement(e,lr(n,this.doc)))}removeHost(e){this.hosts.delete(e)}addElement(e,n){return this.nonce&&n.setAttribute("nonce",this.nonce),this.isServer&&n.setAttribute(Jt,this.appId),e.appendChild(n)}static \u0275fac=function(n){return new(n||t)(p(T),p(Wn),p(Yn,8),p(st))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),cr={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},pr=/%COMP%/g;var Ji="%COMP%",js=`_nghost-${Ji}`,$s=`_ngcontent-${Ji}`,zs=!0,Vs=new y("",{providedIn:"root",factory:()=>zs});function Hs(t){return $s.replace(pr,t)}function Gs(t){return js.replace(pr,t)}function Qi(t,r){return r.map(e=>e.replace(pr,t))}var gr=(()=>{class t{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(e,n,i,o,s,a,u,c=null,h=null){this.eventManager=e,this.sharedStylesHost=n,this.appId=i,this.removeStylesOnCompDestroy=o,this.doc=s,this.platformId=a,this.ngZone=u,this.nonce=c,this.tracingService=h,this.platformIsServer=!1,this.defaultRenderer=new pt(e,s,u,this.platformIsServer,this.tracingService)}createRenderer(e,n){if(!e||!n)return this.defaultRenderer;let i=this.getOrCreateRenderer(e,n);return i instanceof Qt?i.applyToHost(e):i instanceof gt&&i.applyStyles(),i}getOrCreateRenderer(e,n){let i=this.rendererByCompId,o=i.get(n.id);if(!o){let s=this.doc,a=this.ngZone,u=this.eventManager,c=this.sharedStylesHost,h=this.removeStylesOnCompDestroy,v=this.platformIsServer,R=this.tracingService;switch(n.encapsulation){case Zn.Emulated:o=new Qt(u,c,n,this.appId,h,s,a,v,R);break;case Zn.ShadowDom:return new dr(u,c,e,n,s,a,this.nonce,v,R);default:o=new gt(u,c,n,h,s,a,v,R);break}i.set(n.id,o)}return o}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(e){this.rendererByCompId.delete(e)}static \u0275fac=function(n){return new(n||t)(p(hr),p(fr),p(Wn),p(Vs),p(T),p(st),p(ie),p(Yn),p(Ri,8))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),pt=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(r,e,n,i,o){this.eventManager=r,this.doc=e,this.ngZone=n,this.platformIsServer=i,this.tracingService=o}destroy(){}destroyNode=null;createElement(r,e){return e?this.doc.createElementNS(cr[e]||e,r):this.doc.createElement(r)}createComment(r){return this.doc.createComment(r)}createText(r){return this.doc.createTextNode(r)}appendChild(r,e){(Xi(r)?r.content:r).appendChild(e)}insertBefore(r,e,n){r&&(Xi(r)?r.content:r).insertBefore(e,n)}removeChild(r,e){e.remove()}selectRootElement(r,e){let n=typeof r=="string"?this.doc.querySelector(r):r;if(!n)throw new D(-5104,!1);return e||(n.textContent=""),n}parentNode(r){return r.parentNode}nextSibling(r){return r.nextSibling}setAttribute(r,e,n,i){if(i){e=i+":"+e;let o=cr[i];o?r.setAttributeNS(o,e,n):r.setAttribute(e,n)}else r.setAttribute(e,n)}removeAttribute(r,e,n){if(n){let i=cr[n];i?r.removeAttributeNS(i,e):r.removeAttribute(`${n}:${e}`)}else r.removeAttribute(e)}addClass(r,e){r.classList.add(e)}removeClass(r,e){r.classList.remove(e)}setStyle(r,e,n,i){i&(be.DashCase|be.Important)?r.style.setProperty(e,n,i&be.Important?"important":""):r.style[e]=n}removeStyle(r,e,n){n&be.DashCase?r.style.removeProperty(e):r.style[e]=""}setProperty(r,e,n){r!=null&&(r[e]=n)}setValue(r,e){r.nodeValue=e}listen(r,e,n,i){if(typeof r=="string"&&(r=oe().getGlobalEventTarget(this.doc,r),!r))throw new D(5102,!1);let o=this.decoratePreventDefault(n);return this.tracingService?.wrapEventListener&&(o=this.tracingService.wrapEventListener(r,e,o)),this.eventManager.addEventListener(r,e,o,i)}decoratePreventDefault(r){return e=>{if(e==="__ngUnwrap__")return r;r(e)===!1&&e.preventDefault()}}};function Xi(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var dr=class extends pt{sharedStylesHost;hostEl;shadowRoot;constructor(r,e,n,i,o,s,a,u,c){super(r,o,s,u,c),this.sharedStylesHost=e,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let h=i.styles;h=Qi(i.id,h);for(let R of h){let b=document.createElement("style");a&&b.setAttribute("nonce",a),b.textContent=R,this.shadowRoot.appendChild(b)}let v=i.getExternalStyles?.();if(v)for(let R of v){let b=lr(R,o);a&&b.setAttribute("nonce",a),this.shadowRoot.appendChild(b)}}nodeOrShadowRoot(r){return r===this.hostEl?this.shadowRoot:r}appendChild(r,e){return super.appendChild(this.nodeOrShadowRoot(r),e)}insertBefore(r,e,n){return super.insertBefore(this.nodeOrShadowRoot(r),e,n)}removeChild(r,e){return super.removeChild(null,e)}parentNode(r){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(r)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},gt=class extends pt{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(r,e,n,i,o,s,a,u,c){super(r,o,s,a,u),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i;let h=n.styles;this.styles=c?Qi(c,h):h,this.styleUrls=n.getExternalStyles?.(c)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Qt=class extends gt{contentAttr;hostAttr;constructor(r,e,n,i,o,s,a,u,c){let h=i+"-"+n.id;super(r,e,n,o,s,a,u,c,h),this.contentAttr=Hs(h),this.hostAttr=Gs(h)}applyToHost(r){this.applyStyles(),this.setAttribute(r,this.hostAttr,"")}createElement(r,e){let n=super.createElement(r,e);return super.setAttribute(n,this.contentAttr,""),n}};var tn=class t extends lt{supportsDOMEvents=!0;static makeCurrent(){tr(new t)}onAndCancel(r,e,n,i){return r.addEventListener(e,n,i),()=>{r.removeEventListener(e,n,i)}}dispatchEvent(r,e){r.dispatchEvent(e)}remove(r){r.remove()}createElement(r,e){return e=e||this.getDefaultDocument(),e.createElement(r)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(r){return r.nodeType===Node.ELEMENT_NODE}isShadowRoot(r){return r instanceof DocumentFragment}getGlobalEventTarget(r,e){return e==="window"?window:e==="document"?r:e==="body"?r.body:null}getBaseHref(r){let e=Ws();return e==null?null:Ys(e)}resetBaseElement(){mt=null}getUserAgent(){return window.navigator.userAgent}getCookie(r){return ht(document.cookie,r)}},mt=null;function Ws(){return mt=mt||document.head.querySelector("base"),mt?mt.getAttribute("href"):null}function Ys(t){return new URL(t,document.baseURI).pathname}var Zs=(()=>{class t{build(){return new XMLHttpRequest}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),to=(()=>{class t extends ft{constructor(e){super(e)}supports(e){return!0}addEventListener(e,n,i,o){return e.addEventListener(n,i,o),()=>this.removeEventListener(e,n,i,o)}removeEventListener(e,n,i,o){return e.removeEventListener(n,i,o)}static \u0275fac=function(n){return new(n||t)(p(T))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),eo=["alt","control","meta","shift"],Ks={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Xs={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},no=(()=>{class t extends ft{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,n,i,o){let s=t.parseEventName(n),a=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>oe().onAndCancel(e,s.domEventName,a,o))}static parseEventName(e){let n=e.toLowerCase().split("."),i=n.shift();if(n.length===0||!(i==="keydown"||i==="keyup"))return null;let o=t._normalizeKey(n.pop()),s="",a=n.indexOf("code");if(a>-1&&(n.splice(a,1),s="code."),eo.forEach(c=>{let h=n.indexOf(c);h>-1&&(n.splice(h,1),s+=c+".")}),s+=o,n.length!=0||o.length===0)return null;let u={};return u.domEventName=i,u.fullKey=s,u}static matchEventFullKeyCode(e,n){let i=Ks[e.key]||e.key,o="";return n.indexOf("code.")>-1&&(i=e.code,o="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),eo.forEach(s=>{if(s!==i){let a=Xs[s];a(e)&&(o+=s+".")}}),o+=i,o===n)}static eventCallback(e,n,i){return o=>{t.matchEventFullKeyCode(o,e)&&i.runGuarded(()=>n(o))}}static _normalizeKey(e){return e==="esc"?"escape":e}static \u0275fac=function(n){return new(n||t)(p(T))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();function Js(t,r){return xi(d({rootComponent:t},Qs(r)))}function Qs(t){return{appProviders:[...ia,...t?.providers??[]],platformProviders:ra}}function ea(){tn.makeCurrent()}function ta(){return new Vn}function na(){return ci(document),document}var ra=[{provide:st,useValue:ar},{provide:li,useValue:ea,multi:!0},{provide:T,useFactory:na}];var ia=[{provide:si,useValue:"root"},{provide:Vn,useFactory:ta},{provide:en,useClass:to,multi:!0,deps:[T]},{provide:en,useClass:no,multi:!0,deps:[T]},gr,fr,hr,{provide:wi,useExisting:gr},{provide:Fe,useClass:Zs},[]];var Ve=class{},vt=class{},de=class t{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(r){r?typeof r=="string"?this.lazyInit=()=>{this.headers=new Map,r.split(`
`).forEach(e=>{let n=e.indexOf(":");if(n>0){let i=e.slice(0,n),o=e.slice(n+1).trim();this.addHeaderEntry(i,o)}})}:typeof Headers<"u"&&r instanceof Headers?(this.headers=new Map,r.forEach((e,n)=>{this.addHeaderEntry(n,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(r).forEach(([e,n])=>{this.setHeaderEntries(e,n)})}:this.headers=new Map}has(r){return this.init(),this.headers.has(r.toLowerCase())}get(r){this.init();let e=this.headers.get(r.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(r){return this.init(),this.headers.get(r.toLowerCase())||null}append(r,e){return this.clone({name:r,value:e,op:"a"})}set(r,e){return this.clone({name:r,value:e,op:"s"})}delete(r,e){return this.clone({name:r,value:e,op:"d"})}maybeSetNormalizedName(r,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,r)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(r=>this.applyUpdate(r)),this.lazyUpdate=null))}copyFrom(r){r.init(),Array.from(r.headers.keys()).forEach(e=>{this.headers.set(e,r.headers.get(e)),this.normalizedNames.set(e,r.normalizedNames.get(e))})}clone(r){let e=new t;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([r]),e}applyUpdate(r){let e=r.name.toLowerCase();switch(r.op){case"a":case"s":let n=r.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(r.name,e);let i=(r.op==="a"?this.headers.get(e):void 0)||[];i.push(...n),this.headers.set(e,i);break;case"d":let o=r.value;if(!o)this.headers.delete(e),this.normalizedNames.delete(e);else{let s=this.headers.get(e);if(!s)return;s=s.filter(a=>o.indexOf(a)===-1),s.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,s)}break}}addHeaderEntry(r,e){let n=r.toLowerCase();this.maybeSetNormalizedName(r,n),this.headers.has(n)?this.headers.get(n).push(e):this.headers.set(n,[e])}setHeaderEntries(r,e){let n=(Array.isArray(e)?e:[e]).map(o=>o.toString()),i=r.toLowerCase();this.headers.set(i,n),this.maybeSetNormalizedName(r,i)}forEach(r){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>r(this.normalizedNames.get(e),this.headers.get(e)))}};var rn=class{encodeKey(r){return ro(r)}encodeValue(r){return ro(r)}decodeKey(r){return decodeURIComponent(r)}decodeValue(r){return decodeURIComponent(r)}};function oa(t,r){let e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(i=>{let o=i.indexOf("="),[s,a]=o==-1?[r.decodeKey(i),""]:[r.decodeKey(i.slice(0,o)),r.decodeValue(i.slice(o+1))],u=e.get(s)||[];u.push(a),e.set(s,u)}),e}var sa=/%(\d[a-f0-9])/gi,aa={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ro(t){return encodeURIComponent(t).replace(sa,(r,e)=>aa[e]??r)}function nn(t){return`${t}`}var se=class t{map;encoder;updates=null;cloneFrom=null;constructor(r={}){if(this.encoder=r.encoder||new rn,r.fromString){if(r.fromObject)throw new D(2805,!1);this.map=oa(r.fromString,this.encoder)}else r.fromObject?(this.map=new Map,Object.keys(r.fromObject).forEach(e=>{let n=r.fromObject[e],i=Array.isArray(n)?n.map(nn):[nn(n)];this.map.set(e,i)})):this.map=null}has(r){return this.init(),this.map.has(r)}get(r){this.init();let e=this.map.get(r);return e?e[0]:null}getAll(r){return this.init(),this.map.get(r)||null}keys(){return this.init(),Array.from(this.map.keys())}append(r,e){return this.clone({param:r,value:e,op:"a"})}appendAll(r){let e=[];return Object.keys(r).forEach(n=>{let i=r[n];Array.isArray(i)?i.forEach(o=>{e.push({param:n,value:o,op:"a"})}):e.push({param:n,value:i,op:"a"})}),this.clone(e)}set(r,e){return this.clone({param:r,value:e,op:"s"})}delete(r,e){return this.clone({param:r,value:e,op:"d"})}toString(){return this.init(),this.keys().map(r=>{let e=this.encoder.encodeKey(r);return this.map.get(r).map(n=>e+"="+this.encoder.encodeValue(n)).join("&")}).filter(r=>r!=="").join("&")}clone(r){let e=new t({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(r),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(r=>this.map.set(r,this.cloneFrom.map.get(r))),this.updates.forEach(r=>{switch(r.op){case"a":case"s":let e=(r.op==="a"?this.map.get(r.param):void 0)||[];e.push(nn(r.value)),this.map.set(r.param,e);break;case"d":if(r.value!==void 0){let n=this.map.get(r.param)||[],i=n.indexOf(nn(r.value));i!==-1&&n.splice(i,1),n.length>0?this.map.set(r.param,n):this.map.delete(r.param)}else{this.map.delete(r.param);break}}}),this.cloneFrom=this.updates=null)}};var on=class{map=new Map;set(r,e){return this.map.set(r,e),this}get(r){return this.map.has(r)||this.map.set(r,r.defaultValue()),this.map.get(r)}delete(r){return this.map.delete(r),this}has(r){return this.map.has(r)}keys(){return this.map.keys()}};function ua(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function io(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function oo(t){return typeof Blob<"u"&&t instanceof Blob}function so(t){return typeof FormData<"u"&&t instanceof FormData}function ca(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var ao="Content-Type",uo="Accept",lo="X-Request-URL",ho="text/plain",fo="application/json",la=`${fo}, ${ho}, */*`,ze=class t{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;keepalive=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(r,e,n,i){this.url=e,this.method=r.toUpperCase();let o;if(ua(this.method)||i?(this.body=n!==void 0?n:null,o=i):o=n,o&&(this.reportProgress=!!o.reportProgress,this.withCredentials=!!o.withCredentials,this.keepalive=!!o.keepalive,o.responseType&&(this.responseType=o.responseType),o.headers&&(this.headers=o.headers),o.context&&(this.context=o.context),o.params&&(this.params=o.params),this.transferCache=o.transferCache),this.headers??=new de,this.context??=new on,!this.params)this.params=new se,this.urlWithParams=e;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=e;else{let a=e.indexOf("?"),u=a===-1?"?":a<e.length-1?"&":"";this.urlWithParams=e+u+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||io(this.body)||oo(this.body)||so(this.body)||ca(this.body)?this.body:this.body instanceof se?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||so(this.body)?null:oo(this.body)?this.body.type||null:io(this.body)?null:typeof this.body=="string"?ho:this.body instanceof se?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?fo:null}clone(r={}){let e=r.method||this.method,n=r.url||this.url,i=r.responseType||this.responseType,o=r.keepalive??this.keepalive,s=r.transferCache??this.transferCache,a=r.body!==void 0?r.body:this.body,u=r.withCredentials??this.withCredentials,c=r.reportProgress??this.reportProgress,h=r.headers||this.headers,v=r.params||this.params,R=r.context??this.context;return r.setHeaders!==void 0&&(h=Object.keys(r.setHeaders).reduce((b,A)=>b.set(A,r.setHeaders[A]),h)),r.setParams&&(v=Object.keys(r.setParams).reduce((b,A)=>b.set(A,r.setParams[A]),v)),new t(e,n,a,{params:v,headers:h,context:R,reportProgress:c,responseType:i,withCredentials:u,transferCache:s,keepalive:o})}},Oe=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(Oe||{}),He=class{headers;status;statusText;url;ok;type;constructor(r,e=200,n="OK"){this.headers=r.headers||new de,this.status=r.status!==void 0?r.status:e,this.statusText=r.statusText||n,this.url=r.url||null,this.ok=this.status>=200&&this.status<300}},sn=class t extends He{constructor(r={}){super(r)}type=Oe.ResponseHeader;clone(r={}){return new t({headers:r.headers||this.headers,status:r.status!==void 0?r.status:this.status,statusText:r.statusText||this.statusText,url:r.url||this.url||void 0})}},Dt=class t extends He{body;constructor(r={}){super(r),this.body=r.body!==void 0?r.body:null}type=Oe.Response;clone(r={}){return new t({body:r.body!==void 0?r.body:this.body,headers:r.headers||this.headers,status:r.status!==void 0?r.status:this.status,statusText:r.statusText||this.statusText,url:r.url||this.url||void 0})}},yt=class extends He{name="HttpErrorResponse";message;error;ok=!1;constructor(r){super(r,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${r.url||"(unknown url)"}`:this.message=`Http failure response for ${r.url||"(unknown url)"}: ${r.status} ${r.statusText}`,this.error=r.error||null}},da=200,ha=204;function mr(t,r){return{body:r,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache,keepalive:t.keepalive}}var po=(()=>{class t{handler;constructor(e){this.handler=e}request(e,n,i={}){let o;if(e instanceof ze)o=e;else{let u;i.headers instanceof de?u=i.headers:u=new de(i.headers);let c;i.params&&(i.params instanceof se?c=i.params:c=new se({fromObject:i.params})),o=new ze(e,n,i.body!==void 0?i.body:null,{headers:u,context:i.context,params:c,reportProgress:i.reportProgress,responseType:i.responseType||"json",withCredentials:i.withCredentials,transferCache:i.transferCache,keepalive:i.keepalive})}let s=f(o).pipe(ne(u=>this.handler.handle(u)));if(e instanceof ze||i.observe==="events")return s;let a=s.pipe(H(u=>u instanceof Dt));switch(i.observe||"body"){case"body":switch(o.responseType){case"arraybuffer":return a.pipe(w(u=>{if(u.body!==null&&!(u.body instanceof ArrayBuffer))throw new D(2806,!1);return u.body}));case"blob":return a.pipe(w(u=>{if(u.body!==null&&!(u.body instanceof Blob))throw new D(2807,!1);return u.body}));case"text":return a.pipe(w(u=>{if(u.body!==null&&typeof u.body!="string")throw new D(2808,!1);return u.body}));case"json":default:return a.pipe(w(u=>u.body))}case"response":return a;default:throw new D(2809,!1)}}delete(e,n={}){return this.request("DELETE",e,n)}get(e,n={}){return this.request("GET",e,n)}head(e,n={}){return this.request("HEAD",e,n)}jsonp(e,n){return this.request("JSONP",e,{params:new se().append(n,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,n={}){return this.request("OPTIONS",e,n)}patch(e,n,i={}){return this.request("PATCH",e,mr(i,n))}post(e,n,i={}){return this.request("POST",e,mr(i,n))}put(e,n,i={}){return this.request("PUT",e,mr(i,n))}static \u0275fac=function(n){return new(n||t)(p(Ve))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();var fa=new y("");function go(t,r){return r(t)}function pa(t,r){return(e,n)=>r.intercept(e,{handle:i=>t(i,n)})}function ga(t,r,e){return(n,i)=>$(e,()=>r(n,o=>t(o,i)))}var mo=new y(""),Dr=new y(""),vo=new y(""),yr=new y("",{providedIn:"root",factory:()=>!0});function ma(){let t=null;return(r,e)=>{t===null&&(t=(l(mo,{optional:!0})??[]).reduceRight(pa,go));let n=l(Hn);if(l(yr)){let o=n.add();return t(r,e).pipe(ye(o))}else return t(r,e)}}var an=(()=>{class t extends Ve{backend;injector;chain=null;pendingTasks=l(Hn);contributeToStability=l(yr);constructor(e,n){super(),this.backend=e,this.injector=n}handle(e){if(this.chain===null){let n=Array.from(new Set([...this.injector.get(Dr),...this.injector.get(vo,[])]));this.chain=n.reduceRight((i,o)=>ga(i,o,this.injector),go)}if(this.contributeToStability){let n=this.pendingTasks.add();return this.chain(e,i=>this.backend.handle(i)).pipe(ye(n))}else return this.chain(e,n=>this.backend.handle(n))}static \u0275fac=function(n){return new(n||t)(p(vt),p(re))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();var va=/^\)\]\}',?\n/,Da=RegExp(`^${lo}:`,"m");function ya(t){return"responseURL"in t&&t.responseURL?t.responseURL:Da.test(t.getAllResponseHeaders())?t.getResponseHeader(lo):null}var vr=(()=>{class t{xhrFactory;constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new D(-2800,!1);e.keepalive;let n=this.xhrFactory;return(n.\u0275loadImpl?F(n.\u0275loadImpl()):f(null)).pipe(k(()=>new $t(o=>{let s=n.build();if(s.open(e.method,e.urlWithParams),e.withCredentials&&(s.withCredentials=!0),e.headers.forEach((S,E)=>s.setRequestHeader(S,E.join(","))),e.headers.has(uo)||s.setRequestHeader(uo,la),!e.headers.has(ao)){let S=e.detectContentTypeHeader();S!==null&&s.setRequestHeader(ao,S)}if(e.responseType){let S=e.responseType.toLowerCase();s.responseType=S!=="json"?S:"text"}let a=e.serializeBody(),u=null,c=()=>{if(u!==null)return u;let S=s.statusText||"OK",E=new de(s.getAllResponseHeaders()),Y=ya(s)||e.url;return u=new sn({headers:E,status:s.status,statusText:S,url:Y}),u},h=()=>{let{headers:S,status:E,statusText:Y,url:Bt}=c(),I=null;E!==ha&&(I=typeof s.response>"u"?s.responseText:s.response),E===0&&(E=I?da:0);let kn=E>=200&&E<300;if(e.responseType==="json"&&typeof I=="string"){let ys=I;I=I.replace(va,"");try{I=I!==""?JSON.parse(I):null}catch(ws){I=ys,kn&&(kn=!1,I={error:ws,text:I})}}kn?(o.next(new Dt({body:I,headers:S,status:E,statusText:Y,url:Bt||void 0})),o.complete()):o.error(new yt({error:I,headers:S,status:E,statusText:Y,url:Bt||void 0}))},v=S=>{let{url:E}=c(),Y=new yt({error:S,status:s.status||0,statusText:s.statusText||"Unknown Error",url:E||void 0});o.error(Y)},R=!1,b=S=>{R||(o.next(c()),R=!0);let E={type:Oe.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(E.total=S.total),e.responseType==="text"&&s.responseText&&(E.partialText=s.responseText),o.next(E)},A=S=>{let E={type:Oe.UploadProgress,loaded:S.loaded};S.lengthComputable&&(E.total=S.total),o.next(E)};return s.addEventListener("load",h),s.addEventListener("error",v),s.addEventListener("timeout",v),s.addEventListener("abort",v),e.reportProgress&&(s.addEventListener("progress",b),a!==null&&s.upload&&s.upload.addEventListener("progress",A)),s.send(a),o.next({type:Oe.Sent}),()=>{s.removeEventListener("error",v),s.removeEventListener("abort",v),s.removeEventListener("load",h),s.removeEventListener("timeout",v),e.reportProgress&&(s.removeEventListener("progress",b),a!==null&&s.upload&&s.upload.removeEventListener("progress",A)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(n){return new(n||t)(p(Fe))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),Do=new y(""),wa="XSRF-TOKEN",Ca=new y("",{providedIn:"root",factory:()=>wa}),Ea="X-XSRF-TOKEN",Sa=new y("",{providedIn:"root",factory:()=>Ea}),wt=class{},Ra=(()=>{class t{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(e,n){this.doc=e,this.cookieName=n}getToken(){let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=ht(e,this.cookieName),this.lastCookieString=e),this.lastToken}static \u0275fac=function(n){return new(n||t)(p(T),p(Ca))};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();function ba(t,r){let e=t.url.toLowerCase();if(!l(Do)||t.method==="GET"||t.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return r(t);let n=l(wt).getToken(),i=l(Sa);return n!=null&&!t.headers.has(i)&&(t=t.clone({headers:t.headers.set(i,n)})),r(t)}var wr=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(wr||{});function Aa(t,r){return{\u0275kind:t,\u0275providers:r}}function Ta(...t){let r=[po,vr,an,{provide:Ve,useExisting:an},{provide:vt,useFactory:()=>l(fa,{optional:!0})??l(vr)},{provide:Dr,useValue:ba,multi:!0},{provide:Do,useValue:!0},{provide:wt,useClass:Ra}];for(let e of t)r.push(...e.\u0275providers);return ot(r)}var co=new y("");function _a(){return Aa(wr.LegacyInterceptors,[{provide:co,useFactory:ma},{provide:Dr,useExisting:co,multi:!0}])}var yo=(()=>{class t{_doc;constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static \u0275fac=function(n){return new(n||t)(p(T))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var Ma=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:function(n){let i=null;return n?i=new(n||t):i=p(Fa),i},providedIn:"root"})}return t})(),Fa=(()=>{class t extends Ma{_doc;constructor(e){super(),this._doc=e}sanitize(e,n){if(n==null)return null;switch(e){case Re.NONE:return n;case Re.HTML:return $e(n,"HTML")?Se(n):vi(this._doc,String(n)).toString();case Re.STYLE:return $e(n,"Style")?Se(n):n;case Re.SCRIPT:if($e(n,"Script"))return Se(n);throw new D(5200,!1);case Re.URL:return $e(n,"URL")?Se(n):mi(String(n));case Re.RESOURCE_URL:if($e(n,"ResourceURL"))return Se(n);throw new D(5201,!1);default:throw new D(5202,!1)}}bypassSecurityTrustHtml(e){return di(e)}bypassSecurityTrustStyle(e){return hi(e)}bypassSecurityTrustScript(e){return fi(e)}bypassSecurityTrustUrl(e){return pi(e)}bypassSecurityTrustResourceUrl(e){return gi(e)}static \u0275fac=function(n){return new(n||t)(p(T))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var m="primary",Pt=Symbol("RouteTitle"),br=class{params;constructor(r){this.params=r||{}}has(r){return Object.prototype.hasOwnProperty.call(this.params,r)}get(r){if(this.has(r)){let e=this.params[r];return Array.isArray(e)?e[0]:e}return null}getAll(r){if(this.has(r)){let e=this.params[r];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function Le(t){return new br(t)}function To(t,r,e){let n=e.path.split("/");if(n.length>t.length||e.pathMatch==="full"&&(r.hasChildren()||n.length<t.length))return null;let i={};for(let o=0;o<n.length;o++){let s=n[o],a=t[o];if(s[0]===":")i[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:t.slice(0,n.length),posParams:i}}function Pa(t,r){if(t.length!==r.length)return!1;for(let e=0;e<t.length;++e)if(!K(t[e],r[e]))return!1;return!0}function K(t,r){let e=t?Ar(t):void 0,n=r?Ar(r):void 0;if(!e||!n||e.length!=n.length)return!1;let i;for(let o=0;o<e.length;o++)if(i=e[o],!_o(t[i],r[i]))return!1;return!0}function Ar(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function _o(t,r){if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return!1;let e=[...t].sort(),n=[...r].sort();return e.every((i,o)=>n[o]===i)}else return t===r}function Io(t){return t.length>0?t[t.length-1]:null}function ae(t){return Qr(t)?t:qt(t)?F(Promise.resolve(t)):f(t)}var Na={exact:Fo,subset:Oo},Mo={exact:La,subset:ka,ignored:()=>!0};function wo(t,r,e){return Na[e.paths](t.root,r.root,e.matrixParams)&&Mo[e.queryParams](t.queryParams,r.queryParams)&&!(e.fragment==="exact"&&t.fragment!==r.fragment)}function La(t,r){return K(t,r)}function Fo(t,r,e){if(!Pe(t.segments,r.segments)||!ln(t.segments,r.segments,e)||t.numberOfChildren!==r.numberOfChildren)return!1;for(let n in r.children)if(!t.children[n]||!Fo(t.children[n],r.children[n],e))return!1;return!0}function ka(t,r){return Object.keys(r).length<=Object.keys(t).length&&Object.keys(r).every(e=>_o(t[e],r[e]))}function Oo(t,r,e){return Po(t,r,r.segments,e)}function Po(t,r,e,n){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!Pe(i,e)||r.hasChildren()||!ln(i,e,n))}else if(t.segments.length===e.length){if(!Pe(t.segments,e)||!ln(t.segments,e,n))return!1;for(let i in r.children)if(!t.children[i]||!Oo(t.children[i],r.children[i],n))return!1;return!0}else{let i=e.slice(0,t.segments.length),o=e.slice(t.segments.length);return!Pe(t.segments,i)||!ln(t.segments,i,n)||!t.children[m]?!1:Po(t.children[m],r,o,n)}}function ln(t,r,e){return r.every((n,i)=>Mo[e](t[i].parameters,n.parameters))}var J=class{root;queryParams;fragment;_queryParamMap;constructor(r=new C([],{}),e={},n=null){this.root=r,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap??=Le(this.queryParams),this._queryParamMap}toString(){return Ba.serialize(this)}},C=class{segments;children;parent=null;constructor(r,e){this.segments=r,this.children=e,Object.values(e).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return dn(this)}},he=class{path;parameters;_parameterMap;constructor(r,e){this.path=r,this.parameters=e}get parameterMap(){return this._parameterMap??=Le(this.parameters),this._parameterMap}toString(){return Lo(this)}};function xa(t,r){return Pe(t,r)&&t.every((e,n)=>K(e.parameters,r[n].parameters))}function Pe(t,r){return t.length!==r.length?!1:t.every((e,n)=>e.path===r[n].path)}function Ua(t,r){let e=[];return Object.entries(t.children).forEach(([n,i])=>{n===m&&(e=e.concat(r(i,n)))}),Object.entries(t.children).forEach(([n,i])=>{n!==m&&(e=e.concat(r(i,n)))}),e}var me=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>new fe,providedIn:"root"})}return t})(),fe=class{parse(r){let e=new _r(r);return new J(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(r){let e=`/${Ct(r.root,!0)}`,n=za(r.queryParams),i=typeof r.fragment=="string"?`#${ja(r.fragment)}`:"";return`${e}${n}${i}`}},Ba=new fe;function dn(t){return t.segments.map(r=>Lo(r)).join("/")}function Ct(t,r){if(!t.hasChildren())return dn(t);if(r){let e=t.children[m]?Ct(t.children[m],!1):"",n=[];return Object.entries(t.children).forEach(([i,o])=>{i!==m&&n.push(`${i}:${Ct(o,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}else{let e=Ua(t,(n,i)=>i===m?[Ct(t.children[m],!1)]:[`${i}:${Ct(n,!1)}`]);return Object.keys(t.children).length===1&&t.children[m]!=null?`${dn(t)}/${e[0]}`:`${dn(t)}/(${e.join("//")})`}}function No(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function un(t){return No(t).replace(/%3B/gi,";")}function ja(t){return encodeURI(t)}function Tr(t){return No(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function hn(t){return decodeURIComponent(t)}function Co(t){return hn(t.replace(/\+/g,"%20"))}function Lo(t){return`${Tr(t.path)}${$a(t.parameters)}`}function $a(t){return Object.entries(t).map(([r,e])=>`;${Tr(r)}=${Tr(e)}`).join("")}function za(t){let r=Object.entries(t).map(([e,n])=>Array.isArray(n)?n.map(i=>`${un(e)}=${un(i)}`).join("&"):`${un(e)}=${un(n)}`).filter(e=>e);return r.length?`?${r.join("&")}`:""}var Va=/^[^\/()?;#]+/;function Cr(t){let r=t.match(Va);return r?r[0]:""}var Ha=/^[^\/()?;=#]+/;function Ga(t){let r=t.match(Ha);return r?r[0]:""}var qa=/^[^=?&#]+/;function Wa(t){let r=t.match(qa);return r?r[0]:""}var Ya=/^[^&#]+/;function Za(t){let r=t.match(Ya);return r?r[0]:""}var _r=class{url;remaining;constructor(r){this.url=r,this.remaining=r}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new C([],{}):new C([],this.parseChildren())}parseQueryParams(){let r={};if(this.consumeOptional("?"))do this.parseQueryParam(r);while(this.consumeOptional("&"));return r}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let r=[];for(this.peekStartsWith("(")||r.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),r.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(r.length>0||Object.keys(e).length>0)&&(n[m]=new C(r,e)),n}parseSegment(){let r=Cr(this.remaining);if(r===""&&this.peekStartsWith(";"))throw new D(4009,!1);return this.capture(r),new he(hn(r),this.parseMatrixParams())}parseMatrixParams(){let r={};for(;this.consumeOptional(";");)this.parseParam(r);return r}parseParam(r){let e=Ga(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let i=Cr(this.remaining);i&&(n=i,this.capture(n))}r[hn(e)]=hn(n)}parseQueryParam(r){let e=Wa(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let s=Za(this.remaining);s&&(n=s,this.capture(n))}let i=Co(e),o=Co(n);if(r.hasOwnProperty(i)){let s=r[i];Array.isArray(s)||(s=[s],r[i]=s),s.push(o)}else r[i]=o}parseParens(r){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=Cr(this.remaining),i=this.remaining[n.length];if(i!=="/"&&i!==")"&&i!==";")throw new D(4010,!1);let o;n.indexOf(":")>-1?(o=n.slice(0,n.indexOf(":")),this.capture(o),this.capture(":")):r&&(o=m);let s=this.parseChildren();e[o]=Object.keys(s).length===1?s[m]:new C([],s),this.consumeOptional("//")}return e}peekStartsWith(r){return this.remaining.startsWith(r)}consumeOptional(r){return this.peekStartsWith(r)?(this.remaining=this.remaining.substring(r.length),!0):!1}capture(r){if(!this.consumeOptional(r))throw new D(4011,!1)}};function ko(t){return t.segments.length>0?new C([],{[m]:t}):t}function xo(t){let r={};for(let[n,i]of Object.entries(t.children)){let o=xo(i);if(n===m&&o.segments.length===0&&o.hasChildren())for(let[s,a]of Object.entries(o.children))r[s]=a;else(o.segments.length>0||o.hasChildren())&&(r[n]=o)}let e=new C(t.segments,r);return Ka(e)}function Ka(t){if(t.numberOfChildren===1&&t.children[m]){let r=t.children[m];return new C(t.segments.concat(r.segments),r.children)}return t}function pe(t){return t instanceof J}function Uo(t,r,e=null,n=null){let i=Bo(t);return jo(i,r,e,n)}function Bo(t){let r;function e(o){let s={};for(let u of o.children){let c=e(u);s[u.outlet]=c}let a=new C(o.url,s);return o===t&&(r=a),a}let n=e(t.root),i=ko(n);return r??i}function jo(t,r,e,n){let i=t;for(;i.parent;)i=i.parent;if(r.length===0)return Er(i,i,i,e,n);let o=Xa(r);if(o.toRoot())return Er(i,i,new C([],{}),e,n);let s=Ja(o,i,t),a=s.processChildren?St(s.segmentGroup,s.index,o.commands):zo(s.segmentGroup,s.index,o.commands);return Er(i,s.segmentGroup,a,e,n)}function fn(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function bt(t){return typeof t=="object"&&t!=null&&t.outlets}function Er(t,r,e,n,i){let o={};n&&Object.entries(n).forEach(([u,c])=>{o[u]=Array.isArray(c)?c.map(h=>`${h}`):`${c}`});let s;t===r?s=e:s=$o(t,r,e);let a=ko(xo(s));return new J(a,o,i)}function $o(t,r,e){let n={};return Object.entries(t.children).forEach(([i,o])=>{o===r?n[i]=e:n[i]=$o(o,r,e)}),new C(t.segments,n)}var pn=class{isAbsolute;numberOfDoubleDots;commands;constructor(r,e,n){if(this.isAbsolute=r,this.numberOfDoubleDots=e,this.commands=n,r&&n.length>0&&fn(n[0]))throw new D(4003,!1);let i=n.find(bt);if(i&&i!==Io(n))throw new D(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Xa(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new pn(!0,0,t);let r=0,e=!1,n=t.reduce((i,o,s)=>{if(typeof o=="object"&&o!=null){if(o.outlets){let a={};return Object.entries(o.outlets).forEach(([u,c])=>{a[u]=typeof c=="string"?c.split("/"):c}),[...i,{outlets:a}]}if(o.segmentPath)return[...i,o.segmentPath]}return typeof o!="string"?[...i,o]:s===0?(o.split("/").forEach((a,u)=>{u==0&&a==="."||(u==0&&a===""?e=!0:a===".."?r++:a!=""&&i.push(a))}),i):[...i,o]},[]);return new pn(e,r,n)}var We=class{segmentGroup;processChildren;index;constructor(r,e,n){this.segmentGroup=r,this.processChildren=e,this.index=n}};function Ja(t,r,e){if(t.isAbsolute)return new We(r,!0,0);if(!e)return new We(r,!1,NaN);if(e.parent===null)return new We(e,!0,0);let n=fn(t.commands[0])?0:1,i=e.segments.length-1+n;return Qa(e,i,t.numberOfDoubleDots)}function Qa(t,r,e){let n=t,i=r,o=e;for(;o>i;){if(o-=i,n=n.parent,!n)throw new D(4005,!1);i=n.segments.length}return new We(n,!1,i-o)}function eu(t){return bt(t[0])?t[0].outlets:{[m]:t}}function zo(t,r,e){if(t??=new C([],{}),t.segments.length===0&&t.hasChildren())return St(t,r,e);let n=tu(t,r,e),i=e.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){let o=new C(t.segments.slice(0,n.pathIndex),{});return o.children[m]=new C(t.segments.slice(n.pathIndex),t.children),St(o,0,i)}else return n.match&&i.length===0?new C(t.segments,{}):n.match&&!t.hasChildren()?Ir(t,r,e):n.match?St(t,0,i):Ir(t,r,e)}function St(t,r,e){if(e.length===0)return new C(t.segments,{});{let n=eu(e),i={};if(Object.keys(n).some(o=>o!==m)&&t.children[m]&&t.numberOfChildren===1&&t.children[m].segments.length===0){let o=St(t.children[m],r,e);return new C(t.segments,o.children)}return Object.entries(n).forEach(([o,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(i[o]=zo(t.children[o],r,s))}),Object.entries(t.children).forEach(([o,s])=>{n[o]===void 0&&(i[o]=s)}),new C(t.segments,i)}}function tu(t,r,e){let n=0,i=r,o={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(n>=e.length)return o;let s=t.segments[i],a=e[n];if(bt(a))break;let u=`${a}`,c=n<e.length-1?e[n+1]:null;if(i>0&&u===void 0)break;if(u&&c&&typeof c=="object"&&c.outlets===void 0){if(!So(u,c,s))return o;n+=2}else{if(!So(u,{},s))return o;n++}i++}return{match:!0,pathIndex:i,commandIndex:n}}function Ir(t,r,e){let n=t.segments.slice(0,r),i=0;for(;i<e.length;){let o=e[i];if(bt(o)){let u=nu(o.outlets);return new C(n,u)}if(i===0&&fn(e[0])){let u=t.segments[r];n.push(new he(u.path,Eo(e[0]))),i++;continue}let s=bt(o)?o.outlets[m]:`${o}`,a=i<e.length-1?e[i+1]:null;s&&a&&fn(a)?(n.push(new he(s,Eo(a))),i+=2):(n.push(new he(s,{})),i++)}return new C(n,{})}function nu(t){let r={};return Object.entries(t).forEach(([e,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(r[e]=Ir(new C([],{}),0,n))}),r}function Eo(t){let r={};return Object.entries(t).forEach(([e,n])=>r[e]=`${n}`),r}function So(t,r,e){return t==e.path&&K(r,e.parameters)}var Ye="imperative",M=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(M||{}),U=class{id;url;constructor(r,e){this.id=r,this.url=e}},ge=class extends U{type=M.NavigationStart;navigationTrigger;restoredState;constructor(r,e,n="imperative",i=null){super(r,e),this.navigationTrigger=n,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},B=class extends U{urlAfterRedirects;type=M.NavigationEnd;constructor(r,e,n){super(r,e),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},N=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t[t.Aborted=4]="Aborted",t}(N||{}),Ke=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Ke||{}),X=class extends U{reason;code;type=M.NavigationCancel;constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Q=class extends U{reason;code;type=M.NavigationSkipped;constructor(r,e,n,i){super(r,e),this.reason=n,this.code=i}},Xe=class extends U{error;target;type=M.NavigationError;constructor(r,e,n,i){super(r,e),this.error=n,this.target=i}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},At=class extends U{urlAfterRedirects;state;type=M.RoutesRecognized;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},gn=class extends U{urlAfterRedirects;state;type=M.GuardsCheckStart;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},mn=class extends U{urlAfterRedirects;state;shouldActivate;type=M.GuardsCheckEnd;constructor(r,e,n,i,o){super(r,e),this.urlAfterRedirects=n,this.state=i,this.shouldActivate=o}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},vn=class extends U{urlAfterRedirects;state;type=M.ResolveStart;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Dn=class extends U{urlAfterRedirects;state;type=M.ResolveEnd;constructor(r,e,n,i){super(r,e),this.urlAfterRedirects=n,this.state=i}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},yn=class{route;type=M.RouteConfigLoadStart;constructor(r){this.route=r}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},wn=class{route;type=M.RouteConfigLoadEnd;constructor(r){this.route=r}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Cn=class{snapshot;type=M.ChildActivationStart;constructor(r){this.snapshot=r}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},En=class{snapshot;type=M.ChildActivationEnd;constructor(r){this.snapshot=r}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Sn=class{snapshot;type=M.ActivationStart;constructor(r){this.snapshot=r}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Rn=class{snapshot;type=M.ActivationEnd;constructor(r){this.snapshot=r}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Je=class{routerEvent;position;anchor;type=M.Scroll;constructor(r,e,n){this.routerEvent=r,this.position=e,this.anchor=n}toString(){let r=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${r}')`}},Tt=class{},Qe=class{url;navigationBehaviorOptions;constructor(r,e){this.url=r,this.navigationBehaviorOptions=e}};function ru(t){return!(t instanceof Tt)&&!(t instanceof Qe)}function iu(t,r){return t.providers&&!t._injector&&(t._injector=Ht(t.providers,r,`Route: ${t.path}`)),t._injector??r}function q(t){return t.outlet||m}function ou(t,r){let e=t.filter(n=>q(n)===r);return e.push(...t.filter(n=>q(n)!==r)),e}function Nt(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let r=t.parent;r;r=r.parent){let e=r.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var bn=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return Nt(this.route?.snapshot)??this.rootInjector}constructor(r){this.rootInjector=r,this.children=new ke(this.rootInjector)}},ke=(()=>{class t{rootInjector;contexts=new Map;constructor(e){this.rootInjector=e}onChildOutletCreated(e,n){let i=this.getOrCreateContext(e);i.outlet=n,this.contexts.set(e,i)}onChildOutletDestroyed(e){let n=this.getContext(e);n&&(n.outlet=null,n.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let n=this.getContext(e);return n||(n=new bn(this.rootInjector),this.contexts.set(e,n)),n}getContext(e){return this.contexts.get(e)||null}static \u0275fac=function(n){return new(n||t)(p(re))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),An=class{_root;constructor(r){this._root=r}get root(){return this._root.value}parent(r){let e=this.pathFromRoot(r);return e.length>1?e[e.length-2]:null}children(r){let e=Mr(r,this._root);return e?e.children.map(n=>n.value):[]}firstChild(r){let e=Mr(r,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(r){let e=Fr(r,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==r)}pathFromRoot(r){return Fr(r,this._root).map(e=>e.value)}};function Mr(t,r){if(t===r.value)return r;for(let e of r.children){let n=Mr(t,e);if(n)return n}return null}function Fr(t,r){if(t===r.value)return[r];for(let e of r.children){let n=Fr(t,e);if(n.length)return n.unshift(r),n}return[]}var x=class{value;children;constructor(r,e){this.value=r,this.children=e}toString(){return`TreeNode(${this.value})`}};function qe(t){let r={};return t&&t.children.forEach(e=>r[e.value.outlet]=e),r}var _t=class extends An{snapshot;constructor(r,e){super(r),this.snapshot=e,Br(this,r)}toString(){return this.snapshot.toString()}};function Vo(t){let r=su(t),e=new j([new he("",{})]),n=new j({}),i=new j({}),o=new j({}),s=new j(""),a=new ee(e,n,o,s,i,m,t,r.root);return a.snapshot=r.root,new _t(new x(a,[]),r)}function su(t){let r={},e={},n={},i="",o=new Ne([],r,n,i,e,m,t,null,{});return new It("",new x(o,[]))}var ee=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(r,e,n,i,o,s,a,u){this.urlSubject=r,this.paramsSubject=e,this.queryParamsSubject=n,this.fragmentSubject=i,this.dataSubject=o,this.outlet=s,this.component=a,this._futureSnapshot=u,this.title=this.dataSubject?.pipe(w(c=>c[Pt]))??f(void 0),this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=o}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(w(r=>Le(r))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(w(r=>Le(r))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Tn(t,r,e="emptyOnly"){let n,{routeConfig:i}=t;return r!==null&&(e==="always"||i?.path===""||!r.component&&!r.routeConfig?.loadComponent)?n={params:d(d({},r.params),t.params),data:d(d({},r.data),t.data),resolve:d(d(d(d({},t.data),r.data),i?.data),t._resolvedData)}:n={params:d({},t.params),data:d({},t.data),resolve:d(d({},t.data),t._resolvedData??{})},i&&Go(i)&&(n.resolve[Pt]=i.title),n}var Ne=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Pt]}constructor(r,e,n,i,o,s,a,u,c){this.url=r,this.params=e,this.queryParams=n,this.fragment=i,this.data=o,this.outlet=s,this.component=a,this.routeConfig=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Le(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Le(this.queryParams),this._queryParamMap}toString(){let r=this.url.map(n=>n.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${r}', path:'${e}')`}},It=class extends An{url;constructor(r,e){super(e),this.url=r,Br(this,e)}toString(){return Ho(this._root)}};function Br(t,r){r.value._routerState=t,r.children.forEach(e=>Br(t,e))}function Ho(t){let r=t.children.length>0?` { ${t.children.map(Ho).join(", ")} } `:"";return`${t.value}${r}`}function Sr(t){if(t.snapshot){let r=t.snapshot,e=t._futureSnapshot;t.snapshot=e,K(r.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),r.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),K(r.params,e.params)||t.paramsSubject.next(e.params),Pa(r.url,e.url)||t.urlSubject.next(e.url),K(r.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function Or(t,r){let e=K(t.params,r.params)&&xa(t.url,r.url),n=!t.parent!=!r.parent;return e&&!n&&(!t.parent||Or(t.parent,r.parent))}function Go(t){return typeof t.title=="string"||t.title===null}var qo=new y(""),jr=(()=>{class t{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=m;activateEvents=new _e;deactivateEvents=new _e;attachEvents=new _e;detachEvents=new _e;routerOutletData=Li(void 0);parentContexts=l(ke);location=l(at);changeDetector=l(Ie);inputBinder=l(Lt,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(e){if(e.name){let{firstChange:n,previousValue:i}=e.name;if(n)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new D(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new D(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new D(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,n){this.activated=e,this._activatedRoute=n,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,n){if(this.isActivated)throw new D(4013,!1);this._activatedRoute=e;let i=this.location,s=e.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,u=new Pr(e,a,i.injector,this.routerOutletData);this.activated=i.createComponent(s,{index:i.length,injector:u,environmentInjector:n}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(n){return new(n||t)};static \u0275dir=Z({type:t,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ce]})}return t})(),Pr=class{route;childContexts;parent;outletData;constructor(r,e,n,i){this.route=r,this.childContexts=e,this.parent=n,this.outletData=i}get(r,e){return r===ee?this.route:r===ke?this.childContexts:r===qo?this.outletData:this.parent.get(r,e)}},Lt=new y(""),$r=(()=>{class t{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(e){this.unsubscribeFromRouteData(e),this.subscribeToRouteData(e)}unsubscribeFromRouteData(e){this.outletDataSubscriptions.get(e)?.unsubscribe(),this.outletDataSubscriptions.delete(e)}subscribeToRouteData(e){let{activatedRoute:n}=e,i=zt([n.queryParams,n.params,n.data]).pipe(k(([o,s,a],u)=>(a=d(d(d({},o),s),a),u===0?f(a):Promise.resolve(a)))).subscribe(o=>{if(!e.isActivated||!e.activatedComponentRef||e.activatedRoute!==n||n.component===null){this.unsubscribeFromRouteData(e);return}let s=Ui(n.component);if(!s){this.unsubscribeFromRouteData(e);return}for(let{templateName:a}of s.inputs)e.activatedComponentRef.setInput(a,o[a])});this.outletDataSubscriptions.set(e,i)}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})(),zr=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275cmp=Si({type:t,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(n,i){n&1&&_i(0,"router-outlet")},dependencies:[jr],encapsulation:2})}return t})();function Vr(t){let r=t.children&&t.children.map(Vr),e=r?P(d({},t),{children:r}):d({},t);return!e.component&&!e.loadComponent&&(r||e.loadChildren)&&e.outlet&&e.outlet!==m&&(e.component=zr),e}function au(t,r,e){let n=Mt(t,r._root,e?e._root:void 0);return new _t(n,r)}function Mt(t,r,e){if(e&&t.shouldReuseRoute(r.value,e.value.snapshot)){let n=e.value;n._futureSnapshot=r.value;let i=uu(t,r,e);return new x(n,i)}else{if(t.shouldAttach(r.value)){let o=t.retrieve(r.value);if(o!==null){let s=o.route;return s.value._futureSnapshot=r.value,s.children=r.children.map(a=>Mt(t,a)),s}}let n=cu(r.value),i=r.children.map(o=>Mt(t,o));return new x(n,i)}}function uu(t,r,e){return r.children.map(n=>{for(let i of e.children)if(t.shouldReuseRoute(n.value,i.value.snapshot))return Mt(t,n,i);return Mt(t,n)})}function cu(t){return new ee(new j(t.url),new j(t.params),new j(t.queryParams),new j(t.fragment),new j(t.data),t.outlet,t.component,t)}var et=class{redirectTo;navigationBehaviorOptions;constructor(r,e){this.redirectTo=r,this.navigationBehaviorOptions=e}},Wo="ngNavigationCancelingError";function _n(t,r){let{redirectTo:e,navigationBehaviorOptions:n}=pe(r)?{redirectTo:r,navigationBehaviorOptions:void 0}:r,i=Yo(!1,N.Redirect);return i.url=e,i.navigationBehaviorOptions=n,i}function Yo(t,r){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[Wo]=!0,e.cancellationCode=r,e}function lu(t){return Zo(t)&&pe(t.url)}function Zo(t){return!!t&&t[Wo]}var du=(t,r,e,n)=>w(i=>(new Nr(r,i.targetRouterState,i.currentRouterState,e,n).activate(t),i)),Nr=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(r,e,n,i,o){this.routeReuseStrategy=r,this.futureState=e,this.currState=n,this.forwardEvent=i,this.inputBindingEnabled=o}activate(r){let e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,r),Sr(this.futureState.root),this.activateChildRoutes(e,n,r)}deactivateChildRoutes(r,e,n){let i=qe(e);r.children.forEach(o=>{let s=o.value.outlet;this.deactivateRoutes(o,i[s],n),delete i[s]}),Object.values(i).forEach(o=>{this.deactivateRouteAndItsChildren(o,n)})}deactivateRoutes(r,e,n){let i=r.value,o=e?e.value:null;if(i===o)if(i.component){let s=n.getContext(i.outlet);s&&this.deactivateChildRoutes(r,e,s.children)}else this.deactivateChildRoutes(r,e,n);else o&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(r,e){r.value.component&&this.routeReuseStrategy.shouldDetach(r.value.snapshot)?this.detachAndStoreRouteSubtree(r,e):this.deactivateRouteAndOutlet(r,e)}detachAndStoreRouteSubtree(r,e){let n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,o=qe(r);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);if(n&&n.outlet){let s=n.outlet.detach(),a=n.children.onOutletDeactivated();this.routeReuseStrategy.store(r.value.snapshot,{componentRef:s,route:r,contexts:a})}}deactivateRouteAndOutlet(r,e){let n=e.getContext(r.value.outlet),i=n&&r.value.component?n.children:e,o=qe(r);for(let s of Object.values(o))this.deactivateRouteAndItsChildren(s,i);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(r,e,n){let i=qe(e);r.children.forEach(o=>{this.activateRoutes(o,i[o.value.outlet],n),this.forwardEvent(new Rn(o.value.snapshot))}),r.children.length&&this.forwardEvent(new En(r.value.snapshot))}activateRoutes(r,e,n){let i=r.value,o=e?e.value:null;if(Sr(i),i===o)if(i.component){let s=n.getOrCreateContext(i.outlet);this.activateChildRoutes(r,e,s.children)}else this.activateChildRoutes(r,e,n);else if(i.component){let s=n.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let a=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Sr(a.route.value),this.activateChildRoutes(r,null,s.children)}else s.attachRef=null,s.route=i,s.outlet&&s.outlet.activateWith(i,s.injector),this.activateChildRoutes(r,null,s.children)}else this.activateChildRoutes(r,null,n)}},In=class{path;route;constructor(r){this.path=r,this.route=this.path[this.path.length-1]}},Ze=class{component;route;constructor(r,e){this.component=r,this.route=e}};function hu(t,r,e){let n=t._root,i=r?r._root:null;return Et(n,i,e,[n.value])}function fu(t){let r=t.routeConfig?t.routeConfig.canActivateChild:null;return!r||r.length===0?null:{node:t,guards:r}}function nt(t,r){let e=Symbol(),n=r.get(t,e);return n===e?typeof t=="function"&&!oi(t)?t:r.get(t):n}function Et(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=qe(r);return t.children.forEach(s=>{pu(s,o[s.value.outlet],e,n.concat([s.value]),i),delete o[s.value.outlet]}),Object.entries(o).forEach(([s,a])=>Rt(a,e.getContext(s),i)),i}function pu(t,r,e,n,i={canDeactivateChecks:[],canActivateChecks:[]}){let o=t.value,s=r?r.value:null,a=e?e.getContext(t.value.outlet):null;if(s&&o.routeConfig===s.routeConfig){let u=gu(s,o,o.routeConfig.runGuardsAndResolvers);u?i.canActivateChecks.push(new In(n)):(o.data=s.data,o._resolvedData=s._resolvedData),o.component?Et(t,r,a?a.children:null,n,i):Et(t,r,e,n,i),u&&a&&a.outlet&&a.outlet.isActivated&&i.canDeactivateChecks.push(new Ze(a.outlet.component,s))}else s&&Rt(r,a,i),i.canActivateChecks.push(new In(n)),o.component?Et(t,null,a?a.children:null,n,i):Et(t,null,e,n,i);return i}function gu(t,r,e){if(typeof e=="function")return e(t,r);switch(e){case"pathParamsChange":return!Pe(t.url,r.url);case"pathParamsOrQueryParamsChange":return!Pe(t.url,r.url)||!K(t.queryParams,r.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Or(t,r)||!K(t.queryParams,r.queryParams);case"paramsChange":default:return!Or(t,r)}}function Rt(t,r,e){let n=qe(t),i=t.value;Object.entries(n).forEach(([o,s])=>{i.component?r?Rt(s,r.children.getContext(o),e):Rt(s,null,e):Rt(s,r,e)}),i.component?r&&r.outlet&&r.outlet.isActivated?e.canDeactivateChecks.push(new Ze(r.outlet.component,i)):e.canDeactivateChecks.push(new Ze(null,i)):e.canDeactivateChecks.push(new Ze(null,i))}function kt(t){return typeof t=="function"}function mu(t){return typeof t=="boolean"}function vu(t){return t&&kt(t.canLoad)}function Du(t){return t&&kt(t.canActivate)}function yu(t){return t&&kt(t.canActivateChild)}function wu(t){return t&&kt(t.canDeactivate)}function Cu(t){return t&&kt(t.canMatch)}function Ko(t){return t instanceof ei||t?.name==="EmptyError"}var cn=Symbol("INITIAL_VALUE");function tt(){return k(t=>zt(t.map(r=>r.pipe(Ue(1),ii(cn)))).pipe(w(r=>{for(let e of r)if(e!==!0){if(e===cn)return cn;if(e===!1||Eu(e))return e}return!0}),H(r=>r!==cn),Ue(1)))}function Eu(t){return pe(t)||t instanceof et}function Su(t,r){return L(e=>{let{targetSnapshot:n,currentSnapshot:i,guards:{canActivateChecks:o,canDeactivateChecks:s}}=e;return s.length===0&&o.length===0?f(P(d({},e),{guardsResult:!0})):Ru(s,n,i,t).pipe(L(a=>a&&mu(a)?bu(n,o,t,r):f(a)),w(a=>P(d({},e),{guardsResult:a})))})}function Ru(t,r,e,n){return F(t).pipe(L(i=>Mu(i.component,i.route,e,r,n)),ce(i=>i!==!0,!0))}function bu(t,r,e,n){return F(r).pipe(ne(i=>ti(Tu(i.route.parent,n),Au(i.route,n),Iu(t,i.path,e),_u(t,i.route,e))),ce(i=>i!==!0,!0))}function Au(t,r){return t!==null&&r&&r(new Sn(t)),f(!0)}function Tu(t,r){return t!==null&&r&&r(new Cn(t)),f(!0)}function _u(t,r,e){let n=r.routeConfig?r.routeConfig.canActivate:null;if(!n||n.length===0)return f(!0);let i=n.map(o=>Vt(()=>{let s=Nt(r)??e,a=nt(o,s),u=Du(a)?a.canActivate(r,t):$(s,()=>a(r,t));return ae(u).pipe(ce())}));return f(i).pipe(tt())}function Iu(t,r,e){let n=r[r.length-1],o=r.slice(0,r.length-1).reverse().map(s=>fu(s)).filter(s=>s!==null).map(s=>Vt(()=>{let a=s.guards.map(u=>{let c=Nt(s.node)??e,h=nt(u,c),v=yu(h)?h.canActivateChild(n,t):$(c,()=>h(n,t));return ae(v).pipe(ce())});return f(a).pipe(tt())}));return f(o).pipe(tt())}function Mu(t,r,e,n,i){let o=r&&r.routeConfig?r.routeConfig.canDeactivate:null;if(!o||o.length===0)return f(!0);let s=o.map(a=>{let u=Nt(r)??i,c=nt(a,u),h=wu(c)?c.canDeactivate(t,r,e,n):$(u,()=>c(t,r,e,n));return ae(h).pipe(ce())});return f(s).pipe(tt())}function Fu(t,r,e,n){let i=r.canLoad;if(i===void 0||i.length===0)return f(!0);let o=i.map(s=>{let a=nt(s,t),u=vu(a)?a.canLoad(r,e):$(t,()=>a(r,e));return ae(u)});return f(o).pipe(tt(),Xo(n))}function Xo(t){return Jr(O(r=>{if(typeof r!="boolean")throw _n(t,r)}),w(r=>r===!0))}function Ou(t,r,e,n){let i=r.canMatch;if(!i||i.length===0)return f(!0);let o=i.map(s=>{let a=nt(s,t),u=Cu(a)?a.canMatch(r,e):$(t,()=>a(r,e));return ae(u)});return f(o).pipe(tt(),Xo(n))}var Ft=class{segmentGroup;constructor(r){this.segmentGroup=r||null}},Ot=class extends Error{urlTree;constructor(r){super(),this.urlTree=r}};function Ge(t){return rt(new Ft(t))}function Pu(t){return rt(new D(4e3,!1))}function Nu(t){return rt(Yo(!1,N.GuardRejected))}var Lr=class{urlSerializer;urlTree;constructor(r,e){this.urlSerializer=r,this.urlTree=e}lineralizeSegments(r,e){let n=[],i=e.root;for(;;){if(n=n.concat(i.segments),i.numberOfChildren===0)return f(n);if(i.numberOfChildren>1||!i.children[m])return Pu(`${r.redirectTo}`);i=i.children[m]}}applyRedirectCommands(r,e,n,i,o){return Lu(e,i,o).pipe(w(s=>{if(s instanceof J)throw new Ot(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),r,n);if(s[0]==="/")throw new Ot(a);return a}))}applyRedirectCreateUrlTree(r,e,n,i){let o=this.createSegmentGroup(r,e.root,n,i);return new J(o,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(r,e){let n={};return Object.entries(r).forEach(([i,o])=>{if(typeof o=="string"&&o[0]===":"){let a=o.substring(1);n[i]=e[a]}else n[i]=o}),n}createSegmentGroup(r,e,n,i){let o=this.createSegments(r,e.segments,n,i),s={};return Object.entries(e.children).forEach(([a,u])=>{s[a]=this.createSegmentGroup(r,u,n,i)}),new C(o,s)}createSegments(r,e,n,i){return e.map(o=>o.path[0]===":"?this.findPosParam(r,o,i):this.findOrReturn(o,n))}findPosParam(r,e,n){let i=n[e.path.substring(1)];if(!i)throw new D(4001,!1);return i}findOrReturn(r,e){let n=0;for(let i of e){if(i.path===r.path)return e.splice(n),i;n++}return r}};function Lu(t,r,e){if(typeof t=="string")return f(t);let n=t,{queryParams:i,fragment:o,routeConfig:s,url:a,outlet:u,params:c,data:h,title:v}=r;return ae($(e,()=>n({params:c,data:h,queryParams:i,fragment:o,routeConfig:s,url:a,outlet:u,title:v})))}var kr={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function ku(t,r,e,n,i){let o=Jo(t,r,e);return o.matched?(n=iu(r,n),Ou(n,r,e,i).pipe(w(s=>s===!0?o:d({},kr)))):f(o)}function Jo(t,r,e){if(r.path==="**")return xu(e);if(r.path==="")return r.pathMatch==="full"&&(t.hasChildren()||e.length>0)?d({},kr):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(r.matcher||To)(e,t,r);if(!i)return d({},kr);let o={};Object.entries(i.posParams??{}).forEach(([a,u])=>{o[a]=u.path});let s=i.consumed.length>0?d(d({},o),i.consumed[i.consumed.length-1].parameters):o;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:s,positionalParamSegments:i.posParams??{}}}function xu(t){return{matched:!0,parameters:t.length>0?Io(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Ro(t,r,e,n){return e.length>0&&ju(t,e,n)?{segmentGroup:new C(r,Bu(n,new C(e,t.children))),slicedSegments:[]}:e.length===0&&$u(t,e,n)?{segmentGroup:new C(t.segments,Uu(t,e,n,t.children)),slicedSegments:e}:{segmentGroup:new C(t.segments,t.children),slicedSegments:e}}function Uu(t,r,e,n){let i={};for(let o of e)if(Fn(t,r,o)&&!n[q(o)]){let s=new C([],{});i[q(o)]=s}return d(d({},n),i)}function Bu(t,r){let e={};e[m]=r;for(let n of t)if(n.path===""&&q(n)!==m){let i=new C([],{});e[q(n)]=i}return e}function ju(t,r,e){return e.some(n=>Fn(t,r,n)&&q(n)!==m)}function $u(t,r,e){return e.some(n=>Fn(t,r,n))}function Fn(t,r,e){return(t.hasChildren()||r.length>0)&&e.pathMatch==="full"?!1:e.path===""}function zu(t,r,e){return r.length===0&&!t.children[e]}var xr=class{};function Vu(t,r,e,n,i,o,s="emptyOnly"){return new Ur(t,r,e,n,i,s,o).recognize()}var Hu=31,Ur=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(r,e,n,i,o,s,a){this.injector=r,this.configLoader=e,this.rootComponentType=n,this.config=i,this.urlTree=o,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Lr(this.urlSerializer,this.urlTree)}noMatchError(r){return new D(4002,`'${r.segmentGroup}'`)}recognize(){let r=Ro(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(r).pipe(w(({children:e,rootSnapshot:n})=>{let i=new x(n,e),o=new It("",i),s=Uo(n,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,o.url=this.urlSerializer.serialize(s),{state:o,tree:s}}))}match(r){let e=new Ne([],Object.freeze({}),Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),m,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,r,m,e).pipe(w(n=>({children:n,rootSnapshot:e})),De(n=>{if(n instanceof Ot)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof Ft?this.noMatchError(n):n}))}processSegmentGroup(r,e,n,i,o){return n.segments.length===0&&n.hasChildren()?this.processChildren(r,e,n,o):this.processSegment(r,e,n,n.segments,i,!0,o).pipe(w(s=>s instanceof x?[s]:[]))}processChildren(r,e,n,i){let o=[];for(let s of Object.keys(n.children))s==="primary"?o.unshift(s):o.push(s);return F(o).pipe(ne(s=>{let a=n.children[s],u=ou(e,s);return this.processSegmentGroup(r,u,a,s,i)}),ri((s,a)=>(s.push(...a),s)),Bn(null),ni(),L(s=>{if(s===null)return Ge(n);let a=Qo(s);return Gu(a),f(a)}))}processSegment(r,e,n,i,o,s,a){return F(e).pipe(ne(u=>this.processSegmentAgainstRoute(u._injector??r,e,u,n,i,o,s,a).pipe(De(c=>{if(c instanceof Ft)return f(null);throw c}))),ce(u=>!!u),De(u=>{if(Ko(u))return zu(n,i,o)?f(new xr):Ge(n);throw u}))}processSegmentAgainstRoute(r,e,n,i,o,s,a,u){return q(n)!==s&&(s===m||!Fn(i,o,n))?Ge(i):n.redirectTo===void 0?this.matchSegmentAgainstRoute(r,i,n,o,s,u):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(r,i,e,n,o,s,u):Ge(i)}expandSegmentAgainstRouteUsingRedirect(r,e,n,i,o,s,a){let{matched:u,parameters:c,consumedSegments:h,positionalParamSegments:v,remainingSegments:R}=Jo(e,i,o);if(!u)return Ge(e);typeof i.redirectTo=="string"&&i.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Hu&&(this.allowRedirects=!1));let b=new Ne(o,c,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,bo(i),q(i),i.component??i._loadedComponent??null,i,Ao(i)),A=Tn(b,a,this.paramsInheritanceStrategy);return b.params=Object.freeze(A.params),b.data=Object.freeze(A.data),this.applyRedirects.applyRedirectCommands(h,i.redirectTo,v,b,r).pipe(k(E=>this.applyRedirects.lineralizeSegments(i,E)),L(E=>this.processSegment(r,n,e,E.concat(R),s,!1,a)))}matchSegmentAgainstRoute(r,e,n,i,o,s){let a=ku(e,n,i,r,this.urlSerializer);return n.path==="**"&&(e.children={}),a.pipe(k(u=>u.matched?(r=n._injector??r,this.getChildConfig(r,n,i).pipe(k(({routes:c})=>{let h=n._loadedInjector??r,{parameters:v,consumedSegments:R,remainingSegments:b}=u,A=new Ne(R,v,Object.freeze(d({},this.urlTree.queryParams)),this.urlTree.fragment,bo(n),q(n),n.component??n._loadedComponent??null,n,Ao(n)),S=Tn(A,s,this.paramsInheritanceStrategy);A.params=Object.freeze(S.params),A.data=Object.freeze(S.data);let{segmentGroup:E,slicedSegments:Y}=Ro(e,R,b,c);if(Y.length===0&&E.hasChildren())return this.processChildren(h,c,E,A).pipe(w(I=>new x(A,I)));if(c.length===0&&Y.length===0)return f(new x(A,[]));let Bt=q(n)===o;return this.processSegment(h,c,E,Y,Bt?m:o,!0,A).pipe(w(I=>new x(A,I instanceof x?[I]:[])))}))):Ge(e)))}getChildConfig(r,e,n){return e.children?f({routes:e.children,injector:r}):e.loadChildren?e._loadedRoutes!==void 0?f({routes:e._loadedRoutes,injector:e._loadedInjector}):Fu(r,e,n,this.urlSerializer).pipe(L(i=>i?this.configLoader.loadChildren(r,e).pipe(O(o=>{e._loadedRoutes=o.routes,e._loadedInjector=o.injector})):Nu(e))):f({routes:[],injector:r})}};function Gu(t){t.sort((r,e)=>r.value.outlet===m?-1:e.value.outlet===m?1:r.value.outlet.localeCompare(e.value.outlet))}function qu(t){let r=t.value.routeConfig;return r&&r.path===""}function Qo(t){let r=[],e=new Set;for(let n of t){if(!qu(n)){r.push(n);continue}let i=r.find(o=>n.value.routeConfig===o.value.routeConfig);i!==void 0?(i.children.push(...n.children),e.add(i)):r.push(n)}for(let n of e){let i=Qo(n.children);r.push(new x(n.value,i))}return r.filter(n=>!e.has(n))}function bo(t){return t.data||{}}function Ao(t){return t.resolve||{}}function Wu(t,r,e,n,i,o){return L(s=>Vu(t,r,e,n,s.extractedUrl,i,o).pipe(w(({state:a,tree:u})=>P(d({},s),{targetSnapshot:a,urlAfterRedirects:u}))))}function Yu(t,r){return L(e=>{let{targetSnapshot:n,guards:{canActivateChecks:i}}=e;if(!i.length)return f(e);let o=new Set(i.map(u=>u.route)),s=new Set;for(let u of o)if(!s.has(u))for(let c of es(u))s.add(c);let a=0;return F(s).pipe(ne(u=>o.has(u)?Zu(u,n,t,r):(u.data=Tn(u,u.parent,t).resolve,f(void 0))),O(()=>a++),jn(1),L(u=>a===s.size?f(e):te))})}function es(t){let r=t.children.map(e=>es(e)).flat();return[t,...r]}function Zu(t,r,e,n){let i=t.routeConfig,o=t._resolve;return i?.title!==void 0&&!Go(i)&&(o[Pt]=i.title),Vt(()=>(t.data=Tn(t,t.parent,e).resolve,Ku(o,t,r,n).pipe(w(s=>(t._resolvedData=s,t.data=d(d({},t.data),s),null)))))}function Ku(t,r,e,n){let i=Ar(t);if(i.length===0)return f({});let o={};return F(i).pipe(L(s=>Xu(t[s],r,e,n).pipe(ce(),O(a=>{if(a instanceof et)throw _n(new fe,a);o[s]=a}))),jn(1),w(()=>o),De(s=>Ko(s)?te:rt(s)))}function Xu(t,r,e,n){let i=Nt(r)??n,o=nt(t,i),s=o.resolve?o.resolve(r,e):$(i,()=>o(r,e));return ae(s)}function Rr(t){return k(r=>{let e=t(r);return e?F(e).pipe(w(()=>r)):f(r)})}var Hr=(()=>{class t{buildTitle(e){let n,i=e.root;for(;i!==void 0;)n=this.getResolvedTitleForRoute(i)??n,i=i.children.find(o=>o.outlet===m);return n}getResolvedTitleForRoute(e){return e.data[Pt]}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(ts),providedIn:"root"})}return t})(),ts=(()=>{class t extends Hr{title;constructor(e){super(),this.title=e}updateTitle(e){let n=this.buildTitle(e);n!==void 0&&this.title.setTitle(n)}static \u0275fac=function(n){return new(n||t)(p(yo))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ue=new y("",{providedIn:"root",factory:()=>({})}),xe=new y(""),On=(()=>{class t{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=l(Pi);loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return f(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let n=ae(e.loadComponent()).pipe(w(rs),O(o=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=o}),ye(()=>{this.componentLoaders.delete(e)})),i=new Un(n,()=>new V).pipe(xn());return this.componentLoaders.set(e,i),i}loadChildren(e,n){if(this.childrenLoaders.get(n))return this.childrenLoaders.get(n);if(n._loadedRoutes)return f({routes:n._loadedRoutes,injector:n._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(n);let o=ns(n,this.compiler,e,this.onLoadEndListener).pipe(ye(()=>{this.childrenLoaders.delete(n)})),s=new Un(o,()=>new V).pipe(xn());return this.childrenLoaders.set(n,s),s}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function ns(t,r,e,n){return ae(t.loadChildren()).pipe(w(rs),L(i=>i instanceof Ei||Array.isArray(i)?f(i):F(r.compileModuleAsync(i))),w(i=>{n&&n(t);let o,s,a=!1;return Array.isArray(i)?(s=i,a=!0):(o=i.create(e).injector,s=o.get(xe,[],{optional:!0,self:!0}).flat()),{routes:s.map(Vr),injector:o}}))}function Ju(t){return t&&typeof t=="object"&&"default"in t}function rs(t){return Ju(t)?t.default:t}var Pn=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(Qu),providedIn:"root"})}return t})(),Qu=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,n){return e}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Gr=new y(""),qr=new y("");function is(t,r,e){let n=t.get(qr),i=t.get(T);if(!i.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,new Promise(c=>setTimeout(c));let o,s=new Promise(c=>{o=c}),a=i.startViewTransition(()=>(o(),ec(t))),{onViewTransitionCreated:u}=n;return u&&$(t,()=>u({transition:a,from:r,to:e})),s}function ec(t){return new Promise(r=>{bi({read:()=>setTimeout(r)},{injector:t})})}var Wr=new y(""),xt=(()=>{class t{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new V;transitionAbortWithErrorSubject=new V;configLoader=l(On);environmentInjector=l(re);destroyRef=l(zn);urlSerializer=l(me);rootContexts=l(ke);location=l(le);inputBindingEnabled=l(Lt,{optional:!0})!==null;titleStrategy=l(Hr);options=l(ue,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=l(Pn);createViewTransition=l(Gr,{optional:!0});navigationErrorHandler=l(Wr,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>f(void 0);rootComponentType=null;destroyed=!1;constructor(){let e=i=>this.events.next(new yn(i)),n=i=>this.events.next(new wn(i));this.configLoader.onLoadEndListener=n,this.configLoader.onLoadStartListener=e,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(e){let n=++this.navigationId;this.transitions?.next(P(d({},e),{extractedUrl:this.urlHandlingStrategy.extract(e.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:n}))}setupNavigations(e){return this.transitions=new j(null),this.transitions.pipe(H(n=>n!==null),k(n=>{let i=!1;return f(n).pipe(k(o=>{if(this.navigationId>n.id)return this.cancelNavigationTransition(n,"",N.SupersededByNewNavigation),te;this.currentTransition=n,this.currentNavigation={id:o.id,initialUrl:o.rawUrl,extractedUrl:o.extractedUrl,targetBrowserUrl:typeof o.extras.browserUrl=="string"?this.urlSerializer.parse(o.extras.browserUrl):o.extras.browserUrl,trigger:o.source,extras:o.extras,previousNavigation:this.lastSuccessfulNavigation?P(d({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>o.abortController.abort()};let s=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=o.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!s&&a!=="reload"){let u="";return this.events.next(new Q(o.id,this.urlSerializer.serialize(o.rawUrl),u,Ke.IgnoredSameUrlNavigation)),o.resolve(!1),te}if(this.urlHandlingStrategy.shouldProcessUrl(o.rawUrl))return f(o).pipe(k(u=>(this.events.next(new ge(u.id,this.urlSerializer.serialize(u.extractedUrl),u.source,u.restoredState)),u.id!==this.navigationId?te:Promise.resolve(u))),Wu(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),O(u=>{n.targetSnapshot=u.targetSnapshot,n.urlAfterRedirects=u.urlAfterRedirects,this.currentNavigation=P(d({},this.currentNavigation),{finalUrl:u.urlAfterRedirects});let c=new At(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(c)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(o.currentRawUrl)){let{id:u,extractedUrl:c,source:h,restoredState:v,extras:R}=o,b=new ge(u,this.urlSerializer.serialize(c),h,v);this.events.next(b);let A=Vo(this.rootComponentType).snapshot;return this.currentTransition=n=P(d({},o),{targetSnapshot:A,urlAfterRedirects:c,extras:P(d({},R),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=c,f(n)}else{let u="";return this.events.next(new Q(o.id,this.urlSerializer.serialize(o.extractedUrl),u,Ke.IgnoredByUrlHandlingStrategy)),o.resolve(!1),te}}),O(o=>{let s=new gn(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot);this.events.next(s)}),w(o=>(this.currentTransition=n=P(d({},o),{guards:hu(o.targetSnapshot,o.currentSnapshot,this.rootContexts)}),n)),Su(this.environmentInjector,o=>this.events.next(o)),O(o=>{if(n.guardsResult=o.guardsResult,o.guardsResult&&typeof o.guardsResult!="boolean")throw _n(this.urlSerializer,o.guardsResult);let s=new mn(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects),o.targetSnapshot,!!o.guardsResult);this.events.next(s)}),H(o=>o.guardsResult?!0:(this.cancelNavigationTransition(o,"",N.GuardRejected),!1)),Rr(o=>{if(o.guards.canActivateChecks.length!==0)return f(o).pipe(O(s=>{let a=new vn(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),k(s=>{let a=!1;return f(s).pipe(Yu(this.paramsInheritanceStrategy,this.environmentInjector),O({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",N.NoDataFromResolver)}}))}),O(s=>{let a=new Dn(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Rr(o=>{let s=a=>{let u=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&u.push(this.configLoader.loadComponent(a.routeConfig).pipe(O(c=>{a.component=c}),w(()=>{})));for(let c of a.children)u.push(...s(c));return u};return zt(s(o.targetSnapshot.root)).pipe(Bn(null),Ue(1))}),Rr(()=>this.afterPreactivation()),k(()=>{let{currentSnapshot:o,targetSnapshot:s}=n,a=this.createViewTransition?.(this.environmentInjector,o.root,s.root);return a?F(a).pipe(w(()=>n)):f(n)}),w(o=>{let s=au(e.routeReuseStrategy,o.targetSnapshot,o.currentRouterState);return this.currentTransition=n=P(d({},o),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,n}),O(()=>{this.events.next(new Tt)}),du(this.rootContexts,e.routeReuseStrategy,o=>this.events.next(o),this.inputBindingEnabled),Ue(1),$n(new $t(o=>{let s=n.abortController.signal,a=()=>o.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(H(()=>!i&&!n.targetRouterState),O(()=>{this.cancelNavigationTransition(n,n.abortController.signal.reason+"",N.Aborted)}))),O({next:o=>{i=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new B(o.id,this.urlSerializer.serialize(o.extractedUrl),this.urlSerializer.serialize(o.urlAfterRedirects))),this.titleStrategy?.updateTitle(o.targetRouterState.snapshot),o.resolve(!0)},complete:()=>{i=!0}}),$n(this.transitionAbortWithErrorSubject.pipe(O(o=>{throw o}))),ye(()=>{i||this.cancelNavigationTransition(n,"",N.SupersededByNewNavigation),this.currentTransition?.id===n.id&&(this.currentNavigation=null,this.currentTransition=null)}),De(o=>{if(this.destroyed)return n.resolve(!1),te;if(i=!0,Zo(o))this.events.next(new X(n.id,this.urlSerializer.serialize(n.extractedUrl),o.message,o.cancellationCode)),lu(o)?this.events.next(new Qe(o.url,o.navigationBehaviorOptions)):n.resolve(!1);else{let s=new Xe(n.id,this.urlSerializer.serialize(n.extractedUrl),o,n.targetSnapshot??void 0);try{let a=$(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof et){let{message:u,cancellationCode:c}=_n(this.urlSerializer,a);this.events.next(new X(n.id,this.urlSerializer.serialize(n.extractedUrl),u,c)),this.events.next(new Qe(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),o}catch(a){this.options.resolveNavigationPromiseOnError?n.resolve(!1):n.reject(a)}}return te}))}))}cancelNavigationTransition(e,n,i){let o=new X(e.id,this.urlSerializer.serialize(e.extractedUrl),n,i);this.events.next(o),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let e=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),n=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return e.toString()!==n?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function tc(t){return t!==Ye}var os=(()=>{class t{static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(nc),providedIn:"root"})}return t})(),Mn=class{shouldDetach(r){return!1}store(r,e){}shouldAttach(r){return!1}retrieve(r){return null}shouldReuseRoute(r,e){return r.routeConfig===e.routeConfig}},nc=(()=>{class t extends Mn{static \u0275fac=(()=>{let e;return function(i){return(e||(e=Gn(t)))(i||t)}})();static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),ss=(()=>{class t{urlSerializer=l(me);options=l(ue,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=l(le);urlHandlingStrategy=l(Pn);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new J;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:e,initialUrl:n,targetBrowserUrl:i}){let o=e!==void 0?this.urlHandlingStrategy.merge(e,n):n,s=i??o;return s instanceof J?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:e,finalUrl:n,initialUrl:i}){n&&e?(this.currentUrlTree=n,this.rawUrlTree=this.urlHandlingStrategy.merge(n,i),this.routerState=e):this.rawUrlTree=i}routerState=Vo(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:e}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e??this.rawUrlTree)}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:()=>l(rc),providedIn:"root"})}return t})(),rc=(()=>{class t extends ss{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(n=>{n.type==="popstate"&&setTimeout(()=>{e(n.url,n.state,"popstate")})})}handleRouterEvent(e,n){e instanceof ge?this.updateStateMemento():e instanceof Q?this.commitTransition(n):e instanceof At?this.urlUpdateStrategy==="eager"&&(n.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(n),n)):e instanceof Tt?(this.commitTransition(n),this.urlUpdateStrategy==="deferred"&&!n.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(n),n)):e instanceof X&&e.code!==N.SupersededByNewNavigation&&e.code!==N.Redirect?this.restoreHistory(n):e instanceof Xe?this.restoreHistory(n,!0):e instanceof B&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,{extras:n,id:i}){let{replaceUrl:o,state:s}=n;if(this.location.isCurrentPathEqualTo(e)||o){let a=this.browserPageId,u=d(d({},s),this.generateNgRouterState(i,a));this.location.replaceState(e,"",u)}else{let a=d(d({},s),this.generateNgRouterState(i,this.browserPageId+1));this.location.go(e,"",a)}}restoreHistory(e,n=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,o=this.currentPageId-i;o!==0?this.location.historyGo(o):this.getCurrentUrlTree()===e.finalUrl&&o===0&&(this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(n&&this.resetInternalState(e),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,n){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:n}:{navigationId:e}}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Gn(t)))(i||t)}})();static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function Nn(t,r){t.events.pipe(H(e=>e instanceof B||e instanceof X||e instanceof Xe||e instanceof Q),w(e=>e instanceof B||e instanceof Q?0:(e instanceof X?e.code===N.Redirect||e.code===N.SupersededByNewNavigation:!1)?2:1),H(e=>e!==2),Ue(1)).subscribe(()=>{r()})}var ic={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},oc={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},W=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=l(Xn);stateManager=l(ss);options=l(ue,{optional:!0})||{};pendingTasks=l(ui);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=l(xt);urlSerializer=l(me);location=l(le);urlHandlingStrategy=l(Pn);injector=l(re);_events=new V;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=l(os);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=l(xe,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!l(Lt,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}eventsSubscription=new Xr;subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(n=>{try{let i=this.navigationTransitions.currentTransition,o=this.navigationTransitions.currentNavigation;if(i!==null&&o!==null){if(this.stateManager.handleRouterEvent(n,o),n instanceof X&&n.code!==N.Redirect&&n.code!==N.SupersededByNewNavigation)this.navigated=!0;else if(n instanceof B)this.navigated=!0;else if(n instanceof Qe){let s=n.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(n.url,i.currentRawUrl),u=d({browserUrl:i.extras.browserUrl,info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:i.extras.replaceUrl||this.urlUpdateStrategy==="eager"||tc(i.source)},s);this.scheduleNavigation(a,Ye,null,u,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}ru(n)&&this._events.next(n)}catch(i){this.navigationTransitions.transitionAbortWithErrorSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ye,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,n,i)=>{this.navigateToSyncWithBrowser(e,i,n)})}navigateToSyncWithBrowser(e,n,i){let o={replaceUrl:!0},s=i?.navigationId?i:null;if(i){let u=d({},i);delete u.navigationId,delete u.\u0275routerPageId,Object.keys(u).length!==0&&(o.state=u)}let a=this.parseUrl(e);this.scheduleNavigation(a,n,s,o).catch(u=>{this.injector.get(je)(u)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(Vr),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,n={}){let{relativeTo:i,queryParams:o,fragment:s,queryParamsHandling:a,preserveFragment:u}=n,c=u?this.currentUrlTree.fragment:s,h=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":h=d(d({},this.currentUrlTree.queryParams),o);break;case"preserve":h=this.currentUrlTree.queryParams;break;default:h=o||null}h!==null&&(h=this.removeEmptyProps(h));let v;try{let R=i?i.snapshot:this.routerState.snapshot.root;v=Bo(R)}catch{(typeof e[0]!="string"||e[0][0]!=="/")&&(e=[]),v=this.currentUrlTree.root}return jo(v,e,h,c??null)}navigateByUrl(e,n={skipLocationChange:!1}){let i=pe(e)?e:this.parseUrl(e),o=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(o,Ye,null,n)}navigate(e,n={skipLocationChange:!1}){return sc(e),this.navigateByUrl(this.createUrlTree(e,n),n)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,n){let i;if(n===!0?i=d({},ic):n===!1?i=d({},oc):i=n,pe(e))return wo(this.currentUrlTree,e,i);let o=this.parseUrl(e);return wo(this.currentUrlTree,o,i)}removeEmptyProps(e){return Object.entries(e).reduce((n,[i,o])=>(o!=null&&(n[i]=o),n),{})}scheduleNavigation(e,n,i,o,s){if(this.disposed)return Promise.resolve(!1);let a,u,c;s?(a=s.resolve,u=s.reject,c=s.promise):c=new Promise((v,R)=>{a=v,u=R});let h=this.pendingTasks.add();return Nn(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(h))}),this.navigationTransitions.handleNavigationRequest({source:n,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:o,resolve:a,reject:u,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(v=>Promise.reject(v))}static \u0275fac=function(n){return new(n||t)};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function sc(t){for(let r=0;r<t.length;r++)if(t[r]==null)throw new D(4008,!1)}var Ln=(()=>{class t{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=ai(null);get href(){return ut(this.reactiveHref)}set href(e){this.reactiveHref.set(e)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new V;applicationErrorHandler=l(je);options=l(ue,{optional:!0});constructor(e,n,i,o,s,a){this.router=e,this.route=n,this.tabIndexAttribute=i,this.renderer=o,this.el=s,this.locationStrategy=a,this.reactiveHref.set(l(new Ni("href"),{optional:!0}));let u=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=u==="a"||u==="area"||!!(typeof customElements=="object"&&customElements.get(u)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let e=this.preserveFragment,n=i=>i==="merge"||i==="preserve";e||=n(this.queryParamsHandling),e||=!this.queryParamsHandling&&!n(this.options?.defaultQueryParamsHandling),e&&(this.subscription=this.router.events.subscribe(i=>{i instanceof B&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(e){e==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(pe(e)?this.routerLinkInput=e:this.routerLinkInput=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0"))}onClick(e,n,i,o,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(e!==0||n||i||o||s||typeof this.target=="string"&&this.target!="_self"))return!0;let u={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,u)?.catch(c=>{this.applicationErrorHandler(c)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.reactiveHref.set(e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e))??"":null)}applyAttributeValue(e,n){let i=this.renderer,o=this.el.nativeElement;n!==null?i.setAttribute(o,e,n):i.removeAttribute(o,e)}get urlTree(){return this.routerLinkInput===null?null:pe(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(n){return new(n||t)(_(W),_(ee),qn("tabindex"),_(Ae),_(Ee),_(z))};static \u0275dir=Z({type:t,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(n,i){n&1&&Ii("click",function(s){return i.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),n&2&&Ti("href",i.reactiveHref(),Di)("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",ct],skipLocationChange:[2,"skipLocationChange","skipLocationChange",ct],replaceUrl:[2,"replaceUrl","replaceUrl",ct],routerLink:"routerLink"},features:[Ce]})}return t})(),ac=(()=>{class t{router;element;renderer;cdr;link;links;classes=[];routerEventsSubscription;linkInputChangesSubscription;_isActive=!1;get isActive(){return this._isActive}routerLinkActiveOptions={exact:!1};ariaCurrentWhenActive;isActiveChange=new _e;constructor(e,n,i,o,s){this.router=e,this.element=n,this.renderer=i,this.cdr=o,this.link=s,this.routerEventsSubscription=e.events.subscribe(a=>{a instanceof B&&this.update()})}ngAfterContentInit(){f(this.links.changes,f(null)).pipe(it()).subscribe(e=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();let e=[...this.links.toArray(),this.link].filter(n=>!!n).map(n=>n.onChanges);this.linkInputChangesSubscription=F(e).pipe(it()).subscribe(n=>{this._isActive!==this.isLinkActive(this.router)(n)&&this.update()})}set routerLinkActive(e){let n=Array.isArray(e)?e:e.split(" ");this.classes=n.filter(i=>!!i)}ngOnChanges(e){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{let e=this.hasActiveLinks();this.classes.forEach(n=>{e?this.renderer.addClass(this.element.nativeElement,n):this.renderer.removeClass(this.element.nativeElement,n)}),e&&this.ariaCurrentWhenActive!==void 0?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this._isActive!==e&&(this._isActive=e,this.cdr.markForCheck(),this.isActiveChange.emit(e))})}isLinkActive(e){let n=uc(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return i=>{let o=i.urlTree;return o?e.isActive(o,n):!1}}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.link&&e(this.link)||this.links.some(e)}static \u0275fac=function(n){return new(n||t)(_(W),_(Ee),_(Ae),_(Ie),_(Ln,8))};static \u0275dir=Z({type:t,selectors:[["","routerLinkActive",""]],contentQueries:function(n,i,o){if(n&1&&Mi(o,Ln,5),n&2){let s;Fi(s=Oi())&&(i.links=s)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],features:[Ce]})}return t})();function uc(t){return!!t.paths}var Ut=class{};var as=(()=>{class t{router;injector;preloadingStrategy;loader;subscription;constructor(e,n,i,o){this.router=e,this.injector=n,this.preloadingStrategy=i,this.loader=o}setUpPreloading(){this.subscription=this.router.events.pipe(H(e=>e instanceof B),ne(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,n){let i=[];for(let o of n){o.providers&&!o._injector&&(o._injector=Ht(o.providers,e,`Route: ${o.path}`));let s=o._injector??e,a=o._loadedInjector??s;(o.loadChildren&&!o._loadedRoutes&&o.canLoad===void 0||o.loadComponent&&!o._loadedComponent)&&i.push(this.preloadConfig(s,o)),(o.children||o._loadedRoutes)&&i.push(this.processRoutes(a,o.children??o._loadedRoutes))}return F(i).pipe(it())}preloadConfig(e,n){return this.preloadingStrategy.preload(n,()=>{let i;n.loadChildren&&n.canLoad===void 0?i=this.loader.loadChildren(e,n):i=f(null);let o=i.pipe(L(s=>s===null?f(void 0):(n._loadedRoutes=s.routes,n._loadedInjector=s.injector,this.processRoutes(s.injector??e,s.routes))));if(n.loadComponent&&!n._loadedComponent){let s=this.loader.loadComponent(n);return F([o,s]).pipe(it())}else return o})}static \u0275fac=function(n){return new(n||t)(p(W),p(re),p(Ut),p(On))};static \u0275prov=g({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})(),Zr=new y(""),us=(()=>{class t{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource=Ye;restoredId=0;store={};constructor(e,n,i,o,s={}){this.urlSerializer=e,this.transitions=n,this.viewportScroller=i,this.zone=o,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ge?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof B?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof Q&&e.code===Ke.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof Je&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,n){this.zone.runOutsideAngular(()=>jt(this,null,function*(){yield new Promise(i=>{setTimeout(i),typeof requestAnimationFrame<"u"&&requestAnimationFrame(i)}),this.zone.run(()=>{this.transitions.events.next(new Je(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,n))})}))}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(n){Ci()};static \u0275prov=g({token:t,factory:t.\u0275fac})}return t})();function cc(t,...r){return ot([{provide:xe,multi:!0,useValue:t},[],{provide:ee,useFactory:cs,deps:[W]},{provide:Qn,multi:!0,useFactory:ls},r.map(e=>e.\u0275providers)])}function cs(t){return t.routerState.root}function ve(t,r){return{\u0275kind:t,\u0275providers:r}}function lc(t={}){return ve(4,[{provide:Zr,useFactory:()=>{let e=l(ur),n=l(ie),i=l(xt),o=l(me);return new us(o,i,e,n,t)}}])}function ls(){let t=l(Be);return r=>{let e=t.get(er);if(r!==e.components[0])return;let n=t.get(W),i=t.get(ds);t.get(Kr)===1&&n.initialNavigation(),t.get(ps,null,{optional:!0})?.setUpPreloading(),t.get(Zr,null,{optional:!0})?.init(),n.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var ds=new y("",{factory:()=>new V}),Kr=new y("",{providedIn:"root",factory:()=>1});function hs(){let t=[{provide:Kr,useValue:0},Jn(()=>{let r=l(Be);return r.get(nr,Promise.resolve()).then(()=>new Promise(n=>{let i=r.get(W),o=r.get(ds);Nn(i,()=>{n(!0)}),r.get(xt).afterPreactivation=()=>(n(!0),o.closed?f(void 0):o),i.initialNavigation()}))})];return ve(2,t)}function fs(){let t=[Jn(()=>{l(W).setUpLocationChangeListener()}),{provide:Kr,useValue:2}];return ve(3,t)}var ps=new y("");function gs(t){return ve(0,[{provide:ps,useExisting:as},{provide:Ut,useExisting:t}])}function dc(t){return ve(5,[{provide:ue,useValue:t}])}function hc(){return ve(6,[{provide:z,useClass:Xt}])}function ms(){return ve(8,[$r,{provide:Lt,useExisting:$r}])}function vs(t){Kn("NgRouterViewTransitions");let r=[{provide:Gr,useValue:is},{provide:qr,useValue:d({skipNextTransition:!!t?.skipInitialTransition},t)}];return ve(9,r)}var Ds=[le,{provide:me,useClass:fe},W,ke,{provide:ee,useFactory:cs,deps:[W]},On,[]],fc=(()=>{class t{constructor(){}static forRoot(e,n){return{ngModule:t,providers:[Ds,[],{provide:xe,multi:!0,useValue:e},[],n?.errorHandler?{provide:Wr,useValue:n.errorHandler}:[],{provide:ue,useValue:n||{}},n?.useHash?gc():mc(),pc(),n?.preloadingStrategy?gs(n.preloadingStrategy).\u0275providers:[],n?.initialNavigation?vc(n):[],n?.bindToComponentInputs?ms().\u0275providers:[],n?.enableViewTransitions?vs().\u0275providers:[],Dc()]}}static forChild(e){return{ngModule:t,providers:[{provide:xe,multi:!0,useValue:e}]}}static \u0275fac=function(n){return new(n||t)};static \u0275mod=Te({type:t});static \u0275inj=we({})}return t})();function pc(){return{provide:Zr,useFactory:()=>{let t=l(ur),r=l(ie),e=l(ue),n=l(xt),i=l(me);return e.scrollOffset&&t.setOffset(e.scrollOffset),new us(i,n,t,r,e)}}}function gc(){return{provide:z,useClass:Xt}}function mc(){return{provide:z,useClass:Zt}}function vc(t){return[t.initialNavigation==="disabled"?fs().\u0275providers:[],t.initialNavigation==="enabledBlocking"?hs().\u0275providers:[]]}var Yr=new y("");function Dc(){return[{provide:Yr,useFactory:ls},{provide:Qn,multi:!0,useExisting:Yr}]}export{oe as a,Rs as b,bs as c,As as d,Ts as e,Ms as f,Fs as g,Os as h,Wi as i,rd as j,Yi as k,gr as l,Js as m,de as n,se as o,po as p,Ta as q,_a as r,yo as s,Ma as t,B as u,ee as v,jr as w,W as x,Ln as y,ac as z,cc as A,lc as B,hs as C,dc as D,hc as E,vs as F,fc as G};
