import{E as s,F as d,ba as u,ea as C,f as p,ka as g,la as f,ma as v,n as l,oa as x,qa as w}from"./chunk-Y7QKHPW3.js";import"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Hb as t,Ib as e,Jb as n,eb as c,fc as i,ja as r,ka as m}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var o=class o{constructor(){}};o.\u0275fac=function(a){return new(a||o)},o.\u0275cmp=c({type:o,selectors:[["app-register"]],decls:30,vars:0,consts:[[1,"bg-light","dark:bg-transparent","min-vh-100","d-flex","flex-row","align-items-center"],[1,"justify-content-center"],["lg","7","md","9","xl","6"],[1,"mx-4"],[1,"p-4"],["cForm",""],[1,"text-body-secondary"],[1,"mb-3"],["cInputGroupText",""],["cIcon","","name","cilUser"],["autoComplete","name","cFormControl","","placeholder","Username"],["autoComplete","email","cFormControl","","placeholder","Email"],["cIcon","","name","cilLockLocked"],["autoComplete","new-password","cFormControl","","placeholder","Password","type","password"],[1,"mb-4"],["autoComplete","new-password","cFormControl","","placeholder","Repeat password","type","password"],[1,"d-grid"],["cButton","","color","success"]],template:function(a,S){a&1&&(t(0,"div",0)(1,"c-container")(2,"c-row",1)(3,"c-col",2)(4,"c-card",3)(5,"c-card-body",4)(6,"form",5)(7,"h1"),i(8,"Register"),e(),t(9,"p",6),i(10,"Create your account"),e(),t(11,"c-input-group",7)(12,"span",8),r(),n(13,"svg",9),e(),m(),n(14,"input",10),e(),t(15,"c-input-group",7)(16,"span",8),i(17,"@"),e(),n(18,"input",11),e(),t(19,"c-input-group",7)(20,"span",8),r(),n(21,"svg",12),e(),m(),n(22,"input",13),e(),t(23,"c-input-group",14)(24,"span",8),r(),n(25,"svg",12),e(),m(),n(26,"input",15),e(),t(27,"div",16)(28,"button",17),i(29,"Create Account"),e()()()()()()()()())},dependencies:[v,w,x,s,d,u,g,f,p,C,l],encapsulation:2});var E=o;export{E as RegisterComponent};
