import{c as S}from"./chunk-EN2VGXKU.js";import{c as T,f as _,g as C,i as H,n as x,p as I}from"./chunk-VLR5A2CC.js";import{Gb as c,Hb as n,Ib as e,Qb as m,Sb as u,Va as s,_a as a,eb as g,fc as o,gc as d,kb as v,wc as f,xc as h}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";function E(r,i){if(r&1&&(n(0,"div")(1,"h4"),o(2,"Result:"),e(),n(3,"pre"),o(4),f(5,"json"),e()()),r&2){let t=u();s(4),d(h(5,1,t.result))}}function L(r,i){if(r&1&&(n(0,"div",3)(1,"h4"),o(2,"Error:"),e(),n(3,"pre"),o(4),e()()),r&2){let t=u();s(4),d(t.error)}}function P(r,i){r&1&&(n(0,"div")(1,"p"),o(2,"Loading..."),e()())}var l=class l{constructor(i,t){this.http=i;this.authService=t;this.result=null;this.error=""}ngOnInit(){console.log("TestHttpComponent initialized - HttpClient injected successfully!")}testHttpRequest(){let i={headers:new x({"Content-Type":"application/json",Authorization:"Basic "+btoa("med:123456")})};this.http.get("http://localhost:3000/api/customers",i).subscribe({next:t=>{this.result=t,this.error="",console.log("HTTP request successful:",t)},error:t=>{this.error=`HTTP Error: ${t.status} - ${t.message}`,this.result=null,console.error("HTTP request failed:",t)}})}testLogin(){let i={email:"<EMAIL>",mot_de_passe:"89waAmc4XA9"};this.authService.login(i).subscribe({next:t=>{this.result=t,this.error="",console.log("Login successful:",t)},error:t=>{this.error=`Login Error: ${t.type} - ${t.message}`,this.result=null,console.error("Login failed:",t)}})}};l.\u0275fac=function(t){return new(t||l)(a(I),a(S))},l.\u0275cmp=g({type:l,selectors:[["app-test-http"]],decls:11,vars:5,consts:[[3,"click"],[4,"ngIf"],["style","color: red;",4,"ngIf"],[2,"color","red"]],template:function(t,p){t&1&&(n(0,"div")(1,"h3"),o(2,"HTTP Test Component"),e(),n(3,"button",0),m("click",function(){return p.testHttpRequest()}),o(4,"Test Basic HTTP Request"),e(),n(5,"button",0),m("click",function(){return p.testLogin()}),o(6,"Test Login with Provided Credentials"),e(),v(7,E,6,3,"div",1)(8,L,5,1,"div",2)(9,P,3,0,"div",1),f(10,"async"),e()),t&2&&(s(7),c("ngIf",p.result),s(),c("ngIf",p.error),s(),c("ngIf",h(10,3,p.authService.loading$)))},dependencies:[H,T,_,C],encapsulation:2});var y=l;export{y as TestHttpComponent};
