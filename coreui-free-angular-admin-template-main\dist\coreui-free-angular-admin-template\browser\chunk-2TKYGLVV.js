import{b as X}from"./chunk-3MARWV4R.js";import{E as B,F as D,G as A,H as q,I as G,J as U,K as $,L as H,M as j,N as z,db as V,eb as W,fb as Y,gb as Q,hb as J,n as I,oa as K,qa as R,ra as N,s as F,va as M,wa as P}from"./chunk-Y7QKHPW3.js";import{e as O,y as L}from"./chunk-VLR5A2CC.js";import"./chunk-4PPJG5BB.js";import{Db as s,Eb as S,Fb as E,Gb as l,Hb as t,Ib as n,Jb as _,Mb as o,Sb as T,Va as a,ac as g,eb as k,fc as e,hc as h,ja as v,kb as d,tc as u,uc as b,zc as f}from"./chunk-J5YWIVYY.js";import"./chunk-L3UST63Y.js";var y=()=>[],ne=()=>({$implicit:"bottom"}),Z=()=>({g:4}),te=i=>({$implicit:i}),ie=i=>({color:i,width:3}),ae=i=>({top:i});function le(i,r){i&1&&o(0)}function re(i,r){i&1&&o(0)}function me(i,r){i&1&&o(0)}function de(i,r){i&1&&o(0)}function oe(i,r){i&1&&o(0)}function ce(i,r){i&1&&o(0)}function xe(i,r){i&1&&o(0)}function pe(i,r){i&1&&o(0)}function se(i,r){i&1&&o(0)}function Se(i,r){i&1&&o(0)}function Ee(i,r){i&1&&o(0)}function ue(i,r){i&1&&o(0)}function he(i,r){i&1&&o(0)}function ge(i,r){i&1&&o(0)}function Ce(i,r){if(i&1&&(e(0,`
                        `),t(1,"button",57),e(2),n(),e(3,`
                      `)),i&2){let m=r.$implicit;a(),l("itemKey",m)("disabled",m==="Disabled"),a(),h(`
                          `,m,`
                        `)}}function be(i,r){i&1&&o(0)}function fe(i,r){if(i&1&&(e(0,`
                        `),t(1,"c-tab-panel",58),e(2,`
                          `),d(3,be,1,0,"ng-container",48),e(4,`
                        `),n(),e(5,`
                      `)),i&2){let m=r.$implicit;T();let x=g(1278);a(),l("itemKey",m),a(2),l("ngTemplateOutlet",x)("ngTemplateOutletContext",b(3,te,m))}}function ye(i,r){if(i&1&&(e(0,`
                        `),t(1,"button",57),e(2),n(),e(3,`
                      `)),i&2){let m=r.$implicit;a(),l("itemKey",m)("disabled",m==="Disabled"),a(),h(`
                          `,m,`
                        `)}}function _e(i,r){i&1&&o(0)}function Te(i,r){if(i&1&&(e(0,`
                        `),t(1,"c-tab-panel",58),e(2,`
                          `),d(3,_e,1,0,"ng-container",48),e(4,`
                        `),n(),e(5,`
                      `)),i&2){let m=r.$implicit;T();let x=g(1278);a(),l("itemKey",m),a(2),l("ngTemplateOutlet",x)("ngTemplateOutletContext",b(3,te,m))}}function we(i,r){i&1&&o(0)}function ve(i,r){i&1&&o(0)}function ke(i,r){if(i&1&&(e(0,`
              `),t(1,"c-col",19),e(2,`
                `),t(3,"c-card",59),e(4,`
                  `),t(5,"c-card-header"),e(6,"Header"),n(),e(7,`
                  `),t(8,"c-card-body",60),e(9,`
                    `),t(10,"h5",11),e(11),n(),e(12,`
                    `),t(13,"p",12),e(14,`
                      Some quick example text to build on the card title and make up the bulk of
                      the card's content.
                    `),n(),e(15,`
                    `),t(16,"button",61),e(17,"Go somewhere"),n(),e(18,`
                  `),n(),e(19,`
                `),n(),e(20,`
              `),n(),e(21,`
            `)),i&2){let m=r.$implicit;a(3),l("color",m.color)("textColor",m.color==="warning"||m.color==="light"?"dark":"white"),a(8),h("",m.color," card title"),a(5),l("color",m.color)}}function Oe(i,r){if(i&1&&(e(0,`
              `),t(1,"c-col",19),e(2,`
                `),t(3,"c-card",62),e(4,`
                  `),t(5,"c-card-header"),e(6,"Header"),n(),e(7,`
                  `),t(8,"c-card-body"),e(9,`
                    `),t(10,"h5",11),e(11),n(),e(12,`
                    `),t(13,"p",12),e(14,`
                      Some quick example text to build on the card title and make up the bulk of
                      the card's content.
                    `),n(),e(15,`
                    `),t(16,"button",63),e(17,"Go somewhere"),n(),e(18,`
                  `),n(),e(19,`
                `),n(),e(20,`
              `),n(),e(21,`
            `)),i&2){let m=r.$implicit;a(3),l("cBorder",m.color)("textColor",(m==null?null:m.textColor)??""),a(8),h("",m.color," card title"),a(5),l("color",m.color)}}function Le(i,r){if(i&1&&(e(0,`
              `),t(1,"c-col",19),e(2,`
                `),t(3,"c-card",64),e(4,`
                  `),t(5,"c-card-header"),e(6,"Header"),n(),e(7,`
                  `),t(8,"c-card-body"),e(9,`
                    `),t(10,"h5",11),e(11),n(),e(12,`
                    `),t(13,"p",12),e(14,`
                      Some quick example text to build on the card title and make up the bulk of
                      the card's content.
                    `),n(),e(15,`
                    `),t(16,"button",63),e(17,"Go somewhere"),n(),e(18,`
                  `),n(),e(19,`
                `),n(),e(20,`
              `),n(),e(21,`
            `)),i&2){let m=r.$implicit;a(3),l("cBorder",b(6,ae,b(4,ie,m.color)))("textColor",(m==null?null:m.textColor)??""),a(8),h("",m.color," card title"),a(5),l("color",m.color)}}function Ie(i,r){i&1&&o(0)}function Fe(i,r){i&1&&o(0)}function Be(i,r){i&1&&o(0)}function De(i,r){i&1&&o(0)}function Ae(i,r){i&1&&o(0)}function qe(i,r){i&1&&o(0)}function Ge(i,r){i&1&&o(0)}function Ue(i,r){i&1&&o(0)}function $e(i,r){i&1&&o(0)}function He(i,r){i&1&&o(0)}function je(i,r){i&1&&o(0)}function ze(i,r){i&1&&o(0)}function Ke(i,r){i&1&&o(0)}function Re(i,r){i&1&&o(0)}function Ne(i,r){if(i&1&&_(0,"img",65),i&2){let m=r.$implicit;l("cCardImg",m??"top")}}function Me(i,r){if(i&1&&(v(),t(0,"svg",66)(1,"title"),e(2,"Placeholder"),n(),_(3,"rect",67),t(4,"text",68),e(5,"Image cap"),n()()),i&2){let m=r.$implicit;l("cCardImg",m??"top")}}function Pe(i,r){if(i&1&&(t(0,"h5",11),e(1),n(),t(2,"p",12),e(3," Some quick example text to build on the card title and make up the bulk of the card's content. "),n(),t(4,"button",13),e(5,"Go somewhere"),n()),i&2){let m=r.$implicit;a(),h("Card ",m??"title")}}function Ve(i,r){i&1&&(t(0,"h5",11),e(1,"Special title treatment"),n(),t(2,"p",12),e(3," With supporting text below as a natural lead-in to additional content. "),n(),t(4,"button",13),e(5,"Go somewhere"),n())}var C=class C{constructor(){this.colors=[{color:"primary",textColor:"primary"},{color:"secondary",textColor:"secondary"},{color:"success",textColor:"success"},{color:"danger",textColor:"danger"},{color:"warning",textColor:"warning"},{color:"info",textColor:"info"},{color:"light"},{color:"dark"}];this.imgContext={$implicit:"top",bottom:"bottom"};this.tabs=["Active","List","Disabled"]}};C.\u0275fac=function(m){return new(m||C)},C.\u0275cmp=k({type:C,selectors:[["app-cards"]],decls:1281,vars:52,consts:[["imgAngularTemplate",""],["imgPlaceholderTemplate",""],["cardBodyTemplate",""],["headerAndFooterTemplate",""],["xs","12"],[1,"mb-4"],[1,"text-body-secondary","small"],["href","https://coreui.io/docs/utilities/spacing"],["href","components/card"],[2,"width","18rem"],[4,"ngTemplateOutlet"],["cCardTitle",""],["cCardText",""],["cButton","","color","primary"],["href","components/card/#body"],["cCardSubtitle","",1,"mb-2","text-body-secondary"],["cCardLink","",3,"routerLink"],["href","components/card/#images"],["href","components/card/#list-groups"],["lg","4"],["cListGroup","",3,"flush"],["cListGroupItem",""],["href","components/card/#kitchen-sink"],[1,"g-4"],["href","components/card/#header-and-footer",1,"mb-1"],["href","components/card/#header-and-footer"],[1,"blockquote","mb-0"],[1,"blockquote-footer"],["title","Source Title"],[1,"text-center"],[1,"text-body-secondary"],["href","components/card/#sizing",1,"mb-2"],["sm","6"],["href","https://coreui.io/docs/utilities/sizing/"],[1,"w-75","mb-2"],[1,"w-50"],["href","components/card/#sizing"],[1,"mb-2",2,"width","18rem"],["href","https://coreui.io/docs/utilities/text/#text-alignment"],["href","components/card/#text-alignment"],[1,"text-end"],["href","components/card/#navigation"],["activeItemKey","Active"],["variant","tabs",1,"card-header-tabs"],["variant","pills",1,"card-header-pills"],["href","components/card/#image-caps"],["lg","6"],[1,"mb-3"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],["href","components/card/#background-and-color",1,"mb-1"],["href","https://coreui.io/docs/utilities/borders/"],["href","components/card/#border",1,"mb-1"],["href","components/card/#top-border"],["href","components/card/#card-groups"],["href","components/card/#grid-cards"],[1,"mb-3",3,"gutter","md","xs"],[3,"gutter","md","xs"],["cTab","",3,"itemKey","disabled"],[3,"itemKey"],[1,"mb-3",3,"color","textColor"],[1,"bg-gradient"],["cButton","",1,"shadow",3,"color"],[3,"cBorder","textColor"],["cButton","",3,"color"],[1,"mb-3",3,"cBorder","textColor"],["src","assets/images/angular.jpg","alt","CoreUI for Angular",3,"cCardImg"],["aria-label","Placeholder: Image cap","focusable","false","height","180","preserveAspectRatio","xMidYMid slice","role","img","width","100%","xmlns","http://www.w3.org/2000/svg",1,"docs-placeholder-img",3,"cCardImg"],["fill","#868e96","height","100%","width","100%"],["dominant-baseline","middle","dy",".3em","fill","#dee2e6","text-anchor","middle","x","50%","y","50%"]],template:function(m,x){if(m&1&&(t(0,"c-row"),e(1,`
  `),t(2,"c-col",4),e(3,`
    `),t(4,"c-card",5),e(5,`
      `),t(6,"c-card-header"),e(7,`
        `),t(8,"strong"),e(9,"Angular Card"),n(),e(10,`
      `),n(),e(11,`
      `),t(12,"c-card-body"),e(13,`
        `),t(14,"p",6),e(15,`
          Cards are built with as little markup and styles as possible but still
          manage to deliver a bunch of control and customization. Built with
          flexbox, they offer easy alignment and mix well with other CoreUI
          components. Cards have no top, left, and right margins by default, so
          use
          `),t(16,"a",7),e(17,"spacing utilities"),n(),e(18,`
          as needed. They have no fixed width to start, so they'll fill the
          full width of its parent.
        `),n(),e(19,`
        `),t(20,"p",6),e(21,`
          Below is an example of a basic card with mixed content and a fixed
          width. Cards have no fixed width to start, so they'll naturally
          fill the full width of its parent element.
        `),n(),e(22,`
        `),t(23,"app-docs-example",8),e(24,`
          `),t(25,"c-card",9),e(26,`
            `),d(27,le,1,0,"ng-container",10),e(28,`
            `),t(29,"c-card-body"),e(30,`
              `),t(31,"h5",11),e(32,"Card title"),n(),e(33,`
              `),t(34,"p",12),e(35,`
                Some quick example text to build on the card title and make up the bulk of the card's content.
              `),n(),e(36,`
              `),t(37,"button",13),e(38,"Go somewhere"),n(),e(39,`
            `),n(),e(40,`
          `),n(),e(41,`
        `),n(),e(42,`
      `),n(),e(43,`
    `),n(),e(44,`
  `),n(),e(45,`
  `),t(46,"c-col",4),e(47,`
    `),t(48,"c-card",5),e(49,`
      `),t(50,"c-card-header"),e(51,`
        `),t(52,"strong"),e(53,"Angular Card"),n(),e(54," "),t(55,"small"),e(56,"Body"),n(),e(57,`
      `),n(),e(58,`
      `),t(59,"c-card-body"),e(60,`
        `),t(61,"p",6),e(62,`
          The main block of a card is the `),t(63,"code"),e(64,"<c-card-body>"),n(),e(65,`. Use
          it whenever you need a padded section within a card.
        `),n(),e(66,`
        `),t(67,"app-docs-example",14),e(68,`
          `),t(69,"c-card",9),e(70,`
            `),t(71,"c-card-body"),e(72,"This is some text within a card body."),n(),e(73,`
          `),n(),e(74,`
        `),n(),e(75,`
      `),n(),e(76,`
    `),n(),e(77,`
  `),n(),e(78,`
  `),t(79,"c-col",4),e(80,`
    `),t(81,"c-card",5),e(82,`
      `),t(83,"c-card-header"),e(84,`
        `),t(85,"strong"),e(86,"Angular Card"),n(),e(87," "),t(88,"small"),e(89,"Titles, text, and links"),n(),e(90,`
      `),n(),e(91,`
      `),t(92,"c-card-body"),e(93,`
        `),t(94,"p",6),e(95,`
          Card titles are managed by `),t(96,"code"),e(97,"cCardTitle"),n(),e(98,` directive for
          `),t(99,"code"),e(100,"<h*>"),n(),e(101,`. Identically, links are attached and collected
          next to each other by `),t(102,"code"),e(103,"cCardLink"),n(),e(104,` directive for
          `),t(105,"code"),e(106,"<a>"),n(),e(107,` tag. Subtitles are handled by
          `),t(108,"code"),e(109,"cCardSubtitle"),n(),e(110,` directive.
        `),n(),e(111,`
        `),t(112,"p",6),e(113,`
          Store `),t(114,"code"),e(115,"cCardTitle"),n(),e(116," and the "),t(117,"code"),e(118,"cCardSubtitle"),n(),e(119,` items
          in a `),t(120,"code"),e(121,"<c-card-body>"),n(),e(122,`. The card title, and subtitle
          are arranged properly.
        `),n(),e(123,`
        `),t(124,"app-docs-example",14),e(125,`
          `),t(126,"c-card",9),e(127,`
            `),t(128,"c-card-body"),e(129,`
              `),t(130,"h5",11),e(131,"Card title"),n(),e(132,`
              `),t(133,"h6",15),e(134,`
                Card subtitle
              `),n(),e(135,`
              `),t(136,"p",12),e(137,`
                Some quick example text to build on the card title and make up the
                bulk of the card content.
              `),n(),e(138,`
              `),t(139,"a",16),e(140,"Card link"),n(),e(141,`
              `),t(142,"a",16),e(143,"Another link"),n(),e(144,`
            `),n(),e(145,`
          `),n(),e(146,`
        `),n(),e(147,`
      `),n(),e(148,`
    `),n(),e(149,`
  `),n(),e(150,`
  `),t(151,"c-col",4),e(152,`
    `),t(153,"c-card",5),e(154,`
      `),t(155,"c-card-header"),e(156,`
        `),t(157,"strong"),e(158,"Angular Card"),n(),e(159," "),t(160,"small"),e(161,"Images"),n(),e(162,`
      `),n(),e(163,`
      `),t(164,"c-card-body"),e(165,`
        `),t(166,"p",6),e(167,`
          `),t(168,"code"),e(169,'cCardImg="top"'),n(),e(170,` places a picture to the top of the card.
          With `),t(171,"code"),e(172,"cCardText"),n(),e(173,`, text can be added to the card. Text
          within `),t(174,"code"),e(175,"cCardText"),n(),e(176,` can additionally be styled with the
          regular HTML tags.
        `),n(),e(177,`
        `),t(178,"app-docs-example",17),e(179,`
          `),t(180,"c-card",9),e(181,`
            `),d(182,re,1,0,"ng-container",10),e(183,`
            `),t(184,"c-card-body"),e(185,`
              `),t(186,"p",12),e(187,`
                Some quick example `),t(188,"strong"),e(189,"text"),n(),e(190,` to build on the card
                title and make up the bulk of the card content.
              `),n(),e(191,`
            `),n(),e(192,`
          `),n(),e(193,`
        `),n(),e(194,`
      `),n(),e(195,`
    `),n(),e(196,`
  `),n(),e(197,`
  `),t(198,"c-col",4),e(199,`
    `),t(200,"c-card",5),e(201,`
      `),t(202,"c-card-header"),e(203,`
        `),t(204,"strong"),e(205,"Angular Card"),n(),e(206," "),t(207,"small"),e(208,"list groups"),n(),e(209,`
      `),n(),e(210,`
      `),t(211,"c-card-body"),e(212,`
        `),t(213,"p",6),e(214,`
          Create lists of content in a card with a flush list group.
        `),n(),e(215,`
        `),t(216,"app-docs-example",18),e(217,`
          `),t(218,"c-row"),e(219,`
            `),t(220,"c-col",19),e(221,`
              `),t(222,"c-card"),e(223,`
                `),t(224,"ul",20),e(225,`
                  `),t(226,"li",21),e(227,"Cras justo odio"),n(),e(228,`
                  `),t(229,"li",21),e(230,"Dapibus ac facilisis in"),n(),e(231,`
                  `),t(232,"li",21),e(233,"Vestibulum at eros"),n(),e(234,`
                `),n(),e(235,`
              `),n(),e(236,`
            `),n(),e(237,`
            `),t(238,"c-col",19),e(239,`
              `),t(240,"c-card"),e(241,`
                `),t(242,"c-card-header"),e(243,"Header"),n(),e(244,`
                `),t(245,"ul",20),e(246,`
                  `),t(247,"li",21),e(248,"Cras justo odio"),n(),e(249,`
                  `),t(250,"li",21),e(251,"Dapibus ac facilisis in"),n(),e(252,`
                  `),t(253,"li",21),e(254,"Vestibulum at eros"),n(),e(255,`
                `),n(),e(256,`
              `),n(),e(257,`
            `),n(),e(258,`
            `),t(259,"c-col",19),e(260,`
              `),t(261,"c-card"),e(262,`
                `),t(263,"ul",20),e(264,`
                  `),t(265,"li",21),e(266,"Cras justo odio"),n(),e(267,`
                  `),t(268,"li",21),e(269,"Dapibus ac facilisis in"),n(),e(270,`
                  `),t(271,"li",21),e(272,"Vestibulum at eros"),n(),e(273,`
                `),n(),e(274,`
                `),t(275,"c-card-footer"),e(276,"Footer"),n(),e(277,`
              `),n(),e(278,`
            `),n(),e(279,`
          `),n(),e(280,`
        `),n(),e(281,`
      `),n(),e(282,`
    `),n(),e(283,`
  `),n(),e(284,`
  `),t(285,"c-col",4),e(286,`
    `),t(287,"c-card",5),e(288,`
      `),t(289,"c-card-header"),e(290,`
        `),t(291,"strong"),e(292,"Angular Card"),n(),e(293," "),t(294,"small"),e(295,"kitchen sink"),n(),e(296,`
      `),n(),e(297,`
      `),t(298,"c-card-body"),e(299,`
        `),t(300,"p",6),e(301,`
          Combine and match many content types to build the card you need, or
          throw everything in there. Shown below are image styles, blocks, text
          styles, and a list group\u2014all wrapped in a fixed-width card.
        `),n(),e(302,`
        `),t(303,"app-docs-example",22),e(304,`
          `),t(305,"c-card",9),e(306,`
            `),d(307,me,1,0,"ng-container",10),e(308,`
            `),t(309,"c-card-body"),e(310,`
              `),t(311,"h5",11),e(312,"Card title"),n(),e(313,`
              `),t(314,"p",12),e(315,`
                Some quick example text to build on the card title and make up the
                bulk of the card content.
              `),n(),e(316,`
            `),n(),e(317,`
            `),t(318,"ul",20),e(319,`
              `),t(320,"li",21),e(321,"Cras justo odio"),n(),e(322,`
              `),t(323,"li",21),e(324,"Dapibus ac facilisis in"),n(),e(325,`
              `),t(326,"li",21),e(327,"Vestibulum at eros"),n(),e(328,`
            `),n(),e(329,`
            `),t(330,"c-card-body"),e(331,`
              `),t(332,"a",16),e(333,"Card link"),n(),e(334,`
              `),t(335,"a",16),e(336,"Another link"),n(),e(337,`
            `),n(),e(338,`
          `),n(),e(339,`
        `),n(),e(340,`
      `),n(),e(341,`
    `),n(),e(342,`
  `),n(),e(343,`
  `),t(344,"c-col",4),e(345,`
    `),t(346,"c-card",5),e(347,`
      `),t(348,"c-card-header"),e(349,`
        `),t(350,"strong"),e(351,"Card"),n(),e(352," "),t(353,"small"),e(354,"Header and footer"),n(),e(355,`
      `),n(),e(356,`
      `),t(357,"c-card-body"),e(358,`
        `),t(359,"c-row",23),e(360,`
          `),t(361,"c-col"),e(362,`
            `),t(363,"p",6),e(364,`
              Add an optional header and/or footer within a card.
            `),n(),e(365,`
            `),t(366,"app-docs-example",24),e(367,`
              `),t(368,"c-card"),e(369,`
                `),t(370,"c-card-header"),e(371,"Featured"),n(),e(372,`
                `),t(373,"c-card-body"),e(374,`
                  `),d(375,de,1,0,"ng-container",10),e(376,`
                `),n(),e(377,`
              `),n(),e(378,`
            `),n(),e(379,`
          `),n(),e(380,`
          `),t(381,"c-col"),e(382,`
            `),t(383,"p",6),e(384,`
              Card headers can be styled by adding ex.
              `),t(385,"code"),e(386,'"h5"'),n(),e(387,`.
            `),n(),e(388,`
            `),t(389,"app-docs-example",24),e(390,`
              `),t(391,"c-card"),e(392,`
                `),t(393,"c-card-header"),e(394,`
                  `),t(395,"h5"),e(396,"Header"),n(),e(397,`
                `),n(),e(398,`
                `),t(399,"c-card-body"),e(400,`
                  `),d(401,oe,1,0,"ng-container",10),e(402,`
                `),n(),e(403,`
              `),n(),e(404,`
            `),n(),e(405,`
          `),n(),e(406,`
        `),n(),e(407,`
        `),t(408,"c-row"),e(409,`
          `),t(410,"c-col"),e(411,`
            `),t(412,"app-docs-example",25),e(413,`
              `),t(414,"c-card"),e(415,`
                `),t(416,"c-card-header"),e(417,"Quote"),n(),e(418,`
                `),t(419,"c-card-body"),e(420,`
                  `),t(421,"blockquote",26),e(422,`
                    `),t(423,"p"),e(424,`
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                      Integer posuere erat a ante.
                    `),n(),e(425,`
                    `),t(426,"footer",27),e(427,`
                      Someone famous in
                      `),t(428,"cite",28),e(429,"Source Title"),n(),e(430,`
                    `),n(),e(431,`
                  `),n(),e(432,`
                `),n(),e(433,`
              `),n(),e(434,`
            `),n(),e(435,`
          `),n(),e(436,`
          `),t(437,"c-col"),e(438,`
            `),t(439,"app-docs-example",25),e(440,`
              `),t(441,"c-card",29),e(442,`
                `),t(443,"c-card-header"),e(444,"Header"),n(),e(445,`
                `),t(446,"c-card-body"),e(447,`
                  `),d(448,ce,1,0,"ng-container",10),e(449,`
                `),n(),e(450,`
                `),t(451,"c-card-footer",30),e(452,`
                  2 days ago
                `),n(),e(453,`
              `),n(),e(454,`
            `),n(),e(455,`
          `),n(),e(456,`
        `),n(),e(457,`
      `),n(),e(458,`
    `),n(),e(459,`
  `),n(),e(460,`
  `),t(461,"c-col",4),e(462,`
    `),t(463,"c-card",5),e(464,`
      `),t(465,"c-card-header"),e(466,`
        `),t(467,"strong"),e(468,"Angular Card"),n(),e(469," "),t(470,"small"),e(471,"Sizing"),n(),e(472,`
      `),n(),e(473,`
      `),t(474,"c-card-body"),e(475,`
        `),t(476,"p",6),e(477,`
          Cards assume no specific `),t(478,"code"),e(479,"width"),n(),e(480,` to start, so they'll
          be 100% wide unless otherwise stated. You can adjust this as required
          with custom CSS, grid classes, grid Sass mixins, or services.
        `),n(),e(481,`
        `),t(482,"h3"),e(483,"Using grid markup"),n(),e(484,`
        `),t(485,"p",6),e(486,`
          Using the grid, wrap cards in columns and rows as needed.
        `),n(),e(487,`
        `),t(488,"app-docs-example",31),e(489,`
          `),t(490,"c-row",23),e(491,`
            `),t(492,"c-col",32),e(493,`
              `),t(494,"c-card"),e(495,`
                `),t(496,"c-card-body"),e(497,`
                  `),d(498,xe,1,0,"ng-container",10),e(499,`
                `),n(),e(500,`
              `),n(),e(501,`
            `),n(),e(502,`
            `),t(503,"c-col",32),e(504,`
              `),t(505,"c-card"),e(506,`
                `),t(507,"c-card-body"),e(508,`
                  `),d(509,pe,1,0,"ng-container",10),e(510,`
                `),n(),e(511,`
              `),n(),e(512,`
            `),n(),e(513,`
          `),n(),e(514,`
        `),n(),e(515,`
        `),t(516,"h3"),e(517,"Using utilities"),n(),e(518,`
        `),t(519,"p",6),e(520,`
          Use some of `),t(521,"a",33),e(522,"available sizing utilities"),n(),e(523,` to rapidly set a
          card width.
        `),n(),e(524,`
        `),t(525,"app-docs-example",31),e(526,`
          `),t(527,"c-card",34),e(528,`
            `),t(529,"c-card-body"),e(530,`
              `),d(531,se,1,0,"ng-container",10),e(532,`
            `),n(),e(533,`
          `),n(),e(534,`
          `),t(535,"c-card",35),e(536,`
            `),t(537,"c-card-body"),e(538,`
              `),d(539,Se,1,0,"ng-container",10),e(540,`
            `),n(),e(541,`
          `),n(),e(542,`
        `),n(),e(543,`
        `),t(544,"strong"),e(545,"Using custom CSS"),n(),e(546,`
        `),t(547,"p",6),e(548,`
          Use custom CSS in your stylesheets or as inline styles to set a width.
        `),n(),e(549,`
        `),t(550,"app-docs-example",36),e(551,`
          `),t(552,"c-card",37),e(553,`
            `),t(554,"c-card-body"),e(555,`
              `),d(556,Ee,1,0,"ng-container",10),e(557,`
            `),n(),e(558,`
          `),n(),e(559,`
        `),n(),e(560,`
      `),n(),e(561,`
    `),n(),e(562,`
  `),n(),e(563,`
  `),t(564,"c-col",4),e(565,`
    `),t(566,"c-card",5),e(567,`
      `),t(568,"c-card-header"),e(569,`
        `),t(570,"strong"),e(571,"Card"),n(),e(572," "),t(573,"small"),e(574,"Text alignment"),n(),e(575,`
      `),n(),e(576,`
      `),t(577,"c-card-body"),e(578,`
        `),t(579,"p",6),e(580,`
          You can instantly change the text arrangement of any card\u2014in its whole
          or specific parts\u2014with
          `),t(581,"a",38),e(582,"text align"),n(),e(583,`
          classes.
        `),n(),e(584,`
        `),t(585,"app-docs-example",39),e(586,`
          `),t(587,"c-row",23),e(588,`
            `),t(589,"c-col"),e(590,`
              `),t(591,"c-card"),e(592,`
                `),t(593,"c-card-body"),e(594,`
                  `),d(595,ue,1,0,"ng-container",10),e(596,`
                `),n(),e(597,`
              `),n(),e(598,`
            `),n(),e(599,`
            `),t(600,"c-col"),e(601,`
              `),t(602,"c-card",29),e(603,`
                `),t(604,"c-card-body"),e(605,`
                  `),d(606,he,1,0,"ng-container",10),e(607,`
                `),n(),e(608,`
              `),n(),e(609,`
            `),n(),e(610,`
            `),t(611,"c-col"),e(612,`
              `),t(613,"c-card",40),e(614,`
                `),t(615,"c-card-body"),e(616,`
                  `),d(617,ge,1,0,"ng-container",10),e(618,`
                `),n(),e(619,`
              `),n(),e(620,`
            `),n(),e(621,`
          `),n(),e(622,`
        `),n(),e(623,`
      `),n(),e(624,`
    `),n(),e(625,`
  `),n(),e(626,`
  `),t(627,"c-col",4),e(628,`
    `),t(629,"c-card",5),e(630,`
      `),t(631,"c-card-header"),e(632,`
        `),t(633,"strong"),e(634,"Card"),n(),e(635," "),t(636,"small"),e(637,"Navigation"),n(),e(638,`
      `),n(),e(639,`
      `),t(640,"c-card-body"),e(641,`
        `),t(642,"p",6),e(643,`
          Add some navigation to a `),t(644,"code"),e(645,"<c-card-header>"),n(),e(646,` with our
          `),t(647,"code"),e(648,"<c-nav>"),n(),e(649,` component.
        `),n(),e(650,`
        `),t(651,"c-row"),e(652,`
          `),t(653,"c-col"),e(654,`
            `),t(655,"app-docs-example",41),e(656,`
              `),t(657,"c-tabs",42),e(658,`
                `),t(659,"c-card",29),e(660,`
                  `),t(661,"c-card-header"),e(662,`
                    `),t(663,"c-tabs-list",43),e(664,`
                      `),S(665,Ce,4,3,null,null,s),n(),e(667,`
                  `),n(),e(668,`
                  `),t(669,"c-card-body"),e(670,`
                    `),t(671,"c-tabs-content"),e(672,`
                      `),S(673,fe,6,5,null,null,s),n(),e(675,`
                  `),n(),e(676,`
                `),n(),e(677,`
              `),n(),e(678,`
            `),n(),e(679,`
          `),n(),e(680,`
          `),t(681,"c-col"),e(682,`
            `),t(683,"app-docs-example",41),e(684,`
              `),t(685,"c-tabs",42),e(686,`
                `),t(687,"c-card",29),e(688,`
                  `),t(689,"c-card-header"),e(690,`
                    `),t(691,"c-tabs-list",44),e(692,`
                      `),S(693,ye,4,3,null,null,s),n(),e(695,`
                  `),n(),e(696,`
                  `),t(697,"c-card-body"),e(698,`
                    `),t(699,"c-tabs-content"),e(700,`
                      `),S(701,Te,6,5,null,null,s),n(),e(703,`
                  `),n(),e(704,`
                `),n(),e(705,`
              `),n(),e(706,`
            `),n(),e(707,`
          `),n(),e(708,`
        `),n(),e(709,`
      `),n(),e(710,`
    `),n(),e(711,`
  `),n(),e(712,`
  `),t(713,"c-col",4),e(714,`
    `),t(715,"c-card",5),e(716,`
      `),t(717,"c-card-header"),e(718,`
        `),t(719,"strong"),e(720,"Card"),n(),e(721," "),t(722,"small"),e(723,"Image caps"),n(),e(724,`
      `),n(),e(725,`
      `),t(726,"c-card-body"),e(727,`
        `),t(728,"p",6),e(729,`
          Similar to headers and footers, cards can include top and bottom "image
          caps"\u2014images at the top or bottom of a card.
        `),n(),e(730,`
        `),t(731,"app-docs-example",45),e(732,`
          `),t(733,"c-row"),e(734,`
            `),t(735,"c-col",46),e(736,`
              `),t(737,"c-card",47),e(738,`
                `),d(739,we,1,0,"ng-container",10),e(740,`
                `),t(741,"c-card-body"),e(742,`
                  `),t(743,"h5",11),e(744,"Card title"),n(),e(745,`
                  `),t(746,"p",12),e(747,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(748,`
                  `),t(749,"p",12),e(750,`
                    `),t(751,"small",30),e(752,"Last updated 3 mins ago"),n(),e(753,`
                  `),n(),e(754,`
                `),n(),e(755,`
              `),n(),e(756,`
            `),n(),e(757,`
            `),t(758,"c-col",46),e(759,`
              `),t(760,"c-card",47),e(761,`
                `),t(762,"c-card-body"),e(763,`
                  `),t(764,"h5",11),e(765,"Card title"),n(),e(766,`
                  `),t(767,"p",12),e(768,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(769,`
                  `),t(770,"p",12),e(771,`
                    `),t(772,"small",30),e(773,"Last updated 3 mins ago"),n(),e(774,`
                  `),n(),e(775,`
                `),n(),e(776,`
                `),d(777,ve,1,0,"ng-container",48),e(778,`
              `),n(),e(779,`
            `),n(),e(780,`
          `),n(),e(781,`
        `),n(),e(782,`
      `),n(),e(783,`
    `),n(),e(784,`
  `),n(),e(785,`
  `),t(786,"c-col",4),e(787,`
    `),t(788,"c-card",5),e(789,`
      `),t(790,"c-card-header"),e(791,`
        `),t(792,"strong"),e(793,"Card"),n(),e(794," "),t(795,"small"),e(796,"Styles"),n(),e(797,`
      `),n(),e(798,`
      `),t(799,"c-card-body"),e(800,`
        `),t(801,"p",6),e(802,`
          Cards include various options for customizing their backgrounds, borders, and color.
        `),n(),e(803,`
        `),t(804,"h3"),e(805,"Background and color"),n(),e(806,`
        `),t(807,"p",6),e(808,`
          Use `),t(809,"code"),e(810,"color"),n(),e(811,` property to change the appearance of a card.
        `),n(),e(812,`
        `),t(813,"app-docs-example",49),e(814,`
          `),t(815,"c-row"),e(816,`
            `),S(817,ke,22,4,null,null,s),n(),e(819,`
        `),n(),e(820,`
        `),t(821,"h3"),e(822,"Border"),n(),e(823,`
        `),t(824,"p",6),e(825,`
          Use `),t(826,"a",50),e(827,"border utilities"),n(),e(828,` to change
          just the `),t(829,"code"),e(830,"border-color"),n(),e(831,` of a card. Note that you can set
          `),t(832,"code"),e(833,"textColor"),n(),e(834," property on the "),t(835,"code"),e(836,"<c-card>"),n(),e(837,` or a subset of the
          card's contents as shown below.
        `),n(),e(838,`
        `),t(839,"app-docs-example",51),e(840,`
          `),t(841,"c-row",23),e(842,`
            `),S(843,Oe,22,4,null,null,s),n(),e(845,`
        `),n(),e(846,`
        `),t(847,"h3"),e(848,"Top border"),n(),e(849,`
        `),t(850,"p",6),e(851,`
          Use `),t(852,"a",50),e(853,"border utilities"),n(),e(854,` to change
          just the `),t(855,"code"),e(856,"border-color"),n(),e(857,` of a card. Note that you can set
          `),t(858,"code"),e(859,"textColor"),n(),e(860," property on the "),t(861,"code"),e(862,"<c-card>"),n(),e(863,` or a subset of the
          card's contents as shown below.
        `),n(),e(864,`
        `),t(865,"app-docs-example",52),e(866,`
          `),t(867,"c-row"),e(868,`
            `),S(869,Le,22,8,null,null,s),n(),e(871,`
        `),n(),e(872,`
      `),n(),e(873,`
    `),n(),e(874,`
  `),n(),e(875,`
  `),t(876,"c-col",4),e(877,`
    `),t(878,"c-card",5),e(879,`
      `),t(880,"c-card-header"),e(881,`
        `),t(882,"strong"),e(883,"Card"),n(),e(884," "),t(885,"small"),e(886,"Card groups"),n(),e(887,`
      `),n(),e(888,`
      `),t(889,"c-card-body"),e(890,`
        `),t(891,"p",6),e(892,`
          Use card groups to render cards as a single, attached element with equal width and
          height columns. Card groups start off stacked and use `),t(893,"code"),e(894,"display: flex;"),n(),e(895,` to
          become attached with uniform dimensions starting at the `),t(896,"code"),e(897,"sm"),n(),e(898,` breakpoint.
        `),n(),e(899,`
        `),t(900,"app-docs-example",53),e(901,`
          `),t(902,"c-card-group",5),e(903,`
            `),t(904,"c-card"),e(905,`
              `),d(906,Ie,1,0,"ng-container",10),e(907,`
              `),t(908,"c-card-body"),e(909,`
                `),t(910,"h5",11),e(911,"Card title"),n(),e(912,`
                `),t(913,"p",12),e(914,`
                  This is a wider card with supporting text below as a natural lead-in to
                  additional content. This content is a little bit longer.
                `),n(),e(915,`
                `),t(916,"p",12),e(917,`
                  `),t(918,"small",30),e(919,"Last updated 3 mins ago"),n(),e(920,`
                `),n(),e(921,`
              `),n(),e(922,`
            `),n(),e(923,`
            `),t(924,"c-card"),e(925,`
              `),d(926,Fe,1,0,"ng-container",10),e(927,`
              `),t(928,"c-card-body"),e(929,`
                `),t(930,"h5",11),e(931,"Card title"),n(),e(932,`
                `),t(933,"p",12),e(934,`
                  This card has supporting text below as a natural lead-in to additional
                  content.
                `),n(),e(935,`
                `),t(936,"p",12),e(937,`
                  `),t(938,"small",30),e(939,"Last updated 3 mins ago"),n(),e(940,`
                `),n(),e(941,`
              `),n(),e(942,`
            `),n(),e(943,`
            `),t(944,"c-card"),e(945,`
              `),d(946,Be,1,0,"ng-container",10),e(947,`
              `),t(948,"c-card-body"),e(949,`
                `),t(950,"h5",11),e(951,"Card title"),n(),e(952,`
                `),t(953,"p",12),e(954,`
                  This is a wider card with supporting text below as a natural lead-in to
                  additional content. This card has even longer content than the first to show
                  that equal height action.
                `),n(),e(955,`
                `),t(956,"p",12),e(957,`
                  `),t(958,"small",30),e(959,"Last updated 3 mins ago"),n(),e(960,`
                `),n(),e(961,`
              `),n(),e(962,`
            `),n(),e(963,`
          `),n(),e(964,`
        `),n(),e(965,`
        `),t(966,"p",6),e(967,`
          When using card groups with footers, their content will automatically line up.
        `),n(),e(968,`
        `),t(969,"app-docs-example",53),e(970,`
          `),t(971,"c-card-group",5),e(972,`
            `),t(973,"c-card"),e(974,`
              `),d(975,De,1,0,"ng-container",10),e(976,`
              `),t(977,"c-card-body"),e(978,`
                `),t(979,"h5",11),e(980,"Card title"),n(),e(981,`
                `),t(982,"p",12),e(983,`
                  This is a wider card with supporting text below as a natural lead-in to
                  additional content. This content is a little bit longer.
                `),n(),e(984,`
              `),n(),e(985,`
              `),t(986,"c-card-footer"),e(987,`
                `),t(988,"small",30),e(989,"Last updated 3 mins ago"),n(),e(990,`
              `),n(),e(991,`
            `),n(),e(992,`
            `),t(993,"c-card"),e(994,`
              `),d(995,Ae,1,0,"ng-container",10),e(996,`
              `),t(997,"c-card-body"),e(998,`
                `),t(999,"h5",11),e(1e3,"Card title"),n(),e(1001,`
                `),t(1002,"p",12),e(1003,`
                  This card has supporting text below as a natural lead-in to additional
                  content.
                `),n(),e(1004,`
              `),n(),e(1005,`
              `),t(1006,"c-card-footer"),e(1007,`
                `),t(1008,"small",30),e(1009,"Last updated 3 mins ago"),n(),e(1010,`
              `),n(),e(1011,`
            `),n(),e(1012,`
            `),t(1013,"c-card"),e(1014,`
              `),d(1015,qe,1,0,"ng-container",10),e(1016,`
              `),t(1017,"c-card-body"),e(1018,`
                `),t(1019,"h5",11),e(1020,"Card title"),n(),e(1021,`
                `),t(1022,"p",12),e(1023,`
                  This is a wider card with supporting text below as a natural lead-in to
                  additional content. This card has even longer content than the first to show
                  that equal height action.
                `),n(),e(1024,`
              `),n(),e(1025,`
              `),t(1026,"c-card-footer"),e(1027,`
                `),t(1028,"small",30),e(1029,"Last updated 3 mins ago"),n(),e(1030,`
              `),n(),e(1031,`
            `),n(),e(1032,`
          `),n(),e(1033,`
        `),n(),e(1034,`
      `),n(),e(1035,`
    `),n(),e(1036,`
  `),n(),e(1037,`
  `),t(1038,"c-col",4),e(1039,`
    `),t(1040,"c-card",5),e(1041,`
      `),t(1042,"c-card-header"),e(1043,`
        `),t(1044,"strong"),e(1045,"Card"),n(),e(1046," "),t(1047,"small"),e(1048,"Grid cards"),n(),e(1049,`
      `),n(),e(1050,`
      `),t(1051,"c-card-body"),e(1052,`
        `),t(1053,"p",6),e(1054,`
          Use the `),t(1055,"code"),e(1056,"c-row"),n(),e(1057," component and set "),t(1058,"code"),e(1059,"xs|sm|md|lg|xl|xxl"),n(),e(1060,` property
          to control how many grid columns (wrapped around your cards) you show per row. For
          example `),t(1061,"code"),e(1062,'xs="1"'),n(),e(1063," laying out the cards on one column, and "),t(1064,"code"),e(1065,'md="1"'),n(),e(1066,` splitting
          four cards to equal width across multiple rows, from the medium breakpoint up.
        `),n(),e(1067,`
        `),t(1068,"app-docs-example",54),e(1069,`
          `),t(1070,"c-row",55),e(1071,`
            `),t(1072,"c-col"),e(1073,`
              `),t(1074,"c-card"),e(1075,`
                `),d(1076,Ge,1,0,"ng-container",10),e(1077,`
                `),t(1078,"c-card-body"),e(1079,`
                  `),t(1080,"h5",11),e(1081,"Card title"),n(),e(1082,`
                  `),t(1083,"p",12),e(1084,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1085,`
                `),n(),e(1086,`
                `),t(1087,"c-card-footer"),e(1088,`
                  `),t(1089,"small",30),e(1090,"Last updated 3 mins ago"),n(),e(1091,`
                `),n(),e(1092,`
              `),n(),e(1093,`
            `),n(),e(1094,`
            `),t(1095,"c-col"),e(1096,`
              `),t(1097,"c-card"),e(1098,`
                `),d(1099,Ue,1,0,"ng-container",10),e(1100,`
                `),t(1101,"c-card-body"),e(1102,`
                  `),t(1103,"h5",11),e(1104,"Card title"),n(),e(1105,`
                  `),t(1106,"p",12),e(1107,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1108,`
                `),n(),e(1109,`
                `),t(1110,"c-card-footer"),e(1111,`
                  `),t(1112,"small",30),e(1113,"Last updated 3 mins ago"),n(),e(1114,`
                `),n(),e(1115,`
              `),n(),e(1116,`
            `),n(),e(1117,`
            `),t(1118,"c-col"),e(1119,`
              `),t(1120,"c-card"),e(1121,`
                `),d(1122,$e,1,0,"ng-container",10),e(1123,`
                `),t(1124,"c-card-body"),e(1125,`
                  `),t(1126,"h5",11),e(1127,"Card title"),n(),e(1128,`
                  `),t(1129,"p",12),e(1130,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1131,`
                `),n(),e(1132,`
                `),t(1133,"c-card-footer"),e(1134,`
                  `),t(1135,"small",30),e(1136,"Last updated 3 mins ago"),n(),e(1137,`
                `),n(),e(1138,`
              `),n(),e(1139,`
            `),n(),e(1140,`
            `),t(1141,"c-col"),e(1142,`
              `),t(1143,"c-card"),e(1144,`
                `),d(1145,He,1,0,"ng-container",10),e(1146,`
                `),t(1147,"c-card-body"),e(1148,`
                  `),t(1149,"h5",11),e(1150,"Card title"),n(),e(1151,`
                  `),t(1152,"p",12),e(1153,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1154,`
                `),n(),e(1155,`
                `),t(1156,"c-card-footer"),e(1157,`
                  `),t(1158,"small",30),e(1159,"Last updated 3 mins ago"),n(),e(1160,`
                `),n(),e(1161,`
              `),n(),e(1162,`
            `),n(),e(1163,`
          `),n(),e(1164,`
        `),n(),e(1165,`
        `),t(1166,"p",6),e(1167,`
          Change it to `),t(1168,"code"),e(1169,'md="3"'),n(),e(1170,` and you'll see the fourth card wraps.
        `),n(),e(1171,`
        `),t(1172,"app-docs-example",54),e(1173,`
          `),t(1174,"c-row",56),e(1175,`
            `),t(1176,"c-col"),e(1177,`
              `),t(1178,"c-card"),e(1179,`
                `),d(1180,je,1,0,"ng-container",10),e(1181,`
                `),t(1182,"c-card-body"),e(1183,`
                  `),t(1184,"h5",11),e(1185,"Card title"),n(),e(1186,`
                  `),t(1187,"p",12),e(1188,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1189,`
                `),n(),e(1190,`
                `),t(1191,"c-card-footer"),e(1192,`
                  `),t(1193,"small",30),e(1194,"Last updated 3 mins ago"),n(),e(1195,`
                `),n(),e(1196,`
              `),n(),e(1197,`
            `),n(),e(1198,`
            `),t(1199,"c-col"),e(1200,`
              `),t(1201,"c-card"),e(1202,`
                `),d(1203,ze,1,0,"ng-container",10),e(1204,`
                `),t(1205,"c-card-body"),e(1206,`
                  `),t(1207,"h5",11),e(1208,"Card title"),n(),e(1209,`
                  `),t(1210,"p",12),e(1211,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1212,`
                `),n(),e(1213,`
                `),t(1214,"c-card-footer"),e(1215,`
                  `),t(1216,"small",30),e(1217,"Last updated 3 mins ago"),n(),e(1218,`
                `),n(),e(1219,`
              `),n(),e(1220,`
            `),n(),e(1221,`
            `),t(1222,"c-col"),e(1223,`
              `),t(1224,"c-card"),e(1225,`
                `),d(1226,Ke,1,0,"ng-container",10),e(1227,`
                `),t(1228,"c-card-body"),e(1229,`
                  `),t(1230,"h5",11),e(1231,"Card title"),n(),e(1232,`
                  `),t(1233,"p",12),e(1234,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1235,`
                `),n(),e(1236,`
                `),t(1237,"c-card-footer"),e(1238,`
                  `),t(1239,"small",30),e(1240,"Last updated 3 mins ago"),n(),e(1241,`
                `),n(),e(1242,`
              `),n(),e(1243,`
            `),n(),e(1244,`
            `),t(1245,"c-col"),e(1246,`
              `),t(1247,"c-card"),e(1248,`
                `),d(1249,Re,1,0,"ng-container",10),e(1250,`
                `),t(1251,"c-card-body"),e(1252,`
                  `),t(1253,"h5",11),e(1254,"Card title"),n(),e(1255,`
                  `),t(1256,"p",12),e(1257,`
                    This is a wider card with supporting text below as a natural lead-in to
                    additional content. This content is a little bit longer.
                  `),n(),e(1258,`
                `),n(),e(1259,`
                `),t(1260,"c-card-footer"),e(1261,`
                  `),t(1262,"small",30),e(1263,"Last updated 3 mins ago"),n(),e(1264,`
                `),n(),e(1265,`
              `),n(),e(1266,`
            `),n(),e(1267,`
          `),n(),e(1268,`
        `),n(),e(1269,`
      `),n(),e(1270,`
    `),n(),e(1271,`
  `),n(),e(1272,`
`),n(),d(1273,Ne,1,1,"ng-template",null,0,f)(1275,Me,6,1,"ng-template",null,1,f)(1277,Pe,6,1,"ng-template",null,2,f)(1279,Ve,6,0,"ng-template",null,3,f)),m&2){let c=g(1274),p=g(1278),w=g(1280);a(27),l("ngTemplateOutlet",c),a(112),l("routerLink",u(45,y)),a(3),l("routerLink",u(46,y)),a(40),l("ngTemplateOutlet",c),a(42),l("flush",!0),a(21),l("flush",!0),a(18),l("flush",!0),a(44),l("ngTemplateOutlet",c),a(11),l("flush",!0),a(14),l("routerLink",u(47,y)),a(3),l("routerLink",u(48,y)),a(40),l("ngTemplateOutlet",w),a(26),l("ngTemplateOutlet",w),a(47),l("ngTemplateOutlet",p),a(50),l("ngTemplateOutlet",p),a(11),l("ngTemplateOutlet",p),a(22),l("ngTemplateOutlet",p),a(8),l("ngTemplateOutlet",p),a(17),l("ngTemplateOutlet",p),a(39),l("ngTemplateOutlet",p),a(11),l("ngTemplateOutlet",p),a(11),l("ngTemplateOutlet",p),a(48),E(x.tabs),a(8),E(x.tabs),a(20),E(x.tabs),a(8),E(x.tabs),a(38),l("ngTemplateOutlet",c),a(38),l("ngTemplateOutlet",c)("ngTemplateOutletContext",u(49,ne)),a(40),E(x.colors),a(26),E(x.colors),a(26),E(x.colors),a(37),l("ngTemplateOutlet",c),a(20),l("ngTemplateOutlet",c),a(20),l("ngTemplateOutlet",c),a(29),l("ngTemplateOutlet",c),a(20),l("ngTemplateOutlet",c),a(20),l("ngTemplateOutlet",c),a(55),l("gutter",u(50,Z))("md",2)("xs",1),a(6),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c),a(29),l("gutter",u(51,Z))("md",3)("xs",1),a(6),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c),a(23),l("ngTemplateOutlet",c)}},dependencies:[R,K,B,G,D,X,O,z,j,I,H,$,L,M,P,A,F,q,N,U,V,Y,W,Q,J],encapsulation:2});var ee=C;export{ee as CardsComponent};
