import { Injectable } from '@angular/core';
import { TemplateConfig, SIDEBAR_COLORS, SIDEBAR_SIZES } from '../models/theme.interfaces';

/**
 * Configuration Service
 * Manages template configuration and theme settings
 * Based on zen-logistic implementation with Angular best practices
 */
@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  public templateConf!: TemplateConfig;

  constructor() {
    this.setConfigValue();
  }

  /**
   * Set default configuration values
   */
  setConfigValue(): void {
    this.templateConf = {
      layout: {
        variant: 'Light', // options: Dark, Light (managed by CoreUI ColorModeService)
        dir: 'ltr', // Options: ltr, rtl
        customizer: {
          hidden: false // options: true, false
        },
        sidebar: {
          collapsed: false, // options: true, false
          size: SIDEBAR_SIZES.MEDIUM, // Options: 'sidebar-lg', 'sidebar-md', 'sidebar-sm'
          backgroundColor: SIDEBAR_COLORS.WHITE, // Options: 'black', 'pomegranate', 'king-yna', 'ibiza-sunset', 'flickr', 'purple-bliss', 'man-of-steel', 'purple-love'
          
          /* If you want transparent layout add any of the following mentioned classes to backgroundColor of sidebar option :
            bg-glass-1, bg-glass-2, bg-glass-3, bg-glass-4, bg-hibiscus, bg-purple-pizzaz, bg-blue-lagoon, bg-electric-viloet, bg-protage, bg-tundora
          */
          backgroundImage: true, // Options: true, false | Set true to show background image
          backgroundImageURL: 'assets/img/sidebar-bg/01.jpg'
        }
      }
    };
  }

  /**
   * Update configuration
   * @param config - New configuration
   */
  updateConfig(config: Partial<TemplateConfig>): void {
    this.templateConf = { ...this.templateConf, ...config };
  }

  /**
   * Get current configuration
   * @returns Current template configuration
   */
  getConfig(): TemplateConfig {
    return this.templateConf;
  }

  /**
   * Reset configuration to defaults
   */
  resetConfig(): void {
    this.setConfigValue();
  }
}
